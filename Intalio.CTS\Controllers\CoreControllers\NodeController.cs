﻿using Intalio.Core.Model;
using Intalio.Core.UI.Filters;
using Intalio.Core.API;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System;
using Intalio.CTS.Core;
using Intalio.CTS.Filters;

namespace Intalio.CTS.Controllers.CoreControllers
{
    [Route("[controller]/[action]")]
    public class NodeController : BaseController
    {
        #region Ajax

        [HideInSwagger]
        //[CustomMenusAuthorizationFilter(new string[] { nameof(CustomMenus.Nodes) , nameof(CustomMenus.NodesSecurity) })]
        [CustomMenusAuthorizationFilter(new string[] { nameof(CustomMenus.Nodes) })]


        //[Authorize(Roles = "Administrator")]
        [HttpPost]
        //[ValidateAntiForgeryToken]
        public IActionResult Save(NodeViewModel model)
        {
            try
            {
                Result retValue = new Result();
                int code = Microsoft.AspNetCore.Http.StatusCodes.Status400BadRequest;
                bool success = false;
                if (ModelState.IsValid)
                {
                    if (model.Id.HasValue)
                    {
                        var result = ManageNode.Edit(UserId, model,null);
                        success = result.Success;
                        code = Microsoft.AspNetCore.Http.StatusCodes.Status200OK;
                        if (!result.Success)
                        {
                            return StatusCode(code, result.Message);
                        }
                    }
                    else
                    {
                        var result = ManageNode.Create(UserId, model, null);
                        success = result.Success;
                        code = Microsoft.AspNetCore.Http.StatusCodes.Status200OK;
                        if (!result.Success)
                        {
                            return StatusCode(code, result.Message);
                        }
                    }
                }
                return StatusCode(code, model);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        [HideInSwagger]
        [CustomMenusAuthorizationFilter(new string[] { nameof(CustomMenus.UserFolder) })]
        [HttpPost]

        public IActionResult SaveUserNode(NodeViewModel model)
        {
            try
            {
                Result retValue = new Result();
                int code = Microsoft.AspNetCore.Http.StatusCodes.Status400BadRequest;
                bool success = false;
                if (ModelState.IsValid)
                {
                    if (model.Id.HasValue)
                    {
                        var result = ManageNode.Edit(UserId, model , StructureId);
                        success = result.Success;
                        code = Microsoft.AspNetCore.Http.StatusCodes.Status200OK;
                        if (!result.Success)
                        {
                            return StatusCode(code, result.Message);
                        }
                    }
                    else
                    {
                        var result = ManageNode.Create(UserId, model , StructureId);
                        success = result.Success;
                        code = Microsoft.AspNetCore.Http.StatusCodes.Status200OK;
                        if (!result.Success)
                        {
                            return StatusCode(code, result.Message);
                        }
                    }
                }
                return StatusCode(code, model);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }




        [HideInSwagger]
        [CustomMenusAuthorizationFilter(new string[] { nameof(CustomMenus.Nodes), nameof(CustomMenus.NodesSecurity) })]

        //[Authorize(Roles = "Administrator")]
        [HttpDelete]
        public IActionResult Delete(short id)
        {
            try
            {
                ManageNode.Delete(UserId, id);
                return Ok();
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// List nodes
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Produces("application/json")]
        [ProducesResponseType(typeof(List<ValueText>), 200)]
        //[CustomMenusAuthorizationFilter(new string[] { nameof(CustomMenus.Users), nameof(CustomMenus.Roles) })]

        public IActionResult ListNodes()
        {
            try
            {
                return Ok(ManageNode.List(Language));
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// List all tree nodes based on role id and user id
        /// </summary>
        /// <param name="delegationId"></param>
        /// <returns></returns>
        [HttpGet]
        [Produces("application/json")]
        [ProducesResponseType(typeof(List<NodeTreeListViewModel>), 200)]
        public IActionResult ListTreeNodes(long? delegationId = null)
        {
            try
            {
                return Ok(ManageNode.ListNodesByRoleIdUserId(RoleId, UserId, Language, delegationId));
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="delegationId"></param>
        /// <returns></returns>
        [HttpGet]
        [Produces("application/json")]
        public IActionResult ListUserNodes(int structureId, long? delegationId = null)
        {
            var userPermission = Intalio.CTS.Core.Utility.IdentityHelperExtension.GetUserStrucurePermission(UserId, Language, false);
            var ret = ManageNode.ListNodesByRoleIdUserIdStructureId(RoleId, UserId, structureId, (bool)userPermission.IsSecurityBreakedInheritance, Language);
            return Ok(ret);
        }

        /// <summary>
        /// List for node page tree
        /// </summary>
        /// <returns></returns>
        [HideInSwagger]
        //[Authorize(Roles = "Administrator")]
        public IActionResult ListTreeNode()
        {
            try
            {
                return Ok(ManageNode.ListTreeNode());
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }



        public IActionResult ListTreeUserNode()
        {
            try
            {
                return Ok(ManageNode.ListTreeUserNode(UserId , StructureId));
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// List nodes
        /// Not isSearch
        /// </summary>
        /// <returns></returns>
        [HideInSwagger]

        //[Authorize(Roles = "Administrator")]
        [HttpGet]
        [ProducesResponseType(typeof(List<NodeTreeListViewModel>), 200)]
        //[CustomMenusAuthorizationFilter(new string[] { nameof(CustomMenus.Nodes), nameof(CustomMenus.NodesSecurity) })]


        public IActionResult List(string name)
        {
            try
            {
                return Ok(ManageNode.ListNodes(name, Language));
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        [HideInSwagger]
        //[Authorize(Roles = "Administrator")]
        [CustomMenusAuthorizationFilter(new string[] { nameof(CustomMenus.Users), nameof(CustomMenus.Roles) , nameof(CustomMenus) })]

        public IActionResult ListSecurity()
        {
            try
            {
                return Ok(ManageNode.ListSecurity());
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        [HideInSwagger]
        [CustomMenusAuthorizationFilter(new string[] { nameof(CustomMenus.Roles) })]

        public IActionResult CopyFromRole(int fromRole, int toRole)
        {
            try
            {
                ManageNode.CopyFromRole(UserId, fromRole, toRole);
                return Ok();
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        [HideInSwagger]
        //[Authorize(Roles = "Administrator")]
        [CustomMenusAuthorizationFilter(new string[] { nameof(CustomMenus.Users), nameof(CustomMenus.Roles) })]

        public IActionResult UpdateSecurity(List<short> nodeIds, int? roleId, long? userId)
        {
            try
            {
                ManageNode.UpdateSecurity(nodeIds, UserId, roleId, userId);
                return Ok();
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }



        [HideInSwagger]
        [HttpGet]
        public IActionResult GetNameById(long nodeId)
        {
            try
            {
                var NodeName = ManageNode.GetNameById(nodeId);
                return Ok(NodeName);
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }


        #endregion
    }
}
