﻿
.expand-control {
    cursor: pointer;
}

.expand:before {
    content: "\f0fe";
    color: white;
    font-family: Font<PERSON>wesome;
    font-size: 14px; /*arabic*/
}

.colllapse:before {
    content: "\f146";
    color: white;
    font-family: FontAwesome;
    font-size: 14px;
}

td.details-control {
    cursor: pointer;
}

    td.details-control:before {
        content: "\f0fe";
        color: #33ab8d;
        font-family: FontAwesome;
        font-size: 14px; /*arabic*/
    }

tr.shown td.details-control:before {
    content: "\f146";
    color: #33ab8d;
    font-family: FontAwesome;
    font-size: 14px;
}

span.select2 {
    width: 100% !important;
}

.buttons-container {
    display: flex;
}

.marker {
    background-color: Yellow;
}

a:hover, a:focus {
    text-decoration: none;
}

.select2-search__field {
    width: 100% !important;
}

textarea {
    resize: none;
}

.card {
    margin-bottom: 1.25rem
}

    .card .table {
        margin-bottom: 0
    }

        .card .table > thead > tr > th {
            border-top: 0
        }

    .card.card-transparent {
        border: 0;
        background-color: transparent;
        box-shadow: 0 0 0 #000
    }

        .card.card-transparent .card-body,
        .card.card-transparent .card-header {
            background-color: transparent;
            padding-left: 0;
            padding-right: 0
        }

.card-flat {
    margin: 0 !important;
    border: 0
}

.card-columns-2 {
    column-count: 1
}

@media (min-width:768px) {
    .card-columns-2 {
        column-count: 2
    }

    .vipDetailsPanel {
        padding-right: 2px;
        padding-left: 5px;
    }

    .vipCorrPanel {
        padding-left: 5px;
        padding-right: 5px;
    }

    .vipCorrLeftPanel {
    }
}

.card-header > a[data-tool] {
    display: inline-block;
    color: #fff;
    width: 2em;
    text-align: center
}

.card-default .card-header > a[data-tool] {
    color: #c1c2c3
}

.card-header > .badge.float-right {
    margin-top: 3px
}

    .card-header > .badge.float-right + .badge.float-right {
        margin-right: 10px
    }

.card-footer .pagination {
    margin: 0
}

.card-footer .radial-bar,
.card-footer p {
    margin-bottom: 0
}

.card {
    border-color: transparent;
    box-shadow: 0 1px 1px rgba(0, 0, 0, .05)
}

    .card .card-header {
        border-bottom: 0;
        padding: .625rem .9375rem;
        background-color: transparent
    }

        .card .card-header .card-title {
            margin-bottom: 0;
            font-size: 1rem
        }

        .card .card-header a {
            text-decoration: none !important
        }

    .card .card-body {
        padding: .9375rem
    }

    .card .card-footer {
        padding: .625rem .9375rem
    }

.card-default {
    border-top-width: 3px;
    border-color: #cfdbe2
}

    .card-default .card-header {
        background-color: #fff
    }

.card {
    position: relative;
    display: flex;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background-color: #fff;
    background-clip: border-box;
    border: 1px solid rgba(0,0,0,.125);
    border-radius: .25rem;
}

.rounded-circle {
    border-radius: 50% !important;
    width: 60px;
    height: 60px;
}

.btn-float-abs {
    position: absolute;
    bottom: -15px;
    right: 44%
}

.cards tbody tr {
    float: left;
    width: auto;
    margin: 1.5rem;
    border: 0.0625rem solid rgba(0,0,0,.125);
    border-radius: .25rem;
    box-shadow: 0.25rem 0.25rem 0.5rem rgba(0,0,0,0.25);
}

.cards tbody td {
    display: block;
}

.cards thead {
    display: none;
}

.cards td:before {
    content: attr(data-label);
    display: inline;
    position: relative;
    font-size: 85%;
    top: -0.5rem;
    float: left;
    color: #808080;
    min-width: 4rem;
    margin-left: 0;
    margin-right: 1rem;
    text-align: left;
}

tr.selected td:before {
    color: #404040;
}

.card-body p {
    word-wrap: break-word;
    width: 280px;
    white-space: pre-wrap;
    margin: 0;
    margin-bottom: 10px;
    height: 105px;
    text-overflow: ellipsis;
    overflow: hidden;
}

.card-footer span {
    margin-bottom: 10px;
}

.cardSelected {
    background-color: #ccc;
}


.align-self-center {
    -webkit-align-self: center !important;
    -ms-flex-item-align: center !important;
    align-self: center !important;
}

.icon-action-redo, .icon-action-undo, .icon-anchor, .icon-arrow-down, .icon-arrow-left, .icon-arrow-right, .icon-arrow-up, .icon-badge, .icon-bag, .icon-ban, .icon-bar-chart, .icon-basket, .icon-basket-loaded, .icon-bell, .icon-book-open, .icon-briefcase, .icon-bubble, .icon-bubbles, .icon-bulb, .icon-calculator, .icon-calendar, .icon-call-end, .icon-call-in, .icon-call-out, .icon-camcorder, .icon-camera, .icon-check, .icon-chemistry, .icon-clock, .icon-close, .icon-cloud-download, .icon-cloud-upload, .icon-compass, .icon-control-end, .icon-control-forward, .icon-control-pause, .icon-control-play, .icon-control-rewind, .icon-control-start, .icon-credit-card, .icon-crop, .icon-cup, .icon-cursor, .icon-cursor-move, .icon-diamond, .icon-direction, .icon-directions, .icon-disc, .icon-dislike, .icon-doc, .icon-docs, .icon-drawer, .icon-drop, .icon-earphones, .icon-earphones-alt, .icon-emoticon-smile, .icon-energy, .icon-envelope, .icon-envelope-letter, .icon-envelope-open, .icon-equalizer, .icon-eye, .icon-eyeglasses, .icon-feed, .icon-film, .icon-fire, .icon-flag, .icon-folder, .icon-folder-alt, .icon-frame, .icon-game-controller, .icon-ghost, .icon-globe, .icon-globe-alt, .icon-graduation, .icon-graph, .icon-grid, .icon-handbag, .icon-heart, .icon-home, .icon-hourglass, .icon-info, .icon-key, .icon-layers, .icon-like, .icon-link, .icon-list, .icon-lock, .icon-lock-open, .icon-login, .icon-logout, .icon-loop, .icon-magic-wand, .icon-magnet, .icon-magnifier, .icon-magnifier-add, .icon-magnifier-remove, .icon-map, .icon-microphone, .icon-mouse, .icon-moustache, .icon-music-tone, .icon-music-tone-alt, .icon-note, .icon-notebook, .icon-paper-clip, .icon-paper-plane, .icon-pencil, .icon-picture, .icon-pie-chart, .icon-pin, .icon-plane, .icon-playlist, .icon-plus, .icon-pointer, .icon-power, .icon-present, .icon-printer, .icon-puzzle, .icon-question, .icon-refresh, .icon-reload, .icon-rocket, .icon-screen-desktop, .icon-screen-smartphone, .icon-screen-tablet, .icon-settings, .icon-share, .icon-share-alt, .icon-shield, .icon-shuffle, .icon-size-actual, .icon-size-fullscreen, .icon-social-dribbble, .icon-social-dropbox, .icon-social-facebook, .icon-social-tumblr, .icon-social-twitter, .icon-social-youtube, .icon-speech, .icon-speedometer, .icon-star, .icon-support, .icon-symbol-female, .icon-symbol-male, .icon-tag, .icon-target, .icon-trash, .icon-trophy, .icon-umbrella, .icon-user, .icon-user-female, .icon-user-follow, .icon-user-following, .icon-user-unfollow, .icon-users, .icon-vector, .icon-volume-1, .icon-volume-2, .icon-volume-off, .icon-wallet, .icon-wrench {
    font-family: Simple-Line-Icons;
    speak: none;
    font-style: normal;
    font-weight: 400;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
}

.font-large-2 {
    font-size: 3rem !important;
}

.primary {
    color: #00B5B8 !important;
}

.success {
    color: #16D39A !important;
}

.danger {
    color: #FF7588 !important;
}

#grdProductVersionList .ml, .mh {
    margin-left: 1px !important;
}

.warning {
    color: #ff902b !important;
}

.card .pull-left .text-md {
    width: 165px;
    font-size: 18px;
}

.btn-info-dark {
    color: #fff;
    background-color: #5bc0de;
    border-color: #46b8da;
}

    .btn-info-dark:hover {
        color: #fff !important;
        background-color: #138496;
        border-color: #117a8b;
    }

/*.btn-info {
    color: #fff;
    background-color: #3ed2ad;
    border-color: #3ed2ad;
}

    .btn-info:hover {
        color: #fff;
        background-color: #37BC9B;
        border-color: #37BC9B;
    }*/

.example {
    height: 30px;
}

    .example.full-story {
        height: auto;
    }

    .example .toggle:hover {
        opacity: 1;
    }

    .example .toggle {
        font-size: 12px;
        text-decoration: none;
        opacity: .6;
        float: right;
    }

    .example p {
        margin: 0;
        margin-top: 10px;
    }

    .example .toggle:before {
        content: '[ Show more ]';
    }

    .example.full-story .toggle:before {
        content: '[ Show less ]';
    }

@media only screen and (max-width: 1490px) {
    .cards td:before {
        min-width: 1rem;
    }

    .card {
        min-height: 26rem;
    }

    .card-body p {
        width: 255px;
    }

    .card .pull-left .text-md {
        width: 135px;
    }
}

@media only screen and (max-width: 1280px) {
    .card-body p {
        width: 230px;
    }

    .card .pull-left .text-sm, .card .pull-left .text-md {
        width: 125px;
    }
}

.btn-primary {
    background-color: #37BC9B;
    border-color: #37BC9B;
}

a {
    color: #37BC9B;
}

    a:hover, a:focus {
        color: #3ed2ad;
    }

.form-control:focus {
    border-color: #3ed2ad;
}

.btn-primary:active, .btn-primary:hover, .btn-primary:focus {
    background-color: #3dd2ac;
    border-color: #3ed2ad;
}

.sweet-overlay {
    z-index: 1050 !important;
}

.form-control:focus ~ .bootstrap-filestyle .form-control {
    border: #3ed2ad 1px solid;
}

.radio label, .checkbox label {
    font-weight: bold !important;
}

body .bootstrap-tagsinput {
    display: block;
}

.checkbox label {
    font-weight: bold;
}

.no-hover:hover {
    background-color: #fff !important;
}

.table.no-row-border > thead > tr > th, .table.no-row-border > thead > tr > td,
.table.no-row-border > tbody > tr > th, .table.no-row-border > tbody > tr > td,
.table.no-row-border > tfoot > tr > th, .table.no-row-border > tfoot > tr > td {
    border-top: 0px;
    padding-left: 0px;
}


.javascriptInfoDiv {
    padding: 5px 10px;
    position: absolute;
    border-radius: 4px;
    border: 1px solid #ccc;
    max-width: 340px;
    z-index: -1;
    left: 80px;
    margin-top: 0px;
    background-color: #fff;
    box-shadow: 0px 0px 3px 3px #ccc;
    transition: all 0.3s ease-out;
}

.javascriptInfoDivIcon {
    padding-left: 5px;
    color: #b3b3b3;
    cursor: pointer;
}

    .javascriptInfoDivIcon:hover + .javascriptInfoDiv {
        opacity: 1 !important;
        z-index: 20;
    }

.panel.b .panel-heading {
    background-color: #edf1f2;
}

.listValueInfoDiv {
    padding: 5px 10px;
    position: absolute;
    border-radius: 4px;
    border: 1px solid #ccc;
    max-width: 340px;
    z-index: -1;
    left: 80px;
    margin-top: 0px;
    background-color: #fff;
    box-shadow: 0px 0px 3px 3px #ccc;
    transition: all 0.3s ease-out;
}

.listValueInfoDivIcon {
    padding-left: 5px;
    color: #b3b3b3;
    cursor: pointer;
}

    .listValueInfoDivIcon:hover + .listValueInfoDiv {
        opacity: 1 !important;
        z-index: 20;
    }

.text-color-context {
    color: #515253;
}

.parsley-error + .select2.select2-container.select2-container--bootstrap .selection .select2-selection.select2-selection--multiple {
    border-color: #f05050;
}

.profilePicture {
    text-align: center;
}

    .profilePicture .profile-pic {
        width: 200px;
        min-height: 115px;
        max-height: 200px;
        display: block;
    }

    .profilePicture .file-upload {
        display: none;
    }

    .profilePicture .circle {
        text-align: center;
        border-radius: 1000px !important;
        overflow: hidden;
        width: 128px;
        height: 128px;
        border: 8px solid rgba(255, 255, 255, 0.7);
    }

    .profilePicture img {
        max-width: 100%;
        height: auto;
    }

    .profilePicture .p-image {
        cursor: pointer;
        position: relative;
        top: -40px;
        right: -55px;
        color: #666666;
        transition: all .3s cubic-bezier(.175, .885, .32, 1.275);
    }

        .profilePicture .p-image:hover {
            transition: all .3s cubic-bezier(.175, .885, .32, 1.275);
        }

    .profilePicture .upload-button {
        font-size: 1.2em;
    }

        .profilePicture .upload-button:hover {
            transition: all .3s cubic-bezier(.175, .885, .32, 1.275);
            color: #999;
        }

body .bootstrap-tagsinput {
    display: block;
    font-size: 15px;
}

    body .bootstrap-tagsinput input {
        width: auto !important;
        min-width: 50px
    }

.panelCollapseStyle {
    border-top-width: 1px !important;
    box-shadow: none !important;
}
/*start profile card*/
@media only screen and (max-width: 1490px) {
    .card.profile-card-3 {
        min-height: initial;
    }
}

.profile-card-3 {
    font-family: 'Open Sans', Arial, sans-serif;
    position: relative;
    float: left;
    overflow: hidden;
    width: 100%;
    text-align: center;
    border: none;
    box-shadow: 0px 5px 10px rgba(0,0,0,.1);
}

    .profile-card-3 .card-content {
        width: 100%;
        padding: 15px 25px;
        color: #232323;
        float: left;
        border-radius: 0 0 5px 5px;
        position: relative;
        z-index: 9999;
    }

    .profile-card-3 .profile {
        border-radius: 50%;
        position: absolute;
        left: 50%;
        top: 15px;
        max-width: 70px;
        opacity: 1;
        box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.2);
        border: 2px solid rgba(255, 255, 255, 1);
        -webkit-transform: translate(-50%, 0%);
        transform: translate(-50%, 0%);
        z-index: 99999;
    }

    .profile-card-3 .profile-thumb-block {
        height: 100px;
    }

    .profile-card-3 label {
        color: #555;
        margin-bottom: 20px
    }

.hoverable {
    -webkit-transition: all .55s ease-in-out;
    transition: all .55s ease-in-out;
}

    .hoverable:hover {
        -webkit-box-shadow: 0 2px 9px 0 rgba(0,0,0,0.2), 0 1px 4px 0 rgba(0,0,0,0.19);
        box-shadow: 0 2px 9px 0 rgba(0,0,0,0.2), 0 1px 4px 0 rgba(0,0,0,0.19);
        -webkit-transition: all .55s ease-in-out;
        transition: all .55s ease-in-out;
    }
/*end*/
.font-weight-normal {
    font-weight: 400 !important;
}

form {
    counter-reset: section;
}

#questionsContainer .row strong::before {
    counter-increment: section;
    content: counter(section) ". ";
}

*::-webkit-scrollbar {
    width: .7em;
}

*::-webkit-scrollbar-thumb {
    background-color: lightgray;
    outline: 1px solid slategrey;
    /*border-radius: 10px;*/
}

*::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px rgba(255, 255, 255, 0.9);
    background-color: white;
}

.stepwizard-step p {
    margin-top: 10px;
}

.stepwizard-row {
    display: table-row;
}

.stepwizard {
    display: table;
    width: 100%;
    position: relative;
}

.stepwizard-step a[disabled] {
    opacity: 1 !important;
    filter: alpha(opacity=100) !important;
    pointer-events: none;
}

.stepwizard-row:before {
    top: 22px;
    bottom: 0;
    position: absolute;
    content: " ";
    width: 100%;
    height: 1px;
    background-color: #ccc;
}

.stepwizard-step {
    display: table-cell;
    text-align: center;
    position: relative;
}

.btn-circle-45 {
    width: 45px;
    height: 45px;
    text-align: center;
    font-size: 12px;
    line-height: 45px;
    border-radius: 500px;
    padding: 0;
    border: 0;
}

.mt-36px {
    margin-top: 36px !important;
}

.pagination > .active > a, .pagination > .active > a:hover, .pagination > .active > a:focus, .pagination > .active > span, .pagination > .active > span:hover, .pagination > .active > span:focus {
    background-color: #37BC9B;
    border-color: #37BC9B;
}

.previousBtn {
    margin-left: 5px;
    margin-right: 5px;
}

.formio-form .table-bordered {
    border: 1px solid #eee !important;
}

.formio-form .btn-wizard-nav-cancel {
    display: none;
}

.infoDiv {
    padding: 5px 10px;
    position: absolute;
    border-radius: 4px;
    border: 1px solid #ccc;
    z-index: -1;
    right: 110px;
    margin-top: 0px;
    background-color: #fff;
    box-shadow: 0px 0px 3px 3px #ccc;
    transition: all 0.3s ease-out;
    color: #555;
}

.infoDivIcon {
    cursor: pointer;
}

    .infoDivIcon:hover ~ .infoDiv {
        opacity: 1 !important;
        z-index: 20;
    }

.unlock:hover > .infoDiv {
    opacity: 1 !important;
    z-index: 20;
}

.aside-collapsed .parentMenu {
    display: block;
    position: absolute;
    width: 20px;
    top: 10px;
    right: 3px;
}

    .aside-collapsed .parentMenu .label {
        position: unset !important;
        height: 16px !important;
        right: 0px !important;
        margin: 1px !important;
    }

.formio-form .has-feedback .form-control {
    padding-right: 10px;
}

.formio-form .alert-danger > label {
    color: white;
}

.row .nodata {
    font-size: 16px;
    color: #888;
    text-align: center;
    font-weight: 500;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.navbar-nav a {
    cursor: pointer;
}

.btn-scroll {
    cursor: pointer;
}

.with-border {
    margin-bottom: 0px;
    border-bottom-left-radius: 0px;
    border-bottom-right-radius: 0px;
}

.without-border {
    margin-bottom: 0px;
    border-bottom-left-radius: 0px;
    border-bottom-right-radius: 0px;
    border-bottom: 0;
}

.text-primary {
    color: #00AE8D;
}

.pagination > li > a:hover, .pagination > li > a:focus, .pagination > li > span:hover, .pagination > li > span:focus {
    color: #00ae8d;
    cursor: pointer;
}

.pagination > li > a:hover, .pagination > li > a:focus, .pagination > li > span:hover, .pagination > li > span:focus {
    color: #00ae8d;
}

.choices__list--dropdown .choices__item--selectable.is-highlighted {
    background-color: #37bc9b !important;
    color: #fff;
}

.delegationLi {
    padding: 12px 15px !important;
    color: #919DA8 !important;
    font-size: 13px !important;
    letter-spacing: .035em !important;
}

.wrapper > .aside {
    z-index: 112;
}

.nav-floating .parentMenu {
    width: auto;
}

.aside-collapsed .delegationLi {
    color: unset !important;
}

.sidebar .nav > li > a:hover {
    cursor: pointer;
}

.modal-xl {
    width: calc(100% - 3%) !important;
}

.modal-body {
    height: calc(100% - 55px) !important;
}

.modal-content {
    height: 100%;
}

.myDocumentsLoadingMask {
    background-color: #fff;
    height: 58vh;
    left: 20px;
    top: 7px;
    width: calc(100% - 4rem);
    position: absolute;
    z-index: 9999;
    display: none;
}

.pad-t-2 {
    padding-top: 2px !important;
}

.jstreeNoDataAvailable {
    background-color: #fff;
    height: 58vh;
    left: 20px;
    top: 7px;
    width: calc(100% - 4rem);
    position: absolute;
    z-index: 9999;
    align-items: center;
    display: none;
    justify-content: center;
}

.waitingBackground {
    background-image: url('/images/waiting.svg');
    background-position: center;
    background-repeat: no-repeat;
}

.margin-0-auto {
    margin: 0 auto !important;
}

.flex-center {
    display: flex !important;
    align-items: center;
}

.pointer {
    cursor: pointer;
}

a[ref='fileLink'] {
    cursor: pointer;
}

.staticFlatPickrDate .flatpickr-wrapper {
    display: table-cell !important;
}

.modal-window {
    width: 100%;
}

.time {
    font-size: 11px;
}

td.details-form {
    cursor: pointer;
}

@media (min-width: 768px) {
    table td .form-group, table td .form-control, table td .favorite-item {
        display: block !important;
        width: 100% !important;
    }

    .table .formio-component .checkbox {
        width: auto !important;
    }
}

.wid-auto {
    width: auto !important;
}

.min-h-55px {
    min-height: 55px !important;
}

.min-max-width-50-50 {
    min-width: 50px !important;
    max-width: 50px !important;
    word-break: break-word;
}

.min-max-width-50-100 {
    min-width: 50px !important;
    max-width: 100px !important;
    word-break: break-word;
}
.min-max-width-50-150 {
    min-width: 200px !important;
    max-width: 250px !important;
    word-break: break-word;
}

/* column width*/
.min-max-width-50-150 {
    min-width: 50px !important;
    max-width: 250px !important;
    word-break: break-word;
}


.min-max-width-50-250 {
    min-width: 50px !important;
    max-width: 250px !important;
    word-break: break-word;
}

.min-max-width-50-450 {
    min-width: 50px !important;
    max-width: 250px !important;
    word-break: break-all;
}

.colorBoxDt {
    display: inline-flex;
    vertical-align: text-bottom;
    margin: 0px 5px;
    width: 16px;
    height: 16px;
}


.margin-0 {
    margin: 0px !important;
}

.break-word-class {
    word-break: break-word;
}

.bfh-colorpicker-popover > canvas {
    cursor: url('../../images/customcrosshair.png'),auto;
}

.modal-dialog .bfh-colorpicker-popover {
    top: auto !important;
    bottom: 0;
    margin-left: 105%;
    margin-bottom: -5px;
    padding: 10px !important;
}

    .modal-dialog .bfh-colorpicker-popover:lang(ar) {
        margin-right: 105%;
        margin-left: auto !important;
        right: 0 !important;
        left: auto !important;
    }

.break-word-class {
    word-break: break-word;
}

.width-100px {
    width: 100px !important;
}

.col-form-label {
    color: #555;
    font-size: 12px;
}

.dwtDiv {
    padding: 20px;
}


.jstree-icon.jstree-themeicon.default.jstree-themeicon-custom {
    display: none;
}

.display-none {
    display: none;
}


.classInfoDivIcon:hover + .classInfoDiv {
    opacity: 1 !important;
    z-index: 20;
}

.classInfoDiv {
    padding: 5px 10px;
    position: absolute;
    border-radius: 4px;
    border: 1px solid #ccc;
    max-width: 250px;
    z-index: -1;
    left: 80px;
    margin-top: 0px;
    background-color: #fff;
    box-shadow: 0px 0px 3px 3px #ccc;
    transition: all 0.3s ease-out;
}

.dt-center {
    text-align: center;
}

.width20 {
    width: 20px;
}


.minwidth55 {
    min-width: 55px;
}

.mt-15px {
    margin-top: 15px !important;
}

#categoryManagement li.active a {
    background: rgba(234, 234, 234, 1) !important;
    border-left: 2px solid #00ae8d !important;
    border: 0px;
    color: #00ae8d;
    border-radius: 0px;
}

    #categoryManagement li.active a:lang(ar) {
        border-left: 0px !important;
        border-right: 2px solid #00ae8d !important;
    }

#categoryManagement .list-group-item {
    border: 0px;
    line-height: 20px;
    margin-bottom: 5px;
}

.width100 {
    width: 100px !important;
}

.jstree-icon.jstree-themeicon.default.jstree-themeicon-custom {
    display: none;
}

.jstree-proton .jstree-search {
    color: #ff9433 !important;
}

fieldset.fieldset-o {
    padding-bottom: inherit;
    border-bottom: inherit;
    margin-bottom: inherit;
    border: 1px solid rgba(0,0,0,.125);
    border-radius: 6px;
    margin: 0 2px;
    padding: 0.35em 0.625em 0.75em;
}

.panel-body > div > .fieldset-o {
    margin-top: 10px;
}

legend.legend-o {
    display: block;
    font-size: 14px;
    line-height: inherit;
    color: #3a3f51;
    margin-bottom: inherit;
    width: auto;
    border-bottom: none;
}

.panel-title .c-checkbox {
    margin-top: 0px;
    margin-bottom: 0px;
}
/*.checkbox-o {
    margin-left: 12px;
    margin-top: 20px;
    margin-bottom: 0px;
}*/

/*.fieldset-o .checkbox-o {
    margin-top: 12px;
}*/
.panel.b .panel-title {
    font-size: 14px;
}

.context-menu-icon.context-menu-icon--fa::before {
    color: #37BC9B;
}

.context-menu-item.context-menu-hover {
    background-color: #37BC9B;
}

div.parameters {
    margin: 1.5em 2em 1.5em 2em !important;
    border-radius: 4px;
    transition: box-shadow ease .5s;
    position: relative;
}

.parameter-label {
    padding: 0px 31px;
    font-size: medium;
}

.delete-row {
    padding: 3px 5px !important;
    font-size: 15px !important;
}

.classInfoDiv {
    padding: 5px 10px;
    position: absolute;
    border-radius: 4px;
    border: 1px solid #ccc;
    max-width: 250px;
    z-index: -1;
    margin-top: 0px;
    background-color: #fff;
    left: 80px;
}

.classInfoDivAra {
    padding: 5px 10px;
    position: absolute;
    border-radius: 4px;
    border: 1px solid #ccc;
    max-width: 250px;
    z-index: -1;
    margin-top: 0px;
    background-color: #fff;
}

.classInfoDivIcon {
    padding-left: 5px;
    padding-right: 5px;
    color: #b3b3b3;
    cursor: pointer;
}

    .classInfoDivIcon:hover + .classInfoDiv {
        opacity: 1 !important;
        z-index: 20;
    }

    .classInfoDivIcon:hover + .classInfoDivAra {
        opacity: 1 !important;
        z-index: 20;
    }


.removeListStyle li {
    list-style: none;
    cursor: pointer;
}

#containerDiv .fade {
    display: none;
}

    #containerDiv .fade.in {
        display: block;
    }

#containerDiv .fade {
    transition: none;
}

.height-305px {
    height: 305px;
}

@media (min-width:1137px) {
    .modal-mdl {
        width: calc(100% - 600px) !important;
    }
}

.attributeTranslationTable tbody tr {
    height: 55px;
}

.DTE_Field_InputControl {
    margin-top: -20px !important;
    margin-left: -5px !important;
}

    .DTE_Field_InputControl:lang(ar) {
        margin-left: auto !important;
        margin-right: -5px !important;
    }

    .DTE_Field_InputControl input {
        height: 34px !important;
        padding-top: 0px !important;
        padding-left: 7px !important;
        margin-left: -3px !important;
        width: calc(100% - 20px) !important;
        border: 1px solid #ccc !important;
        border-radius: 3px !important;
        outline: 0px solid slategrey !important;
        -webkit-appearance: none !important;
    }

        .DTE_Field_InputControl input:focus {
            border-color: #5dafa3 !important;
            box-shadow: 0px 0px 10px rgba(0,174,52,.3) !important;
        }

        .DTE_Field_InputControl input:lang(ar) {
            padding-right: 7px !important;
            margin-right: -3px !important;
        }

.width-25pc {
    width: 25% !important;
}


.removeListStyle li {
    list-style: none;
    cursor: pointer;
}

#containerDiv .fade {
    display: none;
}

    #containerDiv .fade.in {
        display: block;
    }

#containerDiv .fade {
    transition: none;
}

.vakata-context {
    z-index: 9999 !important;
}

a.disabled {
    pointer-events: none;
    cursor: default;
}

.formio-form .builder-components > div.no-drag {
    background-color: #3dd2ac;
}

.margin-8 {
    margin-top: 8px;
}

.scan-btns {
    margin: 0;
    padding: 0;
    width: 48px;
    line-height: 3;
    background: #fff;
    border-left: 1px solid #ccc;
    min-height: 456px;
    max-height: 500px;
}

.operateGrp {
    padding: 0;
    border: solid 1px #ccc;
    border-right: none;
    border-left: none;
    margin: 0;
    max-height: 510px;
}

    .operateGrp li {
        cursor: pointer;
        list-style: none;
        margin: 0;
        /*height: 50px;
        line-height: 52px;*/
        border-bottom: solid 1px #ccc;
        text-align: center;
    }

        .operateGrp li:last-child {
            border-bottom: none;
        }

        .operateGrp li i {
            font-size: 20px !important;
        }

        .operateGrp li:last-child {
            position: relative;
            bottom: 5px;
        }

.showHideScanDiv #dwtcontrolContainer .ds-dwt-container-box div > div {
    border: 0px !important;
}

#scanContainer .ds-dwt-container-box > div {
    width: 100% !important;
}

    #scanContainer .ds-dwt-container-box > div > div {
        margin-top: -1px;
        margin-left: -1px;
    }

.scrtabs-tab-scroll-arrow-left {
    border-right: 0px !important;
}

.scrtabs-tab-scroll-arrow-right {
    border-left: 0px !important;
}

.scrtabs-tab-scroll-arrow {
    color: #00AE8D;
    text-align: center;
}

.dz-message {
    color: #646C7F;
}

.dropzone .dz-message {
    font-weight: 400;
}

.dropzone {
    border: 2px dashed #37BC9B;
    border-radius: 5px;
}

.modalAdvanceSearchConfigurationColumns {
    overflow: hidden !important;
    z-index: 9999999 !important;
}

.top-left-0 {
    top: 0 !important;
    left: 0 !important;
}

.vakata-context li > a:hover {
    background-color: #fafbfc;
}

.panel.panel-default.borderTop-1 {
    border-top-width: 1px;
}

.documentTabDataBorder {
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
    border-color: #cfdbe2;
}

#documentWithViewerContainer .scrtabs-tabs-fixed-container,
#documentWithViewerContainer .scrtabs-tab-container,
#documentWithViewerContainer .scrtabs-tab-scroll-arrow-left,
#documentWithViewerContainer .scrtabs-tab-scroll-arrow-right {
    height: 60px;
}

.custom-toolbar {
    /*border-radius: 3px;*/
}
/* clears the ‘X’ from Internet Explorer */
input[type=search]::-ms-clear, input[type=text]::-ms-clear {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
}

input[type=search]::-ms-reveal, input[type=text]::-ms-reveal {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
}

/* clears the ‘X’ from Chrome */
input[type="search"]::-webkit-search-decoration,
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-results-button,
input[type="search"]::-webkit-search-results-decoration,
input[type="text"]::-webkit-search-decoration,
input[type="text"]::-webkit-search-cancel-button,
input[type="text"]::-webkit-search-results-button,
input[type="text"]::-webkit-search-results-decoration {
    display: none !important;
}

.form-control[disabled], fieldset[disabled] .form-control, .formio-form.formio-read-only .well {
    background-color: #eee !important;
}

.tree-custom-toolbar {
    background-color: #00AE8D;
    padding: 3px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
}

.tree-custom-toolbarBtn, .tree-custom-toolbarBtn[disabled]:hover {
    border: none;
    background-color: #00AE8D;
    color: #fff;
}

    .tree-custom-toolbarBtn:hover {
        background-color: #4dc3a5;
        color: #fff;
    }

.tree-panel-body {
    padding: 5px 10px;
}

.fa-customGreen {
    color: #37BC9B;
}

.dz-max-files-reached {
    pointer-events: none;
    cursor: default;
}

.dz-remove {
    pointer-events: auto;
}

@media (max-width: 768px) {
    .jstree-proton-responsive .jstree-wholerow {
        border-top: 0px !important;
    }

    .jstree-proton-responsive .jstree-icon, .jstree-proton-responsive .jstree-icon:empty {
        width: 22px;
    }
}

.dropzone .dz-preview .dz-error-message {
    top: 145px;
}

@media (min-width:768px) {
    .mt-33 {
        margin-top: 33px !important;
    }
}

.fs-16 {
    font-size: 16px !important;
}

.line-height-20px {
    line-height: 20px !important;
}

.mt-10-5px {
    margin-top: 10.5px !important;
}

.close:hover[disabled] {
    cursor: not-allowed;
    color: #ccc;
}

.addressBookSelect > div > span {
    width: calc(100% - 35px) !important;
    display: inline-block;
}

.width-100 {
    width: 100% !important;
}


.jstree-container-ul {
    width: 100%;
}

.jstree-ellipsis {
    overflow: hidden
}

    .jstree-ellipsis .jstree-anchor {
        width: calc(100% - 29px);
        text-overflow: ellipsis;
        overflow: hidden
    }

    .jstree-ellipsis.jstree-no-icons .jstree-anchor {
        width: calc(100% - 5px)
    }

#ImgSizeEditor {
    padding: 10px 15px;
    position: absolute;
    z-index: 1;
    top: 147px;
    left: 65px;
    height: auto;
    width: auto;
    font-size: 14px;
    color: #606060;
    text-align: left;
    background-color: #f5f5f5;
    border-collapse: collapse;
    border: 1px solid #ccc;
    box-shadow: 4px 4px 18px #ccc;
    -webkit-box-shadow: 4px 4px 18px #ccc;
    -moz-box-shadow: 4px 4px 18px #ccc;
}

    #ImgSizeEditor input[type='text'] {
        padding-left: 3px;
    }

    #ImgSizeEditor input {
        background: #fff;
        border: solid 1px #ccc;
    }

    #ImgSizeEditor ul {
        list-style: none;
        padding-left: 0px;
        margin: 0px;
    }

    #ImgSizeEditor li {
        margin-bottom: 5px;
    }

    #ImgSizeEditor label {
        display: block;
        font-weight: normal;
    }

@media (max-width:1200px) {
    .mb-panel, #attachmentContainer > .col-lg-3 > .panel {
        margin-bottom: 21px;
    }

    .col-xs-12 .min-height-85vh, .col-sm-12 .min-height-85vh {
        min-height: 85vh;
    }

    #attachmentContainer > .col-lg-3 > div {
        width: 100%;
    }

    #viewerFrame {
        height: 100vh !important;
    }
}

@media (min-width:1200px) {
    .mb-panel, #attachmentContainer > .col-lg-3 > .panel {
        margin-bottom: 0px;
    }

    .div-flex {
        display: flex !important;
    }
}

.width-600px {
    width: 600px;
}

.width-400px {
    width: 400px;
}

.width-300px {
    width: 300px;
}

.width-275px {
    width: 275px !important;
}

.width-250px {
    width: 250px !important;
}

.width-200px {
    width: 200px;
}

.width-150px {
    width: 150px;
}

.width-100px {
    width: 100px;
}

.width-80px {
    width: 80px;
}

.width-50px {
    width: 50px;
}

.metadata-label {
    color: rgba(99, 99, 99, 0.5);
    font-style: italic;
    font-weight: normal !important;
}

hr.metadata-hr {
    margin-top: 4px !important;
}

.col-sm-3.width-auto {
    width: auto !important;
}

.text-wrap {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.break-word {
    word-break: break-word;
    width: 50px !important;
}

.jqte {
    border: none !important;
    margin: 0px;
}

.jqte_focused {
    border-color: #5dafa3 !important;
    box-shadow: 0 0 10px rgba(0,174,52,.3) !important;
    -webkit-box-shadow: 0 0 10px rgba(0,174,52,.3) !important;
    -moz-box-shadow: 0 0 10px rgba(0,174,52,.3) !important;
}

.jqte_editor, .jqte_source {
    max-height: 250px !important;
}

.pad-t-0_align-initial {
    text-align: initial !important;
    padding-top: 0;
}

.width-275px {
    width: 275px !important;
}

.overflow-auto {
    overflow: auto !important;
}

.overflow-x-auto {
    overflow-x: scroll !important;
}

.overflow-y-auto {
    overflow-y: auto !important;
}

.font-14 {
    font-size: 14px !important;
}

.font-15 {
    font-size: 15px !important;
}

.font-16 {
    font-size: 16px !important;
}

.font-18 {
    font-size: 18px !important;
}

.modal-vt {
    width: 515px;
    margin: 30px auto;
}

.three-dots-ellipsis {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

/*.tooltip > .tooltip-inner {
    background-color: #eee;
    color: #222;
    max-width: 400px;
    opacity: 1;
    box-shadow: 0px 0px 5px;
}*/

.padding-2-5 {
    padding: 2px 5px !important;
}

.no_checkbox > i.jstree-checkbox {
    display: none;
}

fieldset.border {
    border: 1px solid #cfdbe2 !important;
    padding: 0 1.4em 1.4em 1.4em !important;
    margin: 0 0 1.5em 0 !important;
    -webkit-box-shadow: 0px 0px 0px 0px #cfdbe2;
    box-shadow: 0px 0px 0px 0px #cfdbe2;
    border-radius: 4px;
}

legend.border {
    color: #656565 !important;
    font-size: 14px !important;
    font-weight: bold !important;
    width: auto;
    padding: 0 10px;
    border-bottom: none;
}

.workingDaysLabel {
    right: 70px;
    white-space: nowrap;
    width: auto !important;
    border: 1px solid !important;
    position: absolute;
    top: 6px;
    padding: 2px 6px;
    background-color: #fff;
    font-family: "RubikRegular",sans-serif;
    font-size: 12px;
    z-index: 99;
    border-radius: 4px;
    color: red;
}

.floating {
    float: left !important;
}

#attachmentContainer > .col-lg-3 {
    padding-left: 0px;
    padding-right: 5px;
}

#attachmentContainer > .col-lg-9 {
    padding-left: 0px;
    padding-right: 0px;
}

#documentWithViewerContainer {
    padding-left: 0px;
    padding-right: 5px;
}

#documentViewerContainer {
    padding-left: 0px;
    padding-right: 0px;
}

#documentWithViewerContainer div div .tab-pane.fade {
    min-height: 85vh;
}

.margin-5 {
    margin-right: 5px !important;
}

.transferInstruction {
    position: absolute;
    box-shadow: rgba(0, 0, 0, 0.05) 5px 15px 10px;
    z-index: 9999;
    Margin-top: 20px;
    width: 60%;
    left: 30%;
}

.padding-left-0 {
    padding-left: 0px;
}

.ml-1-25em {
    margin-left: 1.25em;
}

.ml-0-7em {
    margin-left: 0.7em;
}

#purposeContainer span.select2-dropdown.select2-dropdown--below {
    margin-left: -1px;
}

#setPurposeToAll {
    cursor: pointer;
    margin-right: 15px;
}

.classInfoDivIcon {
    padding-left: 5px;
    color: #b3b3b3;
    cursor: pointer;
}

.evenRowColorRI {
    background-color: #ededed !important;
}

    .EvenRowColorRI:hover {
        background-color: #ededed !important;
    }

.oddRowColorRI {
    background-color: #f7f7f7 !important;
}

    .OddRowColorRI:hover {
        background-color: #f7f7f7 !important;
    }

.break-word-classRI {
    word-break: break-word;
}

.whiteBackgroundColorRI {
    background-color: #fff !important;
}

    .whiteBackgroundColorRI:hover {
        background-color: #fff !important;
    }

.headInDataTableRI {
    margin: 0px !important;
    padding: 4px 0px !important;
}

.headInDataTableThRI {
    padding: 5px 8px !important;
    color: #33ab8d !important;
}

.input-group-btn .select2-selection {
    height: 35px;
    border-left: 0;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.m-xs {
    margin: 3px;
}

.btn-outline.btn-primary {
    border: 1px solid #37BC9B;
    color: #37BC9B;
    background-color: transparent;
}

    .btn-outline.btn-primary:hover, .btn-outline.btn-primary:focus {
        color: white !important;
    }


.searchInfoDiv {
    color: #555;
    background-color: #fbfbfb;
    position: absolute;
    width: 400px;
    border-radius: 3px;
    z-index: 10000000;
    border: thin solid #ccc;
}

.searchInfoDiv-closeIcon {
    padding: 8px 10px;
    cursor: pointer;
    vertical-align: middle;
}

.searchInfoTitleSpan {
    padding: 6px 10px;
    font-size: 15px;
}

.font-13px {
    font-size: 13px !important;
}

.SearchInfoUL {
    columns: 2;
    padding: 10px 15px;
}

    .SearchInfoUL > li {
        font-size: 13px !important;
    }

.searchInfoDivTitleDiv {
    height: 35px;
    border-bottom: 1px solid #bbb;
}

.cursor-move {
    cursor: move !important;
}

.mr-10px {
    margin-right: 10px !important;
}

.classInfoDivIcon:hover + .classInfoDiv {
    z-index: 1053 !important;
}

.light-blue {
    color: #00bcd4;
}

.danger-red {
    color: #f12b2b;
}

.margin-0-5 {
    margin: 0px 5px;
}

.max-width-50 {
    max-width: 50px !important;
}

@media only screen and (min-width: 992px) {
    .nf-tab-with-viewer .html5buttons {
        float: none !important;
    }
}

.width100pc-ml0 {
    width: 100% !important;
    margin-left: 0px !important;
}

input.cke_dialog_ui_input_text:focus, input.cke_dialog_ui_input_password:focus, textarea.cke_dialog_ui_input_textarea:focus, select.cke_dialog_ui_input_select:focus {
    outline: 0;
    border: 1px solid #37BC9B !important;
}

a.cke_dialog_ui_button_ok {
    color: #fff;
    background: #00AE8D !important;
    border: 1px solid #00AE8D !important;
}

    a.cke_dialog_ui_button_ok:hover {
        background: #37BC9B !important;
        border-color: #37BC9B !important;
    }



.linkedDocumentBody {
    padding-bottom: 0px !important;
    background-color: #f5f7fa !important;
}

.linkedDocumentBodyDiv div div div div .tab-content {
    border-bottom: 0px !important;
}

.removestroke {
    stroke: none !important;
}

.formio-component-dataGrid td:last-child div {
    background-color: #3dd2ac !important;
}


.searchDivAll {
    position: absolute;
    width: 250px;
    box-shadow: rgba(0, 0, 0, 0.05) 5px 15px 10px;
    z-index: 9999;
    display: block !important;
}

.dashboardPieChart > .highcharts-container {
    height: 100% !important;
}

.height-250px {
    height: 250px !important;
}

.border-top-1px {
    border-top-width: 1px !important;
}

.mdl-ul {
    margin: 0;
    padding: 0;
    list-style: none;
    height: 100%;
}

.mdl-li {
    border-bottom: 1px solid rgba(0,0,0,.12);
    vertical-align: middle;
    width: auto !important;
    position: relative;
    box-sizing: border-box;
    transition-duration: .18s;
    transition-timing-function: cubic-bezier(.4,0,.2,1);
    transition-property: background-color;
    transition: background-color ease .5s;
    cursor: pointer;
    background-color: white;
    margin-bottom: 10px;
    border-color: #cfdbe2;
    border-style: solid;
    border-width: 1px;
    border-radius: 5px;
    padding: 0px 5px 0px 5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, .20);
    height: auto !important;
}

    .mdl-li.active {
        /*        background-color: #f3f6f8;*/
        border-color: #00ae8d;
        border-width: 2px;
    }

    .mdl-li:hover {
        background-color: #f3f6f8;
    }

.mdl-text-msg {
    height: 75px;
    color: #888;
    font-size: 14px;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    position: relative;
}

.mdl-span {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    display: block;
    padding-bottom: 5px;
    height: 20px;
}

.listContainer::-webkit-scrollbar {
    display: none; /* for Chrome, Safari, and Opera */
}

#leftbox {
    height: 75px;
    padding-top: 8px;
}

#middlebox {
    height: 75px;
    padding-top: 8px;
}

#rightbox {
    width: 26%;
    height: 75px;
}

.mdl-container {
    width: 100%;
    height: 115px;
}

.mdl-time {
    font-size: 12px;
    font-weight: 700;
    height: 50%;
    padding-top: 11px;
}

.mdl-action {
    height: 50%;
    padding-top: 10px;
}

.light_grey_color {
    color: #555;
}

.dark_grey_color {
    font-weight: 700;
}

#middlebox span:nth-child(1) {
    color: #9a9a9a;
}

.dot {
    display: none;
}

.unread .dot {
    display: block;
    height: 7px;
    width: 7px;
    background-color: #00ae8d;
    border-radius: 50%;
    display: inline-block;
    margin: 5px;
    position: absolute;
    top: 17px;
    left: -10px;
}

.documentDetailsContainer {
    height: 82vh;
    overflow: hidden;
}

@media (max-width:1350px) {
    .documentDetailsContainer {
        height: 81.5vh;
    }
}

@media (min-width:1850px) {
    .documentDetailsContainer {
        height: 83vh;
    }
}

.listContainer {
    overflow-y: scroll;
    overflow-x: hidden;
    height: calc(77.5vh - 70px);
}

.documentDetailsContainer .panel.panel-default.borderTop-1 {
    height: auto !important;
    min-height: 30vh;
    margin-bottom: 0;
}

.documentDetailsContainer iframe {
    height: auto !important;
    min-height: 65vh;
}

.documentDetailsContainer .tab-content {
    border-width: 0;
}

.inside_color_line {
    background-color: #52c7b8;
    position: absolute;
    left: 0;
    top: 1px;
    height: 115px;
    width: 5px;
    opacity: .2;
}

.unread .inside_color_line {
    opacity: 1;
}

.ml-10px {
    margin-left: 10px !important;
}

.withBorders {
    border-top-width: 2px !important;
}

.waitingBackground.withBorders {
    border-top-width: 3px !important;
}

.header-container {
    height: 30px;
}

    .header-container .leftbox {
        width: 10%;
    }

    .header-container .rightbox {
        width: 90%;
    }

.ReportInDataTableRI {
    margin: 0px !important;
    padding: 4px 0px !important;
}

.ReportInDataTableThRI {
    padding: 5px 8px !important;
    color: #33ab8d !important;
}

.dropdown-margin {
    margin-left: -66px;
}

.subTable:hover, .subTable {
    background-color: white !important;
}

.hideCheckbox input[type=checkbox] {
    visibility: hidden;
}

.hideCheckbox .leftbox {
    width: 0%;
}

.hideCheckbox .rightbox {
    width: 100%;
}

.hideCheckbox #leftbox {
}

.hideCheckbox #middlebox {
}

.bodyHeight {
    overflow-x: hidden;
    overflow-y: auto;
    height: 58vh;
}

@media (max-width:1350px) {
    .bodyHeight {
        height: 57vh;
    }
}

@media (max-height:768px) {
    .bodyHeight {
        height: 60vh;
    }
}


.detailsGridBackgroundColor {
    background-color: #fafbfc !important;
}

    .detailsGridBackgroundColor > label {
        margin-bottom: 0px !important;
    }

.contextMenu {
    position: absolute;
    width: 150px;
    box-shadow: rgba(0, 0, 0, 0.05) 5px 10px 10px;
    z-index: 10;
}

.contextMenuItem {
    cursor: pointer;
    padding: 5px;
}

    .contextMenuItem:hover {
        background-color: #ececec;
    }

.btnWidgetMenu {
    cursor: pointer;
    border: none;
    background: transparent;
}

.removeWidget {
    border-top: 1px solid #cfdbe2;
    cursor: pointer;
    padding-bottom: 10px;
    padding-top: 10px;
}

    .removeWidget label {
        cursor: pointer;
    }

.widgetContent {
    padding: 0px;
    height: 323px;
}

    .widgetContent .panel {
        height: 323px;
    }

    .widgetContent > .col-md-12 {
        padding: 0px;
    }

.widgetContentPanel {
    padding-top: 0px;
}

.tab-content-vip {
    background-color: #fff;
    overflow-x: hidden;
    overflow-y: auto;
    height: 70vh;
    padding: 0px;
}

.btn-vip-toolbar {
    background-color: transparent;
    border-color: transparent;
    color: #37BC9B;
}

    .btn-vip-toolbar:active, .btn-vip-toolbar:hover {
        background-color: #3dd2ac;
        border-color: #3ed2ad;
        color: #fff;
        border-radius: 3px;
        border-style: solid;
    }
.btn-reject:active, .btn-reject:hover {
    background-color: #FF7588;
    border-color: #FF7588;
    color: #fff;
    border-radius: 3px;
    border-style: solid;
}

.btn-outline.btn-vip-toolbar {
    border: 1px solid #37BC9B;
    color: #37BC9B;
    background-color: transparent;
}
.btn-reject {
    border: 1px solid #fff;
    color: #37BC9B;
    background-color: transparent;
}
    .btn-outline.btn-vip-toolbar:hover, .btn-outline.btn-vip-toolbar:focus {
        color: white !important;
    }

.width10 {
    width: 10% !important;
}

.modalScroll {
    overflow-x: hidden !important;
    overflow-y: auto !important;
}

.c-checkbox input[type=radio]:disabled + span, .c-radio input[type=radio]:disabled + span {
    background-color: #fff !important;
    cursor: not-allowed !important;
}

.filterInfoDiv {
    color: #555;
    background-color: #fff;
    position: absolute;
    width: 500px;
    border-radius: 3px;
    z-index: 1999;
    border: thin solid #ccc;
}

.filterInfoDiv-closeIcon {
    padding: 8px 10px;
    cursor: pointer;
    vertical-align: middle;
}

.filterTitleSpan {
    padding: 6px 10px;
    font-size: 15px;
}

.filterInfoDivTitleDiv {
    height: 35px;
    border-bottom: 1px solid #ccc;
}

.spinnerloader {
    -webkit-animation: spinnerloaderspin 2s linear infinite; /* Safari */
    animation: spinnerloaderspin 2s linear infinite;
}

/* Safari */
@-webkit-keyframes spinnerloaderspin {
    0% {
        -webkit-transform: rotate(0deg);
    }

    100% {
        -webkit-transform: rotate(360deg);
    }
}

@keyframes spinnerloaderspin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.label-tag {
    background: #37BC9B !important;
    font-size: 13px;
    display: inline-block;
    vertical-align: middle;
    word-break: break-word;
    margin-bottom: 6px !important;
    white-space: normal;
    text-align: inherit;
}

.bootstrap-tagsinput {
    border: 1px solid #dde6e9 !important;
    /*   overflow: hidden;
    text-overflow: ellipsis;*/
}

.tag-focus {
    border: 1px solid #37BC9B !important;
}

.bootstrap-tagsinput:focus {
    border: 1px solid #37BC9B !important;
}

.bootstrap-tagsinput[disabled] {
    background-color: #eee;
    cursor: not-allowed;
}

    .bootstrap-tagsinput[disabled] .tag [data-role="remove"]:hover {
        box-shadow: none !important;
        cursor: not-allowed !important;
    }

    .bootstrap-tagsinput[disabled] input {
        cursor: not-allowed !important;
        caret-color: transparent;
    }

.dropdown-menu > .active > a, .dropdown-menu > .active > a:hover, .dropdown-menu > .active > a:focus {
    background-color: #37BC9B !important;
}

.typeahead.dropdown-menu {
    text-align: left !important;
    width: 25%;
    max-height: 200px;
    overflow-y: scroll !important;
}

    .typeahead.dropdown-menu > li > a {
        word-break: break-word;
        white-space: normal;
    }

.auto-cursor {
    cursor: auto !important;
}

.delete-mobile-row {
    padding: 3px 5px !important;
    font-size: 15px !important;
}

#imgPreviewBarcode {
    width: 50%;
}

@media (max-width: 768px) {

    #containerBarcodeDiv {
        margin-top: 10px !important;
        padding-left: 0px;
    }

    #barcodecontrolContainer {
        overflow: visible !important;
    }

    #containerPreviewBarcode {
        margin-left: 15px;
    }
}

.no_Padding {
    padding-left: 0px;
    padding-right: 0px;
}

.sidebarRight > .nav > li.active > a,
.sidebarRight > .nav > li.active > .nav-item,
.sidebarRight > .nav > li.active .nav,
.sidebarRight > .nav > li.open,
.sidebarRight > .nav > li.open > a,
.sidebarRight > .nav > li.open > .nav-item,
.sidebarRight > .nav > li.open .nav {
    background-color: #fcfcfc;
    color: #ff9433;
}

.sidebarRight > .nav > li > a,
.sidebarRight > .nav > li > .nav-item {
    padding: 12px 24px;
    color: #515253;
    letter-spacing: .025em;
    font-weight: normal;
    cursor: pointer;
}

.sidebarRight .nav > li > a {
    -webkit-animation: fadeInLeft .5s;
    animation: fadeInLeft .5s;
}

.sidebarRight > .nav > .nav-heading,
.sidebar > .nav > li > a > span,
.navbar-brand .brand-logo {
    -webkit-animation: fadeIn 1s;
    animation: fadeIn 1s;
}

.sidebarRight > .nav > li {
    border-left: 3px solid transparent;
    -webkit-transition: border-left-color .4s ease;
    -o-transition: border-left-color .4s ease;
    transition: border-left-color .4s ease;
}

.sidebarRight .nav-heading {
    padding: 12px 15px;
    color: #919da8;
    font-size: 13px;
    letter-spacing: .035em;
    pointer-events: none;
    cursor: default;
}

.aside-inner, .navbar-header, .sidebarRight > .nav > li {
    -webkit-transition: width .2s cubic-bezier(.35,0,.25,1);
    -o-transition: width .2s cubic-bezier(.35,0,.25,1);
    transition: width .2s cubic-bezier(.35,0,.25,1);
}

    .sidebarRight > .nav > li.active {
        border-left-color: #ff9433;
    }



.topnavbar .navbar-nav > li > [data-toggle='navbar-search'] {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 20;
    font-size: 16px;
    line-height: 55px;
    color: #fff;
    padding-top: 0;
    padding-bottom: 0;
    transition: color 0.3s ease;
}

@media screen and (prefers-reduced-motion: reduce) {
    .topnavbar .navbar-nav > li > [data-toggle='navbar-search'] {
        transition: none;
    }
}

@media (min-width: 768px) {
    .topnavbar .navbar-nav > li > [data-toggle='navbar-search'] {
        color: #fff;
    }
}

@media (max-width: 767.98px) {
    .topnavbar .navbar-text {
        margin: 10px;
    }
}

.topnavbar .navbar-form {
    position: absolute;
    top: -100%;
    left: 0;
    right: 0;
    margin: 0;
    padding: 0;
    height: 55px;
    z-index: 9001;
    transition: all .3s;
    border: 0;
    border-bottom: 1px solid #e1e2e3;
}

    .topnavbar .navbar-form .form-group {
        height: 100%;
        width: 100%;
    }

    .topnavbar .navbar-form .form-control .favorite-item {
        height: 100%;
        border: 0;
        border-radius: 0;
        width: 100%;
    }

    .topnavbar .navbar-form.open {
        top: 0;
    }

    .topnavbar .navbar-form .navbar-form-close {
        position: absolute;
        height: 30px;
        cursor: pointer;
        top: 50%;
        right: 0;
        margin-top: -15px;
        line-height: 30px;
        margin-right: 10px;
        color: #c1c2c3;
        font-size: 1.5em;
        pointer-events: auto;
    }

@media (min-width: 768px) {
    .topnavbar .navbar-form {
        left: 220px;
    }
}

/*@media (min-width: 768px) {
    .topnavbar .navbar-header {
        background-image: none;
        background-repeat: no-repeat;
        filter: none;
    }

    .topnavbar .navbar-nav > a {
        box-shadow: 0 0 0 #000 inset;
        transition: all 0.2s;
    }
}*/

@media screen and (min-width: 768px) and (prefers-reduced-motion: reduce) {
    .topnavbar .navbar-nav > a {
        transition: none;
    }
}

@media (min-width: 768px) {
    .topnavbar .navbar-nav > .nav-item.show > .nav-link, .topnavbar .navbar-nav > .nav-item.show > .nav-link:hover, .topnavbar .navbar-nav > .nav-item.show > .nav-link:focus {
        box-shadow: 0 -3px 0 #19a5d1 inset;
        transition: all 0.2s;
    }
}

@media screen and (min-width: 768px) and (prefers-reduced-motion: reduce) {
    .topnavbar .navbar-nav > .nav-item.show > .nav-link, .topnavbar .navbar-nav > .nav-item.show > .nav-link:hover, .topnavbar .navbar-nav > .nav-item.show > .nav-link:focus {
        transition: none;
    }
}

@media (min-width: 768px) {
    .topnavbar .navbar-nav > li > [data-toggle='navbar-search'] {
        position: static;
    }
}

/* Tooltip container */
.ctstooltip {
    position: relative;
    display: inline-block;
}

    /* Tooltip text */
    .ctstooltip .tooltiptext {
        font-size: 14px;
        width: 150px;
        visibility: hidden;
        background-color: #555;
        color: #fff;
        text-align: center;
        padding: 5px 0;
        border-radius: 6px;
        /* Position the tooltip text */
        position: absolute;
        z-index: 10;
        top: 3px;
        right: 105%;
        /* Fade in tooltip */
        opacity: 0;
        transition: opacity 0.3s;
    }

        /* Tooltip arrow */
        .ctstooltip .tooltiptext::after {
            content: " ";
            position: absolute;
            top: 50%;
            left: 100%; /* To the right of the tooltip */
            margin-top: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: transparent transparent transparent black;
        }

    /* Show the tooltip text when you mouse over the tooltip container */
    .ctstooltip:hover .tooltiptext {
        visibility: visible;
        opacity: 1;
    }

.lightGreyBorder {
    border-color: lightgrey;
}

.toggle {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 20px;
    background-color: #37BC9B;
    border-radius: 30px;
    /*border: 2px solid gray;*/
}

    /* After slide changes */
    .toggle:after {
        content: '';
        position: absolute;
        width: 15px;
        height: 15px;
        border-radius: 50%;
        background-color: #ded9d9;
        top: 2px;
        left: 2px;
        transition: all 0.5s;
    }

/* Checkbox checked effect */
.checkbox:checked + .toggle::after {
    left: 35px;
}

/* Checkbox checked toggle label bg color */
.checkbox:checked + .toggle {
    background-color: white;
}

.toggledefault {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 20px;
    background-color: white;
    border-radius: 30px;
    border: 2px solid #ded9d9;
}

    .toggledefault:after {
        content: '';
        position: absolute;
        width: 15px;
        height: 15px;
        border-radius: 50%;
        background-color: #ded9d9;
        top: 1px;
        right: 1px;
        transition: all 0.5s;
    }

.checkbox:checked + .toggledefault::after {
    right: 25px;
}

/* Checkbox checked toggle label bg color */
.checkbox:checked + .toggledefault {
    background-color: #37BC9B;
}
/* Checkbox vanished */
.checkboxtoggle {
    display: none;
}

.tabContent {
    padding: 0px 10px 0px 45px;
}

.VIPContainerItem {
}

.defaultview {
    padding: 0px 29px 0px 5px;
}

.buttonstop {
    margin: 5px 0px 0px 0px;
}


/*UI enhancements*/
.sidebar > .nav > li > a, .sidebar > .nav > li > .nav-item {
    padding: 2px 20px;
}

.sidebar-subnav > li > a, .sidebar-subnav > li > .nav-item {
    padding-top: 2px;
    padding-bottom: 2px;
}
/*
.form-group {
    margin-bottom: 5px;
}

.nav-tabs > li > a {
    padding: 6px 15px;
}

*/
.instruction-Input-group {
    display: flex;
    align-items: center;
    width: auto;
    border: 1px solid #ccc;
    padding: 10px;
    border-radius: 5px;
}

    .instruction-Input-group .form-control {
        flex-grow: 1;
        border: none;
        outline: none;
        font-size: 16px;
    }

    .instruction-Input-group .triggerInstruction {
        margin-left: 10px;
        cursor: pointer;
    }

        .instruction-Input-group .triggerInstruction i {
            font-size: 20px;
        }

.btn-action-activitylog:hover {
    background-color: #e9e6e6;
    border-radius: 15px;
}

.jstree-hidden {
    display: none;
}

.content-heading, .content-wrapper > .content-heading {
    margin-bottom: 15px;
}

.form-group {
    margin-bottom: 5px;
}

.wrapper > footer {
    position: fixed;
    height: 32px;
    background-color: white;
    padding: 5px 50px;
    text-align: right;
}

    .wrapper > footer i.fa {
        font-size: 20px;
        cursor: pointer;
    }

.modal-header .close {
    color: #37BC9B;
    font-size: 3em;
    opacity: 1;
}

.popover.top > .arrow {
    display: none;
}

footer div.popover {
    border-color: #37BC9B;
    border-style: solid;
    border-width: 2px;
    width: 300px;
}

.circular--portrait {
    padding: 0.2em 0.6em 0.3em;
    font-size: 60%;
    font-weight: bold;
    line-height: 1;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 30.00em;
}

.nav-pills.tabDocumentDetails a {
    font-size: 18px;
    margin-top: 4px;
    padding: 7px 12px;
}

.nav-pills.tabDocumentDetails > li.active > a, .nav-pills.tabDocumentDetails > li.active > a:hover, .nav-pills.tabDocumentDetails > li.active > a:focus {
    background-color: #37BC9B;
}

.vip-counters {
    top: 0;
    right: 0;
    position: absolute;
}

.table > thead > tr > th {
    padding: 8px 8px;
    color: white;
    background-color: #37BC9B;
}



.wrapper > section {
    margin-bottom: 25px !important;
}

.bold {
    font-weight: bold;
}

.icon-primary {
    color: #37BC9B;
}

.icon-default {
    color: white;
}

.print-barcode {
    background-color: white;
    border-color: #dbd9d9;
    font-size: 20px;
    font-weight: bold;
}

.panel-header-primary {
    background-color: #37BC9B !important;
    color: white !important;
}

.panel-header-default {
    background-color: white !important;
    color: black !important;
}

.br-0 {
    border-radius: 0px;
}

.mdl-container-document {
    width: 100%;
    height: 95px;
}

.modal #documentWithViewerContainer div div .tab-pane.fade {
    min-height: 70vh;
}

.min-max-width-150-250 {
    min-width: 150px !important;
    max-width: 250px !important;
    word-break: break-word;
}

.task-panel {
    box-sizing: border-box !important;
    left: 0px !important;
    color: white !important;
    padding-top: 0px !important;
    position: absolute !important;
    top: 100% !important;
    z-index: 10 !important;
    width: 100% !important;
    background-color: #37BC9B !important
}

.card-max-width{
    max-width:900px !important
}

.card-min-width {
    max-width: 175px !important
}

.arrow-menu {
    margin-left: 5px;
    margin-right: 5px;
    font-weight: bold;
    font-size: 15px;
}


.sidebar > .nav > li > div:focus, .sidebar > .nav > li > div:hover {
    text-decoration: none;
    outline: none;
    color: #ff9433;
}

#documentWithViewerContainer .tab-content {
    height: 65vh;
    overflow-y: auto;
}
.resize-handle--x {
    -webkit-flex: 0 0 auto;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    position: relative;
    box-sizing: border-box;
    width: 3px;
    border-left: 1px solid black;
    border-right: 1px solid black;
    cursor: ew-resize;
    user-select: none;
    z-index: 100;
}

#documentWithViewerContainer {
    min-width: 20%;
    max-width: 60%;
    width:30%;
}

#documentViewerContainer {
    flex: 1;
}

.custome-flex {
    display: flex;
}
body.resizing * {
    pointer-events: none !important;
}

body.resizing .resize-handle--x {
    pointer-events: auto !important;
}


.checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    background-color: #fff;
    padding: 10px;
    border: 1px solid #dee2e6;
    border-radius: 5px;
}

.favoriteStructure-item {
    white-space: normal;
    overflow: visible;
    text-overflow: clip;
    background-color: #f9f9f9;
    display: flex;
    padding: 10px;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    width: calc(25% - 10px);
    box-sizing: border-box;
}

favoriteStructure-item {
    cursor: pointer;
}

.favoriteStructure-item label {
    vertical-align: middle;
}

.favoriteStructure-item input[type="checkbox"] {
    vertical-align: middle;
    margin-right: 10px;
    cursor: pointer;
}

@media (max-width: 768px) {
    .favoriteStructure-item {
        width: calc(50% - 10px);
    }
}

@media (max-width: 480px) {
    .favoriteStructure-item {
        width: 100%;
    }
}

.btn-excel, .btn-pdf {
    padding: 8px 16px;
    margin-right: 10px;
    font-size: 14px;
}
.slider .panel-heading {
    margin: 1.5rem 0 0;
    background: #ededed;
    border: 1px solid #efefef;
    border-radius: 4px;
}

.slider .title {
    background: #FFF;
    border: 1px solid #707070;
    color: #707070;
    font-size: 20px;
    font-weight: normal;
    padding: 0.75rem 1rem;
}

.slider .slick-prev,
.slider .slick-next {
    font-size: 0;
    line-height: 0;
    position: absolute;
    top: 50%;
    display: block;
    width: 20px;
    height: 20px;
    padding: 0;
    transform: translate(0, -50%);
    cursor: pointer;
    color: transparent;
    border: none;
    outline: none;
    background: transparent;
}

    .slider .slick-prev:before,
    .slider .slick-next:before {
        font-family: 'slick';
        font-size: 20px;
        line-height: 1;
        opacity: 0.75;
        color: black;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

.slider .slick-prev {
    left: -15px;
}

.slider .slick-next {
    right: -15px;
}

.slider .slick-prev:before {
    content: '◀';
}

.slider .slick-next:before {
    content: '▶';
}

.slider .slick-disabled {
    opacity: 0.5;
}

.signatures-slider .slick-track {
    display: flex;
}

.signatures-slider .bg-white {
    margin: 0 2rem;
    padding: 1.5rem;
    height: 100%;
    border: 1px solid #ededed;
    border-radius: 5px;
    overflow: hidden;
}

    .signatures-slider .bg-white .c-radio {
        margin-top: 0;
    }

    .signatures-slider .bg-white img {
        margin: auto;
        max-width: 100%;
    }


#defaultSignatureContainer.collapse:not(.in),
.slider .collapse:not(.in) {
    display: block;
    height: 0 !important;
    opacity: 0;
    z-index: -1;
    position: relative;
    overflow: hidden;
}
.expandable-content {
    display: none;
}

    .expandable-content ul {
        padding-left: 0;
    }

        .expandable-content ul li {
            list-style: none;
            padding: 1rem 0 1rem 4rem;
            border-bottom: 1px solid #ededed;
        }

.mb-3 {
    margin-bottom: 1rem;
}

.text-end {
    text-align: end;
}

.select2-results__option[aria-selected="true"] .checkbox .fa-check {
    border-color: #00ae8d;
    background-color: #00ae8d;
}

    .select2-results__option[aria-selected="true"] .checkbox .fa-check:before {
        color: #fff;
        opacity: 1;
        transition: color .3sease-out;
    }


/*transfer popup*/
.btn-td-add {
    width: 34px;
    height: 34px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
}

div#grdFavoriteTransferContainer_wrapper .dataTables_length {
    display: none;
}

[ref="grdTransferItems"].table-bordered > tbody > tr {
    background: transparent;
}

#grdFavoriteTransferContainer, #grdFavoriteTransferContainer > thead > tr > th,
[ref="grdTransferItems"].table-bordered,
[ref="grdTransferItems"].table-bordered > thead > tr > th {
    border: 0;
}

    #grdFavoriteTransferContainer tbody tr td {
        border: 1px solid #eee;
    }

.navbar-nav .nav-link.btn-transparent {
    background: transparent;
    color: #fff;
    padding: 12px;
    outline: 0;
    font-size: 18px;
}

table#grdFollowupPanelItems thead th {
    white-space: nowrap;
}

.list-container > .card {
    max-height: 470px
}

/** Search Menu Sidebar - Start */
.search-container {
    position: relative !important;
    margin-bottom: 20px !important;
    margin-inline: 5px;
    width: calc(100% - 20px) !important;
}

.search-input {
    width: 100% !important;
    padding: 10px 15px 10px 35px !important;
    border-radius: 4px !important;
    border: none !important;
    background-color: rgba(255, 255, 255, 0.1) !important;
    color: black !important;
    font-size: 14px !important;
    transition: all 0.3s ease !important;
    border: 1px solid #dde6e9 !important;
}

.search-input::placeholder {
    color: rgba(255, 255, 255, 0.6) !important;
    color: #3a3f51 !important;
}

.search-input:focus {
    outline: none !important;
    background-color: rgba(255, 255, 255, 0.15) !important;
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.2) !important;
    color: #3a3f51 !important;
}

.search-icon {
    position: absolute !important;
    left: 12px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    color: rgba(255, 255, 255, 0.6) !important;
    color: #3a3f51 !important;
    font-size: 14px !important;
    pointer-events: none !important;
}
/** Search Menu Sidebar - End */

/*td:has(> div > input[name="rdbSignature"]:checked)
{
    outline: 5px solid #16D39A;
}

input[name="rdbSignature"]
{
    display: none !important;
}
*/

/*scrolling tabs*/

#documentWithViewerContainer .scrtabs-tabs-fixed-container,
#documentWithViewerContainer .scrtabs-tab-container,
#documentWithViewerContainer .scrtabs-tab-scroll-arrow-left,
#documentWithViewerContainer .scrtabs-tab-scroll-arrow-right,
.scrtabs-tabs-fixed-container,
.scrtabs-tab-container
{
    height: auto;
}
.scrtabs-tabs-fixed-container {
    width: 100% !important;
    height: auto !important;
}

.scrtabs-tabs-movable-container {
    width: 100% !important;
    left: 0 !important;
    right: 0 !important;
}

.scrtabs-tab-container .nav-tabs {
    display: flex;
    flex-wrap: wrap;
}

.scrtabs-tabs-fixed-container ul.nav-tabs > li {
    white-space: nowrap;
    flex: 1;
}

.scrtabs-tabs-fixed-container .nav-tabs > li > a {
    padding: 6px 10px;
    display: flex;
}

.scrtabs-tab-container .scrtabs-tab-scroll-arrow {
    display: none !important;
}
.flex-nowrap {
    flex-wrap: nowrap;
}
.flex-1 {
    flex: 1
}
.gap-4 {
    gap: 1.5rem
}

.subjectSpan {
    color: blue;
    cursor: pointer;
}
.subjectSpan:hover {
    text-decoration: underline;
}





/*solution 1 scroll over card only */
.vipCorrPanel .listContainer {
    overflow-y: auto;
    height: calc(77vh - 85px);
}

.vipCorrPanel .listContainer::-webkit-scrollbar {
    display: block;
}


/* solution 2, page scroll
.listContainer {
    height: auto;
}*/

.flatpickr-current-month .numInputWrapper { 
    width: fit-content;
    max-width: 100px;
}

.flatpickr-current-month .flatpickr-monthDropdown-months {
    text-align: end
}

.flatpickr-current-month {
    display: flex;
    align-items: center; 
    justify-content: center;
}


.vipCorrLeftPanel .mdl-container, .vipCorrLeftPanel .mdl-container-document {
    display: flex;
}

.vipCorrLeftPanel #middlebox {
    height: auto;
    overflow: hidden;
    flex: 1;
    max-width: unset !important;
}

.align-items-center {
    align-items: center;
}

aside-collapsed .sidebar > .nav > li.hidden,
.aside-collapsed-text .sidebar > .nav > li.hidden,
.aside-collapsed .sidebar > .nav > li > a.hidden,
.aside-collapsed-text .sidebar > .nav > li > a.hidden {
    display: none !important;
}


.aside-collapsed .sidebar .arrow-menu {
    display: none !important;
}

label.control-label {
    font-size: inherit !important;
}

.table.dataTable th {
    font-size: inherit !important;
}

div.dataTables_wrapper div.dataTables_length label {
    font-size: inherit !important;
}

.btn-excel,
.btn-pdf {
    font-size: inherit !important;
}