---
type: "always_apply"
---

When implementing any new code features or making changes to the CTS application, you must test the functionality using Playwright automation in Chrome browser. Follow these specific testing requirements:

1. **Test Environment Setup:**
   - Target URL: http://localhost:44391
   - Browser: Chrome
   - Login credentials: username: `admin`, password: `P@$$w0rd`

2. **Testing Scope:**
   - Test every new feature or code change you implement
   - Verify the functionality works as expected through the web interface
   - Test both positive scenarios (expected behavior) and edge cases where applicable

3. **Testing Process:**
   - Start by navigating to the application URL
   - Log in using the provided admin credentials
   - Navigate to the relevant sections of the application to test your implemented changes
   - Verify that the new functionality works correctly
   - Document any issues or unexpected behavior found during testing

4. **Playwright Implementation:**
   - Use the available Playwright browser tools to automate the testing
   - Include proper error handling and verification steps
   - Take screenshots or capture relevant information if issues are encountered

This testing should be performed after implementing any code changes to ensure the CTS application functions correctly with your modifications.