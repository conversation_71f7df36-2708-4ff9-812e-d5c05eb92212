﻿using AngleSharp.Common;
using Intalio.Core;
using Intalio.Core.Interfaces;
using Intalio.Core.Model;
using Intalio.CTS.Core.Model;
using Intalio.CTS.Core.Utility;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Npgsql;
using Oracle.ManagedDataAccess.Client;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;
using System.Transactions;
using DelegationModel = Intalio.CTS.Core.Model.DelegationModel;
using Intalio.CTS.Core.API;
using System.ComponentModel;
using Intalio.CTS.Core;
using Nest;
using Intalio.Core.DAL;
using NPOI.SS.Formula.Functions;

namespace Intalio.CTS.Core.DAL
{
    public partial class Transfer : IDbObject<Transfer>, IDisposable
    {
        #region Ctor

        public Transfer()
        {
            ActivityLog = new HashSet<ActivityLog>();
        }

        #endregion

        #region Private Fields

        private CTSContext _ctx;

        #endregion

        #region Properties

        public long Id { get; set; }
        public long? ParentTransferId { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public long CreatedByUserId { get; set; }
        public long? DocumentId { get; set; }
        public short? StatusId { get; set; }
        public short? PurposeId { get; set; }
        
        [DefaultValue(0)]
        public bool IsSigned { get; set; }

        [DefaultValue(0)]
        public bool IsExported { get; set; }
        [DefaultValue(1)]
        public short? PriorityId { get; set; }
        public long? FromStructureId { get; set; }
        public long? FromUserId { get; set; }
        public long? ToStructureId { get; set; }
        public long? ToUserId { get; set; }
        public long? OwnerUserId { get; set; }
        public long? OwnerDelegatedUserId { get; set; }
        public DateTime? DueDate { get; set; }
        public DateTime? LockedDate { get; set; }
        public DateTime? OpenedDate { get; set; }
        public DateTime? ClosedDate { get; set; }
        public bool Cced { get; set; }
        public string Instruction { get; set; }
        public bool PrivateInstruction { get; set; }
        public long? VoiceNoteStorageId { get; set; }
        public bool VoiceNotePrivacy { get; set; }
        public long? WorkflowStepId { get; set; }
        public long? FromDelegatedUserId { get; set; }
        public long? RequestStatus { get; set; } = 0;
        public string? RejectionReason { get; set; }
        public long? ExportedDocumentId { get; set; }
        public DateTime? ExportedDate { get; set; }
        [DefaultValue(0)]
        public bool? FromRecall { get; set; }
        public bool IsSigning { get; set; }
        public long? SignedByUserId { get; set; }
        public DateTime? RejectedDate { get; set; }
        public bool IsExporting { get; set; }

        public virtual Document Document { get; set; }
        public virtual Structure FromStructure { get; set; }
        public virtual User FromUser { get; set; }
        public virtual User OwnerDelegatedUser { get; set; }
        public virtual User OwnerUser { get; set; }
        public virtual Purpose Purpose { get; set; }
        public virtual Priority Priority { get; set; }
        public virtual Status Status { get; set; }
        public virtual Structure ToStructure { get; set; }
        public virtual User ToUser { get; set; }
        public virtual ICollection<ActivityLog> ActivityLog { get; set; }
        public virtual ICollection<Note> Note { get; set; }
        public virtual ICollection<Event> Event{ get; set; }
        public virtual ICollection<NonArchivedAttachments> NonArchivedAttachments { get; set; }
        public virtual ICollection<LinkedDocument> LinkedDocument { get; set; }
        public virtual ICollection<Folder> Folder { get; set; }
        public virtual ICollection<Attachment> Attachment { get; set; }
        public virtual Workflow Workflow { get; set; }
        public virtual WorkflowStep WorkflowStep { get; set; }
        public virtual Document ExportedDocument { get; set; }
        public virtual User SignedByUser { get; set; }

        #endregion

        #region Private Methods

        private void OpenDbContext()
        {
            if (_ctx == null)
            {
                _ctx = new CTSContext();
            }
        }

        #endregion

        #region Public Methods

        public void UpdateClosedDate()
        {
            using (var ctx = new CTSContext())
            {
                ModifiedDate = DateTime.Now;
                ctx.Entry(this).Property(x => x.ModifiedDate).IsModified = true;
                ctx.Entry(this).Property(x => x.ClosedDate).IsModified = true;
                ctx.SaveChanges();
            }
        }

        /// <summary>
        /// Set close date and status as completed
        /// </summary>
        /// <param name="transfers"></param>
        public void UpdateStatusAndClosedDateByList(List<Transfer> transfers)
        {
            using (var ctx = new CTSContext())
            {
                for (int i = 0; i < transfers.Count; i++)
                {
                    transfers[i].ModifiedDate = DateTime.Now;
                    transfers[i].ClosedDate = DateTime.Now;
                    transfers[i].StatusId = (short)DocumentStatus.Completed;
                    ctx.Entry(transfers[i]).Property(x => x.ModifiedDate).IsModified = true;
                    ctx.Entry(transfers[i]).Property(x => x.ClosedDate).IsModified = true;
                    ctx.Entry(transfers[i]).Property(x => x.StatusId).IsModified = true;
                }
                ctx.SaveChanges();
            }
        }

        public void UpdateStatusAndClosedDate()
        {
            using (var ctx = new CTSContext())
            {
                ModifiedDate = DateTime.Now;
                ctx.Entry(this).Property(x => x.ModifiedDate).IsModified = true;
                ctx.Entry(this).Property(x => x.StatusId).IsModified = true;
                ctx.Entry(this).Property(x => x.ClosedDate).IsModified = true;
                ctx.Entry(this).Property(x => x.IsSigning).IsModified = true;
                ctx.Entry(this).Property(x => x.IsExporting).IsModified = true;
                ctx.SaveChanges();
            }

        }
        public void UpdateTransferIsSigned(bool IsSigned,long? userId)
        {
            using(var ctx = new CTSContext())
            {
                ModifiedDate = DateTime.Now;
                this.OwnerUserId = null; // Nareman: to make all signed transfers can be exported by all users not the owner only, but must be enhanced
                this.IsSigned = IsSigned;
                this.SignedByUserId = IsSigned ? userId : null;
                ctx.Entry(this).Property(x => x.IsSigned).IsModified = IsSigned;
                ctx.Entry(this).Property(x => x.OwnerUserId).IsModified = true;
                ctx.Entry(this).Property(x => x.SignedByUserId).IsModified = true;
                ctx.SaveChanges();
            }
        }

        public void UpdateClosedDateStatusIdToUserIdByList(List<Transfer> transfers)
        {
            using (var ctx = new CTSContext())
            {
                for (int i = 0; i < transfers.Count; i++)
                {
                    transfers[i].ModifiedDate = DateTime.Now;
                    transfers[i].ClosedDate = DateTime.Now;
                    ctx.Entry(transfers[i]).Property(x => x.ModifiedDate).IsModified = true;
                    ctx.Entry(transfers[i]).Property(x => x.ToUserId).IsModified = true;
                    ctx.Entry(transfers[i]).Property(x => x.StatusId).IsModified = true;
                    ctx.Entry(transfers[i]).Property(x => x.ClosedDate).IsModified = true;
                }
                ctx.SaveChanges();
            }
        }
        public void UpdateStatusIdAndClosedDateInCorrespondence( long documnetId)
        {

            using (var ctx = new CTSContext())
            {
                Transfer transfer = 
                    ctx.Transfer.
                    Include(t=>t.Document)
                    .Where(t=>t.DocumentId == documnetId && t.StatusId == (short)DocumentStatus.Completed).OrderByDescending(t=>t.ClosedDate).FirstOrDefault();
             
                  // include document
                    transfer.StatusId = (short)DocumentStatus.InProgress;
                     transfer.ClosedDate = null;
                    transfer.ModifiedDate = DateTime.Now;
                transfer.Document.StatusId = (short)DocumentStatus.InProgress;
                transfer.Document.ClosedDate = null;
                transfer.Document.ModifiedDate = DateTime.Now;

                ctx.SaveChanges();
 

            }
        }
        public void Insert(List<Transfer> transfers)
        {
            using (var ctx = new CTSContext())
            {
                foreach (var item in transfers)
                {
                    item.PriorityId = item.PriorityId == 0 ? (short)1 : item.PriorityId;
                    item.CreatedDate = DateTime.Now;
                    ctx.Transfer.Add(item);
                }
                ctx.SaveChanges();
            }
        }

        public void Insert()
        {
            using (var ctx = new CTSContext())
            {
                this.PriorityId = this.PriorityId == 0 ? (short)1 : this.PriorityId;
                CreatedDate = DateTime.Now;
                ctx.Transfer.Add(this);
                ctx.SaveChanges();
            }
        }

        public void Update()
        {
            using (var ctx = new CTSContext())
            {
                ctx.Entry(this).State = EntityState.Modified;
                ctx.SaveChanges();
            }
        }

        public void UpdateLocked()
        {
            using (var ctx = new CTSContext())
            {
                ModifiedDate = DateTime.Now;
                ctx.Entry(this).Property(x => x.ModifiedDate).IsModified = true;
                ctx.Entry(this).Property(x => x.LockedDate).IsModified = true;
                ctx.Entry(this).Property(x => x.OpenedDate).IsModified = true;
                ctx.Entry(this).Property(x => x.OwnerUserId).IsModified = true;
                ctx.Entry(this).Property(x => x.OwnerDelegatedUserId).IsModified = true;
                ctx.SaveChanges();
            }
        }
        public void UpdateRequestStatus()
        {
            using (var ctx = new CTSContext())
            {
                
                ctx.Entry(this).Property(x => x.RequestStatus).IsModified = true;
                ctx.Entry(this).Property(x => x.RejectionReason).IsModified = true;
                ctx.Entry(this).Property(x => x.RejectedDate).IsModified = true;


                ctx.SaveChanges();
            }
        }

        public bool Lock()
        {
            bool retValue = false;
            using (var ctx = new CTSContext())
            {
                ModifiedDate = DateTime.Now;
                var connectionString = Configuration.DbConnectionString;
                var queryString = string.Empty;
                string table = $"\"Transfer\"";
                if (Configuration.DatabaseType == DatabaseType.PostgreSQL)
                {
                    table = $"public.{table}";
                }
                if (this.OwnerDelegatedUserId.HasValue)
                {
                    queryString = $"UPDATE {table} SET \"ModifiedDate\" = :ModifiedDate, \"LockedDate\" = :LockedDate, \"OpenedDate\" = :OpenedDate," +
         "\"OwnerUserId\" = :OwnerUserId, \"OwnerDelegatedUserId\" = :OwnerDelegatedUserId WHERE \"Id\" = :Id AND \"OwnerUserId\" IS NULL";
                }
                else
                {
                    queryString = $"UPDATE {table}  SET \"ModifiedDate\" = :ModifiedDate, \"LockedDate\" = :LockedDate, \"OpenedDate\" = :OpenedDate," +
         "\"OwnerUserId\" = :OwnerUserId WHERE \"Id\" = :Id AND \"OwnerUserId\" IS NULL";
                }
                switch (Configuration.DatabaseType)
                {
                    case DatabaseType.MSSQL:
                        using (SqlConnection connection = new SqlConnection(connectionString))
                        {
                            if (this.OwnerDelegatedUserId.HasValue)
                            {
                                queryString = $"UPDATE {table} SET \"ModifiedDate\" = @ModifiedDate, \"LockedDate\" = @LockedDate, \"OpenedDate\" = @OpenedDate," +
                     "\"OwnerUserId\" = @OwnerUserId, \"OwnerDelegatedUserId\" = @OwnerDelegatedUserId WHERE \"Id\" = @Id AND \"OwnerUserId\" IS NULL";
                            }
                            else
                            {
                                queryString = $"UPDATE {table}  SET \"ModifiedDate\" = @ModifiedDate, \"LockedDate\" = @LockedDate, \"OpenedDate\" = @OpenedDate," +
                     "\"OwnerUserId\" = @OwnerUserId WHERE \"Id\" = @Id AND \"OwnerUserId\" IS NULL";
                            }
                            SqlCommand cmd = new SqlCommand(queryString, connection);
                            connection.Open();
                            cmd.Parameters.Add(new SqlParameter("ModifiedDate", System.Data.SqlDbType.DateTime2));
                            cmd.Parameters.Add(new SqlParameter("LockedDate", System.Data.SqlDbType.DateTime2));
                            cmd.Parameters.Add(new SqlParameter("OpenedDate", System.Data.SqlDbType.DateTime2));
                            cmd.Parameters.Add(new SqlParameter("OwnerUserId", System.Data.SqlDbType.BigInt));
                            cmd.Parameters.Add(new SqlParameter("Id", System.Data.SqlDbType.BigInt));
                            cmd.Parameters[0].Value = this.ModifiedDate;
                            cmd.Parameters[1].Value = this.LockedDate;
                            cmd.Parameters[2].Value = this.OpenedDate;
                            cmd.Parameters[3].Value = this.OwnerUserId;
                            cmd.Parameters[4].Value = this.Id;
                            if (this.OwnerDelegatedUserId.HasValue)
                            {
                                cmd.Parameters.Add(new SqlParameter("OwnerDelegatedUserId", System.Data.SqlDbType.BigInt));
                                cmd.Parameters[5].Value = this.OwnerDelegatedUserId;
                            }
                            int count = cmd.ExecuteNonQuery();
                            if (count > 0)
                            {
                                retValue = true;
                            }
                            else
                            {
                                retValue = false;
                            }
                        }
                        break;
                    case DatabaseType.PostgreSQL:
                        using (NpgsqlConnection connection = new NpgsqlConnection(connectionString))
                        {
                            NpgsqlCommand cmd = new NpgsqlCommand(queryString, connection);
                            connection.Open();
                            cmd.Parameters.Add(new NpgsqlParameter("ModifiedDate", NpgsqlTypes.NpgsqlDbType.Timestamp));
                            cmd.Parameters.Add(new NpgsqlParameter("LockedDate", NpgsqlTypes.NpgsqlDbType.Timestamp));
                            cmd.Parameters.Add(new NpgsqlParameter("OpenedDate", NpgsqlTypes.NpgsqlDbType.Timestamp));
                            cmd.Parameters.Add(new NpgsqlParameter("OwnerUserId", NpgsqlTypes.NpgsqlDbType.Bigint));
                            cmd.Parameters.Add(new NpgsqlParameter("Id", NpgsqlTypes.NpgsqlDbType.Bigint));
                            cmd.Parameters[0].Value = this.ModifiedDate;
                            cmd.Parameters[1].Value = this.LockedDate;
                            cmd.Parameters[2].Value = this.OpenedDate;
                            cmd.Parameters[3].Value = this.OwnerUserId;
                            cmd.Parameters[4].Value = this.Id;
                            if (this.OwnerDelegatedUserId.HasValue)
                            {
                                cmd.Parameters.Add(new NpgsqlParameter("OwnerDelegatedUserId", NpgsqlTypes.NpgsqlDbType.Bigint));
                                cmd.Parameters[5].Value = this.OwnerDelegatedUserId;
                            }
                            int count = cmd.ExecuteNonQuery();
                            if (count > 0)
                            {
                                retValue = true;
                            }
                            else
                            {
                                retValue = false;
                            }
                        }
                        break;
                    case DatabaseType.Oracle:
                        using (OracleConnection connection = new OracleConnection(connectionString))
                        {
                            OracleCommand cmd = new OracleCommand(queryString, connection);
                            connection.Open();
                            cmd.Parameters.Add(new OracleParameter("ModifiedDate", OracleDbType.TimeStamp));
                            cmd.Parameters.Add(new OracleParameter("LockedDate", OracleDbType.TimeStamp));
                            cmd.Parameters.Add(new OracleParameter("OpenedDate", OracleDbType.TimeStamp));
                            cmd.Parameters.Add(new OracleParameter("OwnerUserId", OracleDbType.Int64));
                            cmd.Parameters[0].Value = this.ModifiedDate;
                            cmd.Parameters[1].Value = this.LockedDate;
                            cmd.Parameters[2].Value = this.OpenedDate;
                            cmd.Parameters[3].Value = this.OwnerUserId;
                            if (this.OwnerDelegatedUserId.HasValue)
                            {
                                cmd.Parameters.Add(new OracleParameter("OwnerDelegatedUserId", OracleDbType.Int64));
                                cmd.Parameters[4].Value = this.OwnerDelegatedUserId;
                                cmd.Parameters.Add(new OracleParameter("Id", OracleDbType.Int64));
                                cmd.Parameters[5].Value = this.Id;
                            }
                            else
                            {
                                cmd.Parameters.Add(new OracleParameter("Id", OracleDbType.Int64));
                                cmd.Parameters[4].Value = this.Id;
                            }
                            int count = cmd.ExecuteNonQuery();
                            if (count > 0)
                            {
                                retValue = true;
                            }
                            else
                            {
                                retValue = false;
                            }
                        }
                        break;
                }
            }
            return retValue;
        }

        public void UpdateOpenedDate()
        {
            using (var ctx = new CTSContext())
            {
                ModifiedDate = DateTime.Now;
                ctx.Entry(this).Property(x => x.ModifiedDate).IsModified = true;
                ctx.Entry(this).Property(x => x.OpenedDate).IsModified = true;
                ctx.SaveChanges();
            }
        }

        public void UpdateOpenedDateAndOwner()
        {
            using (var ctx = new CTSContext())
            {
                ModifiedDate = DateTime.Now;
                ctx.Entry(this).Property(x => x.ModifiedDate).IsModified = true;
                ctx.Entry(this).Property(x => x.OpenedDate).IsModified = true;
                ctx.Entry(this).Property(x => x.OwnerUserId).IsModified = true;
                ctx.Entry(this).Property(x => x.OwnerDelegatedUserId).IsModified = true;
                ctx.SaveChanges();
            }
        }

        public Transfer Find(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Transfer.AsNoTracking().FirstOrDefault(c => c.Id == id);
            }
        }

        public Transfer FindIncludeDocumnet(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Transfer.AsNoTracking().Include(t=>t.Document).FirstOrDefault(c => c.Id == id);
            }
        }
        public async Task<Transfer> FindAsync(long id)
        {
            using (var ctx = new CTSContext())
            {
                return await ctx.Transfer.AsNoTracking().FirstOrDefaultAsync(c => c.Id == id);
            }
        }

        public async Task<Transfer> FindByDocumentIdAsync(long id, long documentId)
        {
            using (var ctx = new CTSContext())
            {
                return await ctx.Transfer.AsNoTracking().FirstOrDefaultAsync(t => t.Id == id && t.DocumentId == documentId);
            }
        }
        public  Transfer FindByDocumentIdOnly( long documentId)
        {
            using (var ctx = new CTSContext())
            {
                return  ctx.Transfer.FirstOrDefault(t => t.DocumentId == documentId);
            }
        }
        public Transfer FindByDocumentIdToUserId(long documentId, long toUserId)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Transfer.AsNoTracking().FirstOrDefault(t => t.DocumentId == documentId && t.ToUserId == toUserId);
            }
        }

        public List<Transfer> ListByIds(List<long> ids)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Transfer.AsNoTracking().Where(c => ids.Contains(c.Id)).ToList();
            }
        }

        public List<Transfer> ListByIdsIncludeDocument(List<long> ids)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Transfer.Include(t => t.Document).ThenInclude(t => t.Category)
                    .Include(t => t.Document).ThenInclude(x => x.DocumentForm)
                    .Include(t => t.Document).ThenInclude(x=>x.Attachment)
                    .Include(t => t.Document).ThenInclude(x => x.DocumentType).AsNoTracking().Where(c => ids.Contains(c.Id)).ToList();
            }
        }

        public List<Transfer> List()
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Transfer.AsNoTracking().ToList();
            }
        }
        public List<Transfer> ListOverDueDate(List<long> structureIds = null, string fromDate = "", string toDate = "")
        {
            using (var ctx = new CTSContext())
            {
                var today = DateTime.Now.Date;
                IQueryable<Transfer> query = ctx.Transfer.AsNoTracking().Where(t => !t.ClosedDate.HasValue && t.DueDate.HasValue && t.DueDate < today);
                if (!structureIds.IsNullOrEmpty())
                {
                    query = query.Where(t => structureIds.Contains(t.Document.CreatedByStructureId));
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    query = query.Where(t => t.CreatedDate.Date >= from);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    query = query.Where(t => t.CreatedDate.Date <= to);
                }

                return query.ToList();

            }
        }

        public List<Transfer> ListWithCaching()
        {
            List<Transfer> retValue = new CacheUtility().GetCachedItem<List<Transfer>>(typeof(Transfer).Name);
            if (retValue == null)
            {
                retValue = new CacheUtility().InsertCachedItem<List<Transfer>>(List(), typeof(Transfer).Name);
            }
            return retValue;
        }

        public void Delete(long id)
        {
            using (var ctx = new CTSContext())
            {
                Transfer item = new Transfer { Id = id };
                ctx.Transfer.Attach(item);
                ctx.Transfer.Remove(item);
                ctx.SaveChanges();
            }
        }

        public void Delete()
        {
            Delete(Id);
        }

        public void Delete(List<long> ids)
        {
            using (var ctx = new CTSContext())
            {
                foreach (var id in ids)
                {
                    Transfer item = new Transfer { Id = id };
                    ctx.Transfer.Attach(item);
                    ctx.Transfer.Remove(item);
                }
                ctx.SaveChanges();
            }
        }

        public bool CheckPurposeInUse(short purposeId)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Transfer.AsNoTracking().Any(t => t.PurposeId == purposeId);
            }
        }

        public List<Transfer> ListInbox(long structureId,int startIndex, int pageSize, long userId, List<long> structureIds, List<short> categoryIds,
           bool isStructureReceiver, short privacyLevel, Expression<Func<Transfer, bool>> filterExpression = null, List<SortExpression<Transfer>> sortExpression = null,bool fromStructure=false)
        {
            using (var ctx = new CTSContext())
            {
                var retValue = new List<Transfer>();
                IQueryable<Transfer> query = ctx.Transfer
                    .Include(t => t.OwnerUser)

                    .Include(t => t.Document).ThenInclude(t => t.Note)
                    .Include(t => t.OwnerDelegatedUser)
                    .Include(t => t.FromUser)
                    .Include(t => t.ToUser)
                    .Include(t => t.FromStructure)
                    .Include(t => t.ToStructure)
                    .Include(t => t.Document).ThenInclude(t => t.SendingEntity)
                    .Include(t => t.Document).ThenInclude(t => t.Privacy)
                     .Include(t => t.Document).ThenInclude(t => t.Category)
                    .Include(t => t.Document).ThenInclude(t => t.DocumentReceiverEntity).ThenInclude(t => t.Structure)
                    .Include(t => t.Document).ThenInclude(t => t.DocumentReceiverEntity).ThenInclude(t => t.EntityGroup)
                    .Include(t => t.Document).ThenInclude(t => t.CreatedByUser)
                    .Include(t => t.Document).ThenInclude(t => t.Attachment).AsNoTracking()
                    .Include(t => t.Document).ThenInclude(t => t.LinkedDocumentDocument).ThenInclude(t => t.LinkedDocumentNavigation)
                    .Include(t => t.ExportedDocument).ThenInclude(x => x.Transfer)
                    .Include(t => t.SignedByUser);

                if (sortExpression != null)
                {
                    query = query.DynamicOrderBy(sortExpression);
                }
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                if (fromStructure)
                {

                    query = query.Where(x => !x.ClosedDate.HasValue && (x.ToStructureId.Value == structureId && x.Document.Privacy.Level <= privacyLevel));
                }
                else
                {

                    query = query.Where(x => !x.ClosedDate.HasValue && (x.ToUserId == userId || (!x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value) && isStructureReceiver && x.Document.Privacy.Level <= privacyLevel)));
                }

                if (!categoryIds.IsNullOrEmpty())
                {
                    query = query.Where(t => categoryIds.Contains(t.Document.CategoryId));
                }
                //return query.Skip(startIndex).Take(pageSize).ToList();
                return pageSize < 0 ? query.ToList() :  query.Skip(startIndex).Take(pageSize).ToList();

            }
        }
        public List<Transfer> ListInboxWithGrouping(long structureId,int startIndex, int pageSize, long userId, List<long> structureIds, List<short> categoryIds,
           bool isStructureReceiver, short privacyLevel, Expression<Func<Transfer, bool>> filterExpression = null, List<SortExpression<Document>> sortExpression = null,bool fromStructure=false)
        {
            using (var ctx = new CTSContext())
            {
                var retValue = new List<Transfer>();

                var userTransfers = ctx.Transfer
                                     .Where(x => !x.ClosedDate.HasValue && (fromStructure ?
                                     (x.ToStructureId.Value == structureId && x.Document.Privacy.Level <= privacyLevel) :
                                    (x.ToUserId == userId || (!x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value) && isStructureReceiver && x.Document.Privacy.Level <= privacyLevel))))
                                     .Where(t => !categoryIds.IsNullOrEmpty() ? categoryIds.Contains(t.Document.CategoryId) : true)
                                     .Where(filterExpression)
                                     .Include(t => t.OwnerUser).AsNoTracking()
                                     .Include(t => t.Document).ThenInclude(t => t.Note).AsNoTracking()
                                     .Include(t => t.OwnerDelegatedUser).AsNoTracking()
                                     .Include(t => t.FromUser).AsNoTracking()
                                     .Include(t => t.ToUser).AsNoTracking()
                                     .Include(t => t.FromStructure).AsNoTracking()
                                     .Include(t => t.ToStructure).AsNoTracking()
                                     .Include(t => t.Document).ThenInclude(t => t.SendingEntity).AsNoTracking()
                                     .Include(t => t.Document).ThenInclude(t => t.Privacy).AsNoTracking()
                                     .Include(t => t.Document).ThenInclude(t => t.DocumentReceiverEntity).ThenInclude(t => t.Structure).AsNoTracking()
                                     .Include(t => t.Document).ThenInclude(t => t.DocumentReceiverEntity).ThenInclude(t => t.EntityGroup).AsNoTracking()
                                     .Include(t => t.Document).ThenInclude(t => t.CreatedByUser).AsNoTracking()
                                     .Include(t => t.Document).ThenInclude(t => t.Attachment).AsNoTracking()
                                     .Include(t => t.Document).ThenInclude(t => t.LinkedDocumentDocument).ThenInclude(t => t.LinkedDocumentNavigation)
                                     .Include(t => t.SignedByUser)
                                     .GroupBy(t => t.DocumentId)
                                     .Select(g => new
                                     {
                                         transferMaxCreatedDate = g.Max(t => t.CreatedDate),
                                         documentId = g.Key,
                                         filteratedTransfers = g.OrderByDescending(a => a.Id).ToList()
                                     });

                if (sortExpression==null)
                {
                    if (pageSize != -1)
                    {
                        var documentsTransfersQuery = ctx.Document
                                                .Join(userTransfers,
                                                    d => d.Id,
                                                    ut => ut.documentId,
                                                    (d, ut) => new
                                                    {
                                                        Document = d,
                                                        TransferCreatedDate = ut.transferMaxCreatedDate,
                                                        Transfers = ut.filteratedTransfers
                                                    })
                                                .OrderByDescending(c => c.TransferCreatedDate)
                                                .Skip(startIndex)
                                                .Take(pageSize)
                                                .Select(T => new
                                                {
                                                    tarns = T.Transfers
                                                });

                        documentsTransfersQuery.Select(s => s.tarns).ToList().ForEach(joinResult => retValue.AddRange(joinResult));

                    }
                    else
                    {
                        var documentsTransfersQuery = ctx.Document
    .Join(userTransfers,
        d => d.Id,
        ut => ut.documentId,
        (d, ut) => new
        {
            Document = d,
            TransferCreatedDate = ut.transferMaxCreatedDate,
            Transfers = ut.filteratedTransfers
        })
    .OrderByDescending(c => c.TransferCreatedDate);

                        var result = documentsTransfersQuery
                            .Select(T => T.Transfers) 
                            .ToList();

                        result.ForEach(transfers => retValue.AddRange(transfers));



                    }


                }
                else
                {
                    if(pageSize != -1)
                    {
                        var documentsTransfersQuery = ctx.Document
                                                  .DynamicOrderBy(sortExpression)
                                                  .Join(userTransfers,
                                                      d => d.Id,
                                                      ut => ut.documentId,
                                                      (d, ut) => new
                                                      {
                                                          Document = d,
                                                          TransferCreatedDate = ut.transferMaxCreatedDate,
                                                          Transfers = ut.filteratedTransfers
                                                      })
                                                  .Skip(startIndex)
                                                  .Take(pageSize)
                                                  .Select(T => new
                                                  {
                                                      tarns = T.Transfers
                                                  });

                        documentsTransfersQuery.Select(s => s.tarns).ToList().ForEach(joinResult => retValue.AddRange(joinResult));

                    }
                    else
                    {
                        var documentsTransfersQuery = ctx.Document
                                                        .DynamicOrderBy(sortExpression)
                                                        .Join(userTransfers,
                                                            d => d.Id,
                                                            ut => ut.documentId,
                                                            (d, ut) => new
                                                            {
                                                                Document = d,
                                                                TransferCreatedDate = ut.transferMaxCreatedDate,
                                                                Transfers = ut.filteratedTransfers
                                                            });

                        var result = documentsTransfersQuery
                            .Select(T => T.Transfers)
                            .ToList();

                        result.ForEach(transfers => retValue.AddRange(transfers));



                    }

                }
                   

                





                return retValue; 
            }
        }
        public int GetInboxCount(long? structureId,long userId, List<long> structureIds, List<short> categoryIds, bool isStructureReceiver, short privacyLevel,
            Expression<Func<Transfer, bool>> filterExpression = null, bool fromStructure = false)
        {
            OpenDbContext();
            IQueryable<Transfer> query = _ctx.Transfer.Include(t => t.Document).ThenInclude(t => t.Privacy).AsNoTracking();
            var xx = query;
            if (filterExpression != null)
            {
                query = query.Where(filterExpression);
            }

            query = query.Where(x => !x.ClosedDate.HasValue && (fromStructure ?
                    (x.ToStructureId.Value == structureId && x.Document.Privacy.Level <= privacyLevel) :
                   (x.ToUserId == userId || (!x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value) && isStructureReceiver && x.Document.Privacy.Level <= privacyLevel))));

            if (!categoryIds.IsNullOrEmpty())
            {
                query = query.Where(t => categoryIds.Contains(t.Document.CategoryId));
            }

            return query.Count();
        }

        public int GetInboxCountWithGrouping(long? structureId, long userId, List<long> structureIds, List<short> categoryIds, bool isStructureReceiver, short privacyLevel,
            Expression<Func<Transfer, bool>> filterExpression = null, bool fromStructure = false)
        {
            OpenDbContext();
            IQueryable<Transfer> query = _ctx.Transfer.Include(t => t.Document).ThenInclude(t => t.Privacy).AsNoTracking();

            if (filterExpression != null)
            {
                query = query.Where(filterExpression);
            }

            query = query.Where(x => !x.ClosedDate.HasValue && (fromStructure ?
                    (x.ToStructureId.Value == structureId && x.Document.Privacy.Level <= privacyLevel) :
                   (x.ToUserId == userId || (!x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value) && isStructureReceiver && x.Document.Privacy.Level <= privacyLevel))));
            
            if (!categoryIds.IsNullOrEmpty())
            {
                query = query.Where(t => categoryIds.Contains(t.Document.CategoryId));
            }

            var distinctDocumentIdsQuery = query.Select(s => s.DocumentId).Distinct();
            return distinctDocumentIdsQuery.Count();
        }
    
        public int GetInboxUnreadCount(long? structureId,long userId, List<long> structureIds, List<short> categoryIds, bool isStructureReceiver, short privacyLevel,
            Expression<Func<Transfer, bool>> filterExpression = null, bool fromStructure = false)
        {
            OpenDbContext();
            IQueryable<Transfer> query = _ctx.Transfer.Include(t => t.Document).ThenInclude(t => t.Privacy).AsNoTracking();

            if (filterExpression != null)
            {
                query = query.Where(filterExpression);
            }

            query = query.Where(x => !x.OpenedDate.HasValue && !x.ClosedDate.HasValue && (fromStructure ?
                                (x.ToStructureId.Value == structureId && x.Document.Privacy.Level <= privacyLevel) :
                               (x.ToUserId == userId || (!x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value) && isStructureReceiver && x.Document.Privacy.Level <= privacyLevel))));

            if (!categoryIds.IsNullOrEmpty())
            {
                query = query.Where(t => categoryIds.Contains(t.Document.CategoryId));
            }

            return query.Count();
        }

        public int GetInboxUnreadCountWithGrouping(long? structureId,long userId, List<long> structureIds, List<short> categoryIds, bool isStructureReceiver, short privacyLevel,
            Expression<Func<Transfer, bool>> filterExpression = null, bool fromStructure = false)
        {
            OpenDbContext();
            IQueryable<Transfer> query = _ctx.Transfer.Include(t => t.Document).ThenInclude(t => t.Privacy).AsNoTracking();

            if (filterExpression != null)
            {
                query = query.Where(filterExpression);
            }

            query = query.Where(x => !x.OpenedDate.HasValue && !x.ClosedDate.HasValue &&(fromStructure ?
                                         (x.ToStructureId.Value == structureId && x.Document.Privacy.Level <= privacyLevel) :
                                        (x.ToUserId == userId || (!x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value) && isStructureReceiver && x.Document.Privacy.Level <= privacyLevel))));

            if (!categoryIds.IsNullOrEmpty())
            {
                query = query.Where(t => categoryIds.Contains(t.Document.CategoryId));
            }

            var distinctDocumentIdsQuery = query.Select(s => s.DocumentId).Distinct();
            return distinctDocumentIdsQuery.Count();
        }


        public List<Transfer> ListCompleted(int startIndex, int pageSize, long userId, List<long> structureIds, List<short> categoryIds, bool isStructureReceiver,
            short privacyLevel, Expression<Func<Transfer, bool>> filterExpression = null,
            List<SortExpression<Transfer>> sortExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                var retValue = new List<Transfer>();
                IQueryable<Transfer> query = ctx.Transfer
                    .Include(t => t.OwnerUser)
                    .Include(t=>t.Document).ThenInclude(t=>t.Note)
                    .Include(t => t.OwnerDelegatedUser)
                    .Include(t => t.FromUser)
                    .Include(t => t.ToUser)
                    .Include(t => t.FromStructure)
                    .Include(t => t.ToStructure)
                    .Include(t => t.Document).ThenInclude(t => t.SendingEntity)
                    .Include(t => t.Document).ThenInclude(t => t.DocumentReceiverEntity).ThenInclude(t => t.Structure)
                    .Include(t => t.Document).ThenInclude(t => t.DocumentReceiverEntity).ThenInclude(t => t.EntityGroup)
                    .Include(t => t.Document).ThenInclude(t => t.CreatedByUser).AsNoTracking();

                if (sortExpression != null)
                {
                    query = query.DynamicOrderBy(sortExpression);
                }
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }

                query = query.Where(x => x.ClosedDate.HasValue && (x.ToUserId == userId || (!x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value) && isStructureReceiver && x.Document.Privacy.Level <= privacyLevel)));

                if (!categoryIds.IsNullOrEmpty())
                {
                    query = query.Where(t => categoryIds.Contains(t.Document.CategoryId));
                }
                //return query.Skip(startIndex).Take(pageSize).ToList();
                return pageSize < 0 ?  query.ToList() :  query.Skip(startIndex).Take(pageSize).ToList();

            }
        }

        public Task<int> GetCompletedCount(long userId, List<long> structureIds, List<short> categoryIds, bool isStructureReceiver, short privacyLevel,
            Expression<Func<Transfer, bool>> filterExpression = null)
        {
            OpenDbContext();
            IQueryable<Transfer> query = _ctx.Transfer.Include(t => t.Document).ThenInclude(t => t.Privacy).AsNoTracking();

            if (filterExpression != null)
            {
                query = query.Where(filterExpression);
            }

            query = query.Where(x => x.ClosedDate.HasValue && (x.ToUserId == userId || (!x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value) && isStructureReceiver && x.Document.Privacy.Level <= privacyLevel)));

            if (!categoryIds.IsNullOrEmpty())
            {
                query = query.Where(t => categoryIds.Contains(t.Document.CategoryId));
            }
            return query.CountAsync();
        }

        public int GetInboxTodayCount(long? structureId, long userId, List<long> structureIds, List<short> categoryIds, bool isStructureReceiver, short privacyLevel, bool? isOverdue, Expression<Func<Transfer, bool>> filterExpression = null, bool fromStructure = false)
        {
            using (var ctx = new CTSContext())
            {
                var today = DateTime.Today.Date;
                IQueryable<Transfer> query = ctx.Transfer.AsNoTracking();
                query = query.Where(x => !x.ClosedDate.HasValue && x.CreatedDate.Date == today && (fromStructure ?
                                         (x.ToStructureId.Value == structureId && x.Document.Privacy.Level <= privacyLevel) :
                                        (x.ToUserId == userId || (!x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value) && isStructureReceiver && x.Document.Privacy.Level <= privacyLevel))));

                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                if (isOverdue.HasValue)
                {
                    if (isOverdue == true)
                    {
                        query = query.Where(t => t.DueDate.HasValue ? (t.ClosedDate.HasValue ? t.ClosedDate.Value > t.DueDate.Value : DateTime.Now > t.DueDate.Value) : false);
                    }
                    else
                    {
                        query = query.Where(t => t.DueDate.HasValue ? (t.ClosedDate.HasValue ? t.ClosedDate.Value < t.DueDate.Value : DateTime.Now < t.DueDate.Value) : true);
                    }
                }
                if (!categoryIds.IsNullOrEmpty())
                {
                    query = query.Where(t => categoryIds.Contains(t.Document.CategoryId));
                }

                return query.Count();
            }
        } 
         public int GetInboxTodayCountWithGrouping(long? structureId, long userId, List<long> structureIds, List<short> categoryIds, bool isStructureReceiver, short privacyLevel,
             bool? isOverdue, Expression<Func<Transfer, bool>> filterExpression = null, bool fromStructure = false)
        {
            using (var ctx = new CTSContext())
            {
                var today = DateTime.Today.Date;
                IQueryable<Transfer> query = ctx.Transfer.AsNoTracking();
                query = query.Where(x => !x.ClosedDate.HasValue && x.CreatedDate.Date == today && (fromStructure ?
                                         (x.ToStructureId.Value == structureId && x.Document.Privacy.Level <= privacyLevel) :
                                        (x.ToUserId == userId || (!x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value) && isStructureReceiver && x.Document.Privacy.Level <= privacyLevel))));

                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                if (isOverdue.HasValue)
                {
                    if (isOverdue == true)
                    {
                        query = query.Where(t => t.DueDate.HasValue ? (t.ClosedDate.HasValue ? t.ClosedDate.Value > t.DueDate.Value : DateTime.Now > t.DueDate.Value) : false);
                    }
                    else
                    {
                        query = query.Where(t => t.DueDate.HasValue ? (t.ClosedDate.HasValue ? t.ClosedDate.Value < t.DueDate.Value : DateTime.Now < t.DueDate.Value) : true);
                    }
                }
                if (!categoryIds.IsNullOrEmpty())
                {
                    query = query.Where(t => categoryIds.Contains(t.Document.CategoryId));
                }

                var distinctDocumentIdsQuery = query.Select(s => s.DocumentId).Distinct();
                return  distinctDocumentIdsQuery.Count();
            }
        }

        public int GetCompletedTodayCount(long userId, List<long> structureIds, List<short> categoryIds, bool isStructureReceiver, short privacyLevel, Expression<Func<Transfer, bool>> filterExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                var today = DateTime.Today.Date;
                IQueryable<Transfer> query = ctx.Transfer.AsNoTracking();
                query = query.Where(x => x.ClosedDate.HasValue && x.ClosedDate.Value.Date == today && (x.ToUserId == userId || (!x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value) && isStructureReceiver && x.Document.Privacy.Level <= privacyLevel)));
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                if (!categoryIds.IsNullOrEmpty())
                {
                    query = query.Where(t => categoryIds.Contains(t.Document.CategoryId));
                }
                return query.Count();
            }
        }

        public List<ValueText> CountByReceivedByCategoryStatistics(long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Transfer> query = ctx.Transfer.AsNoTracking();

                query = query.Where(t => t.ToUserId.Value == userId
                || (!t.ToUserId.HasValue && structureIds.Contains(t.ToStructureId.Value) && isStructureReceiver
                && (t.Document.Privacy != null ? t.Document.Privacy.Level <= privacyLevel : true)));

                return query.GroupBy(t => t.Document.CategoryId).Select(t => new ValueText { Id = t.Key, Text = Convert.ToString(t.Count()) }).AsNoTracking().ToList();
            }
        }

        public async Task<List<ValueText>> CountByReceivedByCategoryStatisticsAsync(long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, bool useAllStructures)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Transfer> query = ctx.Transfer.AsNoTracking();
                if (useAllStructures)
                {
                    query = query.Where(t => t.ToUserId.Value == userId
                                    || (!t.ToUserId.HasValue && structureIds.Contains(t.ToStructureId.Value) && isStructureReceiver
                                    && (t.Document.Privacy != null ? t.Document.Privacy.Level <= privacyLevel : true)));


                }
                else
                {
                    query = query.Where(t => (t.ToUserId.Value == userId && structureIds.Contains(t.ToStructureId.Value))
                                        || (!t.ToUserId.HasValue && structureIds.Contains(t.ToStructureId.Value) && isStructureReceiver
                                        && (t.Document.Privacy != null ? t.Document.Privacy.Level <= privacyLevel : true)));
                }


                return await query.GroupBy(t => t.Document.CategoryId).Select(t => new ValueText { Id = t.Key, Text = Convert.ToString(t.Count()) }).AsNoTracking().ToListAsync();
            }
        }

        /// <summary>
        /// Include Document
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public Transfer FindIncludeDocument(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Transfer.Include(t => t.Document).AsNoTracking().FirstOrDefault(t => t.Id == id);
            }
        }

        /// <summary>
        /// Include Document
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public Transfer FindIncludeDocumentAndTostructure(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Transfer.Include(t => t.Document).Include(x=>x.ToStructure).AsNoTracking().FirstOrDefault(t => t.Id == id);
            }
        }

        /// <summary>
        /// Include Document and Receivers and Category
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public Transfer FindIncludeDocumentAndReceiversAndCategory(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Transfer.Include(t => t.Document).ThenInclude(r => r.DocumentReceiverEntity)
                    .Include(t => t.Document).ThenInclude(t => t.Category).AsNoTracking().FirstOrDefault(t => t.Id == id);
            }
        }

        /// <summary>
        /// Include Document and Category
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public Transfer FindIncludeDocumentAndCategory(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Transfer.Include(t => t.Document).ThenInclude(t => t.Category).AsNoTracking()
                    .FirstOrDefault(t => t.Id == id);
            }
        }

        /// <summary>
        /// Include Document and privacy
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public Transfer FindIncludeDocumentAndPrivacy(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Transfer.Include(t => t.Document).ThenInclude(t => t.Privacy).AsNoTracking().FirstOrDefault(t => t.Id == id);
            }
        }
        /// <summary>
        /// Include Document and privacy
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public Transfer FindIncludeDocumentAndPrivacyandSendingEntity(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Transfer.Include(t => t.Document).ThenInclude(t => t.Privacy).Include(t => t.Document).ThenInclude(t => t.SendingEntity).AsNoTracking().FirstOrDefault(t => t.Id == id);
            }
        }
        /// <summary>
        /// Include Document , Category and User
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public Transfer FindIncludeDocumentCategoryUser(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Transfer.Include(t => t.Document).ThenInclude(t => t.Category)
                    .Include(t => t.Document).ThenInclude(t => t.CreatedByUser).AsNoTracking()
                    .FirstOrDefault(t => t.Id == id);
            }
        }

        /// <summary>
        /// Include Document , Category and User
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<Transfer> FindIncludeDocumentCategoryUserAsync(long id)
        {
            using (var ctx = new CTSContext())
            {
                return await ctx.Transfer.Include(t => t.Document).ThenInclude(t => t.Category)
                    .Include(t => t.Document).ThenInclude(t => t.CreatedByUser)
                    .Include(t => t.Document).ThenInclude(t => t.AttachmentNavigation).AsNoTracking()
                    .FirstOrDefaultAsync(t => t.Id == id);
            }
        }

        /// <summary>
        /// Include Document, Category, DocumentForm, DocumentReceiverEntity, SendingEntity, DocumentCarbonCopy, Classification, DocumentType
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public Transfer FindIncludeDocumentData(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Transfer.Include(t => t.Document).ThenInclude(t => t.Category)
                    .Include(t => t.Document).ThenInclude(x => x.DocumentForm)
                    .Include(t => t.Document).ThenInclude(x => x.DocumentReceiverEntity).ThenInclude(t => t.Structure)
                    .Include(t => t.Document).ThenInclude(x => x.DocumentReceiverEntity).ThenInclude(t => t.EntityGroup)
                    .Include(t => t.Document).ThenInclude(x => x.SendingEntity)
                    .Include(t => t.Document).ThenInclude(x => x.DocumentCarbonCopy).ThenInclude(t => t.Structure)
                    .Include(t => t.Document).ThenInclude(x => x.CreatedByUser)
                    .Include(t => t.Document).ThenInclude(x => x.Classification)
                    .Include(t => t.Document).ThenInclude(x => x.DocumentType)
                    .AsNoTracking().FirstOrDefault(t => t.Id == id);
            }
        }

        /// <summary>
        /// Include Document, Category, DocumentForm, DocumentReceiverEntity, SendingEntity, DocumentCarbonCopy, Classification, DocumentType
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<Transfer> FindIncludeDocumentDataAsync(long id)
        {
            using (var ctx = new CTSContext())
            {
                return await ctx.Transfer.Include(t => t.Document).ThenInclude(t => t.Category)
                    .Include(t => t.Document).ThenInclude(x => x.DocumentForm)
                    .Include(t => t.Document).ThenInclude(x => x.DocumentReceiverEntity).ThenInclude(t => t.Structure).ThenInclude(t => t.Parent)
                    .Include(t => t.Document).ThenInclude(x => x.DocumentReceiverEntity).ThenInclude(t => t.EntityGroup)
                    .Include(t => t.Document).ThenInclude(x => x.SendingEntity)
                    .Include(t => t.Document).ThenInclude(x => x.DocumentCarbonCopy).ThenInclude(t => t.Structure).ThenInclude(t => t.Parent)
                    .Include(t => t.Document).ThenInclude(x => x.CreatedByUser)
                    .Include(t => t.Document).ThenInclude(x => x.Classification)
                    .Include(t => t.Document).ThenInclude(x => x.DocumentType)
                    .Include(t => t.Document).ThenInclude(x => x.AttachmentNavigation)
                    .Include(t => t.Document).ThenInclude(x => x.Attachment)
                    .AsNoTracking().FirstOrDefaultAsync(t => t.Id == id);
            }
        }

        /// <summary>
        /// Include Document ,Priority,Privacy,SendingEntity,DocumentReceiverEntity Structure,CreatedByUser
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public Transfer GetTransferInfoById(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Transfer
                    .Include(t => t.Document).ThenInclude(t => t.SendingEntity)
                    .Include(t => t.Purpose)
                    .Include(t => t.FromUser)
                    .Include(t => t.ToUser)
                    .Include(t => t.ToStructure)
                    .Include(t => t.FromStructure)
                    .Include(t => t.Document).ThenInclude(t => t.DocumentReceiverEntity).ThenInclude(t => t.Structure)
                    .Include(t => t.Document).ThenInclude(t => t.DocumentReceiverEntity).ThenInclude(t => t.EntityGroup)
                    .Include(t => t.Document).ThenInclude(t => t.CreatedByUser).AsNoTracking()
                    .FirstOrDefault(c => c.Id == id);
            }
        }

        /// <summary>
        /// Include Document ,Priority,Privacy,SendingEntity,DocumentReceiverEntity Structure,CreatedByUser
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<Transfer> GetTransferInfoByIdAsync(long id)
        {
            using (var ctx = new CTSContext())
            {
                return await ctx.Transfer
                    .Include(t => t.Document).ThenInclude(t => t.SendingEntity)
                    .Include(t => t.Purpose)
                    .Include(t => t.Priority)
                    .Include(t => t.FromUser)
                    .Include(t => t.ToUser)
                    .Include(t => t.ToStructure)
                    .Include(t => t.FromStructure)
                    .Include(t => t.Document).ThenInclude(t => t.DocumentReceiverEntity).ThenInclude(t => t.Structure).ThenInclude(t => t.Parent)
                    .Include(t => t.Document).ThenInclude(t => t.DocumentLocks)
                    .Include(t => t.Document).ThenInclude(t => t.DocumentReceiverEntity).ThenInclude(t => t.EntityGroup)
                    .Include(t => t.Document).ThenInclude(t => t.CreatedByUser)
                    .Include(t => t.Document).ThenInclude(t => t.AttachmentNavigation)
                    .Include(t => t.Document).ThenInclude(t => t.Attachment).ThenInclude(t=>t.AttachmentSignUser).ThenInclude(t=>t.SignatureRegion)
                    .Include(t=>t.WorkflowStep).ThenInclude(t=>t.Workflow).ThenInclude(t=>t.InitatorUser)
                    .Include(t => t.Document).ThenInclude(t => t.Category).AsNoTracking()
                    .Include(t => t.Document).ThenInclude(t => t.DocumentCarbonCopy).ThenInclude(t => t.Structure).ThenInclude(t => t.Parent)
                    .FirstOrDefaultAsync(c => c.Id == id);
            }
        }

        public bool IsActive(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Transfer.AsNoTracking().Any(t => t.Id == id && !t.ClosedDate.HasValue);
            }
        }

        /// <summary>
        /// Active transfers include Document and Category
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public Transfer FindActiveIncludeDocument(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Transfer.Include(t => t.Document).ThenInclude(t => t.Category).AsNoTracking()
                    .FirstOrDefault(t => t.Id == id && !t.ClosedDate.HasValue);
            }
        }

        /// <summary>
        /// Check access on transfer
        /// </summary>
        public bool CheckHaveAccess(long transferId, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long roleId, long structureId)
        {
            using (var ctx = new CTSContext())
            {
                using (var CoreContext = new CoreContext())
                {
                    var HaveAccessStructureInbox = CoreContext.NodeSecurity.Include(d => d.Node).AsNoTracking().AnyAsync(s => s.Node.Inherit == "StructureInbox" && (s.RoleId == roleId || (s.UserId == userId && s.StructureId == structureId))).Result;

                    return ctx.Transfer.AsNoTracking()
                .Any(t => t.Id == transferId && (t.ToUserId == userId || (((((t.ToUserId.HasValue && t.IsSigned == true && HaveAccessStructureInbox) || !t.ToUserId.HasValue) && structureIds.Contains(t.ToStructureId.Value)) || (t.RequestStatus.HasValue && structureIds.Contains(t.FromStructureId.Value))) && isStructureReceiver && t.Document.Privacy.Level <= privacyLevel) || t.FromUserId == userId));

                }
            }
        }


        /// <summary>
        /// Check access on transfer
        /// </summary>
        public async Task<bool> CheckHaveAccessAsync(long transferId, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel,long roleId,long structureId)
        {
            using (var ctx = new CTSContext())
            {
                using (var CoreContext = new CoreContext())
                {
                var HaveAccessStructureInbox = CoreContext.NodeSecurity.Include(d=>d.Node).AsNoTracking().AnyAsync(s=> s.Node.Inherit=="StructureInbox" && (s.RoleId==roleId ||(s.UserId==userId && s.StructureId==structureId))).Result;
                return await ctx.Transfer.AsNoTracking()
                  .AnyAsync(t => t.Id == transferId && (t.ToUserId == userId || 
                  (((((t.ToUserId.HasValue && t.IsSigned == true && HaveAccessStructureInbox) 
                  || !t.ToUserId.HasValue) && structureIds.Contains(t.ToStructureId.Value)) || 
                  (t.RequestStatus.HasValue && structureIds.Contains(t.FromStructureId.Value))) 
                  && isStructureReceiver && t.Document.Privacy.Level <= privacyLevel) || t.FromUserId == userId));

                }
            }
        }

        /// <summary>
        /// Check access on transfer and closedDate not null
        /// </summary>
        public bool CheckActiveHaveAccess(long transferId, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long roleId, long structureId)
        {
            using (var ctx = new CTSContext())
            {
                using (var CoreContext = new CoreContext())
                {
                    var HaveAccessStructureInbox = CoreContext.NodeSecurity.Include(d => d.Node).AsNoTracking().AnyAsync(s => s.Node.Inherit == "StructureInbox" && (s.RoleId == roleId || (s.UserId == userId && s.StructureId == structureId))).Result;
                   
                    return ctx.Transfer.AsNoTracking()
                .Any(t => t.Id == transferId && !t.ClosedDate.HasValue && (t.ToUserId == userId ||
                  (((((t.ToUserId.HasValue && t.IsSigned == true && HaveAccessStructureInbox)
                  || !t.ToUserId.HasValue) && structureIds.Contains(t.ToStructureId.Value)) ||
                  (t.RequestStatus.HasValue && structureIds.Contains(t.FromStructureId.Value)))
                  && isStructureReceiver && t.Document.Privacy.Level <= privacyLevel) || t.FromUserId == userId));

                }
            }
        }

        /// <summary>
        /// Check access on transfer and closedDate not null
        /// </summary>
        public async Task<bool> CheckActiveHaveAccessAsync(long transferId, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long roleId, long structureId)
        {
            using (var ctx = new CTSContext())
            {
                using (var CoreContext = new CoreContext())
                {
                    var HaveAccessStructureInbox = CoreContext.NodeSecurity.Include(d => d.Node).AsNoTracking().AnyAsync(s => s.Node.Inherit == "StructureInbox" && (s.RoleId == roleId || (s.UserId == userId && s.StructureId == structureId))).Result;

                    return await ctx.Transfer.AsNoTracking()
                .AnyAsync(t => t.Id == transferId && !t.ClosedDate.HasValue && (t.ToUserId == userId ||
                  (((((t.ToUserId.HasValue && t.IsSigned == true && HaveAccessStructureInbox)
                  || !t.ToUserId.HasValue) && structureIds.Contains(t.ToStructureId.Value)) ||
                  (t.RequestStatus.HasValue && structureIds.Contains(t.FromStructureId.Value)))
                  && isStructureReceiver && t.Document.Privacy.Level <= privacyLevel) || t.FromUserId == userId));

                }
            }
        }

        public bool CheckHaveAccessStructureInboxAsync(long? transferId,long? documentId, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long roleId, long structureId)
        {
            using (var ctx = new CTSContext())
            {
                using (var CoreContext = new CoreContext())
                {
                    var HaveAccessStructureInbox = CoreContext.NodeSecurity.Include(d => d.Node).AsNoTracking().Any(s => s.Node.Inherit == "StructureInbox" && (s.RoleId == roleId || (s.UserId == userId && s.StructureId == structureId)));

                    return  ctx.Transfer.Include(d=>d.Document).AsNoTracking().Any(t => 
                       ((transferId != null && t.Id == transferId) ||(documentId!=null && t.DocumentId==documentId)) && HaveAccessStructureInbox && t.IsSigned == true &&
                      structureIds.Contains(t.ToStructureId.Value) && isStructureReceiver && t.Document.Privacy.Level <= privacyLevel);
                }
            }
        }
        
        public Task<int> GetTransferHistoryCount(long documentId)
        {
            OpenDbContext();
            IQueryable<Transfer> query = _ctx.Transfer.AsNoTracking();
            query = query.Where(x => x.DocumentId == documentId);
            return query.CountAsync();
        }

        public List<Transfer> ListTransferHistory(int startIndex, int pageSize, long documentId, List<SortExpression<Transfer>> sortExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                var retValue = new List<Transfer>();
                IQueryable<Transfer> query = ctx.Transfer
                    .Include(t => t.Document).ThenInclude(t => t.CreatedByUser)
                    .Include(t => t.FromUser)
                    .Include(t => t.ToUser)
                    .Include(t => t.FromStructure)
                    .Include(t => t.ToStructure);

                if (sortExpression != null)
                {
                    query = query.DynamicOrderBy(sortExpression);
                }
                query = query.Where(x => x.DocumentId == documentId);
                return query.Skip(startIndex).Take(pageSize).ToList();
            }
        }

        public async Task<List<Transfer>> ListTransferHistoryAsync(int startIndex, int pageSize, long documentId, List<SortExpression<Transfer>> sortExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                var retValue = new List<Transfer>();
                IQueryable<Transfer> query = ctx.Transfer
                    .Include(t => t.Document).ThenInclude(t => t.CreatedByUser)
                    .Include(t => t.Document).ThenInclude(t => t.Privacy)
                    .Include(t => t.FromUser)
                    .Include(t => t.ToUser)
                    .Include(t => t.FromStructure)
                    .Include(t => t.ToStructure);

                if (sortExpression != null)
                {
                    query = query.DynamicOrderBy(sortExpression);
                }
                query = query.Where(x => x.DocumentId == documentId);
                var gg = query?.ToList();
                return await query.Skip(startIndex).Take(pageSize).ToListAsync();
            }
        }

        public (double AverageTransfers, double AverageCreatedByUser) AverageByCreatedByUserTransferStatistics(long userId)
        {
            using (var ctx = new CTSContext())
            {
                var averageTasksArray = (from transfer in ctx.Transfer
                                         where transfer.ClosedDate.HasValue
                                         select new { transfer.CreatedDate, transfer.ClosedDate }).ToArray();
                var averageTasks = averageTasksArray.Length > 0 ? averageTasksArray.Average(x => (x.ClosedDate - x.CreatedDate).Value.TotalSeconds) : 0;

                var averageCreatedByUserArray = (from transfer in ctx.Transfer
                                                 where transfer.ClosedDate.HasValue
                                                 && transfer.OwnerUserId == userId
                                                 select new { transfer.CreatedDate, transfer.ClosedDate }).ToArray();
                var averageCreatedByUser = averageCreatedByUserArray.Length > 0 ? averageCreatedByUserArray.Average(x => (x.ClosedDate - x.CreatedDate).Value.TotalSeconds) : 0;

                return (averageTasks, averageCreatedByUser);
            }
        }

        public async Task<(double AverageTransfers, double AverageCreatedByUser)> AverageByCreatedByUserTransferStatisticsAsync(long userId)
        {
            using (var ctx = new CTSContext())
            {
                var averageTasksArray = await (from transfer in ctx.Transfer
                                               where transfer.ClosedDate.HasValue
                                               select new { transfer.CreatedDate, transfer.ClosedDate }).ToArrayAsync();
                var averageTasks = averageTasksArray.Length > 0 ? averageTasksArray.Average(x => (x.ClosedDate - x.CreatedDate).Value.TotalSeconds) : 0;

                var averageCreatedByUserArray = await (from transfer in ctx.Transfer
                                                       where transfer.ClosedDate.HasValue
                                                       && transfer.OwnerUserId == userId
                                                       select new { transfer.CreatedDate, transfer.ClosedDate }).ToArrayAsync();
                var averageCreatedByUser = averageCreatedByUserArray.Length > 0 ? averageCreatedByUserArray.Average(x => (x.ClosedDate - x.CreatedDate).Value.TotalSeconds) : 0;

                return (averageTasks, averageCreatedByUser);
            }
        }

        public bool CheckIfLastOpenTransfer(long transferId, long? documentId, bool isBroadcast = false)
        {
            using (var ctx = new CTSContext())
            {
                return !ctx.Transfer.Any(x => x.Id != transferId && x.DocumentId == documentId && x.ClosedDate == null && (isBroadcast || !x.Cced));
            }
        }

        public Task<int> GetSentCount( long? structureId,long userId, Expression<Func<Transfer, bool>> filterExpression = null, bool fromStructure = false)
        {
            OpenDbContext();
            IQueryable<Transfer> query = _ctx.Transfer.Include(t => t.Document).ThenInclude(t => t.Privacy).AsNoTracking();

            if (filterExpression != null)
            {
                query = query.Where(filterExpression);
            }

            query = query.Where(x => ((!fromStructure && x.FromUserId == userId) || (fromStructure && x.FromStructureId == structureId)) && !x.Document.ClosedDate.HasValue && x.FromRecall.Value != true && !x.ExportedDocumentId.HasValue);


            return query.CountAsync();
        }

        public async Task<int> GetSentCountWithGrouping( long? structureId, long userId, Expression<Func<Transfer, bool>> filterExpression = null,bool fromStructure=false)
        {
            OpenDbContext();
            IQueryable<Transfer> query = _ctx.Transfer.Include(t => t.Document).ThenInclude(t => t.Privacy).AsNoTracking();


            if (filterExpression != null)
            {
                query = query.Where(filterExpression);
            }

            query = query.Where(x => ((!fromStructure&&x.FromUserId == userId)||(fromStructure && x.FromStructureId==structureId)) && !x.Document.ClosedDate.HasValue && x.FromRecall.Value != true && !x.ExportedDocumentId.HasValue);

            var distinctDocumentIdsQuery = query.Select(s => s.DocumentId).Distinct();
            return await distinctDocumentIdsQuery.CountAsync();
        }

        public List<Transfer> ListSent(int startIndex, int pageSize, long userId, long structureId, Expression<Func<Transfer, bool>> filterExpression = null, List<SortExpression<Transfer>> sortExpression = null, bool fromStructure = false)
        {
            using (var ctx = new CTSContext())
            {
                var retValue = new List<Transfer>();
                IQueryable<Transfer> query = ctx.Transfer
                    .Include(t => t.OwnerUser)
                    .Include(t=>t.Note)
                    .Include(t => t.OwnerDelegatedUser)
                    .Include(t => t.FromUser)
                    .Include(t => t.ToUser)
                    .Include(t => t.FromStructure)
                    .Include(t => t.ToStructure)
                    .Include(t => t.Document).ThenInclude(t => t.SendingEntity)
                    .Include(t => t.Document).ThenInclude(t => t.Privacy)
                    .Include(t => t.Document).ThenInclude(t => t.DocumentReceiverEntity).ThenInclude(t => t.Structure)
                    .Include(t => t.Document).ThenInclude(t => t.DocumentReceiverEntity).ThenInclude(t => t.EntityGroup)
                    .Include(t => t.Document).ThenInclude(t => t.CreatedByUser).AsNoTracking()
                    .Include(t => t.Document).ThenInclude(t => t.LinkedDocumentDocument).ThenInclude(t => t.LinkedDocumentNavigation);

                query = query.Where(x => ((!fromStructure && x.FromUserId == userId) || (fromStructure && x.FromStructureId == structureId)) && !x.Document.ClosedDate.HasValue && (x.FromRecall.Value != true) && !x.ExportedDocumentId.HasValue);

                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                if (sortExpression != null)
                {
                    query = query.DynamicOrderBy(sortExpression);
                }
                return pageSize < 0 ?  query.ToList() :  query.Skip(startIndex).Take(pageSize).ToList();


            }
        }


        public List<Transfer> ListSentWithGrouping(int startIndex, int pageSize, long userId, long structureId, Expression<Func<Transfer, bool>> filterExpression = null, List<SortExpression<Document>> sortExpression = null, bool fromStructure = false)
        {

            try
            {

                using (var ctx = new CTSContext())
                {
                    var retValue = new List<Transfer>();

                    var userTransfers = ctx.Transfer
                                         .Where(x => ((!fromStructure && x.FromUserId == userId) || (fromStructure && x.FromStructureId == structureId)) && !x.Document.ClosedDate.HasValue && x.FromRecall.Value != true && !x.ExportedDocumentId.HasValue)
                                         .Where(filterExpression)
                                         .Include(t => t.OwnerUser)
                                         .Include(t=>t.Document).ThenInclude(t=>t.Note)
                                         .Include(t => t.OwnerDelegatedUser).AsNoTracking()
                                         .Include(t => t.FromUser).AsNoTracking()
                                         .Include(t => t.ToUser).AsNoTracking()
                                         .Include(t => t.FromStructure).AsNoTracking()
                                         .Include(t => t.ToStructure).AsNoTracking()
                                         .Include(t => t.Document).ThenInclude(d => d.SendingEntity).AsNoTracking()
                                         .Include(t => t.Document).ThenInclude(d => d.Privacy).AsNoTracking()
                                         .Include(t => t.Document).ThenInclude(d => d.DocumentReceiverEntity).ThenInclude(dr => dr.Structure).AsNoTracking()
                                         .Include(t => t.Document).ThenInclude(d => d.DocumentReceiverEntity).ThenInclude(dr => dr.EntityGroup).AsNoTracking()
                                         .Include(t => t.Document).ThenInclude(d => d.CreatedByUser)
                                         .Include(t => t.Document).ThenInclude(t => t.LinkedDocumentDocument).ThenInclude(t => t.LinkedDocumentNavigation)
                                         .AsNoTracking()
                                         .GroupBy(t => t.DocumentId)
                                         .Select(g => new
                                         {
                                             transferMaxCreatedDate = g.Max(t => t.CreatedDate),
                                             documentId = g.Key,
                                             filteratedTransfers = g.OrderByDescending(a => a.Id).ToList()
                                         });

                    if (sortExpression==null)
                    {
                        if (pageSize != -1)
                        {
                            var documentsTransfersQuery = ctx.Document
                                         .Join(userTransfers,
                                             d => d.Id,
                                             ut => ut.documentId,
                                             (d, ut) => new
                                             {
                                                 Document = d,
                                                 TransferCreatedDate = ut.transferMaxCreatedDate,
                                                 Transfers = ut.filteratedTransfers
                                             })
                                         .OrderByDescending(c => c.TransferCreatedDate)
                                         .Skip(startIndex)
                                         .Take(pageSize)
                                         .Select(T => new
                                         {
                                             tarns = T.Transfers
                                         });


                            documentsTransfersQuery.Select(s => s.tarns).ToList().ForEach(joinResult => retValue.AddRange(joinResult));
                        }
                        else
                        {
                            var documentsTransfersQuery = ctx.Document
                                     .Join(userTransfers,
                             d => d.Id,
  ut => ut.documentId,
  (d, ut) => new
  {
      Document = d,
      TransferCreatedDate = ut.transferMaxCreatedDate,
      Transfers = ut.filteratedTransfers
  })
.OrderByDescending(c => c.TransferCreatedDate);

                            var result = documentsTransfersQuery
                                .Select(T => T.Transfers)
                                .ToList();

                            result.ForEach(transfers => retValue.AddRange(transfers));

                        }


                    }
                    else
                    {
                        if(pageSize != -1)
                        {

                            var documentsTransfersQuery = ctx.Document
                                                     .DynamicOrderBy(sortExpression)
                                                     .Join(userTransfers,
                                                         d => d.Id,
                                                         ut => ut.documentId,
                                                         (d, ut) => new
                                                         {
                                                             Document = d,
                                                             TransferCreatedDate = ut.transferMaxCreatedDate,
                                                             Transfers = ut.filteratedTransfers
                                                         })
                                                     .Skip(startIndex)
                                                     .Take(pageSize)
                                                     .Select(T => new
                                                     {
                                                         tarns = T.Transfers
                                                     });


                            documentsTransfersQuery.Select(s => s.tarns).ToList().ForEach(joinResult => retValue.AddRange(joinResult));
                        }
                        else
                        {

                            var documentsTransfersQuery = ctx.Document
                                                 .DynamicOrderBy(sortExpression)
                                                 .Join(userTransfers,
                                                     d => d.Id,
                                                     ut => ut.documentId,
                                                     (d, ut) => new
                                                     {
                                                         Document = d,
                                                         TransferCreatedDate = ut.transferMaxCreatedDate,
                                                         Transfers = ut.filteratedTransfers
                                                     })
                                                 .Skip(startIndex)
                                                 .Take(pageSize)
                                                 .Select(T => new
                                                 {
                                                     tarns = T.Transfers
                                                 });


                            documentsTransfersQuery.Select(s => s.tarns).ToList().ForEach(joinResult => retValue.AddRange(joinResult));





                        }
                      

                    }




                    return retValue;

                }
            }
            catch (Exception e)
            {

                throw e;
            }
        }
        public int GetSentTodayCount(long? structureId, long userId, Expression<Func<Transfer, bool>> filterExpression = null, bool fromStructure = false)
        {
            using (var ctx = new CTSContext())
            {
                var today = DateTime.Today.Date;
                IQueryable<Transfer> query = ctx.Transfer.AsNoTracking();
                query = query.Where(x => ((!fromStructure && x.FromUserId == userId) || (fromStructure && x.FromStructureId == structureId)) && !x.Document.ClosedDate.HasValue && x.CreatedDate.Date == today && x.FromRecall.Value != true && !x.ExportedDocumentId.HasValue);

                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                return query.Count();
            }
        }



        public List<Transfer> ListFollowUps(int startIndex, int pageSize, long userId, List<long> structureIds, List<short> categoryIds,
   bool isStructureReceiver, short privacyLevel, Expression<Func<Transfer, bool>> filterExpression = null, List<SortExpression<Transfer>> sortExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                var retValue = new List<Transfer>();
                IQueryable<Transfer> query = ctx.Transfer
                    .Include(t => t.OwnerUser)
                    .Include(t => t.OwnerDelegatedUser)
                    .Include(t => t.FromUser)
                    .Include(t => t.ToUser)
                    .Include(t => t.FromStructure)
                    .Include(t => t.ToStructure)
                    .Include(t => t.Document).ThenInclude(t => t.SendingEntity)
                    .Include(t => t.Document).ThenInclude(t => t.Privacy)
                    .Include(t => t.Document).ThenInclude(t => t.DocumentReceiverEntity).ThenInclude(t => t.Structure)
                    .Include(t => t.Document).ThenInclude(t => t.DocumentReceiverEntity).ThenInclude(t => t.EntityGroup)
                    .Include(t => t.Document).ThenInclude(t => t.CreatedByUser)
                    .Include(t => t.Document).ThenInclude(t => t.DocumentForm)
                    .Include(t => t.Document).ThenInclude(t => t.Attachment).AsNoTracking();

                int categoryId = Configuration.FollowUpCategory;
                query = query.Where(x => x.Document.CategoryId == categoryId);

                if (!categoryIds.IsNullOrEmpty())
                {
                    query = query.Where(t => categoryIds.Contains(t.Document.CategoryId));
                }

                if (sortExpression != null)
                {
                    query = query.DynamicOrderBy(sortExpression);
                }
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                return query.Skip(startIndex).Take(pageSize).ToList();
            }
        }

        public Task<int> GetFollowUpsCount(long userId, List<long> structureIds, List<short> categoryIds, bool isStructureReceiver, short privacyLevel,
    Expression<Func<Transfer, bool>> filterExpression = null)
        {
            OpenDbContext();
            IQueryable<Transfer> query = _ctx.Transfer.Include(t => t.Document).ThenInclude(t => t.Privacy).AsNoTracking();

            if (filterExpression != null)
            {
                query = query.Where(filterExpression);
            }

            int categoryId = Configuration.FollowUpCategory;

            //query = query.Where(x => !x.ClosedDate.HasValue && (x.ToUserId == userId || (!x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value) && isStructureReceiver && x.Document.Privacy.Level <= privacyLevel)) && x.Document.CategoryId == categoryId);
            query = query.Where(x => !x.ClosedDate.HasValue && x.Document.CategoryId == categoryId);

            if (!categoryIds.IsNullOrEmpty())
            {
                query = query.Where(t => categoryIds.Contains(t.Document.CategoryId));
            }
            return query.CountAsync();
        }

        public int GetSentTodayCountWithGrouping(long? structureId, long userId, Expression<Func<Transfer, bool>> filterExpression = null, bool fromStructure = false)
        {
            using (var ctx = new CTSContext())
            {
                var today = DateTime.Today.Date;
                IQueryable<Transfer> query = ctx.Transfer.AsNoTracking();
                query = query.Where(x => ((!fromStructure && x.FromUserId == userId) || (fromStructure && x.FromStructureId == structureId)) && !x.Document.ClosedDate.HasValue && x.CreatedDate.Date == today && x.FromRecall.Value != true && !x.ExportedDocumentId.HasValue);
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                var distinctDocumentIdsQuery = query.Select(s => s.DocumentId).Distinct();

                return distinctDocumentIdsQuery.Count();
            }
        }

        public Transfer FindIncludeToUserToStructure(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Transfer.Include(x => x.ToUser).Include(x => x.ToStructure).Include(x=>x.Document).AsNoTracking().FirstOrDefault(c => c.Id == id);
            }
        }

        public List<TransferCrawlingModel> ListByCreatedDate(DateTime? lastCreatedDate, int index, int bulkSize)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Transfer> query = ctx.Transfer.AsNoTracking().Where(t => (t.Document.StatusId != (short)DocumentStatus.Draft) && (!lastCreatedDate.HasValue || t.CreatedDate >= lastCreatedDate.Value));
                query = query.OrderBy(o => o.CreatedDate).Skip(index).Take(bulkSize);
                return query.Select(s => (TransferCrawlingModel)s).ToList();
            }
        }

        public List<TransferCrawlingModel> ListByModifiedDate(DateTime? lastModifiedDate, int index, int bulkSize)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Transfer> query = ctx.Transfer.AsNoTracking().Where(t => (t.Document.StatusId != (short)DocumentStatus.Draft) && ((!lastModifiedDate.HasValue && t.ModifiedDate.HasValue) || (lastModifiedDate.HasValue && t.ModifiedDate.HasValue && t.ModifiedDate.Value >= lastModifiedDate)));
                query = query.OrderBy(o => o.ModifiedDate).Skip(index).Take(bulkSize);
                return query.Select(s => (TransferCrawlingModel)s).ToList();
            }
        }

        public bool CheckHasLockedAttachments(long tranferId)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Transfer.AsNoTracking().Any(t => t.Id == tranferId && t.Attachment.Any(a => a.Id != t.Document.AttachmentId && a.IsLocked == true));
            }
        }

        /// <summary>
        /// Include Document 
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public List<Transfer> FindChildWithDocument(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Transfer.Include(t => t.Document).ThenInclude(t => t.Privacy).Include(t => t.Document).ThenInclude(t => t.SendingEntity).AsNoTracking().Where(t => t.ParentTransferId == id).ToList();
            }
        }

        public List<Transfer> ListByDocumentId(long documentId)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Transfer.Where(t => t.DocumentId == documentId).AsNoTracking().OrderBy(t => t.CreatedDate).ToList();
            }
        }
        public List<Transfer> ListByExportDocumentId(long exportDocumentId)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Transfer.Where(t => t.ExportedDocumentId.HasValue && t.ExportedDocumentId == exportDocumentId).AsNoTracking().ToList();
            }
        }
        public List<Transfer> ListByDocumentIdIncludeDocument(long documentId)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Transfer.Include(t => t.Document).Where(t => t.DocumentId == documentId).AsNoTracking().OrderBy(t => t.CreatedDate).ToList();
            }
        }

        public bool IsLockedByUserOrNotLocked(long id, long userId)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Transfer.AsNoTracking().Any(t => t.Id == id && (t.OwnerUserId.HasValue ? t.OwnerUserId == userId : true));
            }
        }
        public bool CheckSameTransferIsExist(long? documentId, long? parentTransferId, long? toStructureId, long? toUserId, long? fromUserId)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Transfer.AsNoTracking().Any(t => t.ParentTransferId == parentTransferId && t.DocumentId == documentId && !t.ClosedDate.HasValue && t.FromUserId == fromUserId && (toUserId != null ? t.ToUserId == toUserId : t.ToStructureId == toStructureId));
            }
        }

        public List<PurposeCountModel> GetStatisticsPerTransferPurpose(List<short> categoryIds)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Transfer> query = ctx.Transfer.AsNoTracking();

                if (!categoryIds.IsNullOrEmpty())
                {
                    query = query.Where(t => categoryIds.Contains(t.Document.CategoryId));
                }
                return query.GroupBy(t => new { t.PurposeId }).Select(t => new PurposeCountModel { PurposeId = t.Key.PurposeId.Value, Count = t.Count() }).ToList();
            }
        }

        public async Task<List<PurposeCountModel>> GetStatisticsPerTransferPurposeAsync(List<short> categoryIds)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Transfer> query = ctx.Transfer.AsNoTracking();

                if (!categoryIds.IsNullOrEmpty())
                {
                    query = query.Where(t => categoryIds.Contains(t.Document.CategoryId));
                }
                return await query.GroupBy(t => new { t.PurposeId }).Select(t => new PurposeCountModel { PurposeId = t.Key.PurposeId.Value, Count = t.Count() }).ToListAsync();
            }
        }

        public List<CategoryCountModel> GetTransfersInProgressOverdue(List<long> structureIds = null, string fromDate = "", string toDate = "")
        {
            using (var ctx = new CTSContext())
            {
                var today = DateTime.Now.Date;
                IQueryable<Transfer> query = ctx.Transfer.AsNoTracking().Where(t => !t.ClosedDate.HasValue && t.DueDate.HasValue && t.DueDate < today);
                if (!structureIds.IsNullOrEmpty())
                {
                    query = query.Where(t => structureIds.Contains(t.Document.CreatedByStructureId));
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    query = query.Where(t => t.CreatedDate.Date >= from);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    query = query.Where(t => t.CreatedDate.Date <= to);
                }
                return query.GroupBy(t => t.Document.CategoryId).Select(t => new CategoryCountModel { CategoryId = t.Key, Count = t.Count() }).ToList();
            }
        }

        public async Task<List<CategoryCountModel>> GetTransfersInProgressOverdueAsync(List<long> structureIds = null, string fromDate = "", string toDate = "")
        {
            using (var ctx = new CTSContext())
            {
                var today = DateTime.Now.Date;
                IQueryable<Transfer> query = ctx.Transfer.AsNoTracking().Where(t => !t.ClosedDate.HasValue && t.DueDate.HasValue && t.DueDate < today);
                if (!structureIds.IsNullOrEmpty())
                {
                    query = query.Where(t => structureIds.Contains(t.Document.CreatedByStructureId));
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    query = query.Where(t => t.CreatedDate.Date >= from);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    query = query.Where(t => t.CreatedDate.Date <= to);
                }
                return await query.GroupBy(t => t.Document.CategoryId).Select(t => new CategoryCountModel { CategoryId = t.Key, Count = t.Count() }).ToListAsync();
            }
        }

        public List<CategoryCountModel> GetTransfersInProgressOnTime(List<long> structureIds = null, string fromDate = "", string toDate = "")
        {
            using (var ctx = new CTSContext())
            {
                var today = DateTime.Now.Date;
                IQueryable<Transfer> query = ctx.Transfer.AsNoTracking().Where(t => !t.ClosedDate.HasValue && t.DueDate.HasValue && t.DueDate >= today);
                if (!structureIds.IsNullOrEmpty())
                {
                    query = query.Where(t => structureIds.Contains(t.Document.CreatedByStructureId));
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    query = query.Where(t => t.CreatedDate.Date >= from);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    query = query.Where(t => t.CreatedDate.Date <= to);
                }
                return query.GroupBy(t => t.Document.CategoryId).Select(t => new CategoryCountModel { CategoryId = t.Key, Count = t.Count() }).ToList();
            }
        }

        public async Task<List<CategoryCountModel>> GetTransfersInProgressOnTimeAsync(List<long> structureIds = null, string fromDate = "", string toDate = "")
        {
            using (var ctx = new CTSContext())
            {
                var today = DateTime.Now.Date;
                IQueryable<Transfer> query = ctx.Transfer.AsNoTracking().Where(t => !t.ClosedDate.HasValue && t.DueDate.HasValue && t.DueDate >= today);
                if (!structureIds.IsNullOrEmpty())
                {
                    query = query.Where(t => structureIds.Contains(t.Document.CreatedByStructureId));
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    query = query.Where(t => t.CreatedDate.Date >= from);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    query = query.Where(t => t.CreatedDate.Date <= to);
                }
                return await query.GroupBy(t => t.Document.CategoryId).Select(t => new CategoryCountModel { CategoryId = t.Key, Count = t.Count() }).ToListAsync();
            }
        }

        public List<CategoryCountModel> GetTransfersCompletedOverdue(List<long> structureIds = null, string fromDate = "", string toDate = "")
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Transfer> query = ctx.Transfer.AsNoTracking().Where(t => t.ClosedDate.HasValue && t.DueDate.HasValue && t.DueDate < t.ClosedDate.Value.Date);
                if (!structureIds.IsNullOrEmpty())
                {
                    query = query.Where(t => structureIds.Contains(t.Document.CreatedByStructureId));
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    query = query.Where(t => t.CreatedDate.Date >= from);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    query = query.Where(t => t.CreatedDate.Date <= to);
                }
                return query.GroupBy(t => t.Document.CategoryId).Select(t => new CategoryCountModel { CategoryId = t.Key, Count = t.Count() }).ToList();
            }
        }

        public async Task<List<CategoryCountModel>> GetTransfersCompletedOverdueAsync(List<long> structureIds = null, string fromDate = "", string toDate = "")
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Transfer> query = ctx.Transfer.AsNoTracking().Where(t => t.ClosedDate.HasValue && t.DueDate.HasValue && t.DueDate < t.ClosedDate.Value.Date);
                if (!structureIds.IsNullOrEmpty())
                {
                    query = query.Where(t => structureIds.Contains(t.Document.CreatedByStructureId));
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    query = query.Where(t => t.CreatedDate.Date >= from);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    query = query.Where(t => t.CreatedDate.Date <= to);
                }
                return await query.GroupBy(t => t.Document.CategoryId).Select(t => new CategoryCountModel { CategoryId = t.Key, Count = t.Count() }).ToListAsync();
            }
        }

        public List<CategoryCountModel> GetTransfersCompletedOnTime(List<long> structureIds = null, string fromDate = "", string toDate = "")
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Transfer> query = ctx.Transfer.AsNoTracking().Where(t => t.ClosedDate.HasValue && t.DueDate.HasValue && t.DueDate >= t.ClosedDate.Value.Date);
                if (!structureIds.IsNullOrEmpty())
                {
                    query = query.Where(t => structureIds.Contains(t.Document.CreatedByStructureId));
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    query = query.Where(t => t.CreatedDate.Date >= from);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    query = query.Where(t => t.CreatedDate.Date <= to);
                }
                return query.GroupBy(t => t.Document.CategoryId).Select(t => new CategoryCountModel { CategoryId = t.Key, Count = t.Count() }).ToList();
            }
        }

        public async Task<List<CategoryCountModel>> GetTransfersCompletedOnTimeAsync(List<long> structureIds = null, string fromDate = "", string toDate = "")
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Transfer> query = ctx.Transfer.AsNoTracking().Where(t => t.ClosedDate.HasValue && t.DueDate.HasValue && t.DueDate >= t.ClosedDate.Value.Date);
                if (!structureIds.IsNullOrEmpty())
                {
                    query = query.Where(t => structureIds.Contains(t.Document.CreatedByStructureId));
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    query = query.Where(t => t.CreatedDate.Date >= from);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    query = query.Where(t => t.CreatedDate.Date <= to);
                }
                return await query.GroupBy(t => t.Document.CategoryId).Select(t => new CategoryCountModel { CategoryId = t.Key, Count = t.Count() }).ToListAsync();
            }
        }

        public List<CategoryCountModel> GetTransfersInProgressOverdueByUser(long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, string fromDate = "", string toDate = "")
        {
            using (var ctx = new CTSContext())
            {
                var today = DateTime.Now.Date;
                IQueryable<Transfer> query = ctx.Transfer.AsNoTracking().Where(t => !t.ClosedDate.HasValue && t.DueDate.HasValue && t.DueDate < today);

                query = query.Where(t => t.ToUserId == userId || (!t.ToUserId.HasValue && structureIds.Contains(t.ToStructureId.Value) && isStructureReceiver && t.Document.Privacy.Level <= privacyLevel));

                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    query = query.Where(t => t.CreatedDate.Date >= from);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    query = query.Where(t => t.CreatedDate.Date <= to);
                }
                return query.GroupBy(t => t.Document.CategoryId).Select(t => new CategoryCountModel { CategoryId = t.Key, Count = t.Count() }).ToList();
            }
        }

        public async Task<List<CategoryCountModel>> GetTransfersInProgressOverdueByUserAsync(long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, bool useAllStructures, string fromDate = "", string toDate = "")
        {
            using (var ctx = new CTSContext())
            {
                var today = DateTime.Now.Date;
                IQueryable<Transfer> query = ctx.Transfer.AsNoTracking().Where(t => !t.ClosedDate.HasValue && t.DueDate.HasValue && t.DueDate < today);

                if (useAllStructures)
                {
                    query = query.Where(t => (t.ToUserId == userId) || (!t.ToUserId.HasValue && structureIds.Contains(t.ToStructureId.Value) && isStructureReceiver && t.Document.Privacy.Level <= privacyLevel));
                }
                else
                {
                    query = query.Where(t => (t.ToUserId == userId && structureIds.Contains(t.ToStructureId.Value)) || (!t.ToUserId.HasValue && structureIds.Contains(t.ToStructureId.Value) && isStructureReceiver && t.Document.Privacy.Level <= privacyLevel));
                }

                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    query = query.Where(t => t.CreatedDate.Date >= from);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    query = query.Where(t => t.CreatedDate.Date <= to);
                }
                return await query.GroupBy(t => t.Document.CategoryId).Select(t => new CategoryCountModel { CategoryId = t.Key, Count = t.Count() }).ToListAsync();
            }
        }

        public List<CategoryCountModel> GetTransfersInProgressOnTimeByUser(long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, string fromDate = "", string toDate = "")
        {
            using (var ctx = new CTSContext())
            {
                var today = DateTime.Now.Date;
                IQueryable<Transfer> query = ctx.Transfer.AsNoTracking().Where(t => !t.ClosedDate.HasValue && t.DueDate.HasValue && t.DueDate >= today);

                query = query.Where(t => t.ToUserId == userId || (!t.ToUserId.HasValue && structureIds.Contains(t.ToStructureId.Value) && isStructureReceiver && t.Document.Privacy.Level <= privacyLevel));

                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    query = query.Where(t => t.CreatedDate.Date >= from);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    query = query.Where(t => t.CreatedDate.Date <= to);
                }
                return query.GroupBy(t => t.Document.CategoryId).Select(t => new CategoryCountModel { CategoryId = t.Key, Count = t.Count() }).ToList();
            }
        }

        public async Task<List<CategoryCountModel>> GetTransfersInProgressOnTimeByUserAsync(long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, bool useAllStructures, string fromDate = "", string toDate = "")
        {
            using (var ctx = new CTSContext())
            {
                var today = DateTime.Now.Date;
                IQueryable<Transfer> query = ctx.Transfer.AsNoTracking().Where(t => !t.ClosedDate.HasValue && t.DueDate.HasValue && t.DueDate >= today);

                if (useAllStructures)
                    query = query.Where(t => (t.ToUserId == userId) || (!t.ToUserId.HasValue && structureIds.Contains(t.ToStructureId.Value) && isStructureReceiver && t.Document.Privacy.Level <= privacyLevel));

                else
                    query = query.Where(t => (t.ToUserId == userId && structureIds.Contains(t.ToStructureId.Value)) || (!t.ToUserId.HasValue && structureIds.Contains(t.ToStructureId.Value) && isStructureReceiver && t.Document.Privacy.Level <= privacyLevel));

                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    query = query.Where(t => t.CreatedDate.Date >= from);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    query = query.Where(t => t.CreatedDate.Date <= to);
                }
                return await query.GroupBy(t => t.Document.CategoryId).Select(t => new CategoryCountModel { CategoryId = t.Key, Count = t.Count() }).ToListAsync();
            }
        }

        public List<CategoryCountModel> GetTransfersCompletedOverdueByUser(long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, string fromDate = "", string toDate = "")
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Transfer> query = ctx.Transfer.AsNoTracking().Where(t => t.ClosedDate.HasValue && t.DueDate.HasValue && t.DueDate < t.ClosedDate.Value.Date);

                query = query.Where(t => t.ToUserId == userId || (!t.ToUserId.HasValue && structureIds.Contains(t.ToStructureId.Value) && isStructureReceiver && t.Document.Privacy.Level <= privacyLevel));

                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    query = query.Where(t => t.CreatedDate.Date >= from);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    query = query.Where(t => t.CreatedDate.Date <= to);
                }
                return query.GroupBy(t => t.Document.CategoryId).Select(t => new CategoryCountModel { CategoryId = t.Key, Count = t.Count() }).ToList();
            }
        }

        public async Task<List<CategoryCountModel>> GetTransfersCompletedOverdueByUserAsync(long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, bool useAllStructures, string fromDate = "", string toDate = "")
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Transfer> query = ctx.Transfer.AsNoTracking().Where(t => t.ClosedDate.HasValue && t.DueDate.HasValue && t.DueDate < t.ClosedDate.Value.Date);

                if (useAllStructures)
                {
                    query = query.Where(t => (t.ToUserId == userId) || (!t.ToUserId.HasValue && structureIds.Contains(t.ToStructureId.Value) && isStructureReceiver && t.Document.Privacy.Level <= privacyLevel));

                }
                else
                {
                    query = query.Where(t => (t.ToUserId == userId && structureIds.Contains(t.ToStructureId.Value)) || (!t.ToUserId.HasValue && structureIds.Contains(t.ToStructureId.Value) && isStructureReceiver && t.Document.Privacy.Level <= privacyLevel));
                }

                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    query = query.Where(t => t.CreatedDate.Date >= from);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    query = query.Where(t => t.CreatedDate.Date <= to);
                }
                return await query.GroupBy(t => t.Document.CategoryId).Select(t => new CategoryCountModel { CategoryId = t.Key, Count = t.Count() }).ToListAsync();
            }
        }

        public List<CategoryCountModel> GetTransfersCompletedOnTimeByUser(long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, string fromDate = "", string toDate = "")
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Transfer> query = ctx.Transfer.AsNoTracking().Where(t => t.ClosedDate.HasValue && t.DueDate.HasValue && t.DueDate >= t.ClosedDate.Value.Date);

                query = query.Where(t => t.ToUserId == userId || (!t.ToUserId.HasValue && structureIds.Contains(t.ToStructureId.Value) && isStructureReceiver && t.Document.Privacy.Level <= privacyLevel));

                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    query = query.Where(t => t.CreatedDate.Date >= from);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    query = query.Where(t => t.CreatedDate.Date <= to);
                }
                return query.GroupBy(t => t.Document.CategoryId).Select(t => new CategoryCountModel { CategoryId = t.Key, Count = t.Count() }).ToList();
            }
        }

        public async Task<List<CategoryCountModel>> GetTransfersCompletedOnTimeByUserAsync(long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, bool useAllStructures, string fromDate = "", string toDate = "")
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Transfer> query = ctx.Transfer.AsNoTracking().Where(t => t.ClosedDate.HasValue && t.DueDate.HasValue && t.DueDate >= t.ClosedDate.Value.Date);

                if (useAllStructures)
                    query = query.Where(t => (t.ToUserId == userId) || (!t.ToUserId.HasValue && structureIds.Contains(t.ToStructureId.Value) && isStructureReceiver && t.Document.Privacy.Level <= privacyLevel));
                else
                    query = query.Where(t => (t.ToUserId == userId && structureIds.Contains(t.ToStructureId.Value)) || (!t.ToUserId.HasValue && structureIds.Contains(t.ToStructureId.Value) && isStructureReceiver && t.Document.Privacy.Level <= privacyLevel));



                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    query = query.Where(t => t.CreatedDate.Date >= from);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    query = query.Where(t => t.CreatedDate.Date <= to);
                }
                return await query.GroupBy(t => t.Document.CategoryId).Select(t => new CategoryCountModel { CategoryId = t.Key, Count = t.Count() }).ToListAsync();
            }
        }

        public (double AverageTransfers, double AverageCreatedByUser) ListAverageTransferCompletionTime(long userId, string fromDate, string toDate)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Transfer> queryAverageTasks = ctx.Transfer.AsNoTracking().Where(t => t.ClosedDate.HasValue);
                IQueryable<Transfer> queryAverageCreatedByUser = ctx.Transfer.AsNoTracking().Where(t => t.ClosedDate.HasValue && t.OwnerUserId == userId);
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    queryAverageTasks = queryAverageTasks.Where(t => t.CreatedDate.Date >= from);
                    queryAverageCreatedByUser = queryAverageCreatedByUser.Where(t => t.CreatedDate.Date >= from);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    queryAverageTasks = queryAverageTasks.Where(t => t.CreatedDate.Date <= to);
                    queryAverageCreatedByUser = queryAverageCreatedByUser.Where(t => t.CreatedDate.Date <= to);
                }

                var averageTasksArray = queryAverageTasks.Select(t => new { t.CreatedDate, t.ClosedDate }).ToArray();
                var averageTasks = averageTasksArray.Length > 0 ? averageTasksArray.Average(x => (x.ClosedDate - x.CreatedDate).Value.TotalSeconds) : 0;

                var averageCreatedByUserArray = queryAverageCreatedByUser.Select(t => new { t.CreatedDate, t.ClosedDate }).ToArray();
                var averageCreatedByUser = averageCreatedByUserArray.Length > 0 ? averageCreatedByUserArray.Average(x => (x.ClosedDate - x.CreatedDate).Value.TotalSeconds) : 0;

                return (averageTasks, averageCreatedByUser);
            }
        }

        public async Task<(double AverageTransfers, double AverageCreatedByUser)> ListAverageTransferCompletionTimeAsync(long userId, string fromDate, string toDate, long? structureId)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Transfer> queryAverageTasks;
                IQueryable<Transfer> queryAverageCreatedByUser;

                if (structureId != null)
                {
                    queryAverageTasks = ctx.Transfer.AsNoTracking().Where(t => t.ClosedDate.HasValue && t.ToStructureId == structureId);
                    queryAverageCreatedByUser = ctx.Transfer.AsNoTracking().Where(t => t.ClosedDate.HasValue && t.OwnerUserId == userId && t.FromStructureId == structureId);
                }
                else
                {
                    queryAverageTasks = ctx.Transfer.AsNoTracking().Where(t => t.ClosedDate.HasValue);
                    queryAverageCreatedByUser = ctx.Transfer.AsNoTracking().Where(t => t.ClosedDate.HasValue && t.OwnerUserId == userId);
                }

                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    queryAverageTasks = queryAverageTasks.Where(t => t.CreatedDate.Date >= from);
                    queryAverageCreatedByUser = queryAverageCreatedByUser.Where(t => t.CreatedDate.Date >= from);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    queryAverageTasks = queryAverageTasks.Where(t => t.CreatedDate.Date <= to);
                    queryAverageCreatedByUser = queryAverageCreatedByUser.Where(t => t.CreatedDate.Date <= to);
                }


                var averageTasksArray = await queryAverageTasks.Select(t => new { t.CreatedDate, t.ClosedDate }).ToArrayAsync();
                var averageTasks = averageTasksArray.Length > 0 ? averageTasksArray.Average(x => (x.ClosedDate - x.CreatedDate).Value.TotalSeconds) : 0;

                var averageCreatedByUserArray = await queryAverageCreatedByUser.Select(t => new { t.CreatedDate, t.ClosedDate }).ToArrayAsync();
                var averageCreatedByUser = averageCreatedByUserArray.Length > 0 ? averageCreatedByUserArray.Average(x => (x.ClosedDate - x.CreatedDate).Value.TotalSeconds) : 0;

                return (averageTasks, averageCreatedByUser);
            }
        }

        public DocumentsAverageDurationMonthWithTotalModel GetAverageDurationForTransferCompletionPerMonth(int year, List<short> categoryIds, long? structureId, long? userId)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Transfer> query = ctx.Transfer.AsNoTracking().Where(t => t.ClosedDate.HasValue && t.CreatedDate.Year == year);
                if (!categoryIds.IsNullOrEmpty())
                {
                    query = query.Where(t => categoryIds.Contains(t.Document.CategoryId));
                }
                if (structureId.HasValue)
                {
                    query = query.Where(t => t.ToStructureId == structureId);
                }
                if (userId.HasValue)
                {
                    query = query.Where(t => t.ToUserId == userId);
                }
                var list = query.Select(t => new { t.CreatedDate, t.ClosedDate, t.DueDate }).ToList();
                var months = new List<int>() { 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12 };
                var averageByMonth = from month in months
                                     join r in list
                                     on month equals r.CreatedDate.Month
                                     into g
                                     select new DocumentsAverageDurationMonthModel
                                     {
                                         Month = month.ToString(),
                                         Average = g.Count() > 0 ? g.Average(x => (x.ClosedDate.Value.Date - x.CreatedDate.Date).TotalDays) : 0
                                     };
                return new DocumentsAverageDurationMonthWithTotalModel
                {
                    TotalAverage = list.Count() > 0 ? list.ToList().Average(x => (x.ClosedDate.Value.Date - x.CreatedDate.Date).TotalDays) : 0,
                    DocumentAverageDurationList = averageByMonth.ToList()
                };
            }
        }
        public async Task<DocumentsAverageDurationMonthWithTotalModel> GetAverageDurationForTransferCompletionPerMonthAsync(int year, List<short> categoryIds, long? structureId, long? userId)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Transfer> query = ctx.Transfer.AsNoTracking().Where(t => t.ClosedDate.HasValue && t.CreatedDate.Year == year);
                if (!categoryIds.IsNullOrEmpty())
                {
                    query = query.Where(t => categoryIds.Contains(t.Document.CategoryId));
                }
                if (structureId.HasValue)
                {
                    query = query.Where(t => t.ToStructureId == structureId);
                }
                if (userId.HasValue)
                {
                    query = query.Where(t => t.ToUserId == userId);
                }
                var list = await query.Select(t => new { t.CreatedDate, t.ClosedDate, t.DueDate }).ToListAsync();
                var months = new List<int>() { 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12 };
                var averageByMonth = from month in months
                                     join r in list
                                     on month equals r.CreatedDate.Month
                                     into g
                                     select new DocumentsAverageDurationMonthModel
                                     {
                                         Month = month.ToString(),
                                         Average = g.Count() > 0 ? g.Average(x => (x.ClosedDate.Value.Date - x.CreatedDate.Date).TotalDays) : 0
                                     };
                return new DocumentsAverageDurationMonthWithTotalModel
                {
                    TotalAverage = list.Count() > 0 ? list.ToList().Average(x => (x.ClosedDate.Value.Date - x.CreatedDate.Date).TotalDays) : 0,
                    DocumentAverageDurationList = averageByMonth.ToList()
                };
            }
        }

        public List<DocumentAverageDurationForCorrespondenceModel> ListStructureAverageDurationForTransferCompletion(int year, List<short> categoryIds)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Transfer> query = ctx.Transfer.AsNoTracking().Where(t => t.ClosedDate.HasValue && t.CreatedDate.Year == year);
                if (!categoryIds.IsNullOrEmpty())
                {
                    query = query.Where(t => categoryIds.Contains(t.Document.CategoryId));
                }

                return query.Select(t => new
                {
                    StructureId = t.ToStructureId.Value,
                    ClosedDate = t.ClosedDate.Value.Date,
                    CreatedDate = t.CreatedDate.Date
                }).ToList().GroupBy(t => t.StructureId).Select(t => new DocumentAverageDurationForCorrespondenceModel
                {
                    StructureId = t.Key,
                    Average = t.Count() > 0 ? t.ToList().Average(x => Convert.ToDouble((x.ClosedDate.Date - x.CreatedDate.Date).TotalDays)) : 0
                }).ToList();
            }
        }

        public async Task<List<DocumentAverageDurationForCorrespondenceModel>> ListStructureAverageDurationForTransferCompletionAsync(int year, List<short> categoryIds)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Transfer> query = ctx.Transfer.AsNoTracking().Where(t => t.ClosedDate.HasValue && t.CreatedDate.Year == year);
                if (!categoryIds.IsNullOrEmpty())
                {
                    query = query.Where(t => categoryIds.Contains(t.Document.CategoryId));
                }

                return (await query.Select(t => new
                {
                    StructureId = t.ToStructureId.Value,
                    ClosedDate = t.ClosedDate.Value.Date,
                    CreatedDate = t.CreatedDate.Date
                }).ToListAsync()).GroupBy(t => t.StructureId).Select(t => new DocumentAverageDurationForCorrespondenceModel
                {
                    StructureId = t.Key,
                    Average = t.Count() > 0 ? t.ToList().Average(x => Convert.ToDouble((x.ClosedDate.Date - x.CreatedDate.Date).TotalDays)) : 0
                }).ToList();
            }
        }

        public List<DocumentAverageDurationForCorrespondenceModel> ListUserStructureAverageDurationForTransferCompletion(long structureId, int year, List<short> categoryIds)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Transfer> query = ctx.Transfer.AsNoTracking().Where(t => t.ToUserId.HasValue && t.ClosedDate.HasValue &&
                t.CreatedDate.Year == year && t.ToStructureId == structureId);

                if (!categoryIds.IsNullOrEmpty())
                {
                    query = query.Where(t => categoryIds.Contains(t.Document.CategoryId));
                }
                return query.Select(t => new
                {
                    UserId = t.ToUserId.Value,
                    ClosedDate = t.ClosedDate.Value.Date,
                    CreatedDate = t.CreatedDate.Date,
                }).ToList().GroupBy(t => t.UserId).Select(t => new DocumentAverageDurationForCorrespondenceModel
                {
                    UserId = t.Key,
                    Average = t.Count() > 0 ? t.ToList().Average(x => Convert.ToDouble((x.ClosedDate.Date - x.CreatedDate.Date).TotalDays)) : 0
                }).ToList();
            }
        }

        public async Task<List<DocumentAverageDurationForCorrespondenceModel>> ListUserStructureAverageDurationForTransferCompletionAsync(long structureId, int year, List<short> categoryIds)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Transfer> query = ctx.Transfer.AsNoTracking().Where(t => t.ToUserId.HasValue && t.ClosedDate.HasValue &&
                t.CreatedDate.Year == year && t.ToStructureId == structureId);

                if (!categoryIds.IsNullOrEmpty())
                {
                    query = query.Where(t => categoryIds.Contains(t.Document.CategoryId));
                }
                return (await query.Select(t => new
                {
                    UserId = t.ToUserId.Value,
                    ClosedDate = t.ClosedDate.Value.Date,
                    CreatedDate = t.CreatedDate.Date,
                }).ToListAsync()).GroupBy(t => t.UserId).Select(t => new DocumentAverageDurationForCorrespondenceModel
                {
                    UserId = t.Key,
                    Average = t.Count() > 0 ? t.ToList().Average(x => Convert.ToDouble((x.ClosedDate.Date - x.CreatedDate.Date).TotalDays)) : 0
                }).ToList();
            }
        }

        public DocumentsAverageDurationMonthWithTotalModel GetAverageDurationForTransferDelayPerMonth(int year, List<short> categoryIds, long? structureId, long? userId)
        {
            using (var ctx = new CTSContext())
            {
                var todayDate = DateTime.Now;
                IQueryable<Transfer> query = ctx.Transfer.AsNoTracking().Where(t => t.CreatedDate.Year == year && t.DueDate.HasValue &&
                ((t.ClosedDate.HasValue && t.DueDate.Value.Date < t.ClosedDate.Value.Date) || (!t.ClosedDate.HasValue && t.DueDate.Value.Date < todayDate.Date)));
                if (!categoryIds.IsNullOrEmpty())
                {
                    query = query.Where(t => categoryIds.Contains(t.Document.CategoryId));
                }
                if (structureId.HasValue)
                {
                    query = query.Where(t => t.ToStructureId == structureId);
                }
                if (userId.HasValue)
                {
                    query = query.Where(t => t.ToUserId == userId);
                }
                var list = query.Select(t => new { t.CreatedDate, t.ClosedDate, t.DueDate }).ToList();
                var months = new List<int>() { 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12 };
                var averageByMonth = from month in months
                                     join r in list
                                     on month equals r.CreatedDate.Month
                                     into g
                                     select new DocumentsAverageDurationMonthModel
                                     {
                                         Month = month.ToString(),
                                         Average = g.Count() > 0 ? g.Average(x => x.ClosedDate.HasValue ? (x.ClosedDate.Value.Date > x.DueDate.Value.Date ? (x.ClosedDate.Value.Date - x.DueDate.Value.Date).TotalDays : 0) :
                                         (x.DueDate.Value.Date < todayDate.Date ? (todayDate.Date - x.DueDate.Value.Date).TotalDays : 0)) : 0
                                     };
                return new DocumentsAverageDurationMonthWithTotalModel
                {
                    TotalAverage = list.Count() > 0 ? list.Average(x => x.ClosedDate.HasValue ? (x.ClosedDate.Value.Date > x.DueDate.Value.Date ? (x.ClosedDate.Value.Date - x.DueDate.Value.Date).TotalDays : 0) :
                    (x.DueDate.Value.Date < todayDate.Date ? (todayDate.Date - x.DueDate.Value.Date).TotalDays : 0)) : 0,
                    DocumentAverageDurationList = averageByMonth.ToList()
                };
            }
        }

        public async Task<DocumentsAverageDurationMonthWithTotalModel> GetAverageDurationForTransferDelayPerMonthAsync(int year, List<short> categoryIds, long? structureId, long? userId)
        {
            using (var ctx = new CTSContext())
            {
                var todayDate = DateTime.Now;
                IQueryable<Transfer> query = ctx.Transfer.AsNoTracking().Where(t => t.CreatedDate.Year == year && t.DueDate.HasValue &&
                ((t.ClosedDate.HasValue && t.DueDate.Value.Date < t.ClosedDate.Value.Date) || (!t.ClosedDate.HasValue && t.DueDate.Value.Date < todayDate.Date)));
                if (!categoryIds.IsNullOrEmpty())
                {
                    query = query.Where(t => categoryIds.Contains(t.Document.CategoryId));
                }
                if (structureId.HasValue)
                {
                    query = query.Where(t => t.ToStructureId == structureId);
                }
                if (userId.HasValue)
                {
                    query = query.Where(t => t.ToUserId == userId);
                }
                var list = await query.Select(t => new { t.CreatedDate, t.ClosedDate, t.DueDate }).ToListAsync();
                var months = new List<int>() { 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12 };
                var averageByMonth = from month in months
                                     join r in list
                                     on month equals r.CreatedDate.Month
                                     into g
                                     select new DocumentsAverageDurationMonthModel
                                     {
                                         Month = month.ToString(),
                                         Average = g.Count() > 0 ? g.Average(x => x.ClosedDate.HasValue ? (x.ClosedDate.Value.Date > x.DueDate.Value.Date ? (x.ClosedDate.Value.Date - x.DueDate.Value.Date).TotalDays : 0) :
                                         (x.DueDate.Value.Date < todayDate.Date ? (todayDate.Date - x.DueDate.Value.Date).TotalDays : 0)) : 0
                                     };
                return new DocumentsAverageDurationMonthWithTotalModel
                {
                    TotalAverage = list.Count() > 0 ? list.Average(x => x.ClosedDate.HasValue ? (x.ClosedDate.Value.Date > x.DueDate.Value.Date ? (x.ClosedDate.Value.Date - x.DueDate.Value.Date).TotalDays : 0) :
                    (x.DueDate.Value.Date < todayDate.Date ? (todayDate.Date - x.DueDate.Value.Date).TotalDays : 0)) : 0,
                    DocumentAverageDurationList = averageByMonth.ToList()
                };
            }
        }

        public List<DocumentAverageDurationForCorrespondenceModel> ListStructureAverageDurationForTransferDelay(int year, List<short> categoryIds)
        {
            using (var ctx = new CTSContext())
            {
                var todayDate = DateTime.Now.Date;
                IQueryable<Transfer> query = ctx.Transfer.AsNoTracking().Where(t => t.CreatedDate.Year == year && t.DueDate.HasValue &&
                ((t.ClosedDate.HasValue && t.DueDate.Value.Date < t.ClosedDate.Value.Date) || (!t.ClosedDate.HasValue && t.DueDate.Value.Date < todayDate)));
                if (!categoryIds.IsNullOrEmpty())
                {
                    query = query.Where(t => categoryIds.Contains(t.Document.CategoryId));
                }
                return query.Select(t => new
                {
                    StructureId = t.ToStructureId.Value,
                    ClosedDate = t.ClosedDate,
                    DueDate = t.DueDate
                }).ToList().GroupBy(t => t.StructureId).Select(t => new DocumentAverageDurationForCorrespondenceModel
                {
                    StructureId = t.Key,
                    Average = t.Count() > 0 ? t.ToList().Average(x => x.ClosedDate.HasValue ? (x.ClosedDate.Value.Date > x.DueDate.Value.Date ? Convert.ToDouble((x.ClosedDate.Value.Date - x.DueDate.Value.Date).TotalDays) : 0) :
                                        (x.DueDate.Value.Date < todayDate ? Convert.ToDouble((todayDate - x.DueDate.Value.Date).TotalDays) : 0)) : 0
                }).ToList();
            }
        }

        public async Task<List<DocumentAverageDurationForCorrespondenceModel>> ListStructureAverageDurationForTransferDelayAsync(int year, List<short> categoryIds)
        {
            using (var ctx = new CTSContext())
            {
                var todayDate = DateTime.Now.Date;
                IQueryable<Transfer> query = ctx.Transfer.AsNoTracking().Where(t => t.CreatedDate.Year == year && t.DueDate.HasValue &&
                ((t.ClosedDate.HasValue && t.DueDate.Value.Date < t.ClosedDate.Value.Date) || (!t.ClosedDate.HasValue && t.DueDate.Value.Date < todayDate)));
                if (!categoryIds.IsNullOrEmpty())
                {
                    query = query.Where(t => categoryIds.Contains(t.Document.CategoryId));
                }
                return (await query.Select(t => new
                {
                    StructureId = t.ToStructureId.Value,
                    ClosedDate = t.ClosedDate,
                    DueDate = t.DueDate
                }).ToListAsync()).GroupBy(t => t.StructureId).Select(t => new DocumentAverageDurationForCorrespondenceModel
                {
                    StructureId = t.Key,
                    Average = t.Count() > 0 ? t.ToList().Average(x => x.ClosedDate.HasValue ? (x.ClosedDate.Value.Date > x.DueDate.Value.Date ? Convert.ToDouble((x.ClosedDate.Value.Date - x.DueDate.Value.Date).TotalDays) : 0) :
                                        (x.DueDate.Value.Date < todayDate ? Convert.ToDouble((todayDate - x.DueDate.Value.Date).TotalDays) : 0)) : 0
                }).ToList();
            }
        }

        public List<DocumentAverageDurationForCorrespondenceModel> ListUserStructureAverageDurationForTransferDelay(long structureId, int year, List<short> categoryIds)
        {
            using (var ctx = new CTSContext())
            {
                var todayDate = DateTime.Now.Date;
                IQueryable<Transfer> query = ctx.Transfer.AsNoTracking().Where(t => t.ToUserId.HasValue && t.CreatedDate.Year == year && t.ToStructureId == structureId && t.DueDate.HasValue &&
                ((t.ClosedDate.HasValue && t.DueDate.Value.Date < t.ClosedDate.Value.Date) || (!t.ClosedDate.HasValue && t.DueDate.Value.Date < todayDate)));

                if (!categoryIds.IsNullOrEmpty())
                {
                    query = query.Where(t => categoryIds.Contains(t.Document.CategoryId));
                }
                return query.Select(t => new
                {
                    UserId = t.ToUserId.Value,
                    ClosedDate = t.ClosedDate,
                    DueDate = t.DueDate
                }).ToList().GroupBy(t => t.UserId).Select(t => new DocumentAverageDurationForCorrespondenceModel
                {
                    UserId = t.Key,
                    Average = t.Count() > 0 ? t.ToList().Average(x => x.ClosedDate.HasValue ? (x.ClosedDate.Value.Date > x.DueDate.Value.Date ? Convert.ToDouble((x.ClosedDate.Value.Date - x.DueDate.Value.Date).TotalDays) : 0) :
                                        (x.DueDate.Value.Date < todayDate ? Convert.ToDouble((todayDate - x.DueDate.Value.Date).TotalDays) : 0)) : 0
                }).ToList();
            }
        }

        public async Task<List<DocumentAverageDurationForCorrespondenceModel>> ListUserStructureAverageDurationForTransferDelayAsync(long structureId, int year, List<short> categoryIds)
        {
            using (var ctx = new CTSContext())
            {
                var todayDate = DateTime.Now.Date;
                IQueryable<Transfer> query = ctx.Transfer.AsNoTracking().Where(t => t.ToUserId.HasValue && t.CreatedDate.Year == year && t.ToStructureId == structureId && t.DueDate.HasValue &&
                ((t.ClosedDate.HasValue && t.DueDate.Value.Date < t.ClosedDate.Value.Date) || (!t.ClosedDate.HasValue && t.DueDate.Value.Date < todayDate)));

                if (!categoryIds.IsNullOrEmpty())
                {
                    query = query.Where(t => categoryIds.Contains(t.Document.CategoryId));
                }
                return (await query.Select(t => new
                {
                    UserId = t.ToUserId.Value,
                    ClosedDate = t.ClosedDate,
                    DueDate = t.DueDate
                }).ToListAsync()).GroupBy(t => t.UserId).Select(t => new DocumentAverageDurationForCorrespondenceModel
                {
                    UserId = t.Key,
                    Average = t.Count() > 0 ? t.ToList().Average(x => x.ClosedDate.HasValue ? (x.ClosedDate.Value.Date > x.DueDate.Value.Date ? Convert.ToDouble((x.ClosedDate.Value.Date - x.DueDate.Value.Date).TotalDays) : 0) :
                                        (x.DueDate.Value.Date < todayDate ? Convert.ToDouble((todayDate - x.DueDate.Value.Date).TotalDays) : 0)) : 0
                }).ToList();
            }
        }

        public List<Transfer> ListCompletedTransfers(int startIndex, int pageSize, Expression<Func<Transfer, bool>> filterExpression = null,
           Expression<Func<Transfer, bool>> stcFilterExpression = null, Expression<Func<Transfer, bool>> userFilterExpression = null, List<SortExpression<Transfer>> sortExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                var retValue = new List<Transfer>();
                IQueryable<Transfer> query = ctx.Transfer
                    .Include(t => t.FromUser)
                    .Include(t => t.ToUser)
                    .Include(t => t.FromStructure)
                    .Include(t => t.ToStructure)
                    .Include(t => t.Document);
                if (sortExpression != null)
                {
                    query = query.DynamicOrderBy(sortExpression);
                }
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                if (stcFilterExpression != null)
                {
                    query = query.Where(stcFilterExpression);
                }
                if (userFilterExpression != null)
                {
                    query = query.Where(userFilterExpression);
                }
                query = query.Where(x => x.ClosedDate.HasValue);
                return pageSize < 0 ? query.ToList() : query.Skip(startIndex).Take(pageSize).ToList();
            }
        }

        public async Task<List<Transfer>> ListCompletedTransfersAsync(int startIndex, int pageSize, Expression<Func<Transfer, bool>> filterExpression = null,
           Expression<Func<Transfer, bool>> stcFilterExpression = null, Expression<Func<Transfer, bool>> userFilterExpression = null, List<SortExpression<Transfer>> sortExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                var retValue = new List<Transfer>();
                IQueryable<Transfer> query = ctx.Transfer
                    .Include(t => t.FromUser)
                    .Include(t => t.ToUser)
                    .Include(t => t.FromStructure)
                    .Include(t => t.ToStructure)
                    .Include(t => t.Document);
                if (sortExpression != null)
                {
                    query = query.DynamicOrderBy(sortExpression);
                }
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                if (stcFilterExpression != null)
                {
                    query = query.Where(stcFilterExpression);
                }
                if (userFilterExpression != null)
                {
                    query = query.Where(userFilterExpression);
                }
                query = query.Where(x => x.ClosedDate.HasValue);
                return pageSize < 0 ? await query.ToListAsync() : await query.Skip(startIndex).Take(pageSize).ToListAsync();
            }
        }

        public Task<int> GetCompletedTransfersCount(Expression<Func<Transfer, bool>> filterExpression = null,
            Expression<Func<Transfer, bool>> stcFilterExpression = null, Expression<Func<Transfer, bool>> userFilterExpression = null)
        {
            OpenDbContext();
            IQueryable<Transfer> query = _ctx.Transfer.AsNoTracking();

            if (filterExpression != null)
            {
                query = query.Where(filterExpression);
            }
            if (stcFilterExpression != null)
            {
                query = query.Where(stcFilterExpression);
            }
            if (userFilterExpression != null)
            {
                query = query.Where(userFilterExpression);
            }
            query = query.Where(x => x.ClosedDate.HasValue);
            return query.CountAsync();
        }

        public List<Transfer> ListInProgressTransfers(int startIndex, int pageSize, Expression<Func<Transfer, bool>> filterExpression = null,
            Expression<Func<Transfer, bool>> stcFilterExpression = null, Expression<Func<Transfer, bool>> userFilterExpression = null, List<SortExpression<Transfer>> sortExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                var retValue = new List<Transfer>();
                IQueryable<Transfer> query = ctx.Transfer
                    .Include(t => t.FromUser)
                    .Include(t => t.ToUser)
                    .Include(t => t.FromStructure)
                    .Include(t => t.ToStructure)
                    .Include(t => t.Document);
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                if (stcFilterExpression != null)
                {
                    query = query.Where(stcFilterExpression);
                }
                if (userFilterExpression != null)
                {
                    query = query.Where(userFilterExpression);
                }
                if (sortExpression != null)
                {
                    query = query.DynamicOrderBy(sortExpression);
                }
                query = query.Where(x => !x.Document.ClosedDate.HasValue && !x.ClosedDate.HasValue);

                return pageSize < 0 ? query.ToList() : query.Skip(startIndex).Take(pageSize).ToList();
            }
        }

        public async Task<List<Transfer>> ListInProgressTransfersAsync(int startIndex, int pageSize, Expression<Func<Transfer, bool>> filterExpression = null,
            Expression<Func<Transfer, bool>> stcFilterExpression = null, Expression<Func<Transfer, bool>> userFilterExpression = null, List<SortExpression<Transfer>> sortExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                var retValue = new List<Transfer>();
                IQueryable<Transfer> query = ctx.Transfer
                    .Include(t => t.FromUser)
                    .Include(t => t.ToUser)
                    .Include(t => t.FromStructure)
                    .Include(t => t.ToStructure)
                    .Include(t => t.Document);
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                if (stcFilterExpression != null)
                {
                    query = query.Where(stcFilterExpression);
                }
                if (userFilterExpression != null)
                {
                    query = query.Where(userFilterExpression);
                }
                if (sortExpression != null)
                {
                    query = query.DynamicOrderBy(sortExpression);
                }
                query = query.Where(x => !x.Document.ClosedDate.HasValue && !x.ClosedDate.HasValue);

                return pageSize < 0 ? await query.ToListAsync() : await query.Skip(startIndex).Take(pageSize).ToListAsync();
            }
        }

        public Task<int> GetInProgressTransfersCount(Expression<Func<Transfer, bool>> filterExpression = null,
            Expression<Func<Transfer, bool>> stcFilterExpression = null, Expression<Func<Transfer, bool>> userFilterExpression = null)
        {
            OpenDbContext();
            IQueryable<Transfer> query = _ctx.Transfer.AsNoTracking();

            if (filterExpression != null)
            {
                query = query.Where(filterExpression);
            }
            if (stcFilterExpression != null)
            {
                query = query.Where(stcFilterExpression);
            }
            if (userFilterExpression != null)
            {
                query = query.Where(userFilterExpression);
            }
            query = query.Where(x => !x.Document.ClosedDate.HasValue && !x.ClosedDate.HasValue);
            return query.CountAsync();
        }
         public async Task<List<Transfer>> ListTransfersSentToStructureAsync(int startIndex, int pageSize,int structureId, Expression<Func<Transfer, bool>> filterExpression = null,
             Expression<Func<Transfer, bool>> userFilterExpression = null, List<SortExpression<Transfer>> sortExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                var retValue = new List<Transfer>();

                IQueryable <Transfer> query = ctx.Transfer
                    .Include(t => t.FromUser)
                    .Include(t => t.ToUser)
                    .Include(t => t.FromStructure)
                    .Include(t => t.ToStructure)
                    .Include(t => t.Document);
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
               
                if (userFilterExpression != null)
                {
                    query = query.Where(userFilterExpression);
                }
                if (sortExpression != null)
                {
                    query = query.DynamicOrderBy(sortExpression);
                }
                query = query.Where(x => !x.Document.ClosedDate.HasValue && 
                                          x.ToUserId==null &&
                                          x.ToStructureId==structureId &&
                                          x.ParentTransferId==null&&
                                          ctx.Transfer.Any(s=>s.ParentTransferId==x.Id));

                return pageSize < 0 ? await query.ToListAsync() : await query.Skip(startIndex).Take(pageSize).ToListAsync();
            }
        }

        public Task<int> GetTransfersSentToStructureCount(int structureId, Expression<Func<Transfer, bool>> filterExpression = null,
             Expression<Func<Transfer, bool>> userFilterExpression = null)
        {
            OpenDbContext();
            IQueryable<Transfer> query = _ctx.Transfer.AsNoTracking();

            if (filterExpression != null)
            {
                query = query.Where(filterExpression);
            }
            if (userFilterExpression != null)
            {
                query = query.Where(userFilterExpression);
            }
            query = query.Where(x => !x.Document.ClosedDate.HasValue &&
                                          x.ToUserId == null &&
                                          x.ToStructureId == structureId &&
                                          x.ParentTransferId == null &&
                                          _ctx.Transfer.Any(s => s.ParentTransferId == x.Id));
            return query.CountAsync();
        }

        public List<Transfer> ListTransferCorrespondenceDetail(int start, int pageSize, Expression<Func<Transfer, bool>> filterExpression = null, List<SortExpression<Transfer>> sortExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                List<Transfer> retValue = new List<Transfer>();
                IQueryable<Transfer> query = ctx.Transfer.Include(t => t.FromStructure)
                     .Include(t => t.FromUser)
                     .Include(t => t.ToStructure)
                     .Include(t => t.ToUser)
                     .AsNoTracking().Where(t => t.Document.StatusId != (short)DocumentStatus.Draft);

                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }

                if (sortExpression != null)
                {
                    query = query.DynamicOrderBy(sortExpression);
                }

                if (pageSize < 0)
                {
                    retValue = query.ToList();
                }
                else
                {
                    retValue = query.Skip(start).Take(pageSize).ToList();
                }

                return retValue;
            }
        }

        public async Task<List<Transfer>> ListTransferCorrespondenceDetailAsync(int start, int pageSize, Expression<Func<Transfer, bool>> filterExpression = null, List<SortExpression<Transfer>> sortExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                List<Transfer> retValue = new List<Transfer>();
                IQueryable<Transfer> query = ctx.Transfer.Include(t => t.FromStructure)
                     .Include(t => t.FromUser)
                     .Include(t => t.ToStructure)
                     .Include(t => t.ToUser)
                     .AsNoTracking().Where(t => t.Document.StatusId != (short)DocumentStatus.Draft);

                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }

                if (sortExpression != null)
                {
                    query = query.DynamicOrderBy(sortExpression);
                }

                if (pageSize < 0)
                {
                    retValue = await query.ToListAsync();
                }
                else
                {
                    retValue = await query.Skip(start).Take(pageSize).ToListAsync();
                }

                return retValue;
            }
        }

        public Task<int> GetDocumentTransfersCorrespondenceDetailCount(Expression<Func<Transfer, bool>> filterExpression = null)
        {
            OpenDbContext();
            IQueryable<Transfer> query = _ctx.Transfer.AsNoTracking().Where(t => t.Document.StatusId != (short)DocumentStatus.Draft);
            if (filterExpression != null)
            {
                query = query.Where(filterExpression);
            }
            return query.CountAsync();
        }

        public List<Transfer> ListInboxVip(int startIndex,long structureId, long userId, List<long> structureIds, List<short> categoryIds, bool isStructureReceiver,
            short privacyLevel, Expression<Func<Transfer, bool>> filterExpression = null,bool fromStructure=false)
        {
            using (var ctx = new CTSContext())
            {
                var retValue = new List<Transfer>();
                IQueryable<Transfer> query = ctx.Transfer
                    .Include(t => t.Document)
                    .Include(t => t.OwnerUser)
                    .Include(t => t.OwnerDelegatedUser)
                    .Include(t => t.FromUser)
                    .Include(t => t.FromStructure)
                    .Include(t => t.ToStructure)
                    .Include(t=>t.Purpose)
                    .Include(t => t.Document).ThenInclude(t => t.Privacy)
                    .Include(t => t.Document).ThenInclude(t => t.DocumentReceiverEntity).ThenInclude(t => t.Structure)
                    .Include(t => t.Document).ThenInclude(t => t.DocumentReceiverEntity).ThenInclude(t => t.EntityGroup)
                    .AsNoTracking();

                query = query.Where(x => !x.ClosedDate.HasValue 
                &&
               ( fromStructure ? (x.ToStructureId == structureId && x.Document.Privacy.Level <= privacyLevel) :
                (x.ToUserId == userId || (!x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value) && isStructureReceiver && x.Document.Privacy.Level <= privacyLevel))
                ));
                if (!categoryIds.IsNullOrEmpty())
                {
                    query = query.Where(t => categoryIds.Contains(t.Document.CategoryId));
                }
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                return query.OrderByDescending(t => t.CreatedDate).Skip(startIndex).Take(Constants.PAGING).ToList();
            }
        }

        public List<Transfer> ListCompletedVip(int startIndex, long userId, List<long> structureIds, List<short> categoryIds, bool isStructureReceiver,
            short privacyLevel, Expression<Func<Transfer, bool>> filterExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                var retValue = new List<Transfer>();
                IQueryable<Transfer> query = ctx.Transfer
                    .Include(t => t.Document).ThenInclude(t => t.Privacy)
                    .Include(t => t.FromUser)
                    .Include(t => t.FromStructure)
                    .Include(t => t.ToStructure)
                    .Include(t => t.Purpose).AsNoTracking();

                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }

                query = query.Where(x => x.ClosedDate.HasValue && (x.ToUserId == userId || (!x.ToUserId.HasValue && structureIds.Contains(x.ToStructureId.Value) && isStructureReceiver && x.Document.Privacy.Level <= privacyLevel)));

                if (!categoryIds.IsNullOrEmpty())
                {
                    query = query.Where(t => categoryIds.Contains(t.Document.CategoryId));
                }
                return query.OrderByDescending(t => t.ClosedDate).Skip(startIndex).Take(Constants.PAGING).ToList();
            }
        }

        public List<Transfer> ListSentVip(int startIndex, long userId, long structureId, Expression<Func<Transfer, bool>> filterExpression = null, bool fromStructure = false)
        {
            using (var ctx = new CTSContext())
            {
                var retValue = new List<Transfer>();
                IQueryable<Transfer> query = ctx.Transfer
                    .Include(t => t.Document).ThenInclude(t => t.Privacy)
                    .Include(t => t.ToUser)
                    .Include(t => t.FromStructure)
                    .Include(t => t.ToStructure)
                    .Include(t => t.Purpose)
                    .AsNoTracking();

                query = query.Where(x => ((!fromStructure && x.FromUserId == userId) || (fromStructure && x.FromStructureId == structureId)) && !x.Document.ClosedDate.HasValue && (x.FromRecall.Value != true) && !x.ExportedDocumentId.HasValue);

                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                return query.OrderByDescending(t => t.CreatedDate).Skip(startIndex).Take(Constants.PAGING).ToList();
            }
        }

        public int GetSentOrReceiveCountByStructure(long targetStrucureId, bool receiver, List<long> structureIds, long userId, bool isAdmin, bool isStructureReceiver, short privacyLevel, List<long> searchAssignedStructures, bool searchAssignedStructureSearchUsersDocuments, List<long> selectedUsers, List<long> selectedStructures, string fromDate, string toDate)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Transfer> query = ctx.Transfer.AsNoTracking().Where(t => t.Document.StatusId != (short)DocumentStatus.Draft);

                if (receiver)
                {
                    query = query.Where(t => t.ToStructureId == targetStrucureId);
                }
                else
                {
                    query = query.Where(t => t.FromStructureId == targetStrucureId);
                }

                if (selectedStructures.Count > 0)
                {
                    query = query.Where(t => selectedStructures.Contains(t.Document.CreatedByStructureId));
                }
                if (selectedUsers.Count > 0)
                {
                    query = query.Where(t => selectedUsers.Contains(t.Document.CreatedByUserId));
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    query = query.Where(t => t.Document.CreatedDate.Date >= from);
                }
                else
                {
                    DateTime from = new DateTime(DateTime.Now.Year, 1, 1).Date;
                    query = query.Where(t => t.Document.CreatedDate.Date >= from);
                }

                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    query = query.Where(t => t.Document.CreatedDate.Date <= to);
                }
                else
                {
                    DateTime to = DateTime.Now.Date.AddDays(1);
                    query = query.Where(t => t.Document.CreatedDate.Date <= to);
                }

                if (!isAdmin)
                {
                    query = query.Where(t => t.Document.CreatedByUserId == userId
                     || t.FromUserId.Value == userId
                     || t.ToUserId.Value == userId
                     || ((!searchAssignedStructures.IsNullOrEmpty() ? (searchAssignedStructureSearchUsersDocuments ? ((!t.ToUserId.HasValue && structureIds.Contains(t.ToStructureId.Value)) || searchAssignedStructures.Contains(t.ToStructureId.Value) || searchAssignedStructures.Contains(t.FromStructureId.Value))
                     : (!t.ToUserId.HasValue && (structureIds.Contains(t.ToStructureId.Value) || searchAssignedStructures.Contains(t.ToStructureId.Value) || searchAssignedStructures.Contains(t.FromStructureId.Value))))
                     : (!t.ToUserId.HasValue && structureIds.Contains(t.ToStructureId.Value)) && isStructureReceiver)
                     && (t.Document.Privacy != null ? t.Document.Privacy.Level <= privacyLevel : true)));
                }
                return query.Count();
            }
        }

        public async Task<int> GetSentOrReceiveCountByStructureAsync(long targetStrucureId, bool receiver, List<long> structureIds, long userId, bool isAdmin, bool isStructureReceiver, short privacyLevel, List<long> searchAssignedStructures, bool searchAssignedStructureSearchUsersDocuments, List<long> selectedUsers, List<long> selectedStructures, string fromDate, string toDate)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Transfer> query = ctx.Transfer.AsNoTracking().Where(t => t.Document.StatusId != (short)DocumentStatus.Draft);

                if (receiver)
                {
                    query = query.Where(t => t.ToStructureId == targetStrucureId);
                }
                else
                {
                    query = query.Where(t => t.FromStructureId == targetStrucureId);
                }

                if (selectedStructures.Count > 0)
                {
                    query = query.Where(t => selectedStructures.Contains(t.Document.CreatedByStructureId));
                }
                if (selectedUsers.Count > 0)
                {
                    query = query.Where(t => selectedUsers.Contains(t.Document.CreatedByUserId));
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    query = query.Where(t => t.Document.CreatedDate.Date >= from);
                }
                else
                {
                    DateTime from = new DateTime(DateTime.Now.Year, 1, 1).Date;
                    query = query.Where(t => t.Document.CreatedDate.Date >= from);
                }

                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    query = query.Where(t => t.Document.CreatedDate.Date <= to);
                }
                else
                {
                    DateTime to = DateTime.Now.Date.AddDays(1);
                    query = query.Where(t => t.Document.CreatedDate.Date <= to);
                }

                if (!isAdmin)
                {
                    query = query.Where(t => t.Document.CreatedByUserId == userId
                     || t.FromUserId.Value == userId
                     || t.ToUserId.Value == userId
                     || ((!searchAssignedStructures.IsNullOrEmpty() ? (searchAssignedStructureSearchUsersDocuments ? ((!t.ToUserId.HasValue && structureIds.Contains(t.ToStructureId.Value)) || searchAssignedStructures.Contains(t.ToStructureId.Value) || searchAssignedStructures.Contains(t.FromStructureId.Value))
                     : (!t.ToUserId.HasValue && (structureIds.Contains(t.ToStructureId.Value) || searchAssignedStructures.Contains(t.ToStructureId.Value) || searchAssignedStructures.Contains(t.FromStructureId.Value))))
                     : (!t.ToUserId.HasValue && structureIds.Contains(t.ToStructureId.Value)) && isStructureReceiver)
                     && (t.Document.Privacy != null ? t.Document.Privacy.Level <= privacyLevel : true)));
                }
                return await query.CountAsync();
            }
        }

        public int GetSentOrReceiveCountByUser(long targetStrucureId, long targetUserId, bool receiver, List<long> structureIds, long userId, bool isAdmin, bool isStructureReceiver, short privacyLevel, List<long> searchAssignedStructures, bool searchAssignedStructureSearchUsersDocuments, List<long> selectedUsers, List<long> selectedStructures, string fromDate, string toDate)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Transfer> query = ctx.Transfer.AsNoTracking().Where(t => t.Document.StatusId != (short)DocumentStatus.Draft);

                if (receiver)
                {
                    query = query.Where(t => t.ToStructureId == targetStrucureId && t.ToUserId == targetUserId);
                }
                else
                {
                    query = query.Where(t => t.FromStructureId == targetStrucureId && t.FromUserId == targetUserId);
                }

                if (selectedStructures.Count > 0)
                {
                    query = query.Where(t => selectedStructures.Contains(t.Document.CreatedByStructureId));
                }

                if (selectedUsers.Count > 0)
                {
                    query = query.Where(t => selectedUsers.Contains(t.Document.CreatedByUserId));
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    query = query.Where(t => t.Document.CreatedDate.Date >= from);
                }
                else
                {
                    DateTime from = new DateTime(DateTime.Now.Year, 1, 1).Date;
                    query = query.Where(t => t.Document.CreatedDate.Date >= from);
                }

                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    query = query.Where(t => t.Document.CreatedDate.Date <= to);
                }
                else
                {
                    DateTime to = DateTime.Now.Date.AddDays(1);
                    query = query.Where(t => t.Document.CreatedDate.Date <= to);
                }

                if (!isAdmin)
                {
                    query = query.Where(t => t.Document.CreatedByUserId == userId
                     || t.FromUserId.Value == userId
                     || t.ToUserId.Value == userId
                     || ((!searchAssignedStructures.IsNullOrEmpty() ? (searchAssignedStructureSearchUsersDocuments ? ((!t.ToUserId.HasValue && structureIds.Contains(t.ToStructureId.Value)) || searchAssignedStructures.Contains(t.ToStructureId.Value) || searchAssignedStructures.Contains(t.FromStructureId.Value))
                     : (!t.ToUserId.HasValue && (structureIds.Contains(t.ToStructureId.Value) || searchAssignedStructures.Contains(t.ToStructureId.Value) || searchAssignedStructures.Contains(t.FromStructureId.Value))))
                     : (!t.ToUserId.HasValue && structureIds.Contains(t.ToStructureId.Value)) && isStructureReceiver)
                     && (t.Document.Privacy != null ? t.Document.Privacy.Level <= privacyLevel : true)));
                }
                return query.Count();
            }
        }

        public async Task<int> GetSentOrReceiveCountByUserAsync(long targetStrucureId, long targetUserId, bool receiver, List<long> structureIds, long userId, bool isAdmin, bool isStructureReceiver, short privacyLevel, List<long> searchAssignedStructures, bool searchAssignedStructureSearchUsersDocuments, List<long> selectedUsers, List<long> selectedStructures, string fromDate, string toDate)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Transfer> query = ctx.Transfer.AsNoTracking().Where(t => t.Document.StatusId != (short)DocumentStatus.Draft);

                if (receiver)
                {
                    query = query.Where(t => t.ToStructureId == targetStrucureId && t.ToUserId == targetUserId);
                }
                else
                {
                    query = query.Where(t => t.FromStructureId == targetStrucureId && t.FromUserId == targetUserId);
                }

                if (selectedStructures.Count > 0)
                {
                    query = query.Where(t => selectedStructures.Contains(t.Document.CreatedByStructureId));
                }

                if (selectedUsers.Count > 0)
                {
                    query = query.Where(t => selectedUsers.Contains(t.Document.CreatedByUserId));
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    query = query.Where(t => t.Document.CreatedDate.Date >= from);
                }
                else
                {
                    DateTime from = new DateTime(DateTime.Now.Year, 1, 1).Date;
                    query = query.Where(t => t.Document.CreatedDate.Date >= from);
                }

                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    query = query.Where(t => t.Document.CreatedDate.Date <= to);
                }
                else
                {
                    DateTime to = DateTime.Now.Date.AddDays(1);
                    query = query.Where(t => t.Document.CreatedDate.Date <= to);
                }

                if (!isAdmin)
                {
                    query = query.Where(t => t.Document.CreatedByUserId == userId
                     || t.FromUserId.Value == userId
                     || t.ToUserId.Value == userId
                     || ((!searchAssignedStructures.IsNullOrEmpty() ? (searchAssignedStructureSearchUsersDocuments ? ((!t.ToUserId.HasValue && structureIds.Contains(t.ToStructureId.Value)) || searchAssignedStructures.Contains(t.ToStructureId.Value) || searchAssignedStructures.Contains(t.FromStructureId.Value))
                     : (!t.ToUserId.HasValue && (structureIds.Contains(t.ToStructureId.Value) || searchAssignedStructures.Contains(t.ToStructureId.Value) || searchAssignedStructures.Contains(t.FromStructureId.Value))))
                     : (!t.ToUserId.HasValue && structureIds.Contains(t.ToStructureId.Value)) && isStructureReceiver)
                     && (t.Document.Privacy != null ? t.Document.Privacy.Level <= privacyLevel : true)));
                }
                return await query.CountAsync();
            }
        }

        public List<long> ListDeletedIds(List<long> ids)
        {
            List<long> dbIds = new List<long>();
            using (var ctx = new CTSContext())
            {
                dbIds = ctx.Transfer.AsNoTracking().Where(d => ids.Contains(d.Id)).Select(s => s.Id).ToList();
                return ids.Except(dbIds).ToList();
            }
        }

        public async Task<long?> GetOwnerId(long id)
        {
            using (var ctx = new CTSContext())
            {
                return await ctx.Transfer.AsNoTracking().Where(c => c.Id == id).Select(x => x.OwnerUserId).FirstOrDefaultAsync();
            }
        }

        public async Task<List<Transfer>> ListManageCorrespondenceSearchAsync(int startIndex, int pageSize, long userId, bool searchAssignedStructureSearchUsersDocuments, Expression<Func<Transfer, bool>> filterExpression = null,
          List<SortExpression<Transfer>> sortExpression = null, string keyword = null, int? DocumentReceiverId = null)
        {
            using (var ctx = new CTSContext())
            {
                
                    IQueryable<Transfer> query = ctx.Transfer.Include(t => t.Document).ThenInclude(t => t.SendingEntity)
                    
                        .Include(t => t.Document).ThenInclude(t => t.CreatedByUser)
                        .Include(t => t.ToUser)
                        .Include(t => t.ToStructure)
                        .Include(t=>t.Document).ThenInclude(t=>t.DocumentCarbonCopy).ThenInclude(t=>t.Structure)
                        .Include(t => t.Document).ThenInclude(t => t.DocumentReceiverEntity).ThenInclude(t => t.Structure)
                        .Include(t => t.Document).ThenInclude(t => t.DocumentReceiverEntity).ThenInclude(t => t.EntityGroup);

                    if (sortExpression != null)
                    {
                        query = query.DynamicOrderBy(sortExpression);
                    }
                    if (filterExpression != null)
                    {
                        query = query.Where(filterExpression);
                    }
                /*
                 * if(Documents!=null && Documents.Count > 0)
                 * {
                 *      query = query.where(t=> Documents.Contains(t.documentId));
                 * }
                */
                if (DocumentReceiverId.HasValue)
                {
                    long? documentReceiverId = (long)DocumentReceiverId;
                    query = query.Where(t => t.Document.DocumentReceiverEntity.Select(s => s.StructureId).Contains(documentReceiverId.Value));
                }

                if (!string.IsNullOrEmpty(keyword))
                    {
                        query = query.Where(t => (t.Document.DocumentForm != null && !string.IsNullOrEmpty(t.Document.DocumentForm.Keyword)) ? (Constants.SPLITTER + t.Document.DocumentForm.Keyword.ToLower() + Constants.SPLITTER).Contains(Constants.SPLITTER + keyword.ToLower() + Constants.SPLITTER) : false);
                    }
                    //query = query.Where(t => t.StatusId == (short)DocumentStatus.InProgress);
                    query = query.Where(t => t.StatusId != (short)DocumentStatus.Draft);


                    return await query.Skip(startIndex).Take(pageSize).ToListAsync();
               
            }
        }
        public async Task<List<Transfer>> ListManageStructureUsersCorrespondencesSearchAsync(int startIndex, int pageSize, long userId, bool searchAssignedStructureSearchUsersDocuments, Expression<Func<Transfer, bool>> filterExpression = null,
          List<SortExpression<Transfer>> sortExpression = null)
        {
            using (var ctx = new CTSContext())
            {

                IQueryable<Transfer> query = ctx.Transfer.Include(t => t.Document).ThenInclude(t => t.SendingEntity)

                    .Include(t => t.Document).ThenInclude(t => t.CreatedByUser)
                    .Include(t => t.ToUser)
                    .Include(t => t.ToStructure)
                    .Include(t => t.Document).ThenInclude(t => t.DocumentCarbonCopy).ThenInclude(t => t.Structure)
                    .Include(t => t.Document).ThenInclude(t => t.DocumentReceiverEntity).ThenInclude(t => t.Structure)
                    .Include(t => t.Document).ThenInclude(t => t.DocumentReceiverEntity).ThenInclude(t => t.EntityGroup);

                if (sortExpression != null)
                {
                    query = query.DynamicOrderBy(sortExpression);
                }
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                query = query.Where(t => t.StatusId != (short)DocumentStatus.Draft);


                return await query.Skip(startIndex).Take(pageSize).ToListAsync();

            }
        }


        public async Task<int> GetManageCorrespondenceSearchCount(long userId, bool searchAssignedStructureSearchUsersDocuments, Expression<Func<Transfer, bool>> filterExpression = null, string keyword = null, int? DocumentReceiverId = null)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Transfer> query = ctx.Transfer.Include(t => t.Document).ThenInclude(d => d.DocumentReceiverEntity)
                    .AsNoTracking().Include(t=>t.Document).ThenInclude(d=>d.DocumentCarbonCopy).AsNoTracking();
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }

                if (!string.IsNullOrEmpty(keyword))
                {
                    query = query.Where(t => (t.Document.DocumentForm != null && !string.IsNullOrEmpty(t.Document.DocumentForm.Keyword)) ? (Constants.SPLITTER + t.Document.DocumentForm.Keyword.ToLower() + Constants.SPLITTER).Contains(Constants.SPLITTER + keyword.ToLower() + Constants.SPLITTER) : false);
                }

                if (DocumentReceiverId.HasValue)
                {
                    long? documentReceiverId = (long)DocumentReceiverId;
                    query = query.Where(t => t.Document.DocumentReceiverEntity.Select(s => s.StructureId).Contains(documentReceiverId.Value));
                }
              

                query = query.Where(t => t.StatusId != (short)DocumentStatus.Draft);
                return await query.CountAsync();
            }
        }
        public async Task<int> GetManageStructureUsersCorrespondencesSearchCount(long userId, bool searchAssignedStructureSearchUsersDocuments, Expression<Func<Transfer, bool>> filterExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<Transfer> query = ctx.Transfer.Include(t => t.Document).ThenInclude(d => d.DocumentReceiverEntity)
                    .AsNoTracking().Include(t => t.Document).ThenInclude(d => d.DocumentCarbonCopy).AsNoTracking();
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                query = query.Where(t => t.StatusId != (short)DocumentStatus.Draft);
                return await query.CountAsync();
            }
        }

        public async Task<List<Transfer>> GetPreviewTransfersForMoving(int startIndex, int pageSize, int? fromUser, int? toUser , int? structure, List<SortExpression<Transfer>> sortExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                var retValue = new List<Transfer>();
                IQueryable<Transfer> query = ctx.Transfer
                    .Include(t => t.OwnerUser)
                    .Include(t => t.OwnerDelegatedUser)
                    .Include(t => t.FromUser)
                    .Include(t => t.ToUser)
                    .Include(t => t.FromStructure)
                    .Include(t => t.ToStructure)
                    .Include(t => t.Document).ThenInclude(t => t.SendingEntity)
                    .Include(t => t.Document).ThenInclude(t => t.Privacy)
                    .Include(t => t.Document).ThenInclude(t => t.DocumentReceiverEntity).ThenInclude(t => t.Structure)
                    .Include(t => t.Document).ThenInclude(t => t.DocumentReceiverEntity).ThenInclude(t => t.EntityGroup)
                    .Include(t => t.Document).ThenInclude(t => t.CreatedByUser)
                    .Include(t => t.Document).ThenInclude(t => t.Attachment).AsNoTracking();

                if (sortExpression != null)
                {
                    query = query.DynamicOrderBy(sortExpression);
                }

                query = query.Where(x => (x.ToStructureId==structure && x.ToUserId == fromUser)|| (x.FromStructureId == structure && x.FromUserId == fromUser));

                return query.Skip(startIndex).Take(pageSize).ToList();
            }
        }

        public Task<int> GetPreviewTransfersCount(int? fromUser  ,int? toUser  , int? structure )
        {
            OpenDbContext();
            IQueryable<Transfer> query = _ctx.Transfer.Include(t => t.Document).ThenInclude(t => t.Privacy).AsNoTracking();

            query = query.Where(x => (x.ToStructureId == structure && x.ToUserId == fromUser) || (x.FromStructureId == structure && x.FromUserId == fromUser));
            return query.CountAsync();
        }

        public async Task<List<long>> GetTransfersIdsForMovingFromUser( int fromUser, int structure)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<long> query = ctx.Transfer.Where(x => x.FromStructureId == structure && x.FromUserId == fromUser).Select(s=>s.Id);
                return query.ToList();
            }
        }
        public async Task<List<long>> GetTransfersIdsForMovingToUser(int fromUser, int structure)
        {
            using (var ctx = new CTSContext())
            {
                IQueryable<long> query = ctx.Transfer.Where(x => x.ToStructureId == structure && x.ToUserId == fromUser).Select(s => s.Id);
                return query.ToList();
            }
        }
        public void UpdateRange(List<Transfer> transfers)
        {
            using (var ctx = new CTSContext())
            {
                ctx.UpdateRange(transfers);
                ctx.SaveChanges();
            }
        }
        public async Task UpdateFromUserIdForMoving(List<long> transfersIds, int fromUser, int newFromUser) 
        {
            using (var context = new CTSContext())
            {
                using (var transaction = context.Database.BeginTransaction())
                {

                    try
                    {
                        string idsAsString = string.Join(",", transfersIds);
                        string formattedDateTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");


                        string transfersHistoryQuery = $"INSERT INTO TransfersMigrateHistory (TransferId, OldFromUserId, NewFromUserId ,CreatedDate) SELECT Id, FromUserId, " + newFromUser.ToString() + ",'" + formattedDateTime + "' FROM Transfer WHERE Id IN(" + idsAsString + ")";
                        context.Database.ExecuteSqlRaw(transfersHistoryQuery);

                        string transfersQuery = "UPDATE Transfer SET FromUserId =" + newFromUser.ToString() + " , CreatedByUserId=" + newFromUser.ToString() +"  WHERE Id in (" + idsAsString + ")";
                        context.Database.ExecuteSqlRaw(transfersQuery);

                        string transfersOwnerUserIdQuery = "UPDATE Transfer SET OwnerUserId =" + newFromUser.ToString() + "  WHERE Id in (" + idsAsString + ") and OwnerUserId=" + fromUser.ToString() + "";
                        context.Database.ExecuteSqlRaw(transfersOwnerUserIdQuery);

                        transaction.Commit();

                    }
                    catch (Exception e)
                    {
                        transaction.Rollback();
                        throw e;
                    }

                }
            }


        }
        public async Task UpdateToUserIdForMoving(List<long> transfersIds,int fromUser, int newToUser)
        {
            using (var context = new CTSContext())
            {
                using (var transaction = context.Database.BeginTransaction())
                {

                    try
                    {
                        string idsAsString = string.Join(",", transfersIds);
                        string formattedDateTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");


                        string transfersHistoryQuery = $"INSERT INTO TransfersMigrateHistory (TransferId, OldToUserId, NewToUserId,CreatedDate) SELECT Id, ToUserId, " + newToUser.ToString() + ",'" + formattedDateTime + "' FROM Transfer WHERE Id IN(" + idsAsString + ")";
                        context.Database.ExecuteSqlRaw(transfersHistoryQuery);

                        string transfersQuery = "UPDATE Transfer SET ToUserId =" + newToUser.ToString() + "  WHERE Id in (" + idsAsString + ")";
                        context.Database.ExecuteSqlRaw(transfersQuery);

                        string transfersOwnerUserIdQuery = "UPDATE Transfer SET OwnerUserId =" + newToUser.ToString() + "  WHERE Id in (" + idsAsString + ") and OwnerUserId=" + fromUser.ToString() + "";
                        context.Database.ExecuteSqlRaw(transfersOwnerUserIdQuery);
                        transaction.Commit();

                    }
                    catch (Exception e)
                    {
                        transaction.Rollback();
                        throw e;
                    }
                   
                }
            }

        }
        public async Task UpdateNotesForMoving(List<long> transfersIds, long fromUser, long toUser)
        {
            using (var context = new CTSContext())
            {
                using (var transaction = context.Database.BeginTransaction())
                {
                    try
                    {
                        string idsAsString = string.Join(",", transfersIds);


                        string transfersQuery = "UPDATE Note SET CreatedByUserId =" + toUser.ToString() + "  WHERE TransferId IN(" + idsAsString + ") and CreatedByUserId =" + fromUser + " ";
                        context.Database.ExecuteSqlRaw(transfersQuery);

                        transaction.Commit();
                    }
                    catch (Exception e)
                    {
                        transaction.Rollback();
                        throw e;
                    }
                }
            }

        }

        public async Task UpdateAttachmentsForMoving(List<long> transfersIds, long fromUser, long toUser)
        {
            using (var context = new CTSContext())
            {
                using (var transaction = context.Database.BeginTransaction())
                {
                    try
                    {
                        string idsAsString = string.Join(",", transfersIds);

                        string transfersQuery = "UPDATE Attachment SET CreatedByUserId =" + toUser.ToString() + "  WHERE TransferId IN(" + idsAsString + ") and CreatedByUserId =" + fromUser + " ";
                        context.Database.ExecuteSqlRaw(transfersQuery);

                        transaction.Commit();
                    }
                    catch (Exception e)
                    {
                        transaction.Rollback();
                        throw e;
                    }
                }
            }

        }

        public async Task UpdateNonArchivedAttachmentsForMoving(List<long> transfersIds, long fromUser, long toUser)
        {
            using (var context = new CTSContext())
            {
                using (var transaction = context.Database.BeginTransaction())
                {
                    try
                    {
                        string idsAsString = string.Join(",", transfersIds);

                        string transfersQuery = "UPDATE NonArchivedAttachments SET CreatedByUserId =" + toUser.ToString() + "  WHERE TransferId IN(" + idsAsString + ") and CreatedByUserId =" + fromUser + " ";
                        context.Database.ExecuteSqlRaw(transfersQuery);

                        transaction.Commit();
                    }
                    catch (Exception e)
                    {
                        transaction.Rollback();
                        throw e;
                    }
                }
            }

        }
        public async Task UpdateLinkedDocumentForMoving(List<long> transfersIds, long fromUser, long toUser)
        {
            using (var context = new CTSContext())
            {
                using (var transaction = context.Database.BeginTransaction())
                {
                    try
                    {
                        string idsAsString = string.Join(",", transfersIds);

                        string transfersQuery = "UPDATE LinkedDocument SET CreatedByUserId =" + toUser.ToString() + "  WHERE TransferId IN(" + idsAsString + ") and CreatedByUserId =" + fromUser + " ";
                        context.Database.ExecuteSqlRaw(transfersQuery);

                        transaction.Commit();
                    }
                    catch (Exception e)
                    {
                        transaction.Rollback();
                        throw e;
                    }
                }
            }

        }
        public async Task<List<Attachment>> GetAttachmentsToDiscardCheckOutForMoving(List<long> transfersIds, long fromUser)
        {
            using (var context = new CTSContext())
            {
                var retValue = new List<Attachment>();

                var transferDocumentQuery = context.Transfer
                                                   .Where(t => transfersIds.Contains(t.Id))
                                                   .Select(t => new
                                                   {
                                                       documentId = t.DocumentId,
                                                       transferId = t.Id
                                                   });

                var documentsTransfersQuery = context.Attachment
                    .SelectMany(a => transferDocumentQuery,
                        (a, ts) => new { a, ts })
                    .Where(x => x.a.IsLocked==true &&
                                x.a.LockedByUserId==fromUser && (
                                                       (x.a.TransferId.HasValue && x.a.TransferId == x.ts.transferId) 
                                                       || (!x.a.TransferId.HasValue && x.a.DocumentId == x.ts.documentId))
                    ).Select(s => s.a);
                retValue = documentsTransfersQuery.ToList();
                return retValue;
            }

        }

        //public async Task<List<MonthlyDelayedTransfer>> GetDelayedTransfersPerMonthStatisticsAsync(int months, int year, long userId)
        //{
        //    using (var ctx = new CTSContext())
        //    {
        //        var todayDate = DateTime.Now;
        //        IQueryable<Transfer> query = ctx.Transfer.AsNoTracking().Where(t => t.DueDate.HasValue &&
        //        ((t.ClosedDate.HasValue && t.DueDate.Value.Date < t.ClosedDate.Value.Date) || (!t.ClosedDate.HasValue && t.DueDate.Value.Date < todayDate.Date)));

        //        if (userId > 0)
        //        {
        //            query = query.Where(t => t.ToUserId == userId);
        //        }
        //        var list = await query.Select(t => new { t.CreatedDate, t.ClosedDate, t.DueDate }).ToListAsync();
        //        List<int> monthsList = new List<int>();
        //        for (int month = months; (month <= months && month > 0); month--)
        //        {
        //            monthsList.Add(month);
        //        }
        //        var countPerMonth = from month in monthsList
        //                             join r in list
        //                             on month equals r.CreatedDate.Month
        //                             into g
        //                             select new MonthlyDelayedTransfer
        //                             {
        //                                 Month = month.ToString(),
        //                                 Count = g.Count()
        //                             };
        //        //return new DocumentsAverageDurationMonthWithTotalModel
        //        //{
        //        //    TotalAverage = list.Count() > 0 ? list.ToList().Average(x => (x.ClosedDate.Value.Date - x.CreatedDate.Date).TotalDays) : 0,
        //        //    DocumentAverageDurationList = averageByMonth.ToList()
        //        //};
        //        return countPerMonth.ToList();
        //    }
        //}
        public async Task<List<MonthlyDelayedTransfer>> GetDelayedTransfersMonthlyStatisticsAsync(int months, long userId)
        {
            using (var ctx = new CTSContext())
            {
                var todayDate = DateTime.Now;
                DateTime startDate = DateTime.Now.AddMonths(-months);
                IQueryable<Transfer> query = ctx.Transfer.AsNoTracking().Where(t => t.DueDate.HasValue && t.CreatedDate >= startDate &&
                ((t.ClosedDate.HasValue && t.DueDate.Value.Date < t.ClosedDate.Value.Date) || (!t.ClosedDate.HasValue && t.DueDate.Value.Date < todayDate.Date)));

                if (userId > 0)
                {
                    query = query.Where(t => t.ToUserId == userId);
                }
                var list = await query.Select(t => new { t.CreatedDate, t.ClosedDate, t.DueDate }).ToListAsync();

                var transfersList = query.GroupBy(t => new { t.Document.CreatedDate.Year, t.CreatedDate.Month })
                    .Select(g => new MonthlyDelayedTransfer
                    {
                        Year = g.Key.Year,
                        Month = g.Key.Month,
                        Count = g.Count()
                    })
                    .OrderByDescending(t => t.Year)
                    .ThenByDescending(t => t.Month)
                    .ToList();
                return transfersList.ToList();
            }
        }


        public async Task<List<Transfer>> ListFollowUpDocumentTransfersAsync(int startIndex, int pageSize, Expression<Func<Transfer, bool>> filterExpression = null,List<SortExpression<Transfer>> sortExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                var retValue = new List<Transfer>();
                IQueryable<Transfer> query = ctx.Transfer
                    .Include(t => t.Status)
                    .Include(t => t.ToUser)
                    .Include(t => t.ToStructure)
                    .Include(t => t.Document)
                    .Include(t => t.Document).ThenInclude(s=>s.Category);
                if (sortExpression != null)
                {
                    query = query.DynamicOrderBy(sortExpression);
                }
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                query = query.Where(x => !x.ClosedDate.HasValue);
                return pageSize < 0 ? query.ToList() : query.Skip(startIndex).Take(pageSize).ToList();
            }
        }
        public async Task<int> FollowUpDocumentTransfersCountAsync(Expression<Func<Transfer, bool>> filterExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                var retValue = new List<Transfer>();
                IQueryable<Transfer> query = ctx.Transfer
                    .Include(t => t.Status)
                    .Include(t => t.ToUser)
                    .Include(t => t.ToStructure)
                    .Include(t => t.Document)
                    .Include(t => t.Document).ThenInclude(s => s.Category);
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                query = query.Where(x => !x.ClosedDate.HasValue);
                return await query.CountAsync();
            }
        }
        public List<Transfer> ListExportedRequests(long documentId)
        {
            using (var ctx = new CTSContext())
            {
                var retValue = new List<Transfer>();
                IQueryable<Transfer> query = ctx.Transfer
                    .Include(t => t.Document).ThenInclude(t => t.CreatedByUser)
                    .Include(t => t.Document).ThenInclude(t => t.Privacy)
                    .Include(t => t.FromUser)
                    .Include(t => t.ToUser)
                    .Include(t => t.FromStructure)
                    .Include(t => t.ToStructure)
                    .Include(t => t.Document);


                query = query.Where(x => x.ExportedDocumentId == documentId);
                return query.ToList();
            }
        }
        public async Task<List<Transfer>> ListExportedDocumentsAsync(int startIndex, int pageSize, long userId, long structureId, bool isStructureReceiver, short privacyLevel, Expression<Func<Transfer, bool>> filterExpression = null, List<SortExpression<Transfer>> sortExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                var retValue = new List<Transfer>();
                IQueryable<Transfer> query = ctx.Transfer
                    .Include(t => t.OwnerUser)
                    .Include(t => t.Note)
                    .Include(t => t.OwnerDelegatedUser)
                    .Include(t => t.FromUser)
                    .Include(t => t.ToUser)
                    .Include(t => t.FromStructure)
                    .Include(t => t.ToStructure)
                    .Include(t => t.ExportedDocument)
                    .Include(t => t.Document).ThenInclude(t => t.SendingEntity)
                    .Include(t => t.Document).ThenInclude(t => t.DocumentForm)
                    .Include(t => t.Document).ThenInclude(t => t.Privacy)
                    .Include(t => t.Document).ThenInclude(t => t.DocumentReceiverEntity).ThenInclude(t => t.Structure)
                    .Include(t => t.Document).ThenInclude(t => t.DocumentReceiverEntity).ThenInclude(t => t.EntityGroup)
                    .Include(t => t.Document).ThenInclude(t => t.CreatedByUser).AsNoTracking()
                    .Include(t => t.Document).ThenInclude(t => t.LinkedDocumentDocument).ThenInclude(t => t.LinkedDocumentNavigation)
                .Include(t => t.ExportedDocument).ThenInclude(x => x.Transfer);
                query = query.Where(x => /*x.FromStructureId == structureId*/ (x.FromUserId == userId || ((x.FromStructureId.Value == structureId) && isStructureReceiver && x.Document.Privacy.Level <= privacyLevel))
                && !x.Document.ClosedDate.HasValue && x.ExportedDocumentId.HasValue && x.RequestStatus != (long)RequestStatuses.Rejected && x.RequestStatus != (long)RequestStatuses.Recalled).OrderByDescending(t => t.Document.CreatedDate/*(t.ExportedDocument.Transfer.FirstOrDefault(s => s.IsSigned == true).ExportedDate)*/);
                
                if (filterExpression!=null)
                {
                    query = query.Where(filterExpression);
                }

                return pageSize < 0 ? query.ToList() : query.Skip(startIndex).Take(pageSize).ToList();

            }
        }
        public async Task<int> GetExportedDocumentsTotalCountAsync(long userId, long structureId, bool isStructureReceiver, short privacyLevel, Expression<Func<Transfer, bool>> filterExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                var retValue = new List<Transfer>();
                IQueryable<Transfer> query = ctx.Transfer;
                query = query.Where(x => /*x.FromStructureId == structureId*/((x.FromUserId == userId && x.FromStructureId == structureId) || ((x.FromStructureId.Value == structureId) && isStructureReceiver && x.Document.Privacy.Level <= privacyLevel))
                && !x.Document.ClosedDate.HasValue && x.ExportedDocumentId.HasValue && x.RequestStatus != (long)RequestStatuses.Rejected && x.RequestStatus != (long)RequestStatuses.Recalled);
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                return await query.CountAsync();
            }
        }

        public async Task<int> GetExportedDocumentsTodayCountAsync(long userId, long structureId, bool isStructureReceiver, short privacyLevel)
        {
            using (var ctx = new CTSContext())
            {
                var retValue = new List<Transfer>();
                var today = DateTime.Today.Date;

                IQueryable<Transfer> query = ctx.Transfer.Include(x => x.ExportedDocument).ThenInclude(x => x.Transfer);
                query = query.Where(x => /*x.FromStructureId == structureId*/ ((x.FromUserId == userId && x.FromStructureId == structureId) || ((x.FromStructureId.Value == structureId) && isStructureReceiver && x.Document.Privacy.Level <= privacyLevel))
                && !x.Document.ClosedDate.HasValue && x.ExportedDocumentId.HasValue && x.RequestStatus != (long)RequestStatuses.Rejected && x.RequestStatus != (long)RequestStatuses.Recalled
                && x.ExportedDocument.Transfer.OrderBy(xx => xx.CreatedDate).LastOrDefault(xx => xx.ExportedDate.HasValue).ExportedDate.HasValue && (x.ExportedDocument.Transfer.OrderBy(xx => xx.CreatedDate).LastOrDefault(xx => xx.ExportedDate.HasValue).ExportedDate.Value.Date == today));
                return await query.CountAsync();
            }
        }

        public async Task<int> GetExportedDocumentsUnreadCountAsync(long userId, long structureId, bool isStructureReceiver, short privacyLevel)
        {
            using (var ctx = new CTSContext())
            {
                var retValue = new List<Transfer>();
                IQueryable<Transfer> query = ctx.Transfer;
                query = query.Where(x => /*x.FromStructureId == structureId*/ (x.FromUserId == userId || ((x.FromStructureId.Value == structureId) && isStructureReceiver && x.Document.Privacy.Level <= privacyLevel))
                && !x.OpenedDate.HasValue &&!x.Document.ClosedDate.HasValue && x.ExportedDocumentId.HasValue && x.RequestStatus != (long)RequestStatuses.Rejected && x.RequestStatus != (long)RequestStatuses.Recalled);
                return await query.CountAsync();
            }
        }

        public async Task<List<Transfer>> ListRejectedDocumentsAsync(int startIndex, int pageSize, long userId, long structureId, bool isStructureReceiver, short privacyLevel, Expression<Func<Transfer, bool>> filterExpression = null, List<SortExpression<Transfer>> sortExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                var retValue = new List<Transfer>();
                IQueryable<Transfer> query = ctx.Transfer
                    .Include(t => t.OwnerUser)
                    .Include(t => t.Note)
                    .Include(t => t.OwnerDelegatedUser)
                    .Include(t => t.FromUser)
                    .Include(t => t.ToUser)
                    .Include(t => t.FromStructure)
                    .Include(t => t.ToStructure)
                    .Include(t => t.ExportedDocument)
                    .Include(t => t.Document).ThenInclude(t => t.SendingEntity)
                    .Include(t => t.Document).ThenInclude(t => t.DocumentForm)
                    .Include(t => t.Document).ThenInclude(t => t.Privacy)
                    .Include(t => t.Document).ThenInclude(t => t.DocumentReceiverEntity).ThenInclude(t => t.Structure)
                    .Include(t => t.Document).ThenInclude(t => t.DocumentReceiverEntity).ThenInclude(t => t.EntityGroup)
                    .Include(t => t.Document).ThenInclude(t => t.CreatedByUser).AsNoTracking()
                    .Include(t => t.Document).ThenInclude(t => t.LinkedDocumentDocument).ThenInclude(t => t.LinkedDocumentNavigation)
                    .Include(t => t.ExportedDocument).ThenInclude(x=>x.Transfer);
                query = query.Where(x => /*x.FromStructureId == structureId */((x.FromUserId == userId && x.FromStructureId == structureId) || ((x.FromStructureId.Value == structureId) && isStructureReceiver && x.Document.Privacy.Level <= privacyLevel))
                && !x.Document.ClosedDate.HasValue && x.ExportedDocumentId.HasValue  && (x.RequestStatus == (long)RequestStatuses.Rejected|| x.RequestStatus == (long)RequestStatuses.Recalled)).OrderByDescending(x => x.Document.ModifiedDate);
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }

                return pageSize < 0 ? query.ToList() : query.Skip(startIndex).Take(pageSize).ToList();
            }
        }
        public async Task<int> GetRejectedDocumentsTotalCountAsync(long userId, long structureId, bool isStructureReceiver, short privacyLevel, Expression<Func<Transfer, bool>> filterExpression = null)
        {
            using (var ctx = new CTSContext())
            {
                var retValue = new List<Transfer>();
                IQueryable<Transfer> query = ctx.Transfer;
                query = query.Where(x => /*x.FromStructureId == structureId*/ ((x.FromUserId == userId && x.FromStructureId == structureId) || ((x.FromStructureId.Value == structureId) && isStructureReceiver && x.Document.Privacy.Level <= privacyLevel))
                && !x.Document.ClosedDate.HasValue && x.ExportedDocumentId.HasValue && (x.RequestStatus == (long)RequestStatuses.Rejected || x.RequestStatus == (long)RequestStatuses.Recalled));
                if (filterExpression != null)
                {
                    query = query.Where(filterExpression);
                }
                return await query.CountAsync();
            }
        }

        public async Task<int> GetRejectedDocumentsTodayCountAsync(long userId, long structureId, bool isStructureReceiver, short privacyLevel)
        {
            using (var ctx = new CTSContext())
            {
                var retValue = new List<Transfer>();
                var today = DateTime.Today.Date;

                IQueryable<Transfer> query = ctx.Transfer;
                query = query.Where(x => (x.RejectedDate.HasValue && (x.RejectedDate.Value.Date == today)) && /*x.FromStructureId == structureId*/ ((x.FromUserId == userId && x.FromStructureId == structureId) || ((x.FromStructureId.Value == structureId) && isStructureReceiver && x.Document.Privacy.Level <= privacyLevel))
                && !x.Document.ClosedDate.HasValue && x.ExportedDocumentId.HasValue && (x.RequestStatus == (long)RequestStatuses.Rejected || x.RequestStatus == (long)RequestStatuses.Recalled));
                return await query.CountAsync();
            }
        }

        public async Task<int> GetRejectedDocumentsUnreadCountAsync(long userId, long structureId, bool isStructureReceiver, short privacyLevel)
        {
            using (var ctx = new CTSContext())
            {
                var retValue = new List<Transfer>();
                IQueryable<Transfer> query = ctx.Transfer;
                query = query.Where(x => /*x.FromStructureId == structureId*/ ((x.FromUserId == userId && x.FromStructureId == structureId) || ((x.FromStructureId.Value == structureId) && isStructureReceiver && x.Document.Privacy.Level <= privacyLevel))
                && !x.OpenedDate.HasValue && !x.Document.ClosedDate.HasValue && x.ExportedDocumentId.HasValue && (x.RequestStatus == (long)RequestStatuses.Rejected || x.RequestStatus == (long)RequestStatuses.Recalled));
                return await query.CountAsync();
            }
        }

        public bool UpdateExportedDate(bool isExportedFlag, DateTime? exportedDateTime)
        {
            bool retValue = false;
            using (var ctx = new CTSContext())
            {
                try
                {
                    IsExported = isExportedFlag;
                    ExportedDate = exportedDateTime;
                    ctx.Entry(this).Property(x => x.ExportedDate).IsModified = true;
                    ctx.Entry(this).Property(x => x.IsExported).IsModified = true;
                    ctx.SaveChanges();
                    retValue = true;
                }
                catch (Exception ex)
                {

                    throw ex;
                }
                return retValue;

            }
        }

        #endregion

        #region Dispose

        public void Dispose()
        {
            if (_ctx != null)
            {
                _ctx.Dispose();
            }
        }

        #endregion

        #region Conversion

        public static implicit operator DocumentDetailsModel(Transfer item)
        {
            DocumentDetailsModel retValue = null;
            if (item != null)
            {
                var lang = Helper.GetLanguage();
                retValue = new DocumentDetailsModel
                {
                    Id = item.DocumentId,
                    CategoryId = item.Document.Category.Id,
                    ReferenceNumber = item.Document.ReferenceNumber,
                    Status = item.Document.StatusId,
                    CreatedByUser = item.Document.CreatedByUser == null ? String.Empty :
                    (lang == Intalio.Core.Language.EN ?
                    $"{item.Document.CreatedByUser?.Firstname} {item.Document.CreatedByUser?.Lastname}" :
                     $"{IdentityHelperExtension.GetFullName(item.Document.CreatedByUser.Id, lang)}"),
                    CreatedByStructureId = item.Document.CreatedByStructureId
                };
            }
            return retValue;
        }

        public static implicit operator TransferCrawlingModel(Transfer item)
        {
            TransferCrawlingModel retValue = null;
            if (item != null)
            {
                retValue = new TransferCrawlingModel
                {
                    Id = item.Id,
                    DocumentId = item.DocumentId ?? 0,
                    StatusId = item.StatusId,
                    PurposeId = item.PurposeId,
                    FromStructureId = item.FromStructureId,
                    FromUserId = item.FromUserId,
                    ToStructureId = item.ToStructureId,
                    ToUserId = item.ToUserId,
                    OpenedDate = item.OpenedDate,
                    OwnerUserId = item.OwnerUserId,
                    OwnerDelegatedUserId = item.OwnerDelegatedUserId,
                    LockedDate = item.LockedDate,
                    ClosedDate = item.ClosedDate,
                    DueDate = item.DueDate,
                    ParentTransferId = item.ParentTransferId,
                    CreatedDate = item.CreatedDate,
                    ModifiedDate = item.ModifiedDate,
                    CreatedByUserId = item.CreatedByUserId,
                    Cced = item.Cced,
                    Instruction = string.IsNullOrEmpty(item.Instruction) ? string.Empty : Regex.Replace(item.Instruction, @"<[^>]+>", string.Empty).Replace("&nbsp;", " ").Trim(),
                    RequestStatus = item.RequestStatus,
                    RejectionReason = item.RejectionReason
                };
            }
            return retValue;
        }

        #endregion
    }
}
