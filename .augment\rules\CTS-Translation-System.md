---
type: "always_apply"
---

# CTS Translation System

## Table of Contents
- [Overview](#overview)
- [Translation Architecture](#translation-architecture)
- [Translation Workflow](#translation-workflow)
- [_Translator.cshtml Usage](#_translatorcshtml-usage)
- [Database Translation Management](#database-translation-management)
- [SQL Script Patterns](#sql-script-patterns)
- [Best Practices](#best-practices)
- [Troubleshooting](#troubleshooting)

## Overview

The CTS (Correspondence Tracking System) implements a comprehensive internationalization (i18n) system supporting multiple languages:
- **English (EN)** - Primary language
- **French (FR)** - Secondary language
- **Arabic (AR)** - RTL language support

The translation system uses a combination of server-side localization and client-side resource management to provide seamless multilingual support.

## 🚨 **MANDATORY REQUIREMENTS**

### **Critical Rules for Translation Management**

#### 1. **SQL Script Naming Convention**
- **Format**: `Ibrahim_{DescriptiveAction}.sql`
- **Location**: `Database/` directory only
- **Encoding**: UTF-8 encoding mandatory
- **Examples**: `Ibrahim_AddFollowUpTranslation.sql`, `Ibrahim_UpdateRecallMessages.sql`

#### 2. **_Translator.cshtml Management**
- **🚨 NEVER DELETE EXISTING LINES** - Only add new resources
- **Check for existing keys first** - Search file before adding
- **No duplicate keys** - Reuse existing keys when possible
- **Add new keys only when necessary**

#### 3. **Database Translation Entries**
- Always check if keyword exists before inserting
- Provide translations for all three languages (EN, FR, AR)
- Use N-prefix for Unicode string literals
- Set IsSystem = 1 for system translations

## Translation Architecture

### Components Overview
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Server-Side   │    │   Client-Side   │    │    Database     │
│   Localizer     │ -> │  _Translator    │ -> │ TranslatorDict  │
│   (ASP.NET)     │    │   (.cshtml)     │    │     (Table)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Key Files and Locations
- **Client-side translations**: `Intalio.CTS/Views/Shared/_Translator.cshtml`
- **Database translations**: `TranslatorDictionary` table
- **SQL scripts**: `Database/` directory with translation scripts
- **Resource files**: Standard ASP.NET Core localization files

## Translation Workflow

### **MANDATORY Step-by-Step Process for Adding New Translations**

#### 1. **Check Existing Translations (REQUIRED)**
Before adding new translations, always check if the text already exists:

```sql
-- Check if translation already exists in database
SELECT * FROM TranslatorDictionary
WHERE Keyword = 'YourKeywordHere'
   OR EN LIKE '%your text%'
   OR FR LIKE '%votre texte%'
   OR AR LIKE '%النص العربي%';
```

```bash
# Check if key exists in _Translator.cshtml
grep -n "YourKeywordHere" Intalio.CTS/Views/Shared/_Translator.cshtml
```

#### 2. **Add to _Translator.cshtml (ONLY IF KEY DOESN'T EXIST)**
🚨 **CRITICAL**: Search first, never delete existing lines

```csharp
// In Intalio.CTS/Views/Shared/_Translator.cshtml
// ONLY add if key doesn't already exist
Resources.YourKeywordHere = '@Html.Raw(Localizer["YourKeywordHere"].Encode())';
```

#### 3. **Create SQL Script (MANDATORY NAMING)**
Create a SQL script following the mandatory naming convention:

```sql
-- File: Database/Ibrahim_{DescriptiveAction}.sql
-- Example: Database/Ibrahim_AddFollowUpTranslation.sql
-- Encoding: UTF-8 (MANDATORY)

IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword = N'YourKeywordHere')
BEGIN
    INSERT [dbo].[TranslatorDictionary] ([Keyword], [EN], [FR], [AR], [IsSystem])
    VALUES (N'YourKeywordHere',
            N'English Text',
            N'French Text',
            N'النص العربي',
            1)
END
```

#### 4. Execute SQL Script
Run the SQL script against the database:

```bash
sqlcmd -S your-server -d CTS_Database -i "Database/YourName_YourFeature_Translation.sql"
```

#### 5. Use in Frontend
Use the translation in JavaScript/Handlebars:

```javascript
// In JavaScript
var message = Resources.YourKeywordHere;
alert(message);

// In Handlebars templates
<span>{{Resources.YourKeywordHere}}</span>
```

## _Translator.cshtml Usage

### File Location
`Intalio.CTS/Views/Shared/_Translator.cshtml`

### Purpose
This file bridges server-side ASP.NET Core localization with client-side JavaScript resources.

### Pattern and Structure
```csharp
@using Microsoft.AspNetCore.Mvc.Localization
@inject IViewLocalizer Localizer

<script type="text/javascript">
    // Initialize Resources object if not exists
    if (typeof Resources === 'undefined') {
        var Resources = {};
    }
    
    // Translation entries follow this pattern:
    Resources.KeywordName = '@Html.Raw(Localizer["KeywordName"].Encode())';
    Resources.AnotherKeyword = '@Html.Raw(Localizer["AnotherKeyword"].Encode())';
    
    // Examples from actual codebase:
    Resources.LargerTextSize = '@Html.Raw(Localizer["LargerTextSize"].Encode())';
    Resources.SmallerTextSize = '@Html.Raw(Localizer["SmallerTextSize"].Encode())';
    Resources.ResetTextSize = '@Html.Raw(Localizer["ResetTextSize"].Encode())';
</script>
```

## 🚨 **CRITICAL _Translator.cshtml Rules**

### **MANDATORY Rule: NEVER DELETE EXISTING LINES**

#### ❌ **PROHIBITED Actions**
```javascript
// NEVER DO THIS - Don't delete existing lines
// Resources.ExistingKey='@Html.Raw(Localizer["ExistingKey"].Encode())';  // ❌ DELETED

// NEVER DO THIS - Don't modify existing lines
Resources.ExistingKey='@Html.Raw(Localizer["ModifiedKey"].Encode())';     // ❌ MODIFIED
```

#### ✅ **REQUIRED Process: Check Before Adding**

**Step 1: Search for Existing Keys**
```bash
# Always search _Translator.cshtml before adding new keys
grep -n "KeywordName" Intalio.CTS/Views/Shared/_Translator.cshtml
```

**Step 2: Reuse Existing Keys When Possible**
```javascript
// If this already exists in _Translator.cshtml:
Resources.CannotBeRecalled='@Html.Raw(Localizer["CannotBeRecalled"].Encode())';

// Then use it in your code:
alert(Resources.CannotBeRecalled);  // ✅ Reuse existing key
```

**Step 3: Add New Keys Only When Necessary**
```javascript
// Only add if the key doesn't exist:
Resources.NewFeatureName='@Html.Raw(Localizer["NewFeatureName"].Encode())';
```

### Adding New Translations
When adding new client-side translations:

```csharp
// 1. FIRST: Check if key already exists (search the file)
// 2. If exists: Use existing key
// 3. If not exists: Add the resource line
Resources.NewFeatureName = '@Html.Raw(Localizer["NewFeatureName"].Encode())';

// 4. Ensure corresponding database entry exists
// 5. Use in JavaScript as: Resources.NewFeatureName
```

### Best Practices for _Translator.cshtml
1. **NEVER delete existing lines** - This is the most critical rule
2. **Check for duplicates** - Search before adding new keys
3. **Alphabetical ordering**: Keep entries sorted for maintainability
4. **Descriptive keywords**: Use clear, descriptive keyword names
5. **Consistent naming**: Follow PascalCase convention
6. **HTML encoding**: Always use `@Html.Raw(Localizer["Key"].Encode())`

## Database Translation Management

### TranslatorDictionary Table Structure
```sql
CREATE TABLE [dbo].[TranslatorDictionary] (
    [Id] [int] IDENTITY(1,1) NOT NULL,
    [Keyword] [nvarchar](255) NOT NULL,
    [EN] [nvarchar](max) NULL,
    [FR] [nvarchar](max) NULL,
    [AR] [nvarchar](max) NULL,
    [IsSystem] [bit] NOT NULL DEFAULT(1),
    CONSTRAINT [PK_TranslatorDictionary] PRIMARY KEY ([Id])
);
```

### Column Descriptions
- **Id**: Auto-increment primary key
- **Keyword**: Unique identifier for the translation (used in code)
- **EN**: English translation text
- **FR**: French translation text  
- **AR**: Arabic translation text
- **IsSystem**: Flag indicating if translation is system-generated (usually 1)

### Querying Translations
```sql
-- Get all translations for a keyword
SELECT * FROM TranslatorDictionary WHERE Keyword = 'DocumentStatus';

-- Find translations containing specific text
SELECT * FROM TranslatorDictionary 
WHERE EN LIKE '%document%' OR FR LIKE '%document%' OR AR LIKE '%وثيقة%';

-- Get all system translations
SELECT * FROM TranslatorDictionary WHERE IsSystem = 1;

-- Find missing translations (empty values)
SELECT * FROM TranslatorDictionary 
WHERE EN IS NULL OR EN = '' 
   OR FR IS NULL OR FR = '' 
   OR AR IS NULL OR AR = '';
```

## 🚨 **MANDATORY SQL Script Naming Convention**

### **Required Format**: `Ibrahim_{DescriptiveAction}.sql`

#### **Naming Rules**
```
✅ CORRECT Examples:
- Ibrahim_AddFollowUpTranslation.sql
- Ibrahim_UpdateRecallMessages.sql
- Ibrahim_FixDocumentStatusTranslation.sql
- Ibrahim_AddExportFeatureTranslation.sql

❌ INCORRECT Examples:
- AddTranslation.sql                    # Missing Ibrahim_ prefix
- Ibrahim_CTSP2690_Translation.sql      # Use descriptive action, not ticket number
- translation_script.sql                # Wrong format entirely
- Ibrahim_Add_Follow_Up.sql             # Use camelCase, not underscores
```

#### **File Requirements**
- **Location**: `Database/` directory only
- **Encoding**: UTF-8 encoding mandatory (see [CTS-SQL-UTF8-Encoding.md](CTS-SQL-UTF8-Encoding.md))
- **Prefix**: Must start with "Ibrahim_"
- **Description**: Use descriptive action names in camelCase

## SQL Script Patterns

### Standard Translation Script Template
```sql
-- File naming: Ibrahim_{DescriptiveAction}.sql
-- Example: Ibrahim_AddFollowUpTranslation.sql
-- Encoding: UTF-8 (MANDATORY)

-- Single translation entry
IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword = N'KeywordName')
BEGIN
    INSERT [dbo].[TranslatorDictionary] ([Keyword], [EN], [FR], [AR], [IsSystem])
    VALUES (N'KeywordName',
            N'English Text',
            N'Texte français',
            N'النص العربي',
            1)
END

-- Multiple translation entries
IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword = N'FirstKeyword')
BEGIN
    INSERT [dbo].[TranslatorDictionary] ([Keyword], [EN], [FR], [AR], [IsSystem]) 
    VALUES (N'FirstKeyword', 
            N'First English Text', 
            N'Premier texte français', 
            N'النص الأول', 
            1)
END

IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword = N'SecondKeyword')
BEGIN
    INSERT [dbo].[TranslatorDictionary] ([Keyword], [EN], [FR], [AR], [IsSystem]) 
    VALUES (N'SecondKeyword', 
            N'Second English Text', 
            N'Deuxième texte français', 
            N'النص الثاني', 
            1)
END
```

### Real Examples from Codebase
```sql
-- From BashirHatoumTranslationFix.sql
IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword = N'LargerTextSize')
BEGIN
    INSERT [dbo].[TranslatorDictionary] ([Keyword], [EN], [FR], [AR], [IsSystem]) 
    VALUES (N'LargerTextSize', 
            N'Larger text size', 
            N'Taille de texte plus grande', 
            N'تكبير حجم الخط', 
            1)
END

IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword = N'SmallerTextSize')
BEGIN
    INSERT [dbo].[TranslatorDictionary] ([Keyword], [EN], [FR], [AR], [IsSystem]) 
    VALUES (N'SmallerTextSize', 
            N'Smaller text size', 
            N'Taille de texte plus petite', 
            N'تصغير حجم الخط', 
            1)
END
```

### Update Translation Script Pattern
```sql
-- Updating existing translations
UPDATE TranslatorDictionary 
SET EN = N'Updated English Text',
    FR = N'Texte français mis à jour',
    AR = N'النص المحدث'
WHERE Keyword = N'ExistingKeyword';

-- Conditional update (only if exists)
IF EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword = N'ExistingKeyword')
BEGIN
    UPDATE TranslatorDictionary 
    SET EN = N'New English Text'
    WHERE Keyword = N'ExistingKeyword';
END
```

## Best Practices

### Translation Content Guidelines

#### 1. Text Length Considerations
```sql
-- Consider text expansion in different languages
-- French text is typically 20-30% longer than English
-- Arabic text may require different character encoding

-- Example: Button text
INSERT [dbo].[TranslatorDictionary] ([Keyword], [EN], [FR], [AR], [IsSystem]) 
VALUES (N'SaveButton', 
        N'Save',                    -- 4 characters
        N'Enregistrer',            -- 12 characters (3x longer)
        N'حفظ',                     -- 3 characters
        1)
```

#### 2. Context-Aware Translations
```sql
-- Provide context in keyword names
INSERT [dbo].[TranslatorDictionary] ([Keyword], [EN], [FR], [AR], [IsSystem]) 
VALUES (N'DocumentStatusDraft', 
        N'Draft', 
        N'Brouillon', 
        N'مسودة', 
        1),
       (N'EmailDraft', 
        N'Draft', 
        N'Brouillon', 
        N'مسودة', 
        1)
```

#### 3. Consistent Terminology
```sql
-- Maintain consistent terminology across the application
-- Create a glossary for key terms

-- Example: "Correspondence" vs "Document"
INSERT [dbo].[TranslatorDictionary] ([Keyword], [EN], [FR], [AR], [IsSystem]) 
VALUES (N'Correspondence', 
        N'Correspondence',          -- Consistent term
        N'Correspondance', 
        N'مراسلة', 
        1)
```

### Development Best Practices

#### 1. Keyword Naming Conventions
```javascript
// Use descriptive, hierarchical naming
Resources.DocumentStatusActive      // Good
Resources.DocumentStatusInactive    // Good
Resources.Status1                   // Bad - not descriptive

// Group related translations
Resources.ButtonSave               // Good grouping
Resources.ButtonCancel             // Good grouping
Resources.ButtonDelete             // Good grouping
```

#### 2. Testing Translations
```javascript
// Test all language variations
function testTranslations() {
    console.log('EN:', Resources.TestKeyword);
    
    // Switch language context and test
    // Verify text fits in UI components
    // Check RTL layout for Arabic
}
```

#### 3. Translation Validation
```sql
-- Validate translations before deployment
SELECT Keyword, 
       CASE WHEN EN IS NULL OR EN = '' THEN 'Missing EN' ELSE 'OK' END as EN_Status,
       CASE WHEN FR IS NULL OR FR = '' THEN 'Missing FR' ELSE 'OK' END as FR_Status,
       CASE WHEN AR IS NULL OR AR = '' THEN 'Missing AR' ELSE 'OK' END as AR_Status
FROM TranslatorDictionary
WHERE EN IS NULL OR EN = '' OR FR IS NULL OR FR = '' OR AR IS NULL OR AR = '';
```

## Troubleshooting

### Common Issues and Solutions

#### 1. Translation Not Appearing
**Problem**: New translation not showing in UI
**Solutions**:
```sql
-- Check if translation exists in database
SELECT * FROM TranslatorDictionary WHERE Keyword = 'YourKeyword';

-- Check if _Translator.cshtml entry exists
-- Search for: Resources.YourKeyword = 

-- Verify application restart after _Translator.cshtml changes
```

#### 2. Special Characters Issues
**Problem**: Special characters not displaying correctly
**Solutions**:
```sql
-- Ensure proper Unicode handling
INSERT [dbo].[TranslatorDictionary] ([Keyword], [EN], [FR], [AR], [IsSystem]) 
VALUES (N'SpecialChars',           -- Use N prefix for Unicode
        N'English with "quotes"',   -- Escape quotes properly
        N'Français avec accents é à ç',
        N'العربية مع الرموز الخاصة',
        1)
```

#### 3. RTL Layout Issues
**Problem**: Arabic text not displaying correctly
**Solutions**:
```css
/* Ensure proper RTL support in CSS */
[dir="rtl"] .text-content {
    text-align: right;
    direction: rtl;
}

/* Handle mixed content */
.mixed-content {
    unicode-bidi: embed;
}
```

### Debugging Tools

#### 1. Translation Audit Query
```sql
-- Find all translations added by specific developer
SELECT * FROM TranslatorDictionary 
WHERE Keyword LIKE '%YourPrefix%'
ORDER BY Keyword;

-- Find recently added translations (if you have audit columns)
SELECT TOP 10 * FROM TranslatorDictionary 
ORDER BY Id DESC;
```

#### 2. Missing Translation Report
```sql
-- Generate report of missing translations
SELECT 
    'Missing English' as Issue,
    Keyword,
    EN,
    FR,
    AR
FROM TranslatorDictionary 
WHERE EN IS NULL OR EN = ''

UNION ALL

SELECT 
    'Missing French' as Issue,
    Keyword,
    EN,
    FR,
    AR
FROM TranslatorDictionary 
WHERE FR IS NULL OR FR = ''

UNION ALL

SELECT 
    'Missing Arabic' as Issue,
    Keyword,
    EN,
    FR,
    AR
FROM TranslatorDictionary 
WHERE AR IS NULL OR AR = '';
```

## Quick Reference

## 🚨 **Common Mistakes and Enforcement**

### ❌ **Critical Mistakes to Avoid**

#### **Mistake 1: Deleting Existing Translation Resources**
```javascript
// NEVER DO THIS
// Resources.ExistingKey='@Html.Raw(Localizer["ExistingKey"].Encode())';  // ❌ Deleted line
```

#### **Mistake 2: Creating Duplicate Keys**
```javascript
// Check first - if this already exists:
Resources.CannotBeRecalled='@Html.Raw(Localizer["CannotBeRecalled"].Encode())';

// Don't add this duplicate:
Resources.CannotBeRecalled='@Html.Raw(Localizer["CannotBeRecalled"].Encode())';  // ❌ Duplicate
```

#### **Mistake 3: Wrong SQL Script Naming**
```
❌ AddTranslation.sql                    # Missing Ibrahim_ prefix
❌ Ibrahim_CTSP2690.sql                  # Use descriptive action
❌ ibrahim_addtranslation.sql            # Wrong case
✅ Ibrahim_AddFollowUpTranslation.sql    # Correct format
```

#### **Mistake 4: Missing Language Translations**
```sql
-- ❌ INCORRECT - Missing Arabic translation
VALUES (N'Key', N'English', N'Français', N'', 1)

-- ✅ CORRECT - All languages provided
VALUES (N'Key', N'English', N'Français', N'عربي', 1)
```

### **Enforcement Guidelines**

#### **Code Review Checklist**
- [ ] SQL script follows `Ibrahim_{DescriptiveAction}.sql` naming
- [ ] SQL file uses UTF-8 encoding
- [ ] No existing lines deleted from _Translator.cshtml
- [ ] No duplicate keys in _Translator.cshtml
- [ ] All three languages provided (EN, FR, AR)
- [ ] N-prefix used for Unicode literals
- [ ] IsSystem = 1 for system translations

### Translation Checklist
1. ✅ Check existing translations first (database AND _Translator.cshtml)
2. ✅ Reuse existing keys when possible
3. ✅ Add to _Translator.cshtml ONLY if key doesn't exist
4. ✅ Create SQL script with mandatory naming: `Ibrahim_{DescriptiveAction}.sql`
5. ✅ Use UTF-8 encoding for SQL files
6. ✅ Execute SQL script
7. ✅ Test in all supported languages
8. ✅ Verify UI layout with longer text
9. ✅ Test RTL layout for Arabic

### File Locations
- **Client translations**: `Intalio.CTS/Views/Shared/_Translator.cshtml`
- **SQL scripts**: `Database/{Developer}_{Feature}_Translation.sql`
- **Database table**: `TranslatorDictionary`

### Common SQL Patterns
```sql
-- Check existence
IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword = N'Key')

-- Insert translation
INSERT [dbo].[TranslatorDictionary] ([Keyword], [EN], [FR], [AR], [IsSystem]) 
VALUES (N'Key', N'EN', N'FR', N'AR', 1)

-- Update translation
UPDATE TranslatorDictionary SET EN = N'New' WHERE Keyword = N'Key'
```
