﻿using Intalio.CTS.Core.API;
using Intalio.CTS.Core.Model;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;

namespace Intalio.CTS.Controllers
{
    [Route("[controller]/[Action]")]
    public class WorkflowController : BaseController
    {
        [HttpPost]
        [ProducesResponseType(typeof(APIResponseViewModel), 200)]
        public async Task<IActionResult> StartWorkflow(WorkflowViewModel model, long? delegationId)
        {
            try
            {
                var retVal = await ManageWorkflow.StartWorkflow(model, model.DocumentId, model.TransferId, UserId, StructureIds, IsStructureSender, IsStructureSender, PrivacyLevel, delegationId, Language);
                return Ok(retVal);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }

        }
        [HttpPost]
        [ProducesResponseType(typeof(APIResponseViewModel), 200)]
        public async Task<IActionResult> UpdateWorkflow(WorkflowViewModel model, long? delegationId)
        {
            try
            {
                var retVal = await ManageWorkflow.UpdateWorkflow(model, model.DocumentId, model.TransferId, UserId, StructureIds, IsStructureSender, IsStructureSender, PrivacyLevel, delegationId);
                return Ok(retVal);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }

        }
        [HttpPost]
        [ProducesResponseType(typeof(APIResponseViewModel), 200)]
        public async Task<IActionResult> Proceed(long documentId, long transferId, long workflowStepId, bool forSignature, long signatureTemplateId, long? delegationId)
        {
            try
            {
                var authHeader = HttpContext.Request.Headers["Authorization"].ToString();
                var usertoken = authHeader.StartsWith("Bearer ") ? authHeader.Substring("Bearer ".Length) : authHeader;

                var retVal = await ManageWorkflow.Proceed(workflowStepId, documentId, transferId, UserId, StructureIds, IsStructureSender, IsStructureSender, PrivacyLevel, forSignature, signatureTemplateId, delegationId, Language, usertoken);
                return Ok(retVal);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }

        }

        [HttpPost]
        [ProducesResponseType(typeof(APIResponseViewModel), 200)]
        public async Task<IActionResult> Resume(long documentId, long transferId, long workflowStepId, long? delegationId)
        {
            try
            {
                var retVal = await ManageWorkflow.ResumeWorkflow(workflowStepId, documentId, transferId, UserId, StructureIds, IsStructureSender, IsStructureSender, PrivacyLevel, delegationId, Language); return Ok(retVal);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }

        }

        [HttpPost]
        [ProducesResponseType(typeof(APIResponseViewModel), 200)]
        public IActionResult Cancel(long documentId, long transferId, long workflowStepId, long? delegationId)
        {
            try
            {
                var retVal = ManageWorkflow.CancelWorkflow(workflowStepId, documentId, transferId, UserId, StructureIds, IsStructureSender, IsStructureSender, PrivacyLevel, delegationId, Language);
                return Ok(retVal);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }

        }

        [HttpGet]
        public IActionResult GetWorkflow(long documentId, long? transferId, long? workflowStepId, long? delegationId)
        {
            try
            {
                WorkflowViewModel retVal = new WorkflowViewModel();
                if (workflowStepId != null)
                    retVal = ManageWorkflow.FindAllWorkflowSteps(workflowStepId.Value, documentId, transferId.Value, UserId, StructureIds, IsStructureSender, IsStructureSender, PrivacyLevel, delegationId, Language);
                else
                    retVal = ManageWorkflow.GetWorkflowForStartWorkflow(documentId, transferId, UserId, StructureIds, IsStructureSender, IsStructureSender, PrivacyLevel, delegationId, Language);
                return Ok(retVal);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }


        [HttpGet]
        public IActionResult GetWorkflowDataTable(long documentId, long? transferId, long workflowId, long? delegationId)
        {
            try
            {
                WorkflowViewModel retVal = new WorkflowViewModel();
                retVal = ManageWorkflow.GetWorkflow(documentId, workflowId, transferId, UserId, StructureIds, IsStructureSender, IsStructureSender, PrivacyLevel, delegationId, Language);
                return Ok(retVal.WorkflowUsers);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }


        [HttpPost]
        [ProducesResponseType(typeof(APIResponseViewModel), 200)]
        public async Task<IActionResult> Save(WorkflowViewModel model,long? delegationId)
        {
            try
            {
                APIResponseViewModel retVal = new APIResponseViewModel();
                if (model.WorkflowId != 0)
                    retVal = await ManageWorkflow.UpdateWorkflow(model, model.DocumentId, model.TransferId, UserId, StructureIds, IsStructureSender, IsStructureSender, PrivacyLevel, delegationId);

                else
                    retVal = await ManageWorkflow.SaveWorkflow(model, model.DocumentId, model.TransferId, UserId, StructureIds, IsStructureSender, IsStructureSender, PrivacyLevel, delegationId);
                return Ok(retVal);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }

        }

        [HttpPost]
        [ProducesResponseType(typeof(APIResponseViewModel), 200)]
        public async Task<IActionResult> CreateWorkflow(WorkflowViewModel model,long? delegationId)
        {
            var retVal = await ManageWorkflow.CreateWorkflow(model, model.DocumentId, model.TransferId, UserId, StructureIds, IsStructureSender, PrivacyLevel, delegationId);
            return Ok(retVal);
        }

    }
}
