﻿using Intalio.Core;
using Intalio.Core.Model;
using Intalio.CTS.Core;
using Intalio.CTS.Core.API;
using Intalio.CTS.Core.DAL;
using Intalio.CTS.Core.Model;
using Intalio.CTS.Filters;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;

namespace Intalio.CTS.Controllers
{
    [Route("/[controller]/[action]")]
    //[ApiController]
    public class InstructionsController : BaseController
    {
        /// <summary>
        /// List Instructions
        /// </summary>
        /// <param name="draw"></param>
        /// <param name="start">Starting record - The number of records to skip before starting to return records from the query. Default value is 0.</param>
        /// <param name="length">Number of records to be returned</param>
        /// <param name="documentId">Document id</param>
        /// <returns></returns>
        [CustomTabsAuthorizationFilter(new string[] { nameof(CustomTabs.Instruction) })]

        [HttpGet]
        [ProducesResponseType(typeof(GridListViewModel<InstructionListViewModel>), 200)]
        public async Task<JsonResult> List(int draw, int start, int length, long documentId)
        {
            try
            {
                var retValue = await ManageInstruction.List(start, length,
                    documentId, UserId, RoleId, StructureId, Language);
                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = retValue.Item2
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }


        /// <summary>
        /// Create or edit Instruction
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CustomActionsAuthorizationFilter(new string[] { nameof(CustomActions.Instruction_New) })]
        [HttpPost]
        public IActionResult Index(InstructionModel model)
        {
            try
            {
                Result retValue = new Result();
                int code = Microsoft.AspNetCore.Http.StatusCodes.Status400BadRequest;
                bool success = false;
                if (ModelState.IsValid)
                {
                    if (model.Id.HasValue)
                    {
                        if (ManageInstruction.Edit(model, UserId, StructureId))
                        {
                            success = true;
                            code = Microsoft.AspNetCore.Http.StatusCodes.Status200OK;
                        }
                    }
                    else
                    {
                        if (ManageInstruction.Create(model, UserId, StructureId))
                        {
                            success = true;
                            code = Microsoft.AspNetCore.Http.StatusCodes.Status200OK;
                        }
                    }
                    if (!success)
                    {
                        retValue.Message = TranslationUtility.Translate("NoPermission", Language);
                        code = Microsoft.AspNetCore.Http.StatusCodes.Status200OK;
                    }
                    else
                    {
                        retValue.Id = model.Id.Value;
                    }
                }
                return StatusCode(code, retValue);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }


        /// <summary>
        /// Delete Instruction
        /// </summary>
        /// <param name="id">Instruction id</param>

        [CustomActionsAuthorizationFilter(new string[] { nameof(CustomActions.Instruction_Delete) })]

        [HttpDelete]
        public IActionResult Delete(long id)
        {
            try
            {
                return Ok(ManageInstruction.Delete(id , UserId));
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

    }
}


