---
type: "always_apply"
---

# CTS SQL Script UTF-8 Encoding Requirements

## Table of Contents
- [Overview](#overview)
- [Mandatory Requirements](#mandatory-requirements)
- [Why UTF-8 Encoding is Critical](#why-utf-8-encoding-is-critical)
- [Editor Configuration](#editor-configuration)
- [Verification Methods](#verification-methods)
- [Common Issues and Solutions](#common-issues-and-solutions)
- [Best Practices](#best-practices)
- [Enforcement Guidelines](#enforcement-guidelines)

## Overview

The CTS (Correspondence Tracking System) supports multilingual content with translations in **English**, **French**, and **Arabic**. All SQL script files in the `Database/` directory **MUST** be saved with **UTF-8 encoding** to ensure proper handling of international characters and prevent data corruption.

## Mandatory Requirements

### 🚨 **CRITICAL RULE**: All SQL Scripts Must Use UTF-8 Encoding

#### Scope
- **All files** in the `Database/` directory and subdirectories
- **All SQL script files** with `.sql` extension
- **Translation scripts** containing multilingual content
- **Schema scripts** that may contain comments in multiple languages

#### File Types Affected
```
Database/
├── *.sql                           # All SQL scripts
├── CTS_Sprint*_Queries/*.sql       # Sprint-specific scripts
├── QDB/*.sql                       # QDB environment scripts
└── archived/*.sql                  # Archived scripts
```

#### Encoding Standard
- **Required**: UTF-8 (Unicode)
- **With BOM**: Optional but recommended for Windows environments
- **Line Endings**: Windows (CRLF) or Unix (LF) - consistent within project

## Why UTF-8 Encoding is Critical

### 1. **Multilingual Translation Support**
CTS translation system stores content in three languages:

```sql
-- ❌ WRONG - Non-UTF-8 encoding corrupts Arabic text
INSERT [dbo].[TranslatorDictionary] ([Keyword], [EN], [FR], [AR], [IsSystem])
VALUES (N'Recall', N'Recall', N'Rappeler', N'??????', 1)  -- Corrupted Arabic

-- ✅ CORRECT - UTF-8 encoding preserves all characters
INSERT [dbo].[TranslatorDictionary] ([Keyword], [EN], [FR], [AR], [IsSystem])
VALUES (N'Recall', N'Recall', N'Rappeler', N'استرجاع', 1)  -- Proper Arabic
```

### 2. **Data Integrity**
- **Arabic Text**: Requires UTF-8 for proper right-to-left script rendering
- **French Accents**: Characters like é, è, à, ç need UTF-8 encoding
- **Special Characters**: Unicode symbols and punctuation marks

### 3. **Database Compatibility**
- SQL Server expects UTF-8 encoded scripts for proper NVARCHAR handling
- Prevents character encoding mismatches during script execution
- Ensures consistent data storage across environments

### 4. **Version Control**
- Git and other VCS systems handle UTF-8 files consistently
- Prevents encoding-related merge conflicts
- Maintains file integrity across different developer environments

## Editor Configuration

### Visual Studio 2022

#### Setting Default Encoding
1. **Tools** → **Options** → **Environment** → **Documents**
2. Check **"Save documents as Unicode when data cannot be saved in codepage"**
3. **Advanced Save Options**: Set to **"Unicode (UTF-8 with signature) - Codepage 65001"**

#### Per-File Encoding
1. **File** → **Advanced Save Options**
2. Select **"Unicode (UTF-8 with signature) - Codepage 65001"**
3. Click **OK** and save the file

#### Project-Level Configuration
```xml
<!-- Add to .editorconfig -->
[*.sql]
charset = utf-8
insert_final_newline = true
trim_trailing_whitespace = true
```

### Visual Studio Code

#### Global Settings
```json
// settings.json
{
    "files.encoding": "utf8",
    "files.autoGuessEncoding": false,
    "[sql]": {
        "files.encoding": "utf8"
    }
}
```

#### Per-File Encoding
1. Click encoding indicator in status bar (bottom-right)
2. Select **"Reopen with Encoding"** → **"UTF-8"**
3. Or **"Save with Encoding"** → **"UTF-8"**

#### Workspace Configuration
```json
// .vscode/settings.json
{
    "files.encoding": "utf8",
    "files.associations": {
        "*.sql": "sql"
    }
}
```

### SQL Server Management Studio (SSMS)

#### Default Encoding
1. **Tools** → **Options** → **Environment** → **International Settings**
2. Set **Language** to support Unicode
3. **File** → **Save As** → **Save with Encoding** → **"UTF-8"**

#### Template Configuration
```sql
-- Add to SQL script templates
-- Encoding: UTF-8
-- Description: CTS Translation Script Template
-- Author: [Developer Name]
-- Date: [Date]
```

### Notepad++

#### Setting Encoding
1. **Encoding** → **Convert to UTF-8** (or **UTF-8-BOM**)
2. **Settings** → **Preferences** → **New Document** → **Encoding**: **UTF-8**

## Verification Methods

### 1. **File Properties Check**

#### Windows PowerShell
```powershell
# Check file encoding
Get-Content "Database\script.sql" -Encoding UTF8 | Out-Null
if ($?) { Write-Host "UTF-8 ✓" } else { Write-Host "Not UTF-8 ✗" }

# Batch check all SQL files
Get-ChildItem "Database\" -Recurse -Filter "*.sql" | ForEach-Object {
    $encoding = [System.Text.Encoding]::GetEncoding((Get-Content $_.FullName -Encoding Byte -TotalCount 4))
    Write-Host "$($_.Name): $($encoding.EncodingName)"
}
```

#### Linux/Mac Terminal
```bash
# Check file encoding
file -bi Database/script.sql

# Check for UTF-8 BOM
hexdump -C Database/script.sql | head -1

# Batch check
find Database/ -name "*.sql" -exec file -bi {} \;
```

### 2. **Content Verification**

#### Arabic Text Test
```sql
-- Test script to verify Arabic rendering
SELECT N'استرجاع' AS ArabicTest;
-- Should display Arabic characters, not question marks
```

#### French Accent Test
```sql
-- Test script to verify French accents
SELECT N'Créé par l''utilisateur' AS FrenchTest;
-- Should display proper accented characters
```

### 3. **Git Pre-commit Hook**
```bash
#!/bin/sh
# .git/hooks/pre-commit
# Check SQL files for UTF-8 encoding

for file in $(git diff --cached --name-only --diff-filter=ACM | grep '\.sql$'); do
    if ! file -bi "$file" | grep -q "charset=utf-8"; then
        echo "Error: $file is not UTF-8 encoded"
        exit 1
    fi
done
```

## Common Issues and Solutions

### Issue 1: **Arabic Text Appears as Question Marks**
```sql
-- Problem: File saved with ANSI encoding
INSERT INTO TranslatorDictionary VALUES (N'Key', N'English', N'Français', N'??????');

-- Solution: Save file as UTF-8 and re-enter Arabic text
INSERT INTO TranslatorDictionary VALUES (N'Key', N'English', N'Français', N'استرجاع');
```

### Issue 2: **French Accents Corrupted**
```sql
-- Problem: Non-UTF-8 encoding
INSERT INTO TranslatorDictionary VALUES (N'Created', N'Created', N'CrÃ©Ã©', N'تم الإنشاء');

-- Solution: UTF-8 encoding preserves accents
INSERT INTO TranslatorDictionary VALUES (N'Created', N'Created', N'Créé', N'تم الإنشاء');
```

### Issue 3: **BOM vs No-BOM**
```sql
-- Both are acceptable, but be consistent within the project
-- UTF-8 with BOM (recommended for Windows)
-- UTF-8 without BOM (common in Unix environments)
```

## Best Practices

### 1. **Script Headers**
```sql
-- =====================================================
-- Script: Ibrahim_AddFollowUpFeature_Translation.sql
-- Author: Ibrahim Mohamed
-- Date: 2025-07-30
-- Encoding: UTF-8
-- Description: Add translations for FollowUp feature
-- Languages: EN, FR, AR
-- =====================================================
```

### 2. **Translation Script Template**
```sql
-- UTF-8 Encoding Required for Multilingual Content
IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword = N'KeywordName')
BEGIN
    INSERT [dbo].[TranslatorDictionary] ([Keyword], [EN], [FR], [AR], [IsSystem])
    VALUES (
        N'KeywordName',
        N'English Text',
        N'Texte Français',
        N'النص العربي',
        1
    )
END
```

### 3. **Validation Queries**
```sql
-- Always test translations after script execution
SELECT [Keyword], [EN], [FR], [AR] 
FROM [dbo].[TranslatorDictionary] 
WHERE [Keyword] = N'YourNewKeyword';

-- Verify character display
SELECT 
    LEN([AR]) AS ArabicLength,
    DATALENGTH([AR]) AS ArabicBytes,
    [AR] AS ArabicText
FROM [dbo].[TranslatorDictionary] 
WHERE [Keyword] = N'YourNewKeyword';
```

## Enforcement Guidelines

### 1. **Code Review Checklist**
- [ ] SQL file is saved with UTF-8 encoding
- [ ] Arabic text displays correctly (not question marks)
- [ ] French accents are preserved
- [ ] File can be opened without encoding warnings
- [ ] Translation test queries execute successfully

### 2. **Automated Checks**
```yaml
# GitHub Actions workflow example
name: Check SQL Encoding
on: [push, pull_request]
jobs:
  encoding-check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Check SQL files encoding
        run: |
          find Database/ -name "*.sql" | while read file; do
            if ! file -bi "$file" | grep -q "charset=utf-8"; then
              echo "::error file=$file::File is not UTF-8 encoded"
              exit 1
            fi
          done
```

### 3. **Developer Onboarding**
- Configure editor for UTF-8 default encoding
- Test with sample multilingual script
- Verify database connection handles UTF-8 properly
- Review existing translation scripts as examples

### 4. **Documentation Requirements**
- Include encoding information in script headers
- Document any special character requirements
- Note language-specific considerations
- Reference this guideline in script comments

## Quick Reference

### ✅ **DO**
- Save all SQL files with UTF-8 encoding
- Test Arabic and French text after script execution
- Use N-prefixed strings for Unicode literals
- Configure editors for UTF-8 default
- Include encoding verification in code reviews

### ❌ **DON'T**
- Save SQL files with ANSI or other encodings
- Ignore encoding warnings in editors
- Copy-paste text from non-UTF-8 sources without verification
- Assume all editors handle UTF-8 by default
- Skip encoding verification for "English-only" scripts

---

**Remember**: UTF-8 encoding is not optional for CTS SQL scripts. It's a mandatory requirement for maintaining data integrity in our multilingual system.
