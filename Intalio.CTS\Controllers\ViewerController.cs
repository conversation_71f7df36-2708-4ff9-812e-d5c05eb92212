﻿using Intalio.Core;
using Intalio.Core.DAL;
using Intalio.Core.UI.Filters;
using Intalio.CTS.Core;
using Intalio.CTS.Core.API;
using Intalio.CTS.Core.DAL;
using Intalio.CTS.Core.Model;
using Intalio.CTS.Core.Utility;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.Json.Nodes;
using System.Threading.Tasks;
using Serilog;

namespace Intalio.CTS.Controllers
{

    [HideInSwagger]
    [Route("[controller]")]
    public class ViewerController : BaseController
    {
        private readonly IHubContext<CommunicationHub> _hubContext;

        public ViewerController(IHubContext<CommunicationHub> hubContext)
        {
            _hubContext = hubContext;
        }


        [HttpGet]
        [Route("file/{fileId}/version/{version}/content")]
        public async Task<FileStreamResult> GetFileContent(long fileId, string version, long ctsDocumentId, long? ctsTransferId, long? delegationId)
        {
            try
            {
                if (version.Contains("T_"))
                {
                    ExceptionLogger.LogException(new Exception("received version with t: " + version), null, null, LoggingLevel.Info);
                    version = version.Replace("T_", "");
                }
                var data = await ManageAttachment.GetAttachmentData(fileId, version, ctsDocumentId, ctsTransferId, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, delegationId, RoleId);
                if (data != null)
                {
                    return File(new MemoryStream(data), "application/octet-stream");
                }
                return null;
            }
            catch (Exception ex)
            {
                HttpAjaxError(ex);
                return null;
            }
        }

        [HttpGet]
        [Route("file/{fileId}/version/{version}/checkout")]
        public IActionResult Checkout(long fileId, double version, long ctsDocumentId, long? ctsTransferId, long? delegationId, bool isDraft = false)
        {
            try
            {
                var file = new Intalio.CTS.Core.DAL.Attachment().FindIncludeAttachmentSecurity(fileId);
                if (ManageAttachmentSecurity.ChackAttachmentSecurity(file, UserId, StructureIds) &&
                    ManageAttachment.Checkout(UserId, StructureIds, IsStructureReceiver, PrivacyLevel, fileId, ctsDocumentId, ctsTransferId, delegationId, isDraft))
                {
                    return StatusCode(Microsoft.AspNetCore.Http.StatusCodes.Status200OK, true);
                }
                return StatusCode(Microsoft.AspNetCore.Http.StatusCodes.Status403Forbidden, false);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        [HttpDelete]
        [Route("file/{fileId}/version/{version}/checkout")]
        public bool DiscardCheckout(long fileId, double version, long ctsDocumentId, long? ctsTransferId, long? delegationId, bool isDraft = false)
        {
            try
            {
                var file = new Core.DAL.Attachment().FindIncludeAttachmentSecurity(fileId);
                if (ManageAttachmentSecurity.ChackAttachmentSecurity(file, UserId, StructureIds)
                    && ManageAttachment.CheckIn(UserId, StructureIds, IsStructureReceiver, PrivacyLevel, fileId, ctsDocumentId, ctsTransferId, delegationId, isDraft))
                {
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                HttpAjaxError(ex);
                return false;
            }
        }

        [HttpGet]
        [Route("file/{fileId}/version/{version}")]
        public async Task<IActionResult> GetFile(long fileId, string version, long ctsDocumentId, long? ctsTransferId, long? delegationId)
        {
            try
            {
                if (version.Contains("T_"))
                {
                    ExceptionLogger.LogException(new Exception("received version with t: " + version), null, null, LoggingLevel.Info);
                    version = version.Replace("T_", "");
                }
                var file = await ManageAttachment.GetAttachmentInfo(fileId, version, ctsDocumentId, ctsTransferId, UserId, StructureIds, IsStructureReceiver, PrivacyLevel,RoleId,StructureId, delegationId);
                if (file != null)
                {
                    var viewerFile = new
                    {
                        fileName = file.FileName,
                        lastModifiedTime = string.Empty,//needed but not used in viewer
                        mimeType = file.ContentType,
                        md5sum = file.MD5Checksum
                    };
                    var user = new
                    {
                        fullName = User.Claims.First(t => t.Type == "DisplayName").Value,
                        id = UserId.ToString(),
                        email = User.Claims.First(t => t.Type == "Email").Value
                    };
                    var result = new
                    {
                        user = JsonConvert.SerializeObject(user),
                        file = JsonConvert.SerializeObject(viewerFile)
                    };
                    return StatusCode(Microsoft.AspNetCore.Http.StatusCodes.Status200OK, JsonConvert.SerializeObject(result));
                }
                return StatusCode(Microsoft.AspNetCore.Http.StatusCodes.Status403Forbidden, null);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        [HttpGet]
        [Route("file/{fileId}/version/{version}/getSignatureCaptionFields")]
        public IActionResult getSignatureCaptionFields(long fileId, string version, long ctsDocumentId, long? ctsTransferId, long? delegationId)
        {
            JArray jsonArray = new JArray();

            if (delegationId != null)
            {
                var delegation = new Core.DAL.Delegation().Find(delegationId.Value);
                var fromuser = IdentityHelperExtension.GetFullName(delegation.FromUserId, Language.EN);
                var touser = IdentityHelperExtension.GetFullName(delegation.ToUserId, Language.EN);
                jsonArray.Add("Signed by: " + touser + " on behalf of " + fromuser);
            }
            string jsonString = jsonArray.ToString();

            return Ok(jsonString);
        }


        [HttpPost]
        [Route("file/{fileId}/version/{version}/checkin")]
        public async Task<IActionResult> CheckinAsync(long fileId, double version, bool isVersionModified, long ctsDocumentId, long? ctsTransferId, long? delegationId, bool isVersionSigned, string message = "", bool isDraft = false)
        {
            try
            {
                var file = new Core.DAL.Attachment().FindIncludeAttachmentSecurity(fileId);
                var securityPermission = ManageAttachmentSecurity.ChackAttachmentSecurity(file, UserId, StructureIds);
                if (!securityPermission)
                    return StatusCode(Microsoft.AspNetCore.Http.StatusCodes.Status403Forbidden, null);
                var checkinResult = ManageAttachment.CheckIn(UserId, StructureIds, IsStructureReceiver, PrivacyLevel, fileId, ctsDocumentId, ctsTransferId, delegationId, isDraft);
                Document document = new Document().FindIncludeTransfers(ctsDocumentId);
                Transfer transfer = null;
                if (ctsTransferId != null)
                    transfer = new Transfer().Find(ctsTransferId.Value);
                if (checkinResult)
                {
                    var versionValue = version.ToString();
                    versionValue = versionValue.Contains(".") ? versionValue : $"{versionValue}.0";
                    if (isVersionSigned)
                    {
                        if (ManageAttachment.CheckIsAttachmentOriginalNotSigned(fileId))
                        {
                            //try
                            //{
                            //    var returnedResult = ManageDocument.GenerateReferenceNumberFirstTime(UserId, ctsDocumentId, ctsTransferId, Language);
                            //    if (!returnedResult.result)
                            //    {
                            //        //return StatusCode(Microsoft.AspNetCore.Http.StatusCodes.Status403Forbidden, null);
                            //    }
                            //}
                            //catch (Exception ex)
                            //{
                            //    return StatusCode(Microsoft.AspNetCore.Http.StatusCodes.Status403Forbidden, null);
                            //}
                            ManageDocument.GenerateReferenceNumberFirstTime(UserId, ctsDocumentId, ctsTransferId, Language);

                            if (ctsTransferId != null) { 
                                transfer.UpdateTransferIsSigned(true, UserId);
                                transfer.OpenedDate = null;
                                if (transfer.ToUserId.IsNull())
                                {
                                    if (delegationId.HasValue)
                                    {
                                        transfer.OwnerDelegatedUserId = null;
                                    }
                                    transfer.OwnerUserId = null;
                                    transfer.UpdateOpenedDateAndOwner();
                                }
                                else
                                {
                                    transfer.UpdateOpenedDate();
                                }
                            }
                            else if (ctsTransferId == null && isDraft)
                            {
                                var transferRes = await ManageTransfer.Transfer(UserId, StructureIds.FirstOrDefault(), StructureIds, IsStructureSender, IsStructureReceiver, PrivacyLevel,
                                    new List<TransferModel>()
                                    { new TransferModel{
                                                DocumentId = ctsDocumentId,
                                                PurposeId = (short)Intalio.CTS.Core.Configuration.ForInfoPurpose /*new Intalio.CTS.Core.DAL.Purpose().List().Where(x=>x.Name=="For Info").FirstOrDefault().Id*/,
                                                FromStructureId = StructureIds.FirstOrDefault(),
                                                ToStructureId = StructureIds.FirstOrDefault(),
                                            }}, language: Language);
                                document = new Document().FindIncludeTransfers(ctsDocumentId);
                                transfer = document.Transfer.LastOrDefault();
                                transfer.UpdateTransferIsSigned(true, UserId);
                            }
                            ManageDocument.UpdateDocumentIsSigned(ctsDocumentId, true, versionValue);
                            await ManageDocumentLock.LockDocumentAsync(ctsDocumentId, UserId);
                            new EventReceivers().OnOriginalDocumentSigned(ctsDocumentId);
                            await _hubContext.Clients.User(UserId.ToString()).SendAsync("RefreshInboxGrid");
                        }
                    }

                    if (isVersionModified)
                    {
                        if (!isVersionSigned)
                        {
                            if (ManageAttachment.CheckIsAttachmentOriginalSigned(fileId))
                            {
                                var doesDocumentHasSignature = await ManageDocument.IsDocumentHasSignatures(fileId, $"T_{versionValue}");
                                if (!doesDocumentHasSignature)
                                {
                                    ManageDocument.UpdateDocumentIsSigned(ctsDocumentId, false);
                                    if (transfer != null)
                                    {
                                        transfer.UpdateTransferIsSigned(false, UserId);

                                    }
                                }
                            }
                        }
                        using (StreamReader reader = new StreamReader(Request.Body, Encoding.UTF8))
                        {
                            using (var requestBodyStream = new MemoryStream())
                            {
                                var body = Request.Body;
                                await Request.Body.CopyToAsync(requestBodyStream);
                                requestBodyStream.Seek(0, SeekOrigin.Begin);
                                byte[] updatedbytes = requestBodyStream.ToArray();
                                var newVersion = await ManageAttachment.ReplaceFile(fileId, ctsDocumentId, updatedbytes, UserId, message, false);
                                versionValue = Convert.ToDouble(newVersion) != -1 && newVersion != default ? newVersion : versionValue;
                                versionValue = versionValue.Contains(".") ? versionValue : $"{versionValue}.0";
                            }
                        }
                    }
                    return StatusCode(Microsoft.AspNetCore.Http.StatusCodes.Status200OK, versionValue);
                }
                return StatusCode(Microsoft.AspNetCore.Http.StatusCodes.Status403Forbidden, null);
            }
            catch (Exception ex)
            {
                return HttpAjaxErrorWithMsg(ex);
            }
        }

        //[HttpGet]
        //[Route("file/{fileId}/versions")]
        //public async Task<IActionResult> GetVersions(long fileId)
        //{
        //    try
        //    {
        //        List<AttachmentVersionModel> versions = await ManageAttachment.ListAllVersionsIncludeCurrent(fileId);
        //        List<ViewerModel> viewerModel = new List<ViewerModel>();
        //        var file = new Core.DAL.Attachment().FindIncludeAttachmentSecurity(fileId);
        //        if (ManageAttachmentSecurity.ChackAttachmentSecurity(file, UserId, StructureIds) && versions != null)
        //        {
        //            foreach (AttachmentVersionModel version in versions)
        //            {
        //                viewerModel.Add(new ViewerModel { VersionNumber = version.Version });
        //            }
        //            var ret = JsonConvert.SerializeObject(viewerModel.OrderBy(t => t.VersionNumber));
        //            return StatusCode(Microsoft.AspNetCore.Http.StatusCodes.Status200OK, ret);
        //        }
        //        return StatusCode(Microsoft.AspNetCore.Http.StatusCodes.Status403Forbidden, null);
        //    }
        //    catch (Exception ex)
        //    {
        //        return HttpAjaxError(ex);
        //    }
        //}

        [HttpGet]
        [Route("file/{fileId}/versions")]
        public async Task<IActionResult> GetVersions(long fileId)
        {
            try
            {
                List<int> Version = new List<int>();
                List<int> Minor = new List<int>();
                List<AttachmentVersionModel> versions = await ManageAttachment.ListAllVersionsIncludeCurrent(fileId);
                List<ViewerModel> viewerModel = new List<ViewerModel>();
                if (versions != null)
                {
                    foreach (AttachmentVersionModel version in versions)
                    {
                        viewerModel.Add(new ViewerModel
                        {
                            VersionNumber = version.Version,
                            Version = int.Parse(version.Version.Split('.')[0]),
                            Minor = int.Parse(version.Version.Split('.')[1]),
                        });
                    }

                    var ret = JsonConvert.SerializeObject(viewerModel.OrderByDescending(t => t.Version).ThenByDescending(t => t.Minor));
                    return StatusCode(Microsoft.AspNetCore.Http.StatusCodes.Status200OK, ret);
                }
                return StatusCode(Microsoft.AspNetCore.Http.StatusCodes.Status403Forbidden, null);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        [HttpPost]
        [Route("log/action")]
        public void LogAction([FromBody] ViewerActionModel actionModel, long ctsDocumentId, long? ctsTransferId)
        {
            try
            {
                if (actionModel == null)
                {
                    Log.Information("viewer log actionModel is null");
                    return;
                }
                switch (Enum.Parse(typeof(ViewerActionType), actionModel.ActionType))
                {
                    case ViewerActionType.load:
                        ManageActivityLog.AddActivityLog(ctsDocumentId, ctsTransferId, (int)ActivityLogs.ViewAttachment, UserId, originalValue: Intalio.Core.Helper.SerializeJson(actionModel.File), note: actionModel.File?.Name);
                        break;
                    case ViewerActionType.print:
                        ManageActivityLog.AddActivityLog(ctsDocumentId, ctsTransferId, (int)ActivityLogs.PrintAttachment, UserId, originalValue: Intalio.Core.Helper.SerializeJson(actionModel.File), note: actionModel.File?.Name);
                        break;
                    case ViewerActionType.download:
                        ManageActivityLog.AddActivityLog(ctsDocumentId, ctsTransferId, (int)ActivityLogs.DownloadAttachment, UserId, originalValue: Intalio.Core.Helper.SerializeJson(actionModel.File), note: actionModel.File?.Name);
                        break;
                    case ViewerActionType.checkin:
                        string note = string.Empty;
                        if (Core.Configuration.AuditTrailMode == AuditTrailMode.Full)
                        {
                            foreach (var item in actionModel.Logs)
                            {
                                note += item.User.FullName + " " + $"{TranslationUtility.Translate("MakeAction", Language)}" + " " + item.Type + " " + $"{TranslationUtility.Translate("On", Language)}" + " " + item.Date + System.Environment.NewLine;
                            }
                            //Intalio.Core.Helper.SerializeJson(actionModel.Logs);
                        }
                        ManageActivityLog.AddActivityLog(ctsDocumentId, ctsTransferId, (int)ActivityLogs.ReplaceAttachment, UserId, newOriginalValue: Intalio.Core.Helper.SerializeJson(actionModel.File), note: note);
                        break;
                }
            }
            catch (Exception ex)
            {
                HttpAjaxError(ex);
            }
        }

        [HttpPost]
        [Route("file/{fileId}/version/{version}/lock")]
        public IActionResult LockEdit(long fileId, double version, long ctsDocumentId, long? ctsTransferId, long? delegationId, bool isDraft = false)
        {
            try
            {
                Log.Information($"locking file {fileId} version {version}");
                ManageAttachment.LockForEditing(UserId, fileId, ctsDocumentId, ctsTransferId);
                return Ok();
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        [HttpPost]
        [Route("file/{fileId}/version/{version}/unlock")]
        public IActionResult UnlockEdit(long fileId, double version, long ctsDocumentId, long? ctsTransferId, long? delegationId, bool isDraft = false)
        {
            try
            {
                ManageAttachment.UnlockForEditing(UserId, fileId);
                Log.Information($"unlocking file {fileId} version {version}");
                return Ok();
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        [HttpGet]
        [Route("file/{fileId}/version/{version}/getBarcodeValue")]
        public IActionResult getBarcodeValue(long fileId, string version, long ctsDocumentId, long? ctsTransferId, long? delegationId)
        {
            byte[] retValue = null;
            retValue = ManageDocument.GenerateBarcode(ctsDocumentId);
            string imagebase64 = Convert.ToBase64String(retValue);
            var jsonObject = new { barcodeImageBase64 = imagebase64 };
            string jsonString = JsonConvert.SerializeObject(jsonObject);
            return Ok(jsonString);
        }
    }
}
