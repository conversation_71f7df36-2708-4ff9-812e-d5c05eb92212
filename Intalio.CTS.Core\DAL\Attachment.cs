﻿using Intalio.Core.DAL;
using Intalio.Core.Interfaces;
using Intalio.CTS.Core.API;
using Intalio.CTS.Core.Model;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Headers;
using System.Net.Http;
using System.Threading.Tasks;

namespace Intalio.CTS.Core.DAL
{
    public partial class Attachment : IDbObject<Attachment>
    {
        #region Ctor

        public Attachment()
        {
            DocumentNavigation = new HashSet<Document>();
            AttachmentForm = new AttachmentForm();
        }

        #endregion

        #region Properties

        public long Id { get; set; }
        public string Name { get; set; }
        public string Extension { get; set; }
        public long? FolderId { get; set; }
        public long StorageAttachmentId { get; set; }
        public long? DocumentId { get; set; }
        public long? TransferId { get; set; }
        public long CreatedByUserId { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public long? CreatedByDelegatedUserId { get; set; }
        public bool? IsLocked { get; set; }
        public long? LockedByUserId { get; set; }
        public bool? IsEditLocked { get; set; }
        public long? EditLockedByUserId { get; set; }
        public byte Status { get; set; }
        public long? DocumentLockId { get; set; }

        public AttachmentForm AttachmentForm { get; set; }

        public virtual Document Document { get; set; }
        public virtual Transfer Transfer { get; set; }
        public virtual Folder Folder { get; set; }
        public virtual ICollection<Document> DocumentNavigation { get; set; }
        public virtual ICollection<AttachmentSecurity> AttachmentSecurities { get; set; }
        public virtual User CreatedByUser { get; set; }
        public virtual User CreatedByDelegatedUser { get; set; }
        public virtual ICollection<AttachmentSignUser> AttachmentSignUser { get; set; }
        public virtual DocumentLock? DocumentLock { get; set; }


        #endregion

        #region Public Methods

        public void Delete(long id)
        {
            using (var ctx = new CTSContext())
            {
                Attachment item = new Attachment { Id = id };
                ctx.Attachment.Attach(item);
                ctx.Attachment.Remove(item);
                ctx.SaveChanges();
            }
        }

        public void Delete()
        {
            Delete(Id);
        }

        public void Delete(List<long> ids)
        {
            using (var ctx = new CTSContext())
            {
                foreach (var id in ids)
                {
                    Attachment item = new Attachment { Id = id };
                    ctx.Attachment.Attach(item);
                    ctx.Attachment.Remove(item);
                }
                ctx.SaveChanges();
            }
        }

        public Attachment Find(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Attachment.FirstOrDefault(t => t.Id == id);
            }
        }

        public Attachment FindIncludeDocument(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Attachment.Include(t => t.Document).FirstOrDefault(t => t.Id == id);
            }
        }

        public void Insert()
        {
            using (var ctx = new CTSContext())
            {
                CreatedDate = DateTime.Now;
                ctx.Attachment.Add(this);
                ctx.SaveChanges();
            }
        }

        public void Update()
        {
            using (var ctx = new CTSContext())
            {
                ModifiedDate = DateTime.Now;
                ctx.Entry(this).State = EntityState.Modified;
                ctx.SaveChanges();
            }
        }
        public void UpdateIncludeAttachmentForm()
        {
            using (var ctx = new CTSContext())
            {
                ModifiedDate = DateTime.Now;
                ctx.Entry(this).State = EntityState.Modified;
                ctx.Entry(AttachmentForm).State = EntityState.Modified;
                ctx.SaveChanges();
            }
        }

        public void ResetAttachmentsTransfer(long transferId)
        {
            using var ctx = new CTSContext();
            var attachments = ctx.Attachment
                .Where(e => e.TransferId == transferId)
                .ToArray();
            Array.ForEach(attachments, e => e.TransferId = null);

            ctx.SaveChanges();
        }

        public void ResetTransferToDelete(long transferId)
        {
            using var ctx = new CTSContext();
            var attachments = ctx.Attachment
                .Where(e => e.TransferId == transferId)
                .ToArray();
            Array.ForEach(attachments, e => e.TransferId = null);

            var Folders = ctx.Folder.Where(e => e.TransferId == transferId).ToArray();
            Array.ForEach(Folders, e => e.TransferId = null);

            var LinkedDocument = ctx.LinkedDocument.Where(e => e.TransferId == transferId).ToArray();
            Array.ForEach(LinkedDocument, e => e.TransferId = null);

            var Notes = ctx.Note.Where(e => e.TransferId == transferId).ToArray();
            Array.ForEach(Notes, e => e.TransferId = null);

            var NonArchivedAttachments = ctx.NonArchivedAttachments.Where(e => e.TransferId == transferId).ToArray();
            Array.ForEach(NonArchivedAttachments, e => e.TransferId = null);

            ctx.SaveChanges();
        }
        public List<Attachment> ListTreeRootAttachments(long documentId, long UserId, List<long> StructureIds)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Attachment.AsNoTracking().Where(e => e.FolderId == null && e.DocumentId == documentId &&
                (e.AttachmentSecurities.Count() > 0 ?
                (e.CreatedByUserId == UserId
                || e.AttachmentSecurities.Select(x => x.UserId).Contains(UserId)
                || (e.AttachmentSecurities.Where(x => (StructureIds.Contains(x.StructureId.Value) && x.UserId == null))).Count() > 0)
                : true)
                ).ToList();
            }
        }
        public List<Attachment> ListAttachments(long documentId, long UserId, List<long> StructureIds)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Attachment.AsNoTracking().Where(e => e.DocumentId == documentId &&
                (e.AttachmentSecurities.Count() > 0 ?
                (e.CreatedByUserId == UserId
                || e.AttachmentSecurities.Select(x => x.UserId).Contains(UserId)
                || (e.AttachmentSecurities.Where(x => (StructureIds.Contains(x.StructureId.Value) && x.UserId == null))).Count() > 0)
                : true)
                ).ToList();
            }
        }
        public List<Attachment> ListTreeFolderAttachments(long documentId, long UserId, List<long> StructureIds,long? FolderId)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Attachment.AsNoTracking().Where(e => e.FolderId == FolderId && e.DocumentId == documentId &&
                (e.AttachmentSecurities.Count() > 0 ?
                (e.CreatedByUserId == UserId
                || e.AttachmentSecurities.Select(x => x.UserId).Contains(UserId)
                || (e.AttachmentSecurities.Where(x => (StructureIds.Contains(x.StructureId.Value) && x.UserId == null))).Count() > 0)
                : true)
                ).ToList();
            }
        }
        public List<Attachment> ListAttachmentsForTabs(long documentId, long UserId, List<long> StructureIds)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Attachment.AsNoTracking().Where(e =>  e.DocumentId == documentId &&
                (e.AttachmentSecurities.Count() > 0 ?
                (e.CreatedByUserId == UserId
                || e.AttachmentSecurities.Select(x => x.UserId).Contains(UserId)
                || (e.AttachmentSecurities.Where(x => (StructureIds.Contains(x.StructureId.Value) && x.UserId == null))).Count() > 0)
                : true)
                ).ToList();
            }
        }

        public async Task<List<Attachment>> ListTreeRootAttachmentsAsync(long documentId,long UserId,List<long> StructureIds)
        {
            using (var ctx = new CTSContext())
            {
                return await ctx.Attachment.Include(x => x.AttachmentSecurities).Where(e => e.FolderId == null && e.DocumentId == documentId &&
                (e.AttachmentSecurities.Count() > 0 ?
                (/*e.CreatedByUserId == UserId||*/
                (e.AttachmentSecurities.Where(x => (x.UserId == UserId || x.UserId == null) && StructureIds.Contains(x.StructureId.Value)).Count() > 0))
                : true)
                ).ToListAsync();
            }
        }
        public async Task<List<Attachment>> ListAllAttachmentsByDocumentIdAsync(long documentId, long UserId, List<long> StructureIds)
        {
            using (var ctx = new CTSContext())
            {
                return await ctx.Attachment.Include(x => x.AttachmentSecurities).Where(e => e.DocumentId == documentId &&
                (e.AttachmentSecurities.Count() > 0 ?
                (/*e.CreatedByUserId == UserId||*/
                (e.AttachmentSecurities.Where(x => (x.UserId == UserId || x.UserId == null) && StructureIds.Contains(x.StructureId.Value)).Count() > 0))
                : true)
                ).ToListAsync();
            }
        }
        public async Task<List<Attachment>> ListAttachmentsAsyncForTabs(long documentId, long UserId, List<long> StructureIds)
        {
            using (var ctx = new CTSContext())
            {
                return await ctx.Attachment.Include(x => x.AttachmentSecurities).Where(e => e.DocumentId == documentId &&
                (e.AttachmentSecurities.Count() > 0 ?
                (e.CreatedByUserId == UserId
                || (e.AttachmentSecurities.Where(x => (x.UserId == UserId || x.UserId == null) && StructureIds.Contains(x.StructureId.Value)).Count() > 0))
                : true)
                ).ToListAsync();
            }
        }
        public async Task<List<Attachment>> ListAttachmentsAsyncForTabsByStructureForDraft(long documentId, long UserId, long? StructureId)
        {
            using (var ctx = new CTSContext())
            {
                return await ctx.Attachment.Include(x => x.AttachmentSecurities).Where(e => e.DocumentId == documentId &&
                (e.AttachmentSecurities.Count() > 0 ?
                (e.CreatedByUserId == UserId
                || (e.AttachmentSecurities.Where(x => (x.UserId == UserId || x.UserId == null) && x.StructureId == StructureId.Value).Count() > 0))
                : true)
                ).ToListAsync();
            }
        }

        public List<long> ListIdsByDocumentId(long documentId)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Attachment.AsNoTracking().Where(e => e.DocumentId == documentId).Select(t => t.Id).ToList();
            }
        }

        public List<long> ListStorageIdsByDocumentId(long documentId)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Attachment.AsNoTracking().Where(e => e.DocumentId == documentId).Select(t => t.StorageAttachmentId).ToList();
            }
        }


        public List<long> ListIdsByDocumentIdNoOriginalMail(long documentId)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Attachment
                    .Include(e => e.Document)
                    .Where(e => e.DocumentId == documentId && e.Document.AttachmentId != e.Id)
                    .Select(t => t.Id)
                    .ToList();
            }
        }
        public bool CheckDocumentHasAttachments(long documentId)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Attachment.AsNoTracking().Any(e => e.DocumentId == documentId);
            }
        }
        public (bool,long) CheckUnique(long? id, long? folderId, long documentId, string name, string extension)
        {
            using (var ctx = new CTSContext())
            {
                if (id.HasValue)
                {
                    var oldobj = ctx.Attachment.AsNoTracking().FirstOrDefault(t => t.Id != id.Value && t.Name.ToLower() == name.ToLower() && t.Extension.ToLower() == extension.ToLower() && t.FolderId == folderId && t.DocumentId == documentId);
                    return (oldobj != null, oldobj != null ? oldobj.Id : 0);
                }
                var oldattachment = ctx.Attachment.AsNoTracking().FirstOrDefault(t => t.Name.ToLower() == name.ToLower() && t.Extension.ToLower() == extension.ToLower() && t.FolderId == folderId && t.DocumentId == documentId);
                return (oldattachment != null, oldattachment != null ? oldattachment.Id : 0);
            }
        }

        public Attachment FindByStorageAttachmentId(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Attachment.AsNoTracking().FirstOrDefault(t => t.StorageAttachmentId == id);
            }
        }

        public long? FindDocumentId(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Attachment.AsNoTracking().Where(t => t.Id == id).Select(x => x.DocumentId).FirstOrDefault();
            }
        }

        public void UpdateLockAndLockedByUserId()
        {
            using (var ctx = new CTSContext())
            {
                ModifiedDate = DateTime.Now;
                ctx.Entry(this).Property(x => x.ModifiedDate).IsModified = true;
                ctx.Entry(this).Property(x => x.IsLocked).IsModified = true;
                ctx.Entry(this).Property(x => x.LockedByUserId).IsModified = true;
                ctx.SaveChanges();
            }
        }

        public bool CheckDocumentAttachmentsIsLocked(long documentId)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Attachment.AsNoTracking().Any(t => t.DocumentId == documentId && t.IsLocked == true);
            }
        }

        public long FindStorageAttachmentId(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Attachment.AsNoTracking().Where(t => t.Id == id).Select(t => t.StorageAttachmentId).FirstOrDefault();
            }
        }

        public List<Attachment> ListPendingAttachmentsByDocumentIds(List<long> documentIds)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Attachment.AsNoTracking().Where(t => (t.DocumentId.HasValue ? documentIds.Contains(t.DocumentId.Value) : false) && t.Status == (int)Core.AttachmentStatus.Pending).ToList();
            }
        }
        
        public List<Attachment> ListByDocumentId(long documentId)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Attachment.AsNoTracking().Where(e => e.DocumentId == documentId).ToList();
            }
        }

        public List<Attachment> ListAttachmentsById(List<long> attachmentIds, long documentId)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Attachment.AsNoTracking().Where(t => (t.DocumentId.HasValue && t.DocumentId.Value == documentId) && attachmentIds.Contains(t.Id)).ToList();
            }
        }

        public void UpdateStatusByListIds(List<long> ids, byte status)
        {
            using (var ctx = new CTSContext())
            {
                for (int i = 0; i < ids.Count; i++)
                {
                    Attachment attachment = new Attachment { Id = ids[i] };
                    attachment.Status = status;
                    attachment.ModifiedDate = DateTime.Now;
                    ctx.Entry(attachment).Property(x => x.Status).IsModified = true;
                    ctx.Entry(attachment).Property(x => x.ModifiedDate).IsModified = true;
                }
                ctx.SaveChanges();
            }
        }

        public bool CheckIsAttachmentOriginal(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Attachment.AsNoTracking().Any(t => t.Id == id && t.Document.AttachmentId == id);
            }
        }

        public bool CheckIsAttachmentOriginalNotSigned(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Attachment.AsNoTracking().Any(t => t.Id == id && t.Document.AttachmentId == id
                && (!t.Document.IsSigned.HasValue || (t.Document.IsSigned.HasValue && !t.Document.IsSigned.Value)));
            }
        }
        public bool CheckIsAttachmentOriginalSigned(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Attachment.AsNoTracking().Any(t => t.Id == id && t.Document.AttachmentId == id
                && (t.Document.IsSigned.HasValue || (t.Document.IsSigned.HasValue && t.Document.IsSigned.Value)));
            }
        }        
        public async Task<bool> DiscardCheckOutAttachment(long AttachmentId, string version)
        {
            //viewer API document/{documentId}/version/{version}/checkout
            string viewerUrl = $"{Configuration.ViewerUrl}/api/document/{AttachmentId}/version/{version}/checkout";
            var result = await HttpDeleteAsync<object>(viewerUrl, Configuration.IdentityAccessToken);
            if (result != null && ((Newtonsoft.Json.Linq.JContainer)result)?.Count > 0)
                return true;
            return false;
        }

        public async Task<List<Attachment>> ListTreeRootFollowUpAttachmentsAsync(long documentId, long UserId, List<long> StructureIds, List<long?> LinkedDocumentDocumentIds)
        {
            using (var ctx = new CTSContext())
            {
                return await ctx.Attachment.Include(x => x.AttachmentSecurities).Where(e => (LinkedDocumentDocumentIds.Contains(e.DocumentId) || e.DocumentId == documentId) &&
                (e.AttachmentSecurities.Count() > 0 ?
                (e.CreatedByUserId == UserId
                || (e.AttachmentSecurities.Where(x => (x.UserId == UserId || x.UserId == null) && StructureIds.Contains(x.StructureId.Value)).Count() > 0))
                : true)
                ).ToListAsync();
            }
        }

        #endregion
        #region Private methods
        public static async Task<T> HttpDeleteAsync<T>(string url, string token)
        {
            HttpClient client = new HttpClient();
            client.BaseAddress = new Uri(url);
            client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
            HttpResponseMessage response = await client.DeleteAsync(url);
            if (response.IsSuccessStatusCode)
            {
                return JsonConvert.DeserializeObject<T>(await response.Content.ReadAsStringAsync());
            }

            return default(T);
        }

        public Attachment FindIncludeAttachmentSecurity(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Attachment.Include(x=>x.AttachmentSecurities).FirstOrDefault(t => t.Id == id);
            }
        }
        public Attachment FindIncludeFormIncludeDocumentThenIncludeCategory(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Attachment.Include(t => t.AttachmentForm).Include(t => t.Document).ThenInclude(d => d.Category).FirstOrDefault(t => t.Id == id);
            }
        }
        public Attachment FindIncludeAttachmentForm(long id)
        {
            using (var ctx = new CTSContext())
            {
                return ctx.Attachment.Include(t => t.AttachmentForm).FirstOrDefault(t => t.Id == id);
            }
        }
        #endregion

        #region Conversion

        public static implicit operator TreeNode(Attachment item)
        {
            TreeNode retValue = null;
            if (item != null)
            {
                retValue = new TreeNode
                {
                    Id = $"file_{item.Id}",
                    Text = $"{item.Name}",
                    Title = $"{item.Name}",
                    Type = ((int)NodeType.File).ToString(),
                    Icon = Helper.GetIcon(item.Extension),
                    ParentId = item.FolderId.HasValue ? $"folder_{item.FolderId}" : "#",
                    State = new TreeNodeState()
                    {
                        Disabled = false,
                        Opened = true,
                        Selected = false
                    },
                    IsLocked = item.IsLocked
                };
            }
            return retValue;
        }

        public static implicit operator AttachmentModel(Attachment item)
        {
            AttachmentModel retValue = null;
            if (item != null)
            {
                retValue = new AttachmentModel
                {
                    Id = item.Id,
                    Name = item.Name,
                    Extension = item.Extension,
                    StorageAttachmentId = item.StorageAttachmentId,
                    IsLocked = item.IsLocked
                };
            }
            return retValue;
        }

        #endregion
    }
}
