---
type: "always_apply"
description: "Example description"
---
# CTS Architecture Overview

## Table of Contents
- [Introduction](#introduction)
- [Core Architecture Pattern](#core-architecture-pattern)
- [Layer Responsibilities](#layer-responsibilities)
- [Data Flow](#data-flow)
- [Code Examples](#code-examples)
- [Best Practices](#best-practices)

## Introduction

The CTS (Correspondence Tracking System) follows a well-defined layered architecture pattern that ensures separation of concerns, maintainability, and scalability. The system is built on ASP.NET Core with Entity Framework Core for data access.

## Core Architecture Pattern

CTS implements a **Controllers → Managers → Entities → Database Context** pattern:

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ Controllers │ -> │  Managers   │ -> │  Entities   │ -> │   Database  │
│   (Web API) │    │ (Business)  │    │ (Data Model)│    │   Context   │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
```

## Layer Responsibilities

### 1. Controllers Layer (`Intalio.CTS/Controllers/`)
- **Purpose**: Handle HTTP requests and responses
- **Responsibilities**:
  - Route handling and parameter validation
  - Authentication and authorization
  - Request/response transformation
  - Calling appropriate Manager methods
- **Location**: `Intalio.CTS/Controllers/`
- **Naming Convention**: `{Entity}Controller.cs`

### 2. Managers Layer (`Intalio.CTS.Core/API/`)
- **Purpose**: Implement business logic and orchestration
- **Responsibilities**:
  - Business rule enforcement
  - Data validation and transformation
  - Orchestrating multiple entity operations
  - Transaction management
- **Location**: `Intalio.CTS.Core/API/`
- **Naming Convention**: `Manage{Entity}.cs`

### 3. Entities Layer (`Intalio.CTS.Core/DAL/`)
- **Purpose**: Data access and persistence
- **Responsibilities**:
  - Database operations (CRUD)
  - Entity relationships management
  - Data mapping and conversion
  - Query execution
- **Location**: `Intalio.CTS.Core/DAL/`
- **Naming Convention**: `{Entity}.cs`

### 4. Database Context (`Intalio.CTS.Core/DAL/CTSContext.cs`)
- **Purpose**: Entity Framework Core context
- **Responsibilities**:
  - Database connection management
  - Entity configuration
  - Change tracking
  - Transaction coordination

## Data Flow

### Typical Request Flow:
1. **HTTP Request** → Controller receives request
2. **Controller** → Validates input and calls Manager
3. **Manager** → Implements business logic and calls Entity methods
4. **Entity** → Performs database operations via CTSContext
5. **Database** → Returns data
6. **Response** → Data flows back through layers to client

### Example Flow for Document Update:
```
POST /Document/UpdateDocumentStatus
    ↓
DocumentController.UpdateDocumentStatus()
    ↓
ManageDocument.UpdateDocumentStatus()
    ↓
Document.UpdateStatus()
    ↓
CTSContext.SaveChanges()
```

## Code Examples

### Controller Layer Example
```csharp
[Route("[controller]/[action]")]
public class DocumentController : BaseController
{
    [HttpPost]
    [HideInSwagger]
    public IActionResult UpdateDocumentStatus([FromForm] long documentId, [FromForm] DocumentStatus status)
    {
        // Controller only handles HTTP concerns
        var res = ManageDocument.UpdateDocumentStatus(documentId, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, status);
        return Ok(res);
    }
}
```

### Manager Layer Example
```csharp
public static class ManageDocument
{
    /// <summary>
    /// Update document status with business logic
    /// </summary>
    internal static void UpdateDocumentStatus(Document item, DocumentStatus status)
    {
        // Business logic: only update if status actually changed
        if (item != null && item.StatusId != (short)status)
        {
            item.StatusId = (short)status;
            item.UpdateStatus(); // Call entity method
        }
    }
    
    /// <summary>
    /// Bulk update with transaction management
    /// </summary>
    internal static void UpdateDocumentStatusByIds(List<long> ids, DocumentStatus status)
    {
        if (ids.Count > 0)
        {
            List<Document> toBeUpdated = new List<Document>();
            List<Document> documents = new Document().ListByIds(ids);
            
            // Business rule: only update drafts
            for (int i = 0; i < documents.Count; i++)
            {
                if (documents[i].StatusId != (short)status && 
                    documents[i].StatusId == (short)DocumentStatus.Draft)
                {
                    documents[i].StatusId = (short)status;
                    toBeUpdated.Add(documents[i]);
                }
            }
            
            // Bulk update via entity
            new Document().UpdateStatusByList(toBeUpdated);
        }
    }
}
```

### Entity Layer Example
```csharp
public partial class Document : IDbObject<Document>, IDisposable
{
    private CTSContext _ctx;
    
    // Properties
    public long Id { get; set; }
    public short CategoryId { get; set; }
    public short StatusId { get; set; }
    public DateTime CreatedDate { get; set; }
    
    // Navigation properties
    public virtual Category Category { get; set; }
    public virtual User CreatedByUser { get; set; }
    
    // Data access methods
    public Document Find(long id)
    {
        using (var ctx = new CTSContext())
        {
            return ctx.Document.FirstOrDefault(d => d.Id == id);
        }
    }
    
    public void UpdateStatus()
    {
        using (var ctx = new CTSContext())
        {
            ctx.Document.Update(this);
            ctx.SaveChanges();
        }
    }
}
```

### Database Context Example
```csharp
public partial class CTSContext : DbContext
{
    public CTSContext(DbContextOptions<CTSContext> options) : base(options) { }
    
    // Entity sets
    public virtual DbSet<Document> Document { get; set; }
    public virtual DbSet<Category> Category { get; set; }
    public virtual DbSet<User> User { get; set; }
    public virtual DbSet<Transfer> Transfer { get; set; }
    // ... more entity sets
}
```

## Best Practices

### Controller Best Practices
- Keep controllers thin - delegate business logic to managers
- Handle only HTTP concerns (routing, validation, serialization)
- Use appropriate HTTP status codes
- Implement proper error handling

### Manager Best Practices
- Implement all business logic in managers
- Use static classes for stateless operations
- Group related operations in the same manager
- Handle transactions at the manager level
- Validate business rules before calling entities

### Entity Best Practices
- Keep entities focused on data access
- Use Entity Framework conventions
- Implement IDisposable for proper resource management
- Use navigation properties for relationships
- Avoid business logic in entities

### General Architecture Best Practices
- Maintain clear separation of concerns
- Follow the established naming conventions
- Use dependency injection where appropriate
- Implement proper logging and error handling
- Write unit tests for each layer independently

## Quick Reference

### File Locations
- **Controllers**: `Intalio.CTS/Controllers/{Entity}Controller.cs`
- **Managers**: `Intalio.CTS.Core/API/Manage{Entity}.cs`
- **Entities**: `Intalio.CTS.Core/DAL/{Entity}.cs`
- **Context**: `Intalio.CTS.Core/DAL/CTSContext.cs`

### Common Patterns
- Controllers call managers, never entities directly
- Managers contain business logic and call entity methods
- Entities handle database operations via CTSContext
- Use transactions in managers for multi-entity operations
- Follow established naming conventions consistently
