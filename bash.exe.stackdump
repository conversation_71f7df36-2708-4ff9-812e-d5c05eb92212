Stack trace:
Frame         Function      Args
0007FFFFB690  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFFA590) msys-2.0.dll+0x2118E
0007FFFFB690  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFFB690  0002100469F2 (00021028DF99, 0007FFFFB548, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFFB690  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFB690  00021006A545 (0007FFFFB6A0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFFB6A0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFC24870000 ntdll.dll
7FFC22DD0000 KERNEL32.DLL
7FFC219E0000 KERNELBASE.dll
7FFC1B570000 apphelp.dll
7FFC24670000 USER32.dll
7FFC220E0000 win32u.dll
000210040000 msys-2.0.dll
7FFC24580000 GDI32.dll
7FFC21DC0000 gdi32full.dll
7FFC22010000 msvcp_win.dll
7FFC21EF0000 ucrtbase.dll
7FFC23750000 advapi32.dll
7FFC245B0000 msvcrt.dll
7FFC239A0000 sechost.dll
7FFC220B0000 bcrypt.dll
7FFC23A50000 RPCRT4.dll
7FFC21180000 CRYPTBASE.DLL
7FFC22110000 bcryptPrimitives.dll
7FFC24110000 IMM32.DLL
