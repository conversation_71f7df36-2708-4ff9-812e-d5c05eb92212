﻿import Intalio from './common.js';
import InstructionIndex from './instructionIndex.js';

class Instruction extends Intalio.Model {
    constructor() {
        super();
        this.documentId = null;
    }
}

var gIsLocked = false;

function openInstructionWindow(row, self) {
   if (!self.model.readonly) {
      var wrapper = $(".modal-window");
       var modelindex = new InstructionIndex.InstructionIndex();
       modelindex.documentId = self.model.documentId;
       var instructionindex = new InstructionIndex.InstructionIndexView(wrapper, modelindex);
       instructionindex.render();
       let tr = $(row).closest('tr');
       let srow = $(self.refs['grdInstructionItems']).DataTable().row(tr);
      var instruction = srow.data();
      if (instruction) {
          $(instructionindex.refs['modalInstructionTitle']).html(Resources.Edit);
          instructionindex.setData(instruction);
           instructionindex.model.instructionId = instruction.id;
       } else {
          $(instructionindex.refs['modalInstructionTitle']).html(Resources.New);
        }
       $(instructionindex.refs['modalInstruction']).modal("show");
       $(instructionindex.refs['modalInstruction']).off("hidden.bs.modal");
       $(instructionindex.refs['modalInstruction']).off("shown.bs.modal");

       $(instructionindex.refs['modalInstruction']).on('hidden.bs.modal', function () {
           instructionindex.model.instructionId = '';
           $(instructionindex.refs['formIndexPost']).parsley().reset();
           $(instructionindex.refs['userPostMessage']).html('');
        });
   }
}

function deleteInstruction(id, self) {
    try {
        Common.showConfirmMsg(Resources.DeleteInstructionMsg, function () {
            if (gIsLocked === false) {
                gIsLocked = true;
                Common.ajaxDelete('/Instructions/Delete', { 'id': id, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val() }, function (data) {
                    gIsLocked = false;
                    if (!data) {
                        setTimeout(function () {
                            Common.alertMsg("No Permission");
                        }, 300);
                    } else {
                    $(self.refs['grdInstructionItems']).DataTable().ajax.reload();
                    }
                }, function () { gIsLocked = false; }, false);
            }
        });
    } catch (ex) {
        gIsLocked = false;
    }
}

function format(row) {
    return '<table style="width:100%" cellspacing="0" border="0">' +
        '<tr>' +
        '<th style="width: 10%;padding:5px">' + Resources.Instructions + ':</th>' +
        '<td style="width: 90%;padding:5px;word-break: break-all;"">' + (row.data().instructions || '') + '</td>' +
        '</tr>' +
        '</table>';
}

class InstructionView extends Intalio.View {
    constructor(element, model) {
        super(element, "instructionList", model);
    }

    render() {
        var self = this;
        var model = this.model;

        /* $(self.refs['btnNewInstruction']).hide();*/
        let buttons = [];
        if (this.model.actionName !== undefined) {
            var actionArray = this.model.actionName.split("_");
            if (actionArray.includes("Instruction.New") && !model.readOnly) {
                buttons = [
                    {
                        className: 'btn-sm btn-primary btnNewInstruction',
                        text: `<span class="fa fa-plus-circle mr-sm"></span><span>${Resources.New}</span>`
                    }
                ]
            }
        }

        
        var table = $(self.refs["grdInstructionItems"]).DataTable({
            processing: true,
            ordering: true,
            serverSide: true,
           pageLength: 10,
            ajax: {
                url: "/Instructions/List",
                type: "GET",
                datatype: "json",
                data: function (d) {
                    return {
                        __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val(),
                        draw: d.draw,
                        start: d.start,
                        length: d.length,
                        documentId: model.documentId
                    };
                }
            },
           order: [],
            columns: [
                {
                   className: 'details-control width20',
                    orderable: false,
                    data: null,
                    defaultContent: '',
                    width: '16px'
                },
                { title: "Id", data: "id", visible: false, orderable: false },
                {
                    title: Resources.Instruction, data: "instructions", orderable: false, className: "min-max-width-50-250"
                },
                //{
                //    title: "structure Name", data: "structureName", orderable: false, className: "min-max-width-50-250"
                //},
                {
                    title: Resources.Username, "orderable": false, orderSequence: ["desc", "asc"], "autoWidth": false, "className": "min-max-width-50-250",
                    render: function (data, type, full, meta) {
                        
                        return full.managerStructureName + " / " + full.name;
                    }
                 },
                 {
                     title: Resources.CreatedBy, "orderable": false, orderSequence: ["desc", "asc"], "autoWidth": false, "className": "min-max-width-50-250",
                    render: function (data, type, full, meta) {
                        return full.structureName + " / " +full.createdBy;
                    }
              },
                {
                   className: "text-right width20 minwidth55",
                    autoWidth: false,
                    bAutoWidth: false,
                    width: "16px",
                    orderable: false,
                    sortable: false,
                    render: function (data, type, full, meta) {
                        var html = "";
                        if (!self.model.readOnly) {
                            let btn = document.createElement("button");
                            btn.setAttribute("class", "btn btn-xs mr-sm btn-warning edit");
                            btn.setAttribute("title", "Edit");
                           btn.setAttribute("clickAttr", "openInstructionWindow(this)");
                            btn.innerHTML = "<i class='fa fa-edit fa-white'/>";
                            html += btn.outerHTML;

                           if (model.actionName != undefined) {
                               var actionArray = model.actionName.split("_");
                               if ((actionArray.includes("Instruction.Delete") && $("#hdUserId").val()) == full.createdByUserId) { 
                                    btn = document.createElement("button");
                                    btn.setAttribute("class", "btn btn-xs btn-danger delete");
                                    btn.setAttribute("title", "Delete");
                                     btn.setAttribute("clickAttr", "deleteInstruction(" + full.id + ")");
                                    btn.innerHTML = "<i class='fa fa-trash fa-white'/>";
                                   html += btn.outerHTML;
                                }
                            }
                        }
                        return html;
                    }
                }
            ],
            fnInitComplete: function (settings, json) {
                $('[data-toggle="tooltip"]').tooltip();
            },
            dom: '<"html5buttons "B>ltrpi',
            buttons: buttons            
        });

     
        $(self.refs["grdInstructionItems"]).find('td').tooltip({
            delay: 0,
            track: true,
            fade: 250
        });

        $(self.refs['btnNewInstruction']).hide();

        $(self.refs['grdInstructionItems']).on('click', ".edit", function () {
            var onclick = $(this).attr("clickattr").slice(0, -1) + ",self)";
            eval(onclick);
        });

        $(self.refs['grdInstructionItems']).find('tbody').on('click', 'td.details-control', function () {
            let tr = $(this).closest('tr');
            let row = table.row(tr);
            if (row.child.isShown()) {
                row.child.hide();
                tr.removeClass('shown');
            }
            else {
                row.child(format(row, self.model.nodeId)).show();
                tr.addClass('shown');
            }
        });
        $(self.refs['grdInstructionItems']).on('click', ".delete", function () {
            var onclick = $(this).attr("clickattr").slice(0, -1) + ",self)";
            eval(onclick);
        });
        
        $('.btnNewInstruction').click(function () {
            openInstructionWindow(null, self);
        });
     

    }
}

export default { Instruction, InstructionView }
