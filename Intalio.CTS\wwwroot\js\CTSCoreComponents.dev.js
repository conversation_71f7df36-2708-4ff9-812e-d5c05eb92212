import CTSSystemDelegation from '../components/systemdelegation.js'
import CTSDelegation from '../components/delegation.js'
import Parameter from '../components/parameter.js'
import ParameterIndex from '../components/parameterindex.js'
import ScanConfiguration from '../components/scanConfigurationList.js'
import ScanConfigurationIndex from '../components/scanConfigurationIndex.js'
import g2g from '../components/g2gList.js'
import G2GExport from '../components/g2gExport.js'
import Template from '../components/templateList.js'
import TemplatesManagement from '../components/templatesManagement.js'
import TemplateIndex from '../components/templateIndex.js'
import TreeNodeActions from '../components/treeNodeActions.js'
import Category from '../components/categoryList.js'
import DocumentAdvanceSearch from '../components/advanceSearch.js'
import DocumentAdvanceSearchConfiguration from '../components/advanceSearchConfiguration.js'
import DocumentAdvanceSearchConfigurationColumns from '../components/advanceSearchConfigurationColumns.js'
import CategoryIndex from '../components/categoryIndex.js'
import CategoryReferenceNumber from '../components/categoryReferenceNumberList.js'
import CategoryReferenceNumberIndex from '../components/categoryReferenceNumberIndex.js'
import CategoryReferenceCounter from '../components/categoryReferenceCounterList.js'
import CategoryReferenceCounterIndex from '../components/categoryReferenceCounterIndex.js'
import EventReceiverIndex from '../components/eventReceiverIndex.js'
import ActivityLog from '../components/activityLogList.js'
import AttachmentFolder from '../components/attachmentFolderList.js'
import AttachmentFolderIndex from '../components/attachmentFolderIndex.js'
import CategoryManagementView from '../components/categoryManagement.js'
import DocumentSearch from '../components/search.js'
import CreateByTemplate from '../components/createByTemplate.js'
import CreateByFile from '../components/createByFile.js'
import NonArchivedAttachmentsTypes from '../components/nonArchivedAttachmentsTypesList.js'
import NonArchivedAttachmentsTypesIndex from '../components/nonArchivedAttachmentsTypesIndex.js'
import DocumentDetails from '../components/documentDetails.js'
import BarcodeConfiguration from '../components/barcodeConfigurationList.js'
import BarcodeConfigurationIndex from '../components/barcodeConfigurationIndex.js'
import FilingPlan from '../components/filingPlanList.js'
import FilingPlanIndex from '../components/filingPlanIndex.js'
import BarcodeIndex from '../components/barcodeIndex.js'
import DocumentSent from '../components/sentList.js'
import Home from '../components/home.js'
import CategoryImport from '../components/categoryImport.js'
import AuditTrailValues from '../components/auditTrailValues.js'
import NodeList from '../components/nodeList.js'
import NodeIndex from '../components/nodeIndex.js'
import Bookmark from '../components/bookmark.js'
import DocumentClosed from '../components/closedList.js'
import Export from '../components/export.js'
import Import from '../components/import.js'
import SystemDashboard from '../components/dashboardSystem.js'
import UserDashboard from '../components/dashboardUser.js'
import KPI from '../components/kpi.js'
import ReportInProgressTransfers from '../components/reportInProgressTransfers.js'
import ReportTransfersSentToStructure from '../components/reportTransfersSentToStructure.js'
import ReportOutgoingFromDepartment from '../components/reportOutgoingFromDepartment.js'
import ReportCompletedTransfers from '../components/reportCompletedTransfers.js'
import ReportOperationByUser from '../components/reportOperationByUser.js'
import ReportOperationByCorrespondence from '../components/reportOperationByCorrespondence.js'
import ReportStatisticalCorrespondences from '../components/reportStatisticalCorrespondences.js'
import ReportCorrespondenceDetailFilter from '../components/reportCorrespondenceDetailFilter.js'
import ReportInProgressCorrespondences from '../components/reportInProgressCorrespondences.js'
import ReportCompletedCorrespondences from '../components/reportCompletedCorrespondences.js'
import EntityGroup from '../components/entityGroupList.js'
import AdminLog from '../components/AdminLog.js'
import DocumentManageCorrespondence from '../components/manageCorrespondence.js'
import Purpose from '../components/purpose.js'
import Privacy from '../components/privacy.js'
import AutoForward from '../components/autoForward.js'
import Widget from '../components/widget.js'
import MyTransfer from '../components/myTransfer.js'
import DocumentMetadata from '../components/document.js'
import DocumentAttachment from '../components/attachment.js'
import DocumentNote from '../components/noteList.js'
import EventList from '../components/eventList.js'
import InstructionList from '../components/instructionList.js'
import DocumentLinkCorrespondence from '../components/linkedCorrespondenceList.js'
import DocumentNonArchivedAttachment from '../components/nonArchivedAttachmentsList.js'
import VisualTracking from '../components/visualTracking.js'
import TransferHistory from '../components/transferHistoryList.js'
import ActivityLogs from '../components/activityLogTimeline.js'
import FavoriteStructures from '../components/favoriteStructures.js'
import DistributionList from '../components/distributionList.js'
import DistributionIndex from '../components/distributionIndex.js'
import ActivityLogTimeline from '../components/activityLogTimeline.js'
import Document from '../components/document.js'
import NoteList from '../components/noteList.js'
import NonArchivedAttachmentList from '../components/nonArchivedAttachmentsList.js'
import LinkedCorrespondence from '../components/linkedCorrespondenceList.js'
import Attachment from '../components/attachment.js'
import Transfer from '../components/transfer.js'
import SendTransferModal from '../components/sendTransfer.js'
import Role from '../components/role.js'
import User from '../components/user.js'
import Tab from '../components/tab.js'; 
import Action from '../components/action.js'; 
import OrganizationManagement from '../components/organizationManagement.js'
import ManageDepartmentUsers from '../components/manageDepartmentUsers.js'
import Committee from '../components/committeeList.js'
import MoveTransfers from '../components/movetransfers.js'
import AgendaTopicsList from '../components/AgendaTopicsList.js'
import MeetingAgendaIndex from '../components/meetingAgendaIndex.js'
import SearchLinkedDocumnet from '../components/searchLinkedDocumnet.js'
import AssigneeIndex from '../components/assigneeIndex.js'
import Assignee from '../components/assigneeList.js'
import ManageFollowUp from '../components/manageFollowUp.js'
import MyFollowUp from '../components/myFollowUp.js'
import FollowUpDetails from '../components/FollowUpDetails.js'
import FollowUpUsers from '../components/FollowUpUsers.js'
import UserNodeIndex from '../components/userNodeIndex.js'
import UserNodeList from '../components/userNodeList.js'
import BasketFromNodeIndex from '../components/addBasketFromNode.js'
import ListStructureUsers from '../components/liststructureusers.js'
import ListAllStructureUsers from '../components/listallstructureusers.js'
import NotificationTemplate from '../components/notificationTemplate.js'
import Node from '../components/node.js'
import DocumentManageStructureUsersCorrespondences from '../components/manageStructureUsersCorrespondences.js'
import CopyOptions from '../components/copyOptions.js'
import { ListSecureUsersView } from '../components/listSecureUsers.js'
import ExportRequests from '../components/outgoingIncomingRequests.js'
import followUpIndex from '../components/followUpIndex.js'
import followUpPostpone from '../components/followUpPostponeIndex.js'
import FollowUpPanel from '../components/FollowUpPanelList.js'
import ExportedDocuments from '../components/exportedDocumentsList.js'
import RejectedDocuments from '../components/rejectedDocumentsList.js'

import { Categories, IdentityService, CategoryModel, Types, DelegationUsers, FollowUpStatuses, DraftStatuses } from '../components/lookup.js'
function withAccessCheck(menuKey, callback) {
    return function () {
        if (window.userMenus != undefined) {
            if (CustomMenus.CheckMenueAccessibility('#' + menuKey)) {
                callback.apply(this, arguments);
            } else {
                window.location.href = "/AccessDenied/Index";
            }
        } else {
            setTimeout(() => {
                if (CustomMenus.CheckMenueAccessibility('#' + menuKey)) {
                    callback.apply(this, arguments);
                } else {
                    window.location.href = "/AccessDenied/Index";
                }
            }, 500);
        }
    };
}

var appRouter = Backbone.Router.extend({
    routes: {
        'systemdelegation': 'systemDelegationRoute',
        'delegation': 'delegationRoute',
        'exceptionlog': 'exceptionLogRoute',
        'purpose': 'purposeRoute',
        'status': 'statusRoute',
        'priority': 'priorityRoute',
        'privacy':'privacyRoute',
        'applicationserver': 'applicationServerRoute',
        'parameter': 'parameterRoute',
        'translatordictionary': 'translatorDictionaryRoute',
        'scanconfiguration': 'scanConfigurationRoute',
        'notificationtemplate': 'notificationTemplateRoute',
        'organizationManagement': 'organizationManagementRoute',
        'manageDepartmentUsers': 'manageDepartmentUsersRoute',
        'manageTemplate': 'manageTemplateRoute',
        'templatesManagement':'templatesManagementRoute',
        'manageCategory': 'manageCategoryRoute',
        'categoryreferencenumber': 'categoryReferenceNumberRoute',
        'categoryreferencecounter': 'categoryReferenceCounterRoute',
        'classification': 'classificationRoute',
        'importance': 'importanceRoute',
        'adminlog': 'adminlogRoute',
        'attachmentfolder': 'attachmentfolderRoute',
        'todolist': 'toDoListRoute',
        'assembly': 'assemblyRoute',
        'activitylog': 'activityLogRoute',
        'menu': 'menuRoute',
        'action': 'actionRoute',
        'tab': 'tabRoute',
        'user': 'userRoute',
        'role': 'roleRoute',
        'node': 'nodeRoute',
        'category': 'categoryRoute',
        'lookup': 'lookupRoute',
        'documenttype': 'documenttypeRoute',
        //'search': 'searchRoute',
        //'search/:term': 'searchRoute',
        'advanceSearch': 'advanceSearchRoute',
        'advanceSearch/:delegationId': 'advanceSearchRoute',
        'advanceSearchConfiguration': 'advanceSearchConfigurationRoute',
        'nonarchivedattachmentstypes': 'nonArchivedAttachmentsTypesRoute',
        'barcodeconfiguration': 'barcodeConfigurationRoute',
        'filingPlan': 'filingPlanRoute',
        'nodelist': 'nodeListRoute',
        'bookmarks': 'bookmarksRoute',
        'systemdashboard': 'systemDashboardRoute',
        'userdashboard': 'userDashboardRoute',
        'systeminprogresstransfers': 'systemInProgressTransfersRoute',
        'averagedurationforcorrespondencecompletion': 'averageDurationForCorrespondenceCompletionRoute',
        'averagedurationforcorrespondencedelay': 'averageDurationForCorrespondenceDelayRoute',
        'averagedurationfortransfercompletion': 'averageDurationForTransferCompletionRoute',
        'averagedurationfortransferdelay': 'averageDurationForTransferDelayRoute',
        'reportinprogresstransfers': 'reportInProgressTransfersRoute',
        'reportTransfersSentToStructure': 'reportTransfersSentToStructureRoute',
        'reportOutgoingFromDepartment': 'reportOutgoingFromDepartmentRoute',
        'reportcompletedtransfers': 'reportCompletedTransfersRoute',
        'reportOperationByUser': 'reportOperationByUserRoute',
        'reportOperationByCorrespondence': 'reportOperationByCorrespondenceRoute',
        'reportstatisticalcorrespondences': 'reportStatisticalCorrespondencesRoute',
        'reportcorrespondencedetailfilter': 'reportCorrespondenceDetailFilterRoute',
        'reportinprogresscorrespondences': 'reportInProgressCorrespondencesRoute',
        'reportcompletedcorrespondences': 'reportCompletedCorrespondencesRoute',
        'entitygroup': 'entityGroupRoute',
        'manageCorrespondence': 'manageCorrespondenceRoute',
        'manageStructureUsersCorrespondences': 'manageStructureUsersCorrespondencesRoute',
        'autoForward': 'autoForwardRoute',
        'favoriteStructures': 'favoriteStructuresRoute',
        'distributionList': 'distributionListRoute',
        'movetransfers': 'movetransfersRoute',
        'committee': 'committeeRoute',
        'createMeetingAgenda': 'meetingAgendaRoute',
        'manageFollowUp': 'manageFollowUpRoute',
        'liststructureusers': 'liststructureusersRoute',
        'listallstructureusers': 'listallstructureusersRoute',
        'userNode': 'userNodeRoute',
        'userNodeListCorrespondance' :'',
        'listsecureusers': 'listsecureusers',
        'followUpPanel': 'followUpPanelRoute',
        '': 'rootRoute',
        'g2g/:G2G_DocumentInbox': 'g2gRoute',
        'g2g/:G2G_DocumentInboxRejected': 'g2gRoute',
        'g2g/:G2G_DocumentInboxPendingToReceive': 'g2gRoute',
        'g2g/:G2G_DocumentInboxRecalled': 'g2gRoute',
        'g2g/:G2G_DocumentInboxReceiveOrReject': 'g2gRoute',
        'g2g/:G2G_DocumentInboxQueued': 'g2gRoute',
        'g2g/:G2G_DocumentInboxSent': 'g2gRoute',
        'g2g/:G2G_DocumentInboxIncomingRejected': 'g2gRoute',
        'g2g/:G2G_DocumentInboxIncomingRecalled': 'g2gRoute',

    },
    systemDelegationRoute: withAccessCheck("systemdelegation", function () {
        CustomMenus.systemDelegation();
    }),
    delegationRoute: withAccessCheck("delegation", function () {
        CustomMenus.delegationRoute();
    }),
    rootRoute: function () {
        CustomMenus.rootRoute();
    },
    applicationServerRoute: withAccessCheck("applicationserver", function () {
        CustomMenus.applicationServerRoute();
    }),
    parameterRoute: withAccessCheck("parameter", function () {
        CustomMenus.parameterRoute();
    }),
    exceptionLogRoute: withAccessCheck("exceptionlog", function () {
        CustomMenus.exceptionLogRoute();
    }),
    purposeRoute: withAccessCheck("purpose", function () {
        CustomMenus.purposeRoute();
    }),
    statusRoute: withAccessCheck("status", function () {
        CustomMenus.statusRoute();
    }),
    priorityRoute: withAccessCheck("priority", function () {
        CustomMenus.priorityRoute();
    }),
    translatorDictionaryRoute: withAccessCheck("translatordictionary", function () {
        CustomMenus.translatorDictionaryRoute();
    }),
    scanConfigurationRoute: withAccessCheck("scanconfiguration", function () {
        CustomMenus.scanConfigurationRoute();
    }),
    notificationTemplateRoute: withAccessCheck("notificationtemplate", function () {
        CustomMenus.notificationTemplateRoute();
    }),
    organizationManagementRoute: withAccessCheck("organizationManagement", function () {
        CustomMenus.organizationManagementRoute();
    }),
    manageDepartmentUsersRoute: withAccessCheck("manageDepartmentUsers", function () {
        CustomMenus.manageDepartmentUsersRoute();
    }),
    manageTemplateRoute: withAccessCheck("manageTemplate", function () {
        CustomMenus.manageTemplateRoute();
    }),
    templatesManagementRoute: withAccessCheck("templatesManagement", function () {
        CustomMenus.templatesManagementRoute();
    }),
    manageCategoryRoute: withAccessCheck("manageCategory", function () {
        CustomMenus.manageCategoryRoute();
    }),
    categoryReferenceNumberRoute: withAccessCheck("categoryreferencenumber", function () {
        CustomMenus.categoryReferenceNumberRoute();
    }),
    categoryReferenceCounterRoute: withAccessCheck("categoryreferencecounter", function () {
        CustomMenus.categoryReferenceCounterRoute();
    }),
    classificationRoute: withAccessCheck("classification", function () {
        CustomMenus.classificationRoute();
    }),
    importanceRoute: withAccessCheck("importance", function () {
        CustomMenus.importanceRoute();
    }),
    privacyRoute: withAccessCheck("privacy", function () {
        CustomMenus.privacyRoute();
    }),
    toDoListRoute: withAccessCheck("todolist", function () {
        CustomMenus.toDoListRoute();
    }),
    assemblyRoute: withAccessCheck("assembly", function () {
        CustomMenus.assemblyRoute();
    }),
    activityLogRoute: withAccessCheck("activitylog", function () {
        CustomMenus.activityLogRoute();
    }),
    menuRoute: withAccessCheck("menu", function () {
        CustomMenus.menuRoute();
    }),
    actionRoute: withAccessCheck("action", function () {
        CustomMenus.actionRoute();
    }),
    tabRoute: withAccessCheck("tab", function () {
        CustomMenus.tabRoute();
    }),
    userRoute: withAccessCheck("user", function () {
        CustomMenus.userRoute();
    }),
    roleRoute: withAccessCheck("role", function () {
        CustomMenus.roleRoute();
    }),
    nodeRoute: withAccessCheck("node", function () {
        CustomMenus.nodeRoute();
    }),
    categoryRoute: withAccessCheck("category", function () {
        CustomMenus.categoryRoute();
    }),
    attachmentfolderRoute: withAccessCheck("attachmentfolder", function () {
        CustomMenus.attachmentfolderRoute();
    }),
    lookupRoute: withAccessCheck("lookup", function () {
        CustomMenus.lookupRoute();
    }),
    documenttypeRoute: withAccessCheck("documenttype", function () {
        CustomMenus.documenttypeRoute();
    }),
    searchRoute: function (term) {
        CustomMenus.searchRoute(term);
    },
    advanceSearchRoute: withAccessCheck("advanceSearch", function (delegationId) {
        CustomMenus.advanceSearchRoute(delegationId);
    }),
    advanceSearchConfigurationRoute: withAccessCheck("advanceSearchConfiguration", function () {
        CustomMenus.advanceSearchConfigurationRoute();
    }),
    noteRoute: function () {
        CustomMenus.noteRoute();
    },
    barcodeConfigurationRoute: withAccessCheck("barcodeconfiguration", function () {
        CustomMenus.barcodeConfigurationRoute();
    }),
    filingPlanRoute: withAccessCheck("filingPlan", function () {
        CustomMenus.filingPlanRoute();
    }),
    nonArchivedAttachmentsTypesRoute: withAccessCheck("nonarchivedattachmentstypes", function () {
        CustomMenus.nonArchivedAttachmentsTypesRoute();
    }),
    nodeListRoute: withAccessCheck("nodelist", function () {
        CustomMenus.nodeListRoute();
    }),
    bookmarksRoute: withAccessCheck("bookmarks", function () {
        CustomMenus.bookmarksRoute();
    }),
    systemDashboardRoute: withAccessCheck("systemdashboard", function () {
        CustomMenus.systemDashboardRoute();
    }),
    userDashboardRoute: withAccessCheck("userdashboard", function () {
        CustomMenus.userDashboardRoute();
    }),
    averageDurationForCorrespondenceCompletionRoute: withAccessCheck("averagedurationforcorrespondencecompletion", function () {
        CustomMenus.averageDurationForCorrespondenceCompletionRoute();
    }),
    averageDurationForCorrespondenceDelayRoute: withAccessCheck("averagedurationforcorrespondencedelay", function () {
        CustomMenus.averageDurationForCorrespondenceDelayRoute();
    }),
    averageDurationForTransferCompletionRoute: withAccessCheck("averagedurationfortransfercompletion", function () {
        CustomMenus.averageDurationForTransferCompletionRoute();
    }),
    averageDurationForTransferDelayRoute: withAccessCheck("averagedurationfortransferdelay", function () {
        CustomMenus.averageDurationForTransferDelayRoute();
    }),
    reportStatisticalCorrespondencesRoute: withAccessCheck("reportstatisticalcorrespondences", function () {
        CustomMenus.reportStatisticalCorrespondencesRoute();
    }),
    reportCorrespondenceDetailFilterRoute: withAccessCheck("reportcorrespondencedetailfilter", function () {
        CustomMenus.reportCorrespondenceDetailFilterRoute();
    }),
    reportInProgressTransfersRoute: withAccessCheck("reportinprogresstransfers", function () {
        CustomMenus.reportInProgressTransfersRoute();
    }),
    reportTransfersSentToStructureRoute: withAccessCheck("reportTransfersSentToStructure", function () {
        CustomMenus.reportTransfersSentToStructureRoute();
    }),
    reportOutgoingFromDepartmentRoute: withAccessCheck("reportOutgoingFromDepartment", function () {
        CustomMenus.reportOutgoingFromDepartmentRoute();
    }),
    reportCompletedTransfersRoute: withAccessCheck("reportcompletedtransfers", function () {
        CustomMenus.reportCompletedTransfersRoute();
    }),
    reportOperationByUserRoute: withAccessCheck("reportOperationByUser", function () {
        CustomMenus.reportOperationByUserRoute();
    }),
    reportOperationByCorrespondenceRoute: withAccessCheck("reportOperationByCorrespondence", function () {
        CustomMenus.reportOperationByCorrespondenceRoute();
    }),
    reportInProgressCorrespondencesRoute: withAccessCheck("reportinprogresscorrespondences", function () {
        CustomMenus.reportInProgressCorrespondencesRoute();
    }),
    reportCompletedCorrespondencesRoute: withAccessCheck("reportcompletedcorrespondences", function () {
        CustomMenus.reportCompletedCorrespondencesRoute();
    }),
    entityGroupRoute: withAccessCheck("entitygroup", function () {
        CustomMenus.entityGroupRoute();
    }),
    adminlogRoute: withAccessCheck("adminlog", function () {
        CustomMenus.adminlogRoute();
    }),
    favoriteStructuresRoute: withAccessCheck("favoriteStructures", function () {
        CustomMenus.favoriteStructuresRoute();
    }),
    distributionListRoute: withAccessCheck("distributionList", function () {
        CustomMenus.distributionListRoute();
    }),
    manageCorrespondenceRoute: withAccessCheck("manageCorrespondence", function () {
        CustomMenus.manageCorrespondenceRoute();
    }),
    manageStructureUsersCorrespondencesRoute: withAccessCheck("manageStructureUsersCorrespondences", function () {
        CustomMenus.manageStructureUsersCorrespondencesRoute();
    }),
    autoForwardRoute: withAccessCheck("autoForward", function () {
        CustomMenus.autoForwardRoute();
    }),
    movetransfersRoute: withAccessCheck("movetransfers", function () {
        CustomMenus.movetransfers();
    }),
    liststructureusersRoute: withAccessCheck("liststructureusers", function () {
        CustomMenus.liststructureusers();
    }),
    listallstructureusersRoute: withAccessCheck("listallstructureusers", function () {
        CustomMenus.listallstructureusers();
    }),
    committeeRoute: withAccessCheck("committee", function () {
        CustomMenus.committee();
    }),
    meetingAgendaRoute: withAccessCheck("createMeetingAgenda", function () {
        CustomMenus.createMeeatinAgenda();
    }), manageFollowUpRoute: withAccessCheck("manageFollowUp", function () {
        CustomMenus.manageFollowUpRoute();
    }), userNodeRoute: withAccessCheck("userNode", function () {
        CustomMenus.userNodeRoute();
    }),
    g2gRoute: function (ViewName) {
        CustomNodes.g2g(ViewName);
    },
    listsecureusers: withAccessCheck("listsecureusers", function () {
        CustomMenus.listSecureUsers();
    }),
    followUpPanelRoute: withAccessCheck("followUpPanel", function () {
        CustomMenus.followUpPanel();
    }),
});
var app = new appRouter();
var gLocked = false;
var gOwnerUserId;
var gInboxTableName = "grdInboxItems";
var inboxGidContainerName = "inboxListContainer-mask";
var gSentTableName = "grdSentItems";
var sentGridContainerName = "sentListContainer-mask";
var gMyRequestsTableName = "grdMyRequestsItems";
var gManageFollowUpTableName = "grdManageFollowUpItems";
var followUpGridContainerName = "manageFollowUpListContainer-mask";
var CustomTabs = (function (E) {
    E = {};

    E.openTransferDetail = function (transferId, delegationId, ComponentId, tabId, documentId, readOnly, fromSent, categoryId, fromInbox, DocumentIsCompleted, actionName, modalComponentId, nodeId, fromFollowUp, fromRejectedDocument, resendModel) {
        
        var params = { "id": transferId };
        if (delegationId !== null) {
            params.delegationId = delegationId;
        }
        if (fromRejectedDocument) {
            params.fromRejectedDocument = fromRejectedDocument;
        }

        var fromStructureInbox = false;
        if (window.location.href.indexOf('StructureInbox') >= 0) {
            params.fromStructure = true;
            fromStructureInbox = true;
        }

        Common.ajaxGet("/Transfer/GetTransferInfoById", params, function (data) {
            gLocked = false;
            var wrapper = $("#" + ComponentId + '_tab' + tabId);
            var model = new MyTransfer.MyTransfer();
            model.privacies = new CoreComponents.Lookup.Privacies().get(window.language);
            if (!readOnly) {
                readOnly = data.viewMode;
            }
            model.parentTransferId = (data.parentTransferId == null || data.parentTransferId == undefined) ? null : data.parentTransferId;
            model.isCCed = data.cced;
            model.readonly = readOnly;
            model.transferId = transferId;
            model.delegationId = delegationId;
            model.sendingEntity = data.sendingEntity;
            model.receivingEntity = data.receivingEntity;
            model.subject = data.subject;
            model.fromStructure = data.fromStructure;
            model.fromUser = data.fromUser;
            model.toStructure = data.toStructure;
            model.toUser = data.toUser;
            model.purpose = data.purpose;
            model.priority = data.priority;
            model.createdDate = data.createdDate;
            model.dueDate = data.dueDate;
            model.openedDate = data.openedDate;
            model.closedDate = data.closedDate;
            model.instruction = data.instruction;
            model.privacyId = data.privacyId;
            model.fromStructureId = data.fromStructureId;
            model.documentId = documentId;
            model.receivingEntityId = data.receivingEntityId;
            model.ownerUserId = data.ownerUserId;
            model.instruction = data.instruction;
            model.VoiceNote = data.voiceNote;
            model.toStructureId = data.toStructureId;
            model.replyToEntity = [{ id: data.fromStructureId, text: data.fromStructure }];
            model.sentToUser = data.sentToUser;
            model.withViewer = window.OpenCorrespondenceMode === CorrespondenceMode.WithViewer;
            model.fromSent = fromSent;
            model.actionsComponentId = modalComponentId;
            model.parentComponentId = ComponentId;
            model.categoryId = categoryId;
            model.fromInbox = fromInbox;
            model.DocumentIsCompleted = DocumentIsCompleted;
            model.closedTransfer = data.closedDate ? true : false;
            model.byTemplate = data.byTemplate;
            model.forSignature = data.forSignature;
            model.hasReferenceNumber = data.hasReferenceNumber;
            model.workflowStepId = data.workflowStepId;
            model.initiatorUser = data.initiatorUser;
            model.isWorkflowReturned = data.isWorkflowReturned;
            model.fromVip =  window.InboxMode === 'InboxVIPView' || window.InboxMode === 'LocalVIPView';
            if (modalComponentId != undefined && modalComponentId != null && modalComponentId != "null")
                model.fromVip = false;
            model.MeetingAgenda = (categoryId == window.MeetingAgendaId) ? true : false;
            model.hasAttachments = data.hasAttachments;
            model.hasUserCofigureSignature = data.hasUserCofigureSignature;
            model.nextStepUserName = data.nextStepUserName;
            model.allowSign = data.allowSign;
            model.referenceNumber = data.referenceNumber;
            model.attachmentExtention = data.attachmentExtention;
            model.templateHasSignature = data.templateHasSignature;
            model.tabId = tabId;
            model.isSigned = data.isSigned;
            model.isDocumentSigned = data.isDocumentSigned;
            if (actionName != undefined)
                model.actionName = actionName;
            var currentCategoryModel = new CategoryModel().findFullById(categoryId);
            if (typeof currentCategoryModel !== 'undefined' && currentCategoryModel !== "" && currentCategoryModel !== null) {
                if (currentCategoryModel.basicAttribute !== "" && currentCategoryModel.basicAttribute !== null) {
                    let basicAttributes = JSON.parse(currentCategoryModel.basicAttribute);
                    if (basicAttributes.length > 0) {
                        let receivingEntityObj = $.grep(basicAttributes, function (e) {
                            return e.Name === "ReceivingEntity";
                        });
                        if (receivingEntityObj[0].BroadcastReceivingEntity && receivingEntityObj[0].Type === "internal") {
                            model.isInternalBroadcast = true;
                            model.isBroadcast = true;
                        } else if (receivingEntityObj[0].BroadcastReceivingEntity) {
                            model.isBroadcast = true;
                        }
                    }
                }
            }
            model.nodeId = nodeId;
            model.fromFollowUp = fromFollowUp;
            model.fromRejectedDocument = fromRejectedDocument;
            model.documentCarbonCopy = data.documentCarbonCopy;
            model.resendData = resendModel;
            model.fromStructureInbox = fromStructureInbox;
            var myTransferView = new MyTransfer.MyTransferView(wrapper, model);
            myTransferView.render();
          
            gOwnerUserId = data.ownerUserId;

        }, null, null, null, false);
    }


    E.openVisualTracking = function (delegationId, ComponentId, tabId, documentId) {

        gLocked = false;
        var wrapper = $("#" + ComponentId + '_tab' + tabId);
        var model = new VisualTracking.VisualTracking();
        model.delegationId = delegationId;
        model.documentId = documentId;
        var documentView = new VisualTracking.VisualTrackingView(wrapper, model);
        documentView.render();
        if (window.OpenCorrespondenceMode !== "OpenCorrespondenceDefault") {
            $("#" + model.ComponentId + "_trackingChart").css("height", "353px");
        }
        //let target = "#" + gSelf.model.ComponentId + "_visualTracking";
        //let actions = initTabActions(gSelf.model.tabsWithStatic, gSelf.model.tabsActions, gSelf.model.readonly, target.split("_")[2]);
        //SecurityMatrix.getTabActions(actions, "#" + $(gSelf.refs[gSelf.model.ComponentId]).find('div[id*="' + target.split("_")[2] + '"]').attr('id'), false);
        //SecurityMatrix.InitTabContextMenu(actions);

    }
    E.openEvent = function (documentId, readOnly, delegationId, ComponentId, tabId, actionName) {
        gLocked = false;
        var wrapper = $("#" + ComponentId + '_tab' + tabId);
        var model = new EventList.Event();
        model.documentId = documentId;
        model.readOnly = readOnly;
        model.delegationId = delegationId;
        if (actionName != undefined)
            model.actionName = actionName;
        var EventView = new EventList.EventView(wrapper, model);
        EventView.render();

    }
    E.openInstruction = function (documentId, readOnly, ComponentId, tabId, actionName) {
        gLocked = false;
        var wrapper = $("#" + ComponentId + '_tab' + tabId);
        var model = new InstructionList.Instruction();
        model.documentId = documentId;
        model.readOnly = readOnly;
        if (actionName != undefined)
            model.actionName = actionName;
        var InstructionView = new InstructionList.InstructionView(wrapper, model);
        InstructionView.render();

    }
    E.openNoteByTask = function (transferId, documentId, readOnly, delegationId, ComponentId, tabId, actionName, categoryId, fromRejectedDocument) {

        var params = { "id": transferId };
        if (delegationId !== null) {
            params.delegationId = delegationId;
        }
        if (fromRejectedDocument) {
            params.fromRejectedDocument = fromRejectedDocument;
        }
        Common.ajaxGet("/Transfer/GetTransferInfoById", params, function (data) {
            
            if (!readOnly) {
                readOnly = data.viewMode;
            }
        }, null, null, null, false);

        gLocked = false;
        var model = new NoteList.Note();
        model.transferId = transferId;
        model.documentId = documentId;
        model.delegationId = delegationId;
        model.readOnly = readOnly;
        if (actionName != undefined)
            model.actionName = actionName;

        var wrapper = $("#" + ComponentId + '_tab' + tabId);
        var noteView = new NoteList.NoteView(wrapper, model); 68
        noteView.render();
        //let target = "#" + gSelf.model.ComponentId + "_notes";
        //let actions = initTabActions(gSelf.model.tabsWithStatic, gSelf.model.tabsActions, gSelf.model.readonly, target.split("_")[2]);
        //SecurityMatrix.getTabActions(actions, "#" + $(gSelf.refs[gSelf.model.ComponentId]).find('div[id*="noteToolbarContainer"]').attr('id'), true);
        //SecurityMatrix.InitTabContextMenu(actions);
    }
    E.openNonArchivedAttachmentsByTask = function (transferId, documentId, readOnly, delegationId, ComponentId, tabId, actionName, fromRejectedDocument) {

        var params = { "id": transferId };
        if (delegationId !== null) {
            params.delegationId = delegationId;
        }
        if (fromRejectedDocument) {
            params.fromRejectedDocument = fromRejectedDocument;
        }
        Common.ajaxGet("/Transfer/GetTransferInfoById", params, function (data) {

            if (!readOnly) {
                readOnly = data.viewMode;
            }
        }, null, null, null, false);
        gLocked = false;
        var model = new NonArchivedAttachmentList.NonArchivedAttachments();
        model.transferId = transferId;
        model.documentId = documentId;
        model.delegationId = delegationId;
        model.readOnly = readOnly;
        if (actionName != undefined)
            model.actionName = actionName;
        var wrapper = $("#" + ComponentId + '_tab' + tabId);
        var nonArchivedAttachmentList = new NonArchivedAttachmentList.NonArchivedAttachmentsView(wrapper, model);
        nonArchivedAttachmentList.render();
        //let target = "#" + gSelf.model.ComponentId + "_nonArchivedAttachments";
        //let actions = initTabActions(gSelf.model.tabsWithStatic, gSelf.model.tabsActions, gSelf.model.readonly, target.split("_")[2]);
        //SecurityMatrix.getTabActions(actions, "#" + $(gSelf.refs[gSelf.model.ComponentId]).find('div[id*="nonArchivedAttachmentsToolbarConatiner"]').attr('id'), true);//todo
        //SecurityMatrix.InitTabContextMenu(actions);

    }
    E.openLinkedCorrespondences = function (transferId, documentId, delegationId, readOnly, ComponentId, tabId, actionName, categoryId, fromManageCorrespondance, fromRejectedDocument) {
        var params = { "id": transferId };
        if (delegationId !== null) {
            params.delegationId = delegationId;
        }
        if (fromRejectedDocument) {
            params.fromRejectedDocument = fromRejectedDocument;
        }
        Common.ajaxGet("/Transfer/GetTransferInfoById", params, function (data) {

            if (!readOnly) {
                readOnly = data.viewMode;
            }
        }, null, null, null, false);
        gLocked = false;
        var wrapper = $("#" + ComponentId + '_tab' + tabId);
        var model = new DocumentLinkCorrespondence.LinkedCorrespondence();
        model.transferId = transferId;
        model.documentId = documentId;
        model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
        model.categories = new Categories().get(window.language);
        model.delegationId = delegationId;
        model.readOnly = readOnly;
        if (fromManageCorrespondance != 'undefined')
            model.fromManageCorrespondance = fromManageCorrespondance;
        
        if (actionName != 'undefined')
            model.actionName = actionName;

        var documentView = new DocumentLinkCorrespondence.LinkedCorrespondenceView(wrapper, model);
        documentView.render();
        //let target = "#" + gSelf.model.ComponentId + "_linkedDocument";
        //let actions = initTabActions(gSelf.model.tabsWithStatic, gSelf.model.tabsActions, gSelf.model.readonly, target.split("_")[2]);
        //SecurityMatrix.getTabActions(actions, "#" + $(gSelf.refs[gSelf.model.ComponentId]).find('div[id*="linkedDocumentToolbarContainer"]').attr('id'), true);
        //SecurityMatrix.InitTabContextMenu(actions);
    }
    E.openAttachments = function (transferId, documentId, delegationId, readOnly, categoryId, fromInbox, fromDraft, isCced, ComponentId, tabId, parentLinkedDocumentId, actionName, fromRejectedDocument, attachmentVersion) {
        var params = { "id": transferId };
        params.categoryId = categoryId;
        if (delegationId !== null) {
            params.delegationId = delegationId;
        }
        if (fromRejectedDocument) {
            params.fromRejectedDocument = fromRejectedDocument;
        }
        var showAttachmentProperties = false;
        Common.ajaxGet("/Transfer/GetTransferInfoById", params, function (data) {
            if (!readOnly) {
                readOnly = data.viewMode;
            }
            showAttachmentProperties = data.showAttachmentProperties
            attachmentVersion = (attachmentVersion == undefined || attachmentVersion == null) ? data.attachmentVersion : attachmentVersion
        }, null, null, null, false);
        gLocked = false;
        var wrapper = $("#" + ComponentId + '_tab' + tabId);
        var model = new Attachment.Attachment();
        model.transferId = transferId;
        model.documentId = documentId;
        model.delegationId = delegationId;
        model.categoryId = categoryId;
        model.fromInbox = fromInbox;
        model.fromDraft = fromDraft;
        model.isCced = isCced;
        model.ownerUserId = gOwnerUserId;
        model.parentLinkedDocumentId = parentLinkedDocumentId;
        model.parentComponentId = ComponentId;
        model.fromVip = window.InboxMode === 'InboxVIPView' || window.InboxMode === 'LocalVIPView';
        model.showAttachmentProperties = showAttachmentProperties;
        model.attachmentVersion = attachmentVersion;
        model.fromRejectedDocument = fromRejectedDocument;
        if (window.FollowUpCategory == model.categoryId)
            model.openCorrespondenceDefault = true;
        if (actionName != undefined)
            model.actionName = actionName;

        var _isFollowUpNode = window.FollowUpCategory == model.categoryId ? true : false;
        if (_isFollowUpNode == true)
            model.readOnly = false;
        else
            model.readOnly = readOnly;
        var attachmentView = new Attachment.AttachmentView(wrapper, model);
        attachmentView.render();
       
    }
    E.openActivityLog = function (documentId, delegationId, ComponentId, tabId) {

        gLocked = false;
        var wrapper = $("#" + ComponentId + '_tab' + tabId);
        var model = new ActivityLogTimeline.ActivityLogTimeline();
        model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
        model.documentId = documentId;
        model.delegationId = delegationId;
        model.parentComponentId = ComponentId;
        model.tabId = tabId;
        var documentView = new ActivityLogTimeline.ActivityLogTimelineView(wrapper, model);
        documentView.render();
        //let target = "#" + gSelf.model.ComponentId + "_activityLog";
        //let actions = initTabActions(gSelf.model.tabsWithStatic, gSelf.model.tabsActions, gSelf.model.readonly, target.split("_")[2]);
        //SecurityMatrix.getTabActions(actions, "#" + $(gSelf.refs[gSelf.model.ComponentId]).find('div[id*="' + target.split("_")[2] + '"]').attr('id'), false);
        //SecurityMatrix.InitTabContextMenu(actions);
    }
    E.openTransfersHistory = function (transferId, documentId, sentToUser, delegationId, ComponentId, tabId) {

        gLocked = false;
        var wrapper = $("#" + ComponentId + '_tab' + tabId);
        var model = new TransferHistory.TransferHistory();
        model.transferId = transferId;
        model.documentId = documentId;
        model.delegationId = delegationId;
        model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
        model.sentToUser = sentToUser;
        var TransferHistoryView = new TransferHistory.TransferHistoryView(wrapper, model);
        TransferHistoryView.render();
        //let target = "#" + gSelf.model.ComponentId + '_transferHistory';
        //let actions = initTabActions(gSelf.model.tabsWithStatic, gSelf.model.tabsActions, gSelf.model.readonly, target.split("_")[2]);
        //SecurityMatrix.getTabActions(actions, "#" + $(gSelf.refs[gSelf.model.ComponentId]).find('div[id*="' + target.split("_")[2] + '"]').attr('id'), false);
        //SecurityMatrix.InitTabContextMenu(actions);
    }

    E.openDocument = function (data, readOnly, ComponentId, tabId, documentId, transferId, categoryId, fromDraft, fromRejectedDocument, actionName, modalComponentId) {
    
        gLocked = false;
        var wrapper = $("#" + ComponentId + '_tab' + tabId);
        var model = new Document.Document();
        model.fromDraft = fromDraft;
        model.fromRejectedDocument = fromRejectedDocument;
        model.id = documentId;
        if (tabId == window.BasketAttribute ) {
            model.actionName = "Attribute.Save";
        }
        model.categoryId = data.categoryId;
        model.categoryName = data.categoryName;
        model.referenceNumber = data.referenceNumber;
        model.subject = data.subject;
        model.basicAttributes = data.basicAttributes !== null && data.basicAttributes !== "" ? JSON.parse(data.basicAttributes) : [];
        model.customAttributes = data.customAttributes !== null && data.customAttributes !== "" ? JSON.parse(data.customAttributes) : null;
        model.customAttributesTranslation = data.customAttributesTranslation !== null && data.customAttributesTranslation !== "" ? JSON.parse(data.customAttributesTranslation) : null;
        model.formData = data.formData !== null && data.formData !== "" ? JSON.parse(data.formData) : [];
        model.receivers = data.receivers;
        model.sendingEntityId = data.sendingEntityId;
        model.dueDate = data.dueDate;
        model.documentDate = data.documentDate;
        model.createdDate = data.createdDate;
        model.createdByUser = data.createdByUser;
        model.priorityId = data.priorityId;
        model.privacyId = data.privacyId;
        model.carbonCopy = data.carbonCopy;
        model.importanceId = data.importanceId;
        model.classificationId = data.classificationId;
        model.sendingEntity = data.sendingEntity;
        model.receivingEntities = data.receivingEntities;
        model.carbonCopies = data.carbonCopies;
        model.classification = data.classification;
        model.documentType = data.documentType;
        model.readonly = (data.fromAccept ? true : (readOnly && data.enableEdit ? !data.enableEdit : readOnly));
        model.delegationId = data.delegationId;
        model.userStructures = new IdentityService().getUserStructures(window.language);
        model.privacies = new CoreComponents.Lookup.Privacies().get(window.language);
        model.priorities = new CoreComponents.Lookup.Priorities().get(window.language);
        model.importances = new CoreComponents.Lookup.Importances().get(window.language);
        model.createdByStructureId = data.createdByStructureId;
        model.body = data.body;
        model.externalReferenceNumber = data.externalReferenceNumber;
        model.keyword = data.keyword;
        model.enableEdit = data.enableEdit;
        model.transferId = transferId;
        model.senderPerson = data.senderPerson;
        model.receiverPerson = data.receiverPerson;
        model.isExternalReceiver = data.isExternalReceiver;
        model.isExternalSender = data.isExternalSender;
        model.hasAttachments = data.attachmentCount > 0;
        model.templateHasSignature = data.TemplateHasSignature;
        model.isSigned = data.IsSigned;
        model.fromVip = window.InboxMode === 'InboxVIPView' || window.InboxMode ==='LocalVIPView';
        if (modalComponentId != undefined && modalComponentId != null && modalComponentId != "null")
            model.fromVip = false;
        model.isFollowUp = (data.categoryId == window.FollowUpCategory);
        model.fullControl = data.fullControl;
        model.isTaskCreator = (data.createdByUserId == $("#hdUserId").val());
        model.status = data.status;
        model.isCompleted = new CoreComponents.Lookup.Statuses().findById(data.status, 'en').text.toLowerCase() === "completed";
        //model.actioncomponentId = ComponentId;
        model.actionComponentId = modalComponentId;
        model.parentComponentId = ComponentId;
        model.g2gInternalId = data.g2GInternalId;
        if (actionName != undefined)
            model.actionName = actionName;

        if (model.isFollowUp && !model.isCompleted) {
            model.enableEdit = true;
            model.readonly = false;
        }
        model.requestStatus = data.requestStatus;
        model.byTemplate = data.byTemplate;
        model.tabId = tabId;
        model.documentCarbonCopy = data.documentCarbonCopy;
        model.hasUserCofigureSignature = data.hasUserCofigureSignature;
        model.templateHasSignature = data.templateHasSignature;
        model.attachmentExtention = data.attachmentExtention;
        model.allowSign = data.allowSign;
        model.isSigned = data.isSigned;
        model.attachmentId = data.attachmentId;
        var documentView = new Document.DocumentView(wrapper, model);
        setTimeout(function () {
            $('#txtCustomAttributeSubject').focus();
        }, 500);
        let url = readOnly ? '/Document/Edit' : '/Document/Save';
        documentView.render({
            url: url,
            params: {
                'CategoryId': data.categoryId,
                'CategoryName': data.categoryName,
                '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
            },
            callback: function (response) {
                if (readOnly) {
                    GridCommon.RefreshCurrentPage("grdInboxItems", false);
                } else {
                    GridCommon.RefreshCurrentPage("grdDraftItems", false);
                }
            }
        }, {
            url: '/Document/Send',
            params: {
                '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
            },
            callback: function (response) {
                TreeNode.addToNodeCount(TreeNode.MyRequests, 1, 1);
                TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
                Common.alertMsg(Common.format(Resources.ReferenceNumberMsg, response), function () {
                    var nodeId = $('[data-inherit="' + TreeNode.MyRequests + '"]').first().data("id");
                    if (nodeId !== undefined && $.isNumeric(nodeId)) {
                        window.location.href = '#myrequests/' + nodeId;
                    } else {
                        window.location.href = '/';
                    }
                });
            }
        });
        //let target = "#" + gSelf.model.ComponentId + '_documentMetadata';
        //let actions = initTabActions(gSelf.model.tabsWithStatic, gSelf.model.tabsActions, gSelf.model.readonly, target.split("_")[2]);
        //SecurityMatrix.getTabActions(actions, "#" + $(gSelf.refs[gSelf.model.ComponentId]).find('div[id*="' + target.split("_")[2] + '"]').attr('id'), false);
        //SecurityMatrix.InitTabContextMenu(actions);






    }

    E.openDocumentBasket = function (documentId, basketId, ComponentId, tabId, actionName) {
        var params = { id: documentId, basketId: basketId };
        Common.ajaxGet('/Document/GetDocument', params, function (response) {
            E.openDocument(response, true, ComponentId, tabId, documentId, null, 3, false,false, actionName);

        }, null, true);
    }

    E.openDocumentByTransfer = function (transferId, delegationId, ComponentId, tabId, documentId, readOnly, categoryId, fromDraft, fromRejectedDocument, actionName, modalComponentId, fromSearch) {
        if (window.location.hash.includes('#search') || window.location.hash.includes('#tasksearch') || fromSearch) {
            Common.ajaxGet('/Document/GetSearchDocument', { id: documentId }, function (data) {
                E.openDocument(data, readOnly, ComponentId, tabId, documentId, transferId, categoryId, fromDraft, fromRejectedDocument, actionName, modalComponentId);
            }, null, true);
        }
        else {
            var params = { id: transferId };
            if (delegationId !== null) {
                params.delegationId = delegationId;
            }


            if (fromDraft == true || fromRejectedDocument == true || transferId == null) {
                Common.ajaxGet('/Document/Get', { id: documentId, delegationId: delegationId, fromRejectedDocument: fromRejectedDocument }, function (data) {
                    if (window.location.hash.toLowerCase().includes("sent")) {
                        data.enableEdit = false;
                        if (data.delegationId == undefined)
                            data.delegationId = delegationId;

                    }
                    if (window.location.hash.includes("Draft")) {
                            data.delegationId = delegationId;

                    }
                    E.openDocument(data, readOnly, ComponentId, tabId, documentId, transferId, categoryId, fromDraft, fromRejectedDocument, actionName, modalComponentId);
                }, null, true);
            }
            else {
                if (window.location.href.indexOf("StructureInbox") >= 0)
                    params.fromStructureInbox = true;

                Common.ajaxGet('/Document/GetDocumentByTransferId', params, function (data) {
                    
                    if (window.location.hash.toLowerCase().includes("sent")) {
                        
                        data.enableEdit = false;
                    }

                    E.openDocument(data, true, ComponentId, tabId, documentId, transferId, categoryId, fromDraft, fromRejectedDocument, actionName, modalComponentId);
                }, null, true);
            }
        }


    }

    E.openAgendaTopicsList = function (documentId, delegationId, ComponentId, tabId, readonly, categoryId, actionName) {
        
        var wrapper = $("#" + ComponentId + '_tab' + tabId);
        var model = new AgendaTopicsList.AgendaTopicsList();
        model.documentId = documentId;
        model.delegationId = delegationId;
        model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
        model.readOnly = readonly;
        model.MeetingResolution = (categoryId == window.MeetingResolutionId) ? true : false;
        if (actionName != undefined)
            model.actionName = actionName;
        var documentView = new AgendaTopicsList.AgendaTopicsListView(wrapper, model);
        documentView.render();
    };

    E.openAssigneeTab = function (documentId, componentId, tabId) {
        let param = {
            'documentId': documentId
        };
        Common.ajaxGet("Assignee/GetAssigneePermission", param, function (data) {
            var wrapper = $("#" + componentId + '_tab' + tabId);
            var model = new Assignee.Assignee();
            model.documentId = documentId;
            model.assigneeOperationPermission = data.assigneeOperationPermission;
            var assigneeView = new Assignee.AssigneeView(wrapper, model);
            assigneeView.render();

            return data;
        }, function () { Common.showScreenErrorMsg(); }, true);
    };

    E.openMyFollowUpTab = function (transferId, delegationId, componentId, tabId, documentId, readOnly, categoryId, fromDraft, actionName) {
        var wrapper = $("#" + componentId + '_tab' + tabId);
        var model = new MyFollowUp.MyFollowUp();

        model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
        model.categories = new Categories().get(window.language);
        model.transferId = transferId;
        model.delegationId = delegationId;
        model.componentId = componentId;
        model.tabId = tabId;
        model.documentId = documentId;
        model.readOnly = readOnly;
        model.categoryId = categoryId;
        model.fromDraft = fromDraft;
        model.actionName = actionName;

        var myFollowUpView = new MyFollowUp.MyFollowUpView(wrapper, model);
        myFollowUpView.render();
    }

    E.openFollowUpDetails = function (followupId, componentId, tabId, readOnly, categoryId, actionName, modalComponentId) {
        Common.ajaxGet('/FollowUp/Get', { id: followupId }, function (result) {
            if (result.status) {
                if (result.model) {
                    var data = result.model;
                    var wrapper = $("#" + componentId + '_tab' + tabId);
                    var model = new FollowUpDetails.FollowUpDetailsModel();

                    model.followUpId = followupId;
                    model.followupStatusId = data.followUpStatusId;
                    model.followUpStatus = data.followUpStatusName;
                    model.followUpDocumentId = data.followUpDocument.id;
                    model.followUpDocument = data.followUpDocument;
                    model.originalDocumentId = data.originalDocument.id;
                    model.originalDocument = data.originalDocument;
                    model.createdByUserId = data.createdByUserId;
                    model.createdByUser = data.createdByUser;
                    model.readonly = readOnly != null ? readOnly: data.readonly;
                    model.actions = actionName;
                    model.showBackButton = true;
                    model.parentLinkedDocumentId = modalComponentId;
                    model.parentComponentId = componentId;
                    model.IsPrivate = data.isPrivate;
                    model.followUpUserRole = data.followUpUserRole;
                    model.followUpSendingEntity = data.sendingEntity;
                    model.followUpReceivingEntity = data.receivingEntities;
                    model.followUpPriority = data.priority;
                    model.followUpSubject = data.subject;
                    model.odAttachmentVersion = data.odAttachmentVersion,
                    model.tabId = tabId;
                    model.Language = window.language;
                    model.dueDate = data.dueDate;
                    if (actionName != undefined)
                        model.actionName = actionName;

                    var view = new FollowUpDetails.FollowUpDetailsView(wrapper, model);
                    view.render();
                }
            } else {
                if (result.message) {
                    Common.showScreenErrorMsg(result.message);
                }
            }
        }, function () { Common.showScreenErrorMsg(); }, true);
    }
    E.openFollowUpUsersTab=function(teamId, followupId, documentId, delegationId, componentId, tabId, readOnly, categoryId, actionName)
    {
        Common.ajaxGet('/FollowUp/Get', { id: followupId }, function (result) {
            if (result.status) {
                if (result.model) {
                    var data = result.model;
                    var wrapper = $("#" + componentId + '_tab' + tabId);
                    var model = new FollowUpUsers.FollowUpUsers();
    

                    model.followUpId = followupId;
                    model.followupStatusId = data.followUpStatusId;
                    model.followUpStatus = data.followUpStatusName;
                    model.followUpDocumentId = data.followUpDocument.id;
                    model.followUpDocument = data.followUpDocument;
                    model.originalDocumentId = data.originalDocument.id;
                    model.originalDocument = data.originalDocument;
                    model.createdByUserId = data.createdByUserId;
                    model.createdByUser = data.createdByUser;
                    model.readOnly = data.readonly;
                    model.actions = actionName;
                    model.showBackButton = true;
                    model.parentLinkedDocumentId = null;
                    model.parentComponentId = componentId;
                    model.IsPrivate = data.isPrivate;
                    model.followUpUserRole = data.followUpUserRole;
                    model.followUpSendingEntity = data.sendingEntity;
                    model.followUpReceivingEntity = data.receivingEntities;
                    model.followUpPriority = data.priority;
                    model.followUpSubject = data.subject;
                    model.Language = window.language;
                    if (actionName != undefined)
                        model.actionName = actionName;

                    var view = new FollowUpUsers.FollowUpUsersView(wrapper, model);
                    view.render();
                }
            } else {
                if (result.message) {
                    Common.showScreenErrorMsg(result.message);
                }
            }
        }, function () { Common.showScreenErrorMsg(); }, true);
    }
    E.openExportedRequests = function (documentId, ComponentId, tabId) {
        gLocked = false;
        var wrapper = $("#" + ComponentId + '_tab' + tabId);
        var model = new ExportRequests.OutgoingIncomingRequest();
        model.documentId = documentId;
        var OutgoingIncomingRequestView = new ExportRequests.OutgoingIncomingRequestView(wrapper, model);
        OutgoingIncomingRequestView.render();
    }
    return E;
}(CustomTabs));
var transferComponent;
var CustomActions = (function (E) {

    E = {};
    var gTableName = "grdSearchItems";

    E.openSearchDocumentEdit = function (data, delegationId, fromLink) {
        if (data.statusId !== SystemStatus.Completed && !data.isSigned) {
            var params = {};
            if (delegationId !== null) {
                params.delegationId = delegationId;
            }
            var url = "";
            if (window.location.hash.includes("#search")) {
                params = { id: data.id };
            }
            else {
                params = { id: data.documentId };
            }

            Common.ajaxGet("/Document/GetSearchDocumentEdit", params, function (response) {

                if (response && response === "NoAccess") {
                    Common.alertMsg(Resources.NoPermission);
                }
                else {
                    if (!response.id) {
                        return;
                    }
                    var wrapper = $(".modal-window");
                    wrapper.empty();
                    var model = new SearchLinkedDocumnet.SearchLinkedDocumnet();
                    var searchLinkedDocumnet = new SearchLinkedDocumnet.SearchLinkedDocumnetView(wrapper, model);

                    searchLinkedDocumnet.render();
                    model = new Document.Document();
                    model.id = params.id;
                    model.categoryId = response.categoryId;
                    model.categoryName = response.categoryName;
                    model.referenceNumber = response.referenceNumber;
                    model.subject = response.subject;
                    model.basicAttributes = response.basicAttributes !== null && response.basicAttributes !== "" ? JSON.parse(response.basicAttributes) : [];
                    model.customAttributes = response.customAttributes !== null && response.customAttributes !== "" ? JSON.parse(response.customAttributes) : null;
                    model.customAttribsutesTranslation = response.customAttributesTranslation !== null && response.customAttributesTranslation !== "" ? JSON.parse(response.customAttributesTranslation) : null;
                    model.formData = response.formData !== null && response.formData !== "" ? JSON.parse(response.formData) : [];
                    model.receivers = response.receivers;
                    model.sendingEntityId = response.sendingEntityId;
                    model.dueDate = response.dueDate;
                    model.statusId = response.status;
                    model.priorityId = response.priorityId;
                    model.privacyId = response.privacyId;
                    model.carbonCopy = response.carbonCopy;
                    model.importanceId = response.importanceId;
                    model.classificationId = response.classificationId;
                    model.sendingEntity = response.sendingEntity;
                    model.receivingEntities = response.receivingEntities;
                    model.carbonCopies = response.carbonCopies;
                    model.classification = response.classification;
                    model.documentType = response.documentType;
                    model.readonly = false;
                    model.delegationId = response.delegationId;
                    model.userStructures = new IdentityService().getUserStructures(window.language);
                    model.privacies = new CoreComponents.Lookup.Privacies().get(window.language);
                    model.priorities = new CoreComponents.Lookup.Priorities().get(window.language);
                    model.importances = new CoreComponents.Lookup.Importances().get(window.language);

                    model.receiverPerson = response.receiverPerson;
                    model.senderPerson = response.senderPerson;
                    model.createdByStructureId = response.createdByStructureId;
                    model.body = response.body;
                    model.externalReferenceNumber = response.externalReferenceNumber;
                    model.keyword = response.keyword;
                    model.enableEdit = response.enableEdit;
                    model.byTemplate = response.byTemplate;
                    model.isDocumentEdit = true;
                    model.actionName = "Attribute.Save";

                    wrapper = $(searchLinkedDocumnet.refs['linkDocumentDiv2']);
                    wrapper.empty();
                    var view = new Document.DocumentView(wrapper, model);
                    $(view.refs['cmbUserStructures']).val(model.createdByStructureId);
                    var title = response.categoryName;
                    if (response.referenceNumber) {
                        title += ' - ' + response.referenceNumber;
                    }
                    if (response.createdByUser) {
                        title += ' - ' + response.createdByUser;
                    }
                    $(searchLinkedDocumnet.refs['modalSearchLinkedDocumnetTitle']).html(title);
                    $(searchLinkedDocumnet.refs['modalSearchLinkedDocumnet']).off("hidden.bs.modal");
                    $(searchLinkedDocumnet.refs['modalSearchLinkedDocumnet']).off("shown.bs.modal");
                    $(searchLinkedDocumnet.refs['modalSearchLinkedDocumnet']).on('shown.bs.modal', function () {
                    });
                    $(searchLinkedDocumnet.refs['modalSearchLinkedDocumnet']).on('hidden.bs.modal', function () {
                        $(searchLinkedDocumnet.refs[searchLinkedDocumnet.model.ComponentId]).remove();
                        swal.close();
                        //if ($(".modal-window").children().length > 0 || $(".modal-documents").children().length > 0) {
                        //    $('body').addClass('modal-open');
                        //}
                    });

                    //$(searchLinkedDocumnet.refs['modalSearchLinkedDocumnet']).modal("show");
                    //view.render();
                    let url = '/Document/SaveComplete';

                    view.render({
                        url: url,
                        params: {
                            'CategoryId': model.categoryId,
                            'CategoryName': model.categoryName,
                            'createdByStructureId': model.createdByStructureId,
                            '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                        },
                        callback: function (response) {
                            GridCommon.Refresh(gTableName);
                            //GridCommon.RefreshCurrentPage("grdInboxItems", false);
                        }
                    });
                    $('#' + view.model.ComponentId + '_btnAttributeRegister').remove()
                    $(".modalSearchLinkedDocumnet").modal("show");
                }
            }, function () { Common.showScreenErrorMsg(); }, true);
        }
    }


    E.CreateMeetingAgenda = function (selectedRows) {
        sessionStorage.setItem("SelectedTransfers", selectedRows);
        window.location.href = '#createbytemplate/4/Meeteing Agenda';
    }

    function createListData(data) {
        var delegationId = window.location.hash.split("/")[2];
        if (delegationId == undefined)
            delegationId = null;
        var html = '';
        if (data.length === 0 && gPageIndex === 0) {
            html = '<ul class="mdl-ul"><div class="nodata dataTables_empty pt-lg">' + Resources.NoDataToDisplay + '</div></ul>';
            $('#inboxListContainer').html(html);
        } else if (data.length > 0) {
            html = '<ul class="mdl-ul VIPContainerItem">';
            var htmlLi = '';
            for (var i = 0; i < data.length; i++) {
                var transfer = data[i];
                var liClass = "mdl-li";
                var color = "";

                if (!transfer.isRead) {

                    liClass += " unread";
                    TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                    //loadInboxList();

                }
                var lockedByMe = false;
                //var delegatedUserIds = $("#hdDelegatedUserIds").val().split(window.Splitter);
                var delegatedUser = delegationId !== null ? new DelegationUsers().getById(Number(delegationId)) : null;
                var delegatedUserId = delegatedUser !== null ? delegatedUser.fromUserId : 0;
                if (transfer.isLocked && (transfer.ownerUserId !== null && transfer.ownerUserId === Number($("#hdUserId").val()) && delegationId === null)
                    || (transfer.ownerDelegatedUserId !== null && transfer.ownerDelegatedUserId === Number($("#hdUserId").val())
                        && delegatedUserId === transfer.ownerUserId && delegationId !== null)
                    || (transfer.ownerUserId !== null && delegatedUserId === transfer.ownerUserId && delegationId !== null)) {
                    lockedByMe = true;
                }
                var priorities = new CoreComponents.Lookup.Priorities().get(window.language);
                var privacies = new CoreComponents.Lookup.Privacies().get(window.language);

                if (window.PriorityPrivacyAction == "2") {
                    for (var j = 0; j < privacies.length; j++) {
                        if (privacies[j].id === transfer.privacyId) {
                            color = privacies[j].color;
                            break;
                        }
                    }
                } else {
                    for (var j = 0; j < priorities.length; j++) {
                        if (priorities[j].id === transfer.priorityId) {
                            color = priorities[j].color;
                            break;
                        }
                    }
                }
                var htmlIcons = "";
                if (transfer.importanceId) {
                    var importances = new CoreComponents.Lookup.Importances().get(window.language);
                    for (var j = 0; j < importances.length; j++) {
                        if (importances[j].id === transfer.importanceId) {
                            htmlIcons += "<i class='fa fa-exclamation fa-lg mr-sm' title='" + importances[j].text + "' style='color:" + importances[j].color + "'></i>";
                        }
                    }
                }
                if (transfer.isOverDue) {
                    htmlIcons += "<i class='fa fa-clock-o fa-lg text-danger mr-sm' title='" + Resources.OverDue + "'></i>";
                }
                if (transfer.isRead && !transfer.lockedDate) {
                    htmlIcons += "<button class='btn btn-xs mr-sm unReadIcon' title='" + Resources.UnRead + "' style='padding: 0px 3px;' " +
                        "data-id='" + transfer.id + "' " +
                        "data-isread='" + transfer.isRead + "' " +
                        "data-delegationid='" + transfer.delegationId + "' " +
                        "data-cced='" + transfer.cced + "' " +
                        "data-lockeddate='" + transfer.lockedDate + "' " +
                        "data-lockedby='" + transfer.lockedBy + "'>" +
                        "<i class='fa fa-envelope text-warning'></i>" +
                        "</button>";
                }
                if (transfer.requestStatus == "Pending") {
                    let btn = document.createElement("button");
                    btn.setAttribute("class", "btn btn-xs btn-primary mr-sm accept");
                    btn.setAttribute("title", Resources.Accept);
                    btn.setAttribute("clickattr", "acceptRequest(" + transfer.id + ")");
                    btn.innerHTML = "<i class='fa fa-check'/>";
                    htmlIcons += btn.outerHTML;

                    let rejectBtn = document.createElement("button");
                    rejectBtn.setAttribute("class", "btn btn-xs btn-danger mr-sm reject");
                    rejectBtn.setAttribute("title", Resources.Reject);
                    rejectBtn.setAttribute("clickattr", "rejectRequest(" + transfer.id + ")");
                    rejectBtn.innerHTML = "<i class='fa fa-close'/>";
                    htmlIcons += rejectBtn.outerHTML;
                }
                let categories = new Categories().get(window.language, null)
                let category = $.grep(categories, function (e) {
                    return e.id === transfer.categoryId;
                });
                if (category[0] && category[0].isBroadcast) {
                    htmlIcons += "<i class='fa fa-bullhorn text-primary mr-sm'  title='" + Resources.Broadcast + "'></i>";
                } else if (transfer.cced) {
                    htmlIcons += "<i class='fa fa-cc text-warning mr-sm'  title='" + Resources.CarbonCopy + "'></i>";
                }
                if (!transfer.cced && transfer.requestStatus != "Pending" && (lockedByMe || !transfer.isLocked)) {
                    htmlIcons += "<button class='edit btn btn-xs btn-primary mr-sm lockIcon' style='padding: 0px 3px;' title='" + Resources.Edit + "'><i class='fa fa-edit'></i></button>";
                }
                var lockedByUser = transfer.ownerUserId === Number($("#hdUserId").val()) ? Resources.You : transfer.lockedBy;
                var lockedBy = transfer.lockedByDelegatedUser !== '' ? transfer.lockedByDelegatedUser + " " + Resources.OnBehalfOf + " " + transfer.lockedBy : lockedByUser;
                var titleLock = Resources.LockedBy + ": " + lockedBy + "\n" + Resources.LockedDate + ": " + DateConverter.toHijriFormated(transfer.lockedDate, null, window.CalendarType);
                if (transfer.sentToStructure && lockedByMe && window.location.hash.split("/")[1] != window.AcceptRejectNodeId) {
                    htmlIcons += "<button class='btn btn-xs btn-success mr-sm unlockIcon' style='padding: 0px 3px;' title='" + titleLock + "'><i class='fa fa-unlock fa-white'></i></button>";
                } else if (transfer.isLocked && !transfer.cced && window.location.hash.split("/")[1] != window.AcceptRejectNodeId) {
                    htmlIcons += "<i class='fa fa-lock fa-lg text-danger mr-sm' title='" + titleLock + "'></i>";
                }
                transfer.referenceNumber = transfer.referenceNumber ?? "";
                var from = transfer.fromStructure !== "" ? transfer.fromStructure + '/' + transfer.fromUser : transfer.fromUser;
                var to = "";
                transfer.receivingEntities.forEach((item) => {
                    to += item.text;
                })
                htmlLi += '<li class="' + liClass + '" style="color:' + color + ';" data-transfer="' + transfer.id + '">'; // Apply the dynamic color here

                htmlLi += '<div class="mdl-container">';
                htmlLi += '<div id="leftbox" class="pull-left">';
                htmlLi += '<div class="inside_color_line pull_left"></div>';
                htmlLi += '<input data-id=' + transfer.id + ' data-categoryid=' + transfer.categoryId + ' data-cced=' + transfer.cced + ' data-read=' + transfer.isRead +
                    ' data-lockedbyme=' + lockedByMe + '  data-islocked=' + transfer.isLocked +
                    '  data-senttouser=' + transfer.sentToUser +
                    ' type="checkbox" class="pointer m-sm ml-10px" onclick="event.stopPropagation();">';
                htmlLi += "<input type='hidden' data-id=" + transfer.id + " value='" + JSON.stringify(transfer) + "'/>";
                htmlLi += "<span class='mdl-span' style='margin-left:10px; margin-right: 10px;'><i class='fa fa-circle icon-primary'></i></span>"
                htmlLi += "<span class='mdl-span' style='margin-left:10px; margin-right: 10px;'><i class='fa fa-arrow-left'></i></span>"
                htmlLi += "<span class='mdl-span' style='margin-left:10px; margin-right: 10px;'><i class='fa fa-arrow-right'></i></span>"

                htmlLi += '</div>';
                htmlLi += '<div id="middlebox" class="mdl-text-msg pull-left pl-sm card-max-width">';
                //htmlLi += '<span class="dot"></span>';
                htmlLi += '<span class="mdl-span" data-field="referenceNumber" style="color:' + color + '" title="' + transfer.referenceNumber + '" data-ref>' + transfer.referenceNumber + '</span>';

                htmlLi += '<span class="mdl-span text-primary" data-field="subject"  style="color:' + color + '" title="' + transfer.subject + '">' + transfer.subject + '</span>';
                htmlLi += '<span class="mdl-span" data-field="from"  style="color:' + color + '" title="' + from + '">' + from + '</span>';

                htmlLi += '<span class="mdl-span light_grey_color"  data-field="toStructure" style="color:' + color + '" title="' + transfer.toStructure + '">' + transfer.toStructure + '</span>';
                htmlLi += '<span class="mdl-span light_grey_color" data-field="purposeName" style="color:' + color + '" title="' + transfer.purposeName + '">' + transfer.purposeName + '</span>';
                htmlLi += '</div>';
                htmlLi += '<div id="rightbox" class="pull-right text-right"><div class="mdl-time mr-sm" style="color:' + color + '" title="' + Resources.TransferDate + '">' + dateFormat(transfer.transferDate) + '</div>';

                if (htmlIcons !== "") {
                    htmlLi += '<div class="mdl-action">' + htmlIcons + '</div>';
                }
                htmlLi += '</div>';
                htmlLi += '</div>';
                htmlLi += '</div>';
                htmlLi += '</li>';
            }
            html += htmlLi;
            html += '</ul>';
            if (gPageIndex === 15) {
                $('#inboxListContainer').html(html);
            } else {
                $('#inboxListContainer ul').append(htmlLi);
            }

            $(".unread").css('font-weight', 'bold')

            if (gSelf.model.transferId && gSelf.model.transferId != null) {
                var transfer = data.find(d => d.id == gSelf.model.transferId);
                var checkbox = $('#leftbox input[data-id="' + gSelf.model.transferId.toString() + '"]');
                if (checkbox.length) {
                    var container = checkbox.closest('.mdl-container');
                    if (container.length) {
                        if (transfer && transfer.sentToStructure) {

                            container.find('#rightbox .edit').click();
                        } else {

                            container.parent().click();
                        }

                    }
                }
            }

        }
    }
    function addFilters(d) {
        if (gFromSearch) {
            d.PriorityId = $("#cmbFilterInboxPriority").val() !== null && typeof $("#cmbFilterInboxPriority").val() !== "undefined" ? $("#cmbFilterInboxPriority").val() : "0";
            d.PrivacyId = $("#cmbFilterInboxPrivacy").val() !== null && typeof $("#cmbFilterInboxPrivacy").val() !== "undefined" ? $("#cmbFilterInboxPrivacy").val() : "0";
            d.PurposeId = $("#cmbFilterInboxPurpose").val() !== null && typeof $("#cmbFilterInboxPurpose").val() !== "undefined" ? $("#cmbFilterInboxPurpose").val() : "0";
            d.CategoryId = $("#cmbFilterInboxCategory").val() !== null && typeof $("#cmbFilterInboxCategory").val() !== "undefined" ? $("#cmbFilterInboxCategory").val() : "0";
            d.ReferenceNumber = $("#txtFilterInboxReferenceNumber").val() !== "" && typeof $("#txtFilterInboxReferenceNumber").val() !== "undefined" ? $("#txtFilterInboxReferenceNumber").val() : "";
            d.FromDate = $("#filterInboxFromDate").val() !== "" && typeof $("#filterInboxFromDate").val() !== "undefined" ? $("#filterInboxFromDate").val() : "";
            d.ToDate = $("#filterInboxToDate").val() !== "" && typeof $("#filterInboxToDate").val() !== "undefined" ? $("#filterInboxToDate").val() : "";
            d.Read = $("#chkFilterInboxRead").is(':checked');
            d.Locked = $("#chkFilterInboxLocked").is(':checked');
            d.Overdue = $("#chkFilterInboxOverdue").is(':checked');
            d.Subject = $("#txtFilterInboxSubject").val() !== "" && typeof $("#txtFilterInboxSubject").val() !== "undefined" ? $("#txtFilterInboxSubject").val() : "";
            d.StructureIds = $("#cmbFilterInboxStructure").val() !== null && typeof $("#cmbFilterInboxStructure").val() !== "undefined" ? $("#cmbFilterInboxStructure").val() : [];
            d.UserIds = $("#cmbFilterInboxUser").val() !== null && typeof $("#cmbFilterInboxUser").val() !== "undefined" ? $("#cmbFilterInboxUser").val() : [];
        }
    }
    function dateFormat(dateText) {
        var dateFull = dateText.split(" ")[0].split("/");
        var today = new Date();
        var dd = today.getDate();
        var mm = today.getMonth() + 1; //January is 0!
        var yy = today.getFullYear();
        var time = DateConverter.toHijriFormated(dateText.split(" ")[0], null, window.CalendarType);
        if (parseInt(dateFull[0]) === dd && parseInt(dateFull[1]) === mm && parseInt(dateFull[2]) === yy) {
            time = "";
            var timeSeparator = ":";
            var hours = parseInt(dateText.split(" ")[1].split(":")[0]);
            var amPm = Resources.AM;
            if (hours > 12) {
                time += (hours - 12) + timeSeparator;
                amPm = Resources.PM;
            } else if (hours === 12) {
                time += "12" + timeSeparator;
                amPm = Resources.PM;
            } else {
                time += (hours < 10 ? '0' : '') + hours + timeSeparator;
                amPm = Resources.AM;
            }
            var minutes = parseInt(dateText.split(" ")[1].split(":")[1]);
            minutes = (minutes < 10 ? '0' : '') + minutes;
            time += minutes + " " + amPm;
        } else if (parseInt(dateFull[0]) === (dd - 1) && parseInt(dateFull[1]) === mm && parseInt(dateFull[2]) === yy) {
            time = Resources.Yesterday;
        }
        return time;
    }

    var gNoMoreData = false;
    var gFromSearch = false;
    var gPageIndex = 0;
    var gSelectedRowId, gSelf;
    function loadInboxList() {
        if (!gNoMoreData) {
            var delegationId = window.location.hash.split("/")[2];
            if (delegationId == undefined)
                delegationId = null;
            var nodeId = window.location.hash.split("/")[1];
            if (nodeId == undefined)
                nodeId = null;
            Common.mask(document.getElementById('inboxListContainer'), "inboxListContainer-mask");
            var params = {};
            addFilters(params);
            params.NodeId = nodeId;
            params.DelegationId = delegationId;
            params.start = gPageIndex;
            params.structureId = window.EnablePerStructure ? $('#hdLoggedInStructureId').val() : $("#hdStructureId").val();
            Common.ajaxPost('/Transfer/ListInboxVip', params, function (response) {
                if (response.length > 0) {
                    gPageIndex += window.Paging;
                    if (response.length < window.Paging) {
                        gNoMoreData = true;
                    }
                } else {
                    gNoMoreData = true;
                }
                createListData(response);
                gLocked = false;
                Common.unmask("inboxListContainer-mask");
                if (gFromSearch) {
                    $("#divSearchInbox").fadeOut();
                }
            }, function () { gLocked = false; Common.showScreenErrorMsg(); });
        } else {
            gLocked = false;
        }
    }
    function refreshInboxList(ids) {
        for (var i = 0; i < ids.length; i++) {
            var canDelete = false;
            var li = $($("input[data-id='" + ids[i] + "']").parents("li")[0]);
            var spans = li.find("#middlebox span");
            if (spans.length >= 3) {
                canDelete = true;
            }
            if (canDelete) {
                li.fadeOut().remove();
                $(".withBorders-o").addClass("waitingBackground");
                $("#inboxDocumentDetailsContainer").empty();
                gSelectedRowId = null;
            }
        }
        gPageIndex = 0;
        gNoMoreData = false;
        loadInboxList();
    }
    function openCompleteReasonModal(callback) {
        // The modal has a header having >> "modal-header" div contains the closing button icon that closes the modal
        // The modal also has a textarea for notes, and two buttons: "Submit" and "Cancel".
        const modal = $(`
    <div data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-hidden="true" class="modal fade modalComplete" id="completeModal">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="border-bottom:0px; padding-bottom:3px;">
                    <button type="button" ref="completeClose" id="completeInboxListModalClose" class="close" data-dismiss="modal">&times;</button>
                    <h4 ref="modalCompleteTitle" class="modal-title"></h4>
                </div> 
                <div class="modal-body" style="padding-top: 2px;">
                    <form ref="formPost" method="post" data-parsley-validate="" novalidate="">
                        <div class="row">
                            <div class="col-md-12" ref="completeReasonContainer">
                                <label class="control-label field-required" style="font-size: medium;">${Resources.CompletionNotes}</label>
                                <textarea id="completeReason" rows="3" class="form-control" required></textarea>
                                <div class="invalid-feedback" style="display:none; color:red;">
                                    ${Resources.RequiredField}
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer" style="border-top:0px;">
                    <button type="button" class="btn btn-primary" id="submitReason">${Resources.Complete}</button>
                    <button type="button" class="btn btn-secondary" id="cancelComplete" data-bs-dismiss="modal">${Resources.Cancel}</button>
                </div>
            </div>
        </div>
    </div>
    `);

        $('body').append(modal); // Append modal to the body
        modal.modal('show');   // Display the modal

        modal.find('#submitReason').on('click', function () {
            const textarea = modal.find('#completeReason');
            const reason = textarea.val().trim(); // removes starting and ending white spaces from input value
            const errorMsg = textarea.siblings('.invalid-feedback');

            if (!reason) {
                textarea.addClass('is-invalid');  // Adds red border
                errorMsg.show();  // Shows the error message
                modal.find('form').addClass('was-validated'); // Ensure Bootstrap applies styles
                return;
            } else {
                textarea.removeClass('is-invalid'); // Removes red border
                errorMsg.hide(); // Hides error message
                $("#completeInboxListModalClose").trigger("click");  //  triggering the close button to close the entire modal with its shadow
                // To close the modal after submission.
                callback(reason);
            }
        });

        // Remove validation styles on input change
        modal.find('#completeReason').on('input', function () {
            $(this).removeClass('is-invalid');
            $(this).siblings('.invalid-feedback').hide();
        });

        modal.find('#cancelComplete').on('click', function () {
            modal.find('#completeReason').val('');
            modal.modal('hide');   //  closes the modal when the user clicks "Cancel."
        });

        modal.on('hidden.bs.modal', function () {
            modal.remove();
        });
    }

    function completeTransfer(ids, delegationId)
    {
        var g = document.getElementById('grdInboxItems');
        openCompleteReasonModal(function (reason) {
            
            if (window.InboxMode == "InboxVIPView" || window.InboxMode === 'LocalVIPView')
                Common.mask(document.getElementById('vipContainer'), "vipContainer-mask");
            else
                Common.mask(document.getElementById('grdInboxItems'), "inboxListContainer-mask");

                Common.ajaxPost('/Transfer/Complete',
                    {
                        'ids': ids, 'delegationId': delegationId, 'completeReasonNote': reason, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                    },
                    function (result) {
                        if (result != null && result.length > 0) {
                            swal.close()
                            let msg = "";
                            for (var i = 0; i < result.length; i++) {
                                if (!result[i].updated) {
                                    msg += "\n ○ " + result[i].uncompletedDocumentReferenceNumber;
                                }
                            }
                            if (msg !== "") {
                                setTimeout(function () {
                                    Common.alertMsg(Resources.CannotCompleteWarning + msg);
                                }, 300);

                                if (window.InboxMode == "InboxVIPView" || window.InboxMode === 'LocalVIPView') {
                                    Common.unmask("vipContainer-mask");
                                    $(".withBorders-o").addClass("waitingBackground");
                                    $("#inboxDocumentDetailsContainer").empty();
                                    $($("input[data-id='" + ids + "']").parents("li")[0]).fadeOut().remove();
                                }
                                else {
                                    Common.unmask("inboxListContainer-mask");
                                    GridCommon.RefreshCurrentPage("grdInboxItems", true);
                                    GridCommon.Refresh(gTableName);
                                }
                               
                                TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                            }
                            else {
                                if (window.InboxMode == "InboxVIPView" || window.InboxMode === 'LocalVIPView') {
                                    Common.unmask("vipContainer-mask");
                                    $(".withBorders-o").addClass("waitingBackground");
                                    $("#inboxDocumentDetailsContainer").empty();
                                    $($("input[data-id='" + ids + "']").parents("li")[0]).fadeOut().remove();
                                } else {
                                    Common.unmask("inboxListContainer-mask");
                                    GridCommon.RefreshCurrentPage("grdInboxItems", true);
                                    Common.showScreenSuccessMsg();
                                    GridCommon.Refresh(gTableName);

                                }
                                TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                                TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                                TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);
                                TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                            }
                        } else {
                            Common.showScreenErrorMsg();
                        }
                    }, null, false);
        });
    }
    function openCompleteReasonVIP(callback) {
        // The modal has a header having >> "modal-header" div contains the closing button icon that closes the modal
        // The modal also has a textarea for notes, and two buttons: "Submit" and "Cancel".
        const modal = $(`
                        <div data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-hidden="true" class="modal fade modalCompleteActionVIP" id="CompleteInboxVIP" tabindex="-1">
                        <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                        <div class="modal-header" style="border-bottom:0px; padding-bottom:3px;">
                        <button type="button" ref="completeInboxCloseVIP" id="completeCloseVIP" class="close" data-dismiss="modal">&times;</button>
                        <h4 ref="modalCompleteTitleVIP" class="modal-title"></h4>
                        </div> 
                        <div class="modal-body" style="padding-top: 2px; ">
                        <form ref="formPost" method="post" data-parsley-validate="" novalidate="">
                        <div class="row">
                        <div class="col-md-12" ref="completeReasonContainerVIP">
                        <label class="control-label field-required" style="font-size: medium; ">${Resources.CompletionNotes}</label>
                        <textarea id="completeActionReasonVIP" rows="3" data-parsley-required="true"class="form-control" required></textarea>
                        <div class="invalid-feedback" style="display:none; color:red;">
                                                         ${Resources.Thisfieldvalueisrequired}
                        </div>
                        </div>
                        </div>
                        </form>
                        </div>
                        <div class="modal-footer" style="border-top:0px;">
                        <button type="button" class="btn btn-primary" id="submitCompleteReasonVIP">${Resources.Submit}</button>
                        <button type="button" class="btn btn-secondary" id="cancelCompleteVIP" data-bs-dismiss="modal">${Resources.Cancel}</button>
                        </div>
                        </div>
                        </div>
                        </div>
                        `);
        // UI Production: The modal is dynamically created using a jQuery template literal and appended to the body.

        $('body').append(modal); // This body is the default screen html body, so we basically append this modal template into the screen content

        modal.modal('show');   //  displays the modal
        
        modal.find('#submitCompleteReasonVIP').on('click', function () {
            const textarea = modal.find('#completeActionReasonVIP');
            const completeVIP = textarea.val().trim();// Removes any leading or trailing whitespace from the complete reason 
            const errorMsg = textarea.siblings('.invalid-feedback');

            if (!completeVIP) {
                textarea.addClass('is-invalid');  // Adds red border
                errorMsg.show();  // Shows the error message
                modal.find('form').addClass('was-validated'); // Ensure Bootstrap applies styles
                return;
            }
            else {
                textarea.removeClass('is-invalid'); // Removes red border
                errorMsg.hide(); // Hides error message
                $("#completeCloseVIP").trigger("click");  //  triggering the close button to close the entire modal with its shadow
                callback(completeVIP);
                // Close the modal after the callback is executed
            }
        });
        modal.find('#completeActionReasonVIP').on('input', function () {
            $(this).removeClass('is-invalid');
            $(this).siblings('.invalid-feedback').hide();
        });
        modal.find('#cancelCompleteVIP').on('click', function () {
            modal.find('#completeReasonVIP').val('');
            modal.modal('hide');
        });

        modal.on('hidden.bs.modal', function () {
            modal.remove();
        });
    }
    function completeTransferVip(ids, delegationId) {
        openCompleteReasonVIP(function (reasonVIP) {
            
            Common.mask(document.getElementById('vipContainer'), "vipContainer-mask");
            Common.ajaxPost('/Transfer/Complete',
                {
                    'ids': ids, 'delegationId': delegationId, 'completeReasonNote': reasonVIP, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                },
                function (result) {
                    if (result != null && result.length > 0) {
                        swal.close()
                        let msg = "";
                        for (var i = 0; i < result.length; i++) {
                            if (!result[i].updated) {
                                msg += "\n ○ " + result[i].uncompletedDocumentReferenceNumber;
                            }
                        }
                        if (msg !== "") {
                            setTimeout(function () {
                                Common.alertMsg(Resources.CannotCompleteWarning + msg);
                            }, 300);
                            Common.unmask("vipContainer-mask");
                            for (var i = 0; i < ids.length; i++) {
                                var canDelete = true;
                                var li = $($("input[data-id='" + ids[i] + "']").parents("li")[0]);
                                if (result.length > 0) {
                                    var spans = li.find("#middlebox span");
                                    if (spans.length >= 3) {
                                        if ($.grep(result, function (element, index) {
                                            return !element.updated && element.uncompletedDocumentReferenceNumber === spans[1].textContent;
                                        }).length > 0) {
                                            canDelete = false;
                                        }
                                    }
                                }
                                if (canDelete) {
                                    li.fadeOut().remove();
                                    if (Number(gSelectedRowId) === Number(ids[i])) {
                                        $(".withBorders-o").addClass("waitingBackground");
                                        $("#inboxDocumentDetailsContainer").empty();
                                    }
                                }
                            }
                            Common.unmask("vipContainer-mask");
                            TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                        }
                        else {
                            Common.unmask("vipContainer-mask");
                            Common.showScreenSuccessMsg();
                            TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                            TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                            TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);
                            TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                            TreeNode.refreshTreeNodeCounts(TreeNode.Closed);
                            TreeNode.refreshTreeNodeCounts(TreeNode.MyRequests);
                            TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
                            for (var i = 0; i < ids.length; i++) {
                                $($("input[data-id='" + ids[i] + "']").parents("li")[0]).fadeOut().remove();
                                /*if (Number(gSelectedRowId) === Number(ids[i])) {*/
                                    $(".withBorders-o").addClass("waitingBackground");
                                    $("#inboxDocumentDetailsContainer").empty();
                                /*}*/
                            }
                        }
                    } else {
                        Common.showScreenErrorMsg();
                    }
                }, null, false);
        });
    }
    function dismissCarbonCopy(dismissIds, delegationId, allSelectedData) { 
        if (window.InboxMode == "InboxVIPView" || window.InboxMode === 'LocalVIPView')
            Common.mask(document.getElementById('vipContainer'), "vipContainer-mask");
        else
            Common.mask(document.getElementById('grdInboxItems'), "inboxListContainer-mask");

        Common.ajaxPost('/Transfer/DismissCarbonCopy',
            {
                'ids': dismissIds, 'delegationId': delegationId, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
            },
            function (result) {
                if (result != null && result.length > 0) {
                    swal.close()
                    let msg = "";
                    for (var i = 0; i < result.length; i++) {
                        if (!result[i].updated) {
                            if (allSelectedData != null && allSelectedData != undefined) {
                                var transfer = $.grep(allSelectedData, function (e) {
                                    return e.id === result[i].transferId;
                                });
                                if (transfer[0]) {
                                    msg += "\n ○ " + transfer[0].referenceNumber;

                                } else {
                                    msg += "\n ○ " + result[i].transferId;
                                }
                            }
                            else {
                                msg += "\n ○ " + result[i].transferId;
                            }
                        }
                    }
                    if (msg !== "") {
                        setTimeout(function () {
                            Common.alertMsg(Resources.CannotDismissCarbonCopyWarning + msg);
                        }, 300);
                        if (window.InboxMode == "InboxVIPView" || window.InboxMode === 'LocalVIPView') {
                            Common.unmask("vipContainer-mask");
                            $(".withBorders-o").addClass("waitingBackground");
                            $("#inboxDocumentDetailsContainer").empty();
                        }
                        else {
                            Common.unmask("inboxListContainer-mask");
                            GridCommon.Refresh(gTableName);
                            TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                        }
                    } else {
                        if (window.InboxMode == "InboxVIPView" || window.InboxMode === 'LocalVIPView') {
                            Common.unmask("vipContainer-mask");
                            $(".withBorders-o").addClass("waitingBackground");
                            $("#inboxDocumentDetailsContainer").empty();
                            $($("input[data-id='" + dismissIds + "']").parents("li")[0]).fadeOut().remove();
                        }
                        else {
                            Common.unmask("inboxListContainer-mask");
                            Common.showScreenSuccessMsg();
                            GridCommon.Refresh(gTableName);
                        }
                        TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                        TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                        TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);
                        TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                    }
                } else {
                    Common.showScreenErrorMsg();
                }
            }, null, false);
    }

    function dismissCarbonCopyVip(dismissIds, delegationId, allSelectedData) {
        Common.mask(document.getElementById('vipContainer'), "vipContainer-mask");
        Common.ajaxPost('/Transfer/DismissCarbonCopy',
            {
                'ids': dismissIds, 'delegationId': delegationId, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
            },
            function (result) {
                if (result != null && result.length > 0) {
                    swal.close()
                    let msg = "";
                    for (var i = 0; i < result.length; i++) {
                        if (!result[i].updated) {
                            var transfer = $.grep(allSelectedData, function (e) {
                                return e.id === result[i].transferId;
                            });
                            if (transfer[0]) {
                                msg += "\n ○ " + transfer[0].referenceNumber;

                            } else {
                                msg += "\n ○ " + result[i].transferId;
                            }
                        }
                    }
                    if (msg !== "") {
                        setTimeout(function () {
                            Common.alertMsg(Resources.CannotDismissCarbonCopyWarning + msg);
                        }, 300);
                        Common.unmask("vipContainer-mask");
                        for (var i = 0; i < dismissIds.length; i++) {
                            var canDelete = false;
                            var li = $($("input[data-id='" + dismissIds[i] + "']").parents("li")[0]);
                            if (result.length > 0) {
                                if ($.grep(result, function (element, index) {
                                    return element.updated && element.transferId === dismissIds[i];
                                }).length > 0) {
                                    canDelete = true;
                                }
                            }
                            if (canDelete) {
                                li.fadeOut().remove();
                                if (Number(gSelectedRowId) === Number(dismissIds[i])) {
                                    $(".withBorders-o").addClass("waitingBackground");
                                    $("#inboxDocumentDetailsContainer").empty();
                                }
                            }
                        }
                        Common.unmask("vipContainer-mask");
                        TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                    } else {
                        Common.unmask("vipContainer-mask");
                        Common.showScreenSuccessMsg();
                        TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                        TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                        for (var i = 0; i < dismissIds.length; i++) {
                            while ($("input[data-id='" + dismissIds[i] + "']")[0]) {
                                $($("input[data-id='" + dismissIds[i] + "']").parents("li")[0]).fadeOut().remove();
                            }
                            /*if (Number(gSelectedRowId) === Number(dismissIds[i])) {*/
                                $(".withBorders-o").addClass("waitingBackground");
                                $("#inboxDocumentDetailsContainer").empty();
                            /*}*/
                        }
                    }
                } else {
                    Common.showScreenErrorMsg();
                }
            }, null, false);
    }


    function saveDocument(data, isFollowUp) {
        var createdByStructureId = $("#hdLoggedInStructureId").val();

        var saveParams = {
            'CreatedByStructureId': createdByStructureId,
            'IsFollowUp': isFollowUp,
            'CategoryId': window.FollowUpCategory,
            '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
        };
        Common.ajaxPost('/Document/Save', saveParams, function (data2) {
            (data2, data);
            if (isFollowUp == true)
                addLinkedCorrespondence(data2, data);
            TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
            getDocument(data2);
        }, function () {
            Common.showScreenErrorMsg();
        });
    }

    function getDocument(id) {
        Common.ajaxGet('/Document/Get', { id: id }, function (data) {
            if (!data.id) {
                return;
            }
            $(".modal-window").empty();
            let modalWrapper = $(".modal-window");
            modalWrapper.append(`<div ref="modalDocument" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-hidden="true" class="modal fade modalDocument">
                                    <div class="modal-dialog modal-lg">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <button type="button" ref="documentClose" class="close" data-dismiss="modal">&times;</button>
                                                <h4 ref="modalDocumentTitle" class="modal-title">`+ Resources.NewFollowUp + `</h4>
                                            </div>
                                            <div class="modal-body modalDocumentBody">
                                                <form ref="formDocumentPost" method="post" data-parsley-validate="" novalidate="">
                                                    <div ref="documentPostMessage">
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>`);

            modalWrapper = $(".modalDocumentBody");
            var readonly = false;
            var model = new DocumentMetadata.Document();
            model.id = id;
            model.categoryId = data.categoryId;
            model.categoryName = data.categoryName;
            model.referenceNumber = data.referenceNumber;
            model.subject = data.subject;
            model.basicAttributes = data.basicAttributes !== null && data.basicAttributes !== "" ? JSON.parse(data.basicAttributes) : [];
            model.customAttributes = data.customAttributes !== null && data.customAttributes !== "" ? JSON.parse(data.customAttributes) : null;
            model.customAttributesTranslation = data.customAttributesTranslation !== null && data.customAttributesTranslation !== "" ? JSON.parse(data.customAttributesTranslation) : null;
            model.formData = data.formData !== null && data.formData !== "" ? JSON.parse(data.formData) : [];
            model.receivers = data.receivers;
            model.sendingEntityId = data.sendingEntityId;
            model.dueDate = data.dueDate;
            model.priorityId = data.priorityId;
            model.privacyId = data.privacyId;
            model.carbonCopy = data.carbonCopy;
            model.importanceId = data.importanceId;
            model.classificationId = data.classificationId;
            model.sendingEntity = data.sendingEntity;
            model.receivingEntities = data.receivingEntities;
            model.carbonCopies = data.carbonCopies;
            model.classification = data.classification;
            model.documentType = data.documentType;
            model.readonly = readonly && data.enableEdit ? !data.enableEdit : readonly;
            model.delegationId = data.delegationId;
            model.userStructures = new IdentityService().getUserStructures(window.language);
            model.privacies = new CoreComponents.Lookup.Privacies().get(window.language);
            model.priorities = new CoreComponents.Lookup.Priorities().get(window.language);
            model.importances = new CoreComponents.Lookup.Importances().get(window.language);
            model.createdByStructureId = data.createdByStructureId;
            model.body = data.body;
            model.externalReferenceNumber = data.externalReferenceNumber;
            model.keyword = data.keyword;
            model.enableEdit = data.enableEdit;
            model.byTemplate = data.byTemplate ?? false;
            //model.transferId = gSelf.model.id;
            model.transferId = 0;
            model.isFollowUp = true;
            model.isTaskCreator = true;
            model.actionName = "Attribute.Save";
            var documentView = new DocumentMetadata.DocumentView(modalWrapper, model);
            setTimeout(function () {
                $('#txtCustomAttributeSubject').focus();
            }, 500);
            let url = readonly ? '/Document/Edit' : '/Document/Save';
            $('[ref="btnAttributeCancelTask"]').remove();
            documentView.render({
                url: url,
                params: {
                    'CategoryId': data.categoryId,
                    'CategoryName': data.categoryName,
                    '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                },
                callback: function (response) {
                    if (readonly) {
                        GridCommon.RefreshCurrentPage("grdInboxItems", false);
                    } else {
                        GridCommon.RefreshCurrentPage("grdDraftItems", false);
                    }
                    $('.modalDocument').modal('hide');
                    window.location.href = "/#document/" + id;
                }
            }, {
                url: '/Document/Send',
                params: {
                    '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                },
                callback: function (response) {
                    TreeNode.addToNodeCount(TreeNode.MyRequests, 1, 1);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
                    Common.alertMsg(Common.format(Resources.ReferenceNumberMsg, response), function () {
                        var nodeId = $('[data-inherit="' + TreeNode.MyRequests + '"]').first().data("id");
                        if (nodeId !== undefined && $.isNumeric(nodeId)) {
                            window.location.href = '#myrequests/' + nodeId;
                        } else {
                            window.location.href = '/';
                        }
                    });
                }
            });
            //let target = "#" + gSelf.model.ComponentId + '_documentMetadata';
            //let actions = initTabActions(gSelf.model.tabsWithStatic, gSelf.model.tabsActions, gSelf.model.readonly, target.split("_")[2]);
            //SecurityMatrix.getTabActions(actions, "#" + $(gSelf.refs[gSelf.model.ComponentId]).find('div[id*="' + target.split("_")[2] + '"]').attr('id'), false);
            //SecurityMatrix.InitTabContextMenu(actions);

            $('.modalDocument').modal('show');
            $(".modalDocument").off("hidden.bs.modal");
            $(".modalDocument").off("shown.bs.modal");
            $('.modalDocument').on('hidden.bs.modal', function () {
                //$(".modalDocument").parent().remove();
                swal.close();
            });

        }, function () { Common.showScreenErrorMsg(); }, true);
    }
    function addLinkedCorrespondence(documentId, selectedDocIds) {
        let params = {
            "documentId": documentId,
            "linkDocumentIds": selectedDocIds,
            "isFollowUp": true,
            "__RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val()
        }
        Common.ajaxPost('/LinkedDocument/Index', params, function (data) {
            $(".grdLinkedCorrespondences").DataTable().ajax.reload();
        }, false);
    }
    function requestToCompleteTask(ids, tableName, gridContainerName) {
        Common.mask(document.getElementById(tableName), gridContainerName);
        Common.ajaxPost('/Transfer/RequestToComplete',
            {
                'ids': ids, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
            },
            function (result) {
                if (result != null && result.length > 0) {
                    swal.close()
                    let msg = "";
                    for (var i = 0; i < result.length; i++) {
                        if (!result[i].updated) {
                            if (msg == "") {
                                msg = '○ ' + result[i].message;
                            }
                            else {
                                msg += "\n ○ " + result[i].message;
                            }
                        }
                    }
                    if (msg !== "") {
                        setTimeout(function () {
                            Common.alertMsg(msg);
                        }, 300);
                    }
                    else {
                        Common.unmask(gridContainerName);
                        Common.showScreenSuccessMsg();
                        if ($("#grdInboxItems").val() != undefined || $("#grdSentItems").val() != undefined) {
                            GridCommon.Refresh(tableName);
                        }
                        else {
                            switch ((window.location.hash.split("/")[0]).toLowerCase()) {
                                case '#inbox':
                                    for (var i = 0; i < ids.length; i++) {
                                        var canDelete = true;
                                        var li = $($("input[data-id='" + ids[i] + "']").parents("li")[0]);
                                        li.fadeOut().remove();
                                        if (Number(gSelectedRowId) === Number(ids[i])) {
                                            $(".withBorders-o").addClass("waitingBackground");
                                            $("#inboxListContainer").empty();
                                        }
                                    }
                                    TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                                    break;
                                case '#sent':
                                    for (var i = 0; i < ids.length; i++) {
                                        var canDelete = true;
                                        var li = $($("input[data-id='" + ids[i] + "']").parents("li")[0]);
                                        li.fadeOut().remove();
                                        if (Number(gSelectedRowId) === Number(ids[i])) {
                                            $(".withBorders-o").addClass("waitingBackground");
                                            $("#sentListContainer").empty();
                                        }
                                    }
                                    TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
                                    break;
                                default:
                            }
                        }
                    }
                } else {
                    Common.showScreenErrorMsg();
                }
            }, null, false);
    }
    function completeFollowUp(paramData) {
        
        Common.ajaxPost('/FollowUp/CompleteFollowUp',
            { followUpCompleteModels: paramData },
            function (result) {
                
                if (result != null && result.length > 0) {
                    swal.close()
                    let msg = "";
                    for (var i = 0; i < result.length; i++) {
                        if (!result[i].updated) {
                            if (msg == "") {
                                msg = '○ ' + result[i].message + " : " + result[i].subject;
                            }
                            else { 
                                msg += "\n ○ " + result[i].message + " : " + result[i].subject;
                            }
                        }
                    }
                    if (msg !== "") {
                        setTimeout(function () {
                            Common.alertMsg(msg);
                        }, 300);
                    }
                    else {
                        //Common.unmask(sentGridContainerName);
                        Common.showScreenSuccessMsg();
                        GridCommon.Refresh('grdFollowUpItems');
                        TreeNode.refreshTreeNodeCounts(TreeNode.FollowUp);
                    }
                } else {
                    Common.showScreenErrorMsg();
                }
            }, null, false);
    }

    function cancelFollowUp(paramData) {
        Common.ajaxPost('/FollowUp/CancelFollowUp',
            { followUpCancelModels: paramData },
            function (result) {
                
                if (result != null && result.length > 0) {
                    swal.close()
                    let msg = "";
                    for (var i = 0; i < result.length; i++) {
                        if (!result[i].updated) {
                            if (msg == "") {
                                msg = '○ ' + result[i].message + " : " + result[i].subject;
                            }
                            else {
                                msg += "\n ○ " + result[i].message + " : " + result[i].subject;
                            }
                        }
                    }
                    if (msg !== "") {
                        setTimeout(function () {
                            Common.alertMsg(msg);
                        }, 300);
                    }
                    else {
                        //Common.unmask(sentGridContainerName);
                        Common.showScreenSuccessMsg();
                        GridCommon.Refresh('grdFollowUpItems');
                        TreeNode.refreshTreeNodeCounts(TreeNode.FollowUp);
                    }
                } else {
                    Common.showScreenErrorMsg();
                }
            }, null, false);
    }

    function postponeFollowUp(paramData) {
        Common.ajaxPost('/FollowUp/postponeFollowUp',
            { followUpPostponeModels: paramData },
            function (result) {
                
                if (result != null && result.length > 0) {
                    swal.close()
                    let msg = "";
                    for (var i = 0; i < result.length; i++) {
                        if (!result[i].updated) {
                            if (msg == "") {
                                msg = '○ ' + result[i].message + " : " + result[i].subject;
                            }
                            else {
                                msg += "\n ○ " + result[i].message + " : " + result[i].subject;
                            }
                        }
                    }
                    if (msg !== "") {
                        setTimeout(function () {
                            Common.alertMsg(msg);
                        }, 300);
                    }
                    else {
                        Common.showScreenSuccessMsg();
                    }

                    var btn = $('#btnSubmitFollowUpPostpone');
                    var btnClose = $('#btnCloseFollowUpPostpone');
                    var btnCloseX = $('#postponeModalClose');
                    btn.button('reset');
                    btnClose.removeAttr('disabled');
                    btnCloseX.removeAttr('disabled');
                    $('#modalfollowUpPostponeIndex').modal('hide');
                        GridCommon.Refresh('grdFollowUpItems');
                        TreeNode.refreshTreeNodeCounts(TreeNode.FollowUp);


                } else {
                    Common.showScreenErrorMsg();
                }
            }, null, false);
    }

    function openPostponeFollowUp(allSelectedData)
    {
        let modalWrapper = $(".modal-window");
        modalWrapper.find("#modalfollowUpPostponeIndex").remove();
        var followUpPostponeModel = new followUpPostpone.FollowUpPostponeIndex();
        followUpPostponeModel.callback = function (data) {
            var followUpIdsData = []
            for (var i = 0; i < allSelectedData.length; i++) {


                followUpIdsData.push({ 'followUpId': allSelectedData[i].id, 'subject': allSelectedData[i].subject })
            }

            var prams = {
                postponeType: data.postponeType,
                toDate: data.toDate,
                months: data.months != "" ? parseInt(data.months) : 0,
                weeks: data.weeks != "" ? parseInt(data.weeks) : 0,
                days: data.days != "" ? parseInt(data.days) : 0,
                followUpActionModels: followUpIdsData
            }
            postponeFollowUp(prams);
        };
        var followUpPostponeView = new followUpPostpone.FollowUpPostponeIndexView(modalWrapper, followUpPostponeModel);
        followUpPostponeView.render();

        $('#modalfollowUpPostponeIndex').modal('show');
        $("#modalfollowUpPostponeIndex").off("hidden.bs.modal");
        $("#modalfollowUpPostponeIndex").off("shown.bs.modal");
        $('#modalfollowUpPostponeIndex').on('shown.bs.modal', function () {
        });
        $('#modalfollowUpPostponeIndex').on('hidden.bs.modal', function () {
            $('#formFollowUpPostponeIndexPost').parsley().reset();
            $('#cmbFollowUpUserRole').val('');
            $('#modalfollowUpPostponeIndex').remove();
            if ($(".modal-documents").children().length > 0 || $(".modal-window").children().length > 0) {
                $('body').addClass('modal-open');
            }
        });
    }

    function openRecallReasonModal(callback) {
        const modal = $(`
    <div data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-hidden="true" class="modal fade modalRecall" id="recallModal">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="border-bottom:0px; padding-bottom:3px;">
                    <button type="button" ref="recallClose" id="recallActionModalClose" class="close" data-dismiss="modal">&times;</button>
                    <h4 ref="modalRecallTitle" class="modal-title"></h4>
                </div> 
                <div class="modal-body" style="padding-top: 2px;">
                    <form ref="formPost" method="post" data-parsley-validate="" novalidate="">
                        <div class="row">
                            <div class="col-md-12" ref="recallReasonContainer">
                                <label class="control-label field-required" style="font-size: medium;">${Resources.RecallReason}</label>
                                <textarea id="recallReason" rows="3" class="form-control" required></textarea>
                                <div class="invalid-feedback" style="display:none; color:red;">
                                    ${Resources.RequiredField}
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer" style="border-top:0px;">
                    <button type="button" class="btn btn-primary" id="submitReason">${Resources.Recall}</button>
                    <button type="button" class="btn btn-secondary" id="cancelRecall" data-bs-dismiss="modal">${Resources.Cancel}</button>
                </div>
            </div>
        </div>
    </div>
    `);

        $('body').append(modal);
        modal.modal('show');

        modal.find('#submitReason').on('click', function () {
            const textarea = modal.find('#recallReason');
            const reason = textarea.val().trim();                 // Removes any leading or trailing whitespace from the recall reason input value
            const errorMsg = textarea.siblings('.invalid-feedback');

            if (!reason) {
                textarea.addClass('is-invalid');
                errorMsg.show();  // Shows the error message
                //modal.find('form').addClass('was-validated');
                return;
            } else {
                textarea.removeClass('is-invalid');
                errorMsg.hide(); // Hides error message
                // modal.modal('hide').remove();           // Once there is added text  removes the error msg and red border automatically.
                $("#recallActionModalClose").trigger("click");
                callback(reason);
            }
        });

        // Remove required validation styles once there is an input change or input added
        modal.find('#recallReason').on('input', function () {
            $(this).removeClass('is-invalid');
            $(this).siblings('.invalid-feedback').hide();
        });
        modal.find('#cancelRecall').on('click', function () {
            modal.find('#recallReason').val('');
            modal.modal('hide');

        });
        modal.on('hidden.bs.modal', function () {
            modal.remove();
        });
    }
    function recallRows(id, delegationId, fromExported, cannotBeRecalled, reason) {
        Common.ajaxPost('/Transfer/Recall',
            {
                'id': id, 'delegationId': delegationId, 'note': reason, 'fromExported': fromExported, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
            },
            function (result) {
                if (fromExported) {
                    if (result) {
                        showCannotBeDoneMessages(cannotBeRecalled,'Recalled')
                        Common.showScreenSuccessMsg();
                        GridCommon.Refresh('grdExportedDocumentsItems');
                    }
                } else {
                    swal.close()
                    TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                    TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);
                    TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                    $($("input[data-id='" + id + "']").parents("li")[0]).fadeOut().remove();
                    if (gSelectedRowId === id) {
                        $(".withBorders-o").addClass("waitingBackground");
                        $("#sentDocumentDetailsContainer").empty();
                    }
                }
            }, function () {
                gLocked = false; Common.showScreenErrorMsg();
            }, function () { gLocked = false; });
    }
    function showCannotBeDoneMessages(cannotBeDone,action) {
        if (cannotBeDone.length > 0) {

            let msg = "";
            let CannotDoneWarning = "";
            switch (action) {
                case 'resent':
                    CannotDoneWarning = Resources.CannotResentWarning;
                    break;
                case 'Recalled':
                    CannotDoneWarning = Resources.CannotRecalledWarning;
                    break;
                case 'Dismissed':
                    CannotDoneWarning = Resources.CannotDismissedWarning;
                    break;
            }
            cannotBeDone.forEach(item => {
                if (item.toStructure || item.subject) {
                    msg += `\n ○ -`;

                    if (item.toStructure) {
                        msg += ` ${Resources.Structure}: "${item.toStructure}"`;
                    }

                    if (item.subject) {
                        msg += `${item.toStructure ? ',' : ''} ${Resources.Subject}: "${item.subject}"`;
                    }
                    msg += `\n`;
                }
            });

            if (msg !== "") {
                setTimeout(function () {
                    Common.alertMsg(`${CannotDoneWarning}:\n` + msg);
                }, 300);
            }
        }
    }
    // send transfer Via context (send id of the row) or toolbar (get the selected rows)
    function Transfers(id) {
        var vipId;
        var prioritiesList;
        var delegationId = window.location.hash.split("/")[2];
        if (delegationId == undefined)
            delegationId = null;
        var gTableName = "grdInboxItems";
        var ids = [];
        if (id == null) // from toolbar 
            ids = GridCommon.GetSelectedRows(gTableName);
        else if (id != null && !(window.InboxMode == "InboxVIPView" || window.InboxMode == 'LocalVIPView')) // from context menu default view
            ids.push(id);
        else
            vipId = id;
        let categories = new Categories().get(window.language, null)
        var privacies = new CoreComponents.Lookup.Privacies().get(window.language);
        Common.ajaxGet("Priority/List", null, function (data) {
            prioritiesList = data;
            for (var i = 0; i < data.length; i++) {
                var text = data[i].name;
                if (window.language === "ar" && data[i].nameAr !== "") {
                    text = data[i].nameAr;
                } else if (window.language === "fr" && data[i].nameFr !== "") {
                    text = data[i].nameFr;
                }
                prioritiesList[i].text = text;
            }
        }, function () { Common.showScreenErrorMsg(); }, null, null, false);

        var gIsDueDateRelatedToPriority;
        if (ids.length > 0) {
            if (ids.length > 0) {
                var table = $('#' + gTableName).DataTable();
                var allRows = table.rows().data();
                var allCced = true;
                var cCedAndNotBroadcast = false;
                var allReadOnly = true;
                var allSelectedData = [];
                var broadcastIds = [];
                for (var i = 0; i < ids.length; i++) {
                    var selectedRowData = $.grep(allRows, function (element, index) {
                        return element.id === Number(ids[i]);
                    });
                    if (selectedRowData && selectedRowData[0]) {

                        let category = $.grep(categories, function (e) {
                            return e.id === selectedRowData[0].categoryId;
                        });

                        let categoriesObj = new CategoryModel().findFullById(category[0].id);
                        if (categoriesObj) {
                            let basicAttributes = JSON.parse(categoriesObj.basicAttribute);

                            if (basicAttributes.length > 0) {
                                var dueDateRelatedToPriority = $.grep(basicAttributes, function (e) {
                                    return e.Name === "DueDate";
                                });
                                gIsDueDateRelatedToPriority = dueDateRelatedToPriority[0].RelatedToPriority;
                            }
                        }
                        if (allCced && (!selectedRowData[0].cced || category[0].isInternalBroadcast)) {
                            allCced = false;
                        }
                        if (selectedRowData[0].cced && !category[0].isInternalBroadcast) {
                            cCedAndNotBroadcast = true;
                        }
                        if ((!selectedRowData[0].cced || category[0].isInternalBroadcast) && !selectedRowData[0].isLocked && selectedRowData[0].workflowStepId == null) {
                            allReadOnly = false;
                            allSelectedData.push(selectedRowData[0]);
                            if (category[0].isInternalBroadcast) {
                                broadcastIds.push(Number(ids[i]));
                            }
                        } else if (selectedRowData[0].isLocked) {
                            //var delegatedUserIds = $("#hdDelegatedUserIds").val().split(window.Splitter);
                            var delegatedUser = delegationId !== null ? new DelegationUsers().getById(Number(delegationId)) : null; //$("#hdDelegatedUserIds").val().split(window.Splitter);
                            var delegatedUserId = delegatedUser !== null ? delegatedUser.fromUserId : 0;
                            if ((selectedRowData[0].ownerUserId !== null && selectedRowData[0].ownerUserId === Number($("#hdUserId").val()) && delegationId === null)
                                || (selectedRowData[0].ownerDelegatedUserId !== null && selectedRowData[0].ownerDelegatedUserId === Number($("#hdUserId").val())
                                    && delegatedUserId === selectedRowData[0].ownerUserId && delegationId !== null)
                                || (selectedRowData[0].ownerUserId !== null && delegatedUserId === Number(selectedRowData[0].ownerUserId) && delegationId !== null)) {
                                allReadOnly = false;
                                allSelectedData.push(selectedRowData[0]);
                                if (category[0].isInternalBroadcast) {
                                    broadcastIds.push(Number(ids[i]));
                                }
                            }
                        }
                    }
                }



                if (allCced || allReadOnly) {
                    if (allCced) {
                        Common.alertMsg(Resources.AllSelectedItemsCCed);
                    } else {
                        Common.alertMsg(Resources.AllSelectedItemsAreReadOnlyOrHasWorkflow);
                    }
                } else {
                    if (allSelectedData.length > 0) {
                        var nonBroadcastIds = $.grep(allSelectedData, function (value) {
                            return $.inArray(value.id, broadcastIds) < 0;
                        });
                        if (nonBroadcastIds.length == 0) {
                            if (cCedAndNotBroadcast) {
                                Common.alertMsg(Resources.AllSelectedItemsAreBroadcastAndCarbonCopies);
                                return;
                            } else {
                                Common.alertMsg(Resources.AllSelectedItemsAreBroadcast);
                                return;
                            }
                        }
                        var dataSelected = allSelectedData.map(t => t.toStructureId).filter(function (value, index, array) {
                            return array.indexOf(value) === index;
                        });
                        if (window.EnableSendingRules === "True" && dataSelected.length > 1) {
                            Common.alertMsg(Resources.AllSelectedItemsHaveDifferentSender);
                            return;
                        }
                        var callback = function (data) {
                            var arrayOfTransfers = [];
                            var transferToStructures = [], transferToStructureIds = [];
                            var ccedTransfer = 0;
                            var purposes = new CoreComponents.Lookup.Purposes().get(window.language);

                            var hasPrivacyLevel = true;
                            var htmlPrivacy = Resources.NoUserWithSelectedPrivacy;
                            var structureExist = data.some(a => a.toStructureId !== null && a.toUserId === null);
                            let selectedPrivacy = $.grep(privacies, function (e) {
                                return e.id === selectedRowData[0].privacyId;
                            });
                            for (var i = 0; i < data.length; i++) {
                                var currentPurpose = $.grep(purposes, function (e) {
                                    return e.id.toString() === data[i].purposeId;
                                });
                                if (currentPurpose[0].cCed === true) {
                                    ccedTransfer++;
                                }
                                if (data[i].toUserId === null) {
                                    transferToStructures.push({ dataId: data[i].toStructureId, text: data[i].name });
                                    transferToStructureIds.push(data[i].toStructureId);
                                }
                                for (var j = 0; j < allSelectedData.length; j++) {
                                    arrayOfTransfers.push({
                                        toStructureId: data[i].toStructureId,
                                        toUserId: data[i].toUserId,
                                        name: data[i].name,
                                        dueDate: data[i].dueDate,
                                        purposeId: data[i].purposeId,
                                        instruction: data[i].instruction,
                                        cced: data[i].cced,
                                        fromStructureId: allSelectedData[j].toStructureId,
                                        parentTransferId: allSelectedData[j].id,
                                        isStructure: data[i].toUserId === null,
                                        documentId: allSelectedData[j].documentId,
                                        privacyId: allSelectedData[j].privacyId,
                                        referenceNumber: allSelectedData[j].referenceNumber
                                    });
                                }
                            }

                            var userStructureIds = $("#hdStructureIds").val();

                            var message = SendTransferModal.checkAbilityToSend(transferToStructureIds.length === 0 ? userStructureIds.split(window.Seperator) : transferToStructureIds, transferToStructures, selectedPrivacy, privacies, null, false);
                            if (message === "error") {
                                return;
                            }
                            for (var i = 0; i < arrayOfTransfers.length; i++) {
                                if (arrayOfTransfers[i].toUserId !== null) {
                                    // arrayOfTransfers[i].privacyId = 0;
                                    var userObj = new IdentityService().getFullUser(arrayOfTransfers[i].toUserId);
                                    if (userObj !== null) {
                                        var attributePrivacy = $.grep(userObj.attributes, function (e) {
                                            return e.text === window.UserPrivacy ? e.value : 0;
                                        });
                                        //if (attributePrivacy.length > 0)
                                        //{
                                        //    arrayOfTransfers[i].privacyId = attributePrivacy[0].value === "" ? 0 : attributePrivacy[0].value;
                                        //}
                                    }
                                    //var currentPrivacy = $.grep(model.privacies, function (e)
                                    //{
                                    //    return e.id.toString() === arrayOfTransfers[i].privacyId.toString();
                                    //});
                                    if (attributePrivacy !== null && attributePrivacy.length > 0) {
                                        if (arrayOfTransfers[i].privacyId > parseInt(attributePrivacy[0].value)) {
                                            hasPrivacyLevel = false;
                                            htmlPrivacy += ' \n ○ ' + arrayOfTransfers[i].name;
                                            if (arrayOfTransfers[i].referenceNumber) {
                                                htmlPrivacy += ' : ' + arrayOfTransfers[i].referenceNumber;
                                            }
                                        }
                                    } else {
                                        hasPrivacyLevel = false;
                                        htmlPrivacy += ' \n ○ ' + arrayOfTransfers[i].name;
                                        if (arrayOfTransfers[i].referenceNumber) {
                                            htmlPrivacy += ' : ' + arrayOfTransfers[i].referenceNumber;
                                        }
                                    }
                                }
                            }
                            if (!hasPrivacyLevel) {
                                message += (message !== "" ? " \n " : "") + htmlPrivacy;
                            }
                            if (message !== "") {
                                if (!structureExist) {
                                    Common.showConfirmMsg(message + " \n " + Resources.ContinueConfirmation, function () {
                                        SendTransferModal.transfer(transferComponent, arrayOfTransfers, false, false, true, delegationId, true);
                                    }, function () {
                                    });
                                }
                                else {
                                    if (window.SendWithoutStructureReceiverOrPrivacyLevel === "True") {
                                        Common.showConfirmMsg(message + " \n " + Resources.ContinueConfirmation, function () {
                                            SendTransferModal.transfer(transferComponent, arrayOfTransfers, false, false, true, delegationId, true);
                                        }, function () {
                                        });
                                    } else {
                                        Common.alertMsg(message);
                                    }
                                }
                            } else {
                                if (ccedTransfer === data.length) {
                                    Common.showConfirmCcedMsg(Resources.AllSelectedTransfersAreCCconfirmation, function () {
                                        SendTransferModal.transfer(transferComponent, arrayOfTransfers, true, false, true, delegationId, true);
                                    }, function () {
                                        SendTransferModal.transfer(transferComponent, arrayOfTransfers, false, true, true, delegationId, true);
                                    });
                                } else {
                                    SendTransferModal.transfer(transferComponent, arrayOfTransfers, false, false, true, delegationId, true);
                                }
                            }
                        };
                        let modalWrapper = $(".modal-window");
                        var documentId = selectedRowData[0].documentId
                        if (ids.length > 1) {
                            transferComponent = new Transfer(dataSelected[0], $("#hdUserId").val(), $("#hdStructureIds").val().split(window.Seperator),
                                window.IsStructureSender === "True", window.EnableSendingRules === "True", window.EnableTransferToUsers === "True", null, delegationId,
                                callback, modalWrapper, false, null, gIsDueDateRelatedToPriority, prioritiesList, documentId);
                        } else {
                            transferComponent = new Transfer(dataSelected[0], $("#hdUserId").val(), $("#hdStructureIds").val().split(window.Seperator),
                                window.IsStructureSender === "True", window.EnableSendingRules === "True", window.EnableTransferToUsers === "True", selectedRowData[0].documentDueDate, delegationId,
                                callback, modalWrapper, false, null, gIsDueDateRelatedToPriority, prioritiesList, documentId);
                        }
                       
                        transferComponent.render();
                        $('.modalTransfer').modal('show');
                        $(".modalTransfer").off("hidden.bs.modal");
                        $(".modalTransfer").off("shown.bs.modal");
                        $('.modalTransfer').on('hidden.bs.modal', function () {
                            $(".modalTransfer").parent().remove();
                            swal.close();
                        });

                    }
                }
            }
            else {

                ///////////////////////////////////
                Common.alertMsg(Resources.NoRowSelected);
            }

        }

        else {
            if (!$(".filterInfoDiv").hasClass("hidden")) {
                $(".filterInfoDiv").addClass("hidden");
            }

            var ids = new Array();
            if (vipId != null)
                ids.push(vipId);
            else {
                var checkedRows = $('#inboxListContainer li').find('input[type="checkbox"]:checked');
                checkedRows.each(function (index, obj) {
                    ids.push(obj.getAttribute('data-id'));
                });
            }
                
            if (ids.length > 0) {
                
                var allCced = true;
                var allReadOnly = true;
                var allSelectedData = [];
                var broadcastIds = [];
                var cCedAndNotBroadcast = false;
                for (var i = 0; i < ids.length; i++) {
                    var selectedRowData = $("input[type='hidden'][data-id='" + ids[i] + "']").val();
                    if (selectedRowData && selectedRowData !== "") {
                        selectedRowData = JSON.parse(selectedRowData);
                        let category = $.grep(categories, function (e) {
                            return e.id === selectedRowData.categoryId;
                        });

                        let categoriesObj = new CategoryModel().findFullById(category[0].id);
                        if (categoriesObj) {
                            let basicAttributes = JSON.parse(categoriesObj.basicAttribute);

                            if (basicAttributes.length > 0) {
                                var dueDateRelatedToPriority = $.grep(basicAttributes, function (e) {
                                    return e.Name === "DueDate";
                                });
                                gIsDueDateRelatedToPriority = dueDateRelatedToPriority[0].RelatedToPriority;
                            }
                        }
                        if (allCced && (!selectedRowData.cced || category[0].isInternalBroadcast)) {
                            allCced = false;
                        }
                        if (selectedRowData.cced && !category[0].isInternalBroadcast) {
                            cCedAndNotBroadcast = true;
                        }
                        if ((!selectedRowData.cced || category[0].isInternalBroadcast) && !selectedRowData.isLocked) {
                            allReadOnly = false;
                            allSelectedData.push(selectedRowData);
                            if (category[0].isInternalBroadcast) {
                                broadcastIds.push(Number(ids[i]));
                            }
                        } else if (selectedRowData.isLocked) {
                            var delegatedUser = delegationId !== null ? new DelegationUsers().getById(Number(delegationId)) : null; //$("#hdDelegatedUserIds").val().split(window.Splitter);
                            var delegatedUserId = delegatedUser !== null ? delegatedUser.fromUserId : 0;
                            if ((selectedRowData.ownerUserId !== null && selectedRowData.ownerUserId === Number($("#hdUserId").val()) && delegationId === null)
                                || (selectedRowData.ownerDelegatedUserId !== null && selectedRowData.ownerDelegatedUserId === Number($("#hdUserId").val())
                                    && delegatedUserId === selectedRowData.ownerUserId && delegationId !== null)
                                || (selectedRowData.ownerUserId !== null && delegatedUserId === Number(selectedRowData.ownerUserId) && delegationId !== null)) {
                                allReadOnly = false;
                                allSelectedData.push(selectedRowData);
                                if (category[0].isInternalBroadcast) {
                                    broadcastIds.push(Number(ids[i]));
                                }
                            }
                        }
                    }
                }
                if (allCced || allReadOnly) {
                    if (allCced) {
                        Common.alertMsg(Resources.AllSelectedItemsCCed);
                    } else {
                        Common.alertMsg(Resources.AllSelectedItemsAreReadOnly);
                    }
                } else {
                    if (allSelectedData.length > 0) {
                        var nonBroadcastIds = $.grep(allSelectedData, function (value) {
                            return $.inArray(value.id, broadcastIds) < 0;
                        });
                        if (nonBroadcastIds.length == 0) {
                            if (cCedAndNotBroadcast) {
                                Common.alertMsg(Resources.AllSelectedItemsAreBroadcastAndCarbonCopies);
                                return;
                            } else {
                                Common.alertMsg(Resources.AllSelectedItemsAreBroadcast);
                                return;
                            }
                        }
                        var dataSelected = allSelectedData.map(t => t.toStructureId).filter(function (value, index, array) {
                            return array.indexOf(value) === index;
                        });
                        if (window.EnableSendingRules === "True" && dataSelected.length > 1) {
                            Common.alertMsg(Resources.AllSelectedItemsHaveDifferentSender);
                            return;
                        }
                        var callback = function (data) {
                            var arrayOfTransfers = [];
                            var transferToStructures = [], transferToStructureIds = [];
                            var ccedTransfer = 0;
                            var purposes = new CoreComponents.Lookup.Purposes().get(window.language);
                            var hasPrivacyLevel = true;
                            var htmlPrivacy = Resources.NoUserWithSelectedPrivacy;
                            var structureExist = data.some(a => a.toStructureId !== null && a.toUserId === null);
                            let selectedPrivacy = $.grep(privacies, function (e) {
                                return e.id === selectedRowData.privacyId;
                            });
                            for (var i = 0; i < data.length; i++) {
                                var currentPurpose = $.grep(purposes, function (e) {
                                    return e.id.toString() === data[i].purposeId;
                                });
                                if (currentPurpose[0].cCed === true) {
                                    ccedTransfer++;
                                }
                                if (data[i].toUserId === null) {
                                    transferToStructures.push({ dataId: data[i].toStructureId, text: data[i].name });
                                    transferToStructureIds.push(data[i].toStructureId);
                                }
                                for (var j = 0; j < allSelectedData.length; j++) {
                                    arrayOfTransfers.push({
                                        toStructureId: data[i].toStructureId,
                                        toUserId: data[i].toUserId,
                                        name: data[i].name,
                                        dueDate: data[i].dueDate,
                                        purposeId: data[i].purposeId,
                                        instruction: data[i].instruction,
                                        cced: data[i].cced,
                                        fromStructureId: allSelectedData[j].toStructureId,
                                        parentTransferId: allSelectedData[j].id,
                                        isStructure: data[i].toUserId === null,
                                        documentId: allSelectedData[j].documentId,
                                        privacyId: allSelectedData[j].privacyId,
                                        referenceNumber: allSelectedData[j].referenceNumber
                                    });
                                }
                            }
                            var userStructureIds = $("#hdStructureIds").val();
                            var message = SendTransferModal.checkAbilityToSend(transferToStructureIds.length === 0 ? userStructureIds.split(window.Seperator) : transferToStructureIds, transferToStructures, selectedPrivacy, privacies, null, false);
                            if (message === "error") {
                                return;
                            }
                            for (var i = 0; i < arrayOfTransfers.length; i++) {
                                if (arrayOfTransfers[i].toUserId !== null) {
                                    var userObj = new IdentityService().getFullUser(arrayOfTransfers[i].toUserId);
                                    if (userObj !== null) {
                                        var attributePrivacy = $.grep(userObj.attributes, function (e) {
                                            return e.text === window.UserPrivacy ? e.value : 0;
                                        });
                                    }
                                    if (attributePrivacy !== null && attributePrivacy.length > 0) {
                                        if (arrayOfTransfers[i].privacyId > parseInt(attributePrivacy[0].value)) {
                                            hasPrivacyLevel = false;
                                            htmlPrivacy += ' \n ○ ' + arrayOfTransfers[i].name;
                                            if (arrayOfTransfers[i].referenceNumber) {
                                                htmlPrivacy += ' : ' + arrayOfTransfers[i].referenceNumber;
                                            }
                                        }
                                    } else {
                                        hasPrivacyLevel = false;
                                        htmlPrivacy += ' \n ○ ' + arrayOfTransfers[i].name;
                                        if (arrayOfTransfers[i].referenceNumber) {
                                            htmlPrivacy += ' : ' + arrayOfTransfers[i].referenceNumber;
                                        }
                                    }
                                }
                            }
                            if (!hasPrivacyLevel) {
                                message += (message !== "" ? " \n " : "") + htmlPrivacy;
                            }
                            if (message !== "") {
                                if (!structureExist) {
                                    Common.showConfirmMsg(message + " \n " + Resources.ContinueConfirmation, function () {
                                        SendTransferModal.transfer(transferComponent, arrayOfTransfers, false, false, true, delegationId, true, true, refreshInboxList);
                                    }, function () {
                                    });
                                }
                                else {
                                    if (window.SendWithoutStructureReceiverOrPrivacyLevel === "True") {
                                        Common.showConfirmMsg(message + " \n " + Resources.ContinueConfirmation, function () {
                                            SendTransferModal.transfer(transferComponent, arrayOfTransfers, false, false, true, delegationId, true, true, refreshInboxList);
                                        }, function () {
                                        });
                                    } else {
                                        Common.alertMsg(message);
                                    }
                                }
                            } else {
                                if (ccedTransfer === data.length) {
                                    Common.showConfirmCcedMsg(Resources.AllSelectedTransfersAreCCconfirmation, function () {
                                        SendTransferModal.transfer(transferComponent, arrayOfTransfers, true, false, true, delegationId, true, true, refreshInboxList);
                                    }, function () {
                                        SendTransferModal.transfer(transferComponent, arrayOfTransfers, false, true, true, delegationId, true, true, refreshInboxList);
                                    });
                                } else {
                                    SendTransferModal.transfer(transferComponent, arrayOfTransfers, false, false, true, delegationId, true, true, refreshInboxList);
                                }
                            }
                        };

                        let modalWrapper = $(".modal-window");

                        var transferComponent = new Transfer(dataSelected[0], $("#hdUserId").val(), $("#hdStructureIds").val().split(window.Seperator),
                            window.IsStructureSender === "True", window.EnableSendingRules === "True", window.EnableTransferToUsers === "True", "", delegationId,
                            callback, modalWrapper, false, null, gIsDueDateRelatedToPriority, prioritiesList);
                        transferComponent.render();
                        $('.modalTransfer').modal('show');
                        $(".modalTransfer").off("hidden.bs.modal");
                        $(".modalTransfer").off("shown.bs.modal");
                        $('.modalTransfer').on('hidden.bs.modal', function () {
                            $(".modalTransfer").parent().remove();
                            swal.close();
                        });
                    }
                }
            }
            else {
                Common.alertMsg(Resources.NoRowSelected);
            }
        }
    }
    E.completeTransfer = function () {
        var delegationId = window.location.hash.split("/")[2];
        if (delegationId == undefined)
            delegationId = null;
        let categories = new Categories().get(window.language, null)
        var gTableName = "grdInboxItems";
        var ids = GridCommon.GetSelectedRows(gTableName);
        if (ids.length > 0) {
            var lstReceivers = [];
            var allSelectedData = [];
            if (ids.length > 0) {
                var table = $('#' + gTableName).DataTable();
                var allRows = table.rows().data();
                var allCced = true;
                for (var i = 0; i < ids.length; i++) {
                    var selectedRowData = $.grep(allRows, function (element, index) {
                        return element.id === Number(ids[i]);
                    });
                    allSelectedData.push(selectedRowData[0]);
                    let category = $.grep(categories, function (e) {
                        return e.id === selectedRowData[0].categoryId;
                    });
                    if (selectedRowData && selectedRowData[0] && allCced && (!selectedRowData[0].cced || category[0].isInternalBroadcast)) {
                        allCced = false;
                    }
                }
                if (allCced) {
                    Common.alertMsg(Resources.AllSelectedItemsCCed);
                } else {
                    if (window.EnableConfirmationMessage === "True") {
                        Common.showConfirmMsg(Resources.CompleteConfirmation, function () {
                            completeTransfer(ids, delegationId);
                        });
                    } else {
                        completeTransfer(ids, delegationId);
                    }
                }
            }
            else {
                Common.alertMsg(Resources.NoRowSelected);
            }

        }

        else {
            if (!$(".filterInfoDiv").hasClass("hidden")) {
                $(".filterInfoDiv").addClass("hidden");
            }
            var checkedRows = $('#inboxListContainer li').find('input[type="checkbox"]:checked');
            if (checkedRows.length > 0) {
                var ids = new Array();
                checkedRows.each(function (index, obj) {
                    ids.push(obj.getAttribute('data-id'));
                });
                var allCced = true;
                var allSelectedData = [];
                for (var i = 0; i < ids.length; i++) {
                    var selectedRowData = $("input[type='hidden'][data-id='" + ids[i] + "']").val();
                    if (selectedRowData && selectedRowData !== "") {
                        selectedRowData = JSON.parse(selectedRowData);
                        allSelectedData.push(selectedRowData);
                        let category = $.grep(categories, function (e) {
                            return e.id === selectedRowData.categoryId;
                        });
                        if (allCced && (!selectedRowData.cced || category[0].isInternalBroadcast)) {
                            allCced = false;
                        }
                    }
                }
                if (allCced) {
                    Common.alertMsg(Resources.AllSelectedItemsCCed);
                } else {
                    if (window.EnableConfirmationMessage === "True") {
                        Common.showConfirmMsg(Resources.CompleteConfirmation, function () {
                            completeTransferVip(ids, delegationId);
                        });
                    } else {
                        completeTransferVip(ids, delegationId);
                    }
                }
            }
            else {
                Common.alertMsg(Resources.NoRowSelected);
            }
        }



    }

    E.dismissCarbonCopy = function () {

        var delegationId = window.location.hash.split("/")[2];
        if (delegationId == undefined)
            delegationId = null;
        var gTableName = "grdInboxItems";


        var ids = GridCommon.GetSelectedRows(gTableName);
        if (ids.length > 0) {
            if (ids.length > 0) {
                var table = $('#' + gTableName).DataTable();
                var allRows = table.rows().data();
                var atLeastOneCced = false;
                var dismissIds = [];
                var broadcastIds = [];
                var allSelectedData = [];
                //var categories = [];
                for (var i = 0; i < ids.length; i++) {
                    var selectedRowData = $.grep(allRows, function (element, index) {
                        return element.id === Number(ids[i]);
                    });
                    if (selectedRowData && selectedRowData[0] && selectedRowData[0].cced) {
                        allSelectedData.push(selectedRowData[0]);
                        let categories = new Categories().get(window.language, null)
                        //let category = new CategoryModel().findFullById(selectedRowData[0].categoryId);


                        let category = $.grep(categories, function (e) {
                            return e.id === selectedRowData[0].categoryId;
                        });
                        if (category[0].isInternalBroadcast) {
                            broadcastIds.push(Number(ids[i]));
                        }
                        if (!atLeastOneCced && !category[0].isInternalBroadcast) {
                            atLeastOneCced = true;
                        }
                        dismissIds.push(Number(ids[i]));
                    }
                }
                var nonBroadcastIds = $.grep(dismissIds, function (value) {
                    return $.inArray(Number(value), broadcastIds) < 0;
                });

                if (!atLeastOneCced && broadcastIds.length !== ids.length) {
                    Common.alertMsg(Resources.AllSelectedItemsNotCCed);
                } else if (nonBroadcastIds.length === 0) {
                    Common.alertMsg(Resources.AllSelectedItemsAreBroadcast);
                }
                else {
                    if (dismissIds.length > 0) {
                        if (window.EnableConfirmationMessage === "True") {
                            Common.showConfirmMsg(Resources.DismissCarbonCopyConfirmation, function () {
                                dismissCarbonCopy(dismissIds, delegationId, allSelectedData);
                            });
                        } else {
                            dismissCarbonCopy(dismissIds, delegationId, allSelectedData);
                        }
                    }
                }
            }
            else {
                Common.alertMsg(Resources.NoRowSelected);
            }
        }

        else {
            if (!$(".filterInfoDiv").hasClass("hidden")) {
                $(".filterInfoDiv").addClass("hidden");
            }
            var checkedRows = $('#inboxListContainer li').find('input[type="checkbox"]:checked');
            if (checkedRows.length > 0) {
                var ids = new Array();
                checkedRows.each(function (index, obj) {
                    ids.push(obj.getAttribute('data-id'));
                });
                var atLeastOneCced = false;
                var dismissIds = [];
                var broadcastIds = [];
                var allSelectedData = [];
                for (var i = 0; i < ids.length; i++) {


                    let categories = new Categories().get(window.language, null)
                    var selectedRowData = $("input[type='hidden'][data-id='" + ids[i] + "']").val();
                    if (selectedRowData && selectedRowData !== "") {
                        selectedRowData = JSON.parse(selectedRowData);
                        if (selectedRowData.cced) {
                            dismissIds.push(Number(ids[i]));
                            allSelectedData.push(selectedRowData);

                            let category = $.grep(categories, function (e) {
                                return e.id === selectedRowData.categoryId;
                            });
                            if (category[0].isInternalBroadcast) {
                                broadcastIds.push(Number(ids[i]));
                            }
                            if (!atLeastOneCced && !category[0].isInternalBroadcast) {
                                atLeastOneCced = true;
                            }
                        }
                    }
                }
                var nonBroadcastIds = $.grep(dismissIds, function (value) {
                    return $.inArray(Number(value), broadcastIds) < 0;
                });

                if (!atLeastOneCced && broadcastIds.length !== ids.length) {
                    Common.alertMsg(Resources.AllSelectedItemsNotCCed);
                } else if (nonBroadcastIds.length === 0) {
                    Common.alertMsg(Resources.AllSelectedItemsAreBroadcast);
                } else {
                    if (dismissIds.length > 0) {
                        if (window.EnableConfirmationMessage === "True") {
                            Common.showConfirmMsg(Resources.DismissCarbonCopyConfirmation, function () {
                                dismissCarbonCopyVip(dismissIds, delegationId, allSelectedData);
                            });
                        } else {
                            dismissCarbonCopyVip(dismissIds, delegationId, allSelectedData);
                        }
                    }
                }
            }
            else {
                Common.alertMsg(Resources.NoRowSelected);
            }
        }

    }

    E.TransferContext = function (data) {
        Transfers(data.id);
    }
    E.dismissCarbonCopyContext = function (data) {
        dismissCarbonCopy(data.id);
    }
    E.CompleteTransferContext = function (data) {
        completeTransfer(data.id);
    }
    E.ShowActivityLogs = function (data) {

        $('#modalActivityLogs').remove();
        const modalContainer = $(`
            <div id="modalActivityLogs" tabindex="-1" role="dialog" aria-hidden="true" class="modal fade">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close ActivityLogModalClose">&times;</button>
                            <h4 id="modalActivityLogsTitle" class="modal-title">${Resources.ActivityLog}</h4>
                        </div>
                        <div class="modal-body">
                        </div>
                    </div>
                </div>
            </div>     
        `);

        $('.modal-window').append(modalContainer);

        $(document).on('click', '.ActivityLogModalClose', function () {
            $('#modalActivityLogs').modal('hide');
        });
        const wrapper = modalContainer.find('.modal-body');
        var model = new ActivityLogTimeline.ActivityLogTimeline();
        model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
        model.documentId = data.documentId ? data.documentId : data.document ? data.document.id:data.id;
        model.delegationId = data.delegationId;

        var documentView = new ActivityLogTimeline.ActivityLogTimelineView(wrapper, model);
        documentView.render();
        $('#modalActivityLogs').modal('show');
        $('#modalActivityLogs').on('hidden.bs.modal', function () {
            $(this).remove(); 
        });
    }
    E.transfer = function () {
        Transfers(null);
    }
    E.addToBasket = function (data) {
        var vipId = null;
        var delegationId = window.location.hash.split("/")[2];
        if (delegationId == undefined)
            delegationId = null;
        var selectedRadio = document.querySelector('input[name="InboxMode"]:checked');
        var selectedValue = window.InboxMode; 
        var selectedDocumentIds = [];
        var gTableName = "grdInboxItems";
        var ids = [];
        if (data == null) // from toolbar 
            ids = GridCommon.GetSelectedRows(gTableName);
        else if (data != null && !(window.InboxMode == "InboxVIPView" || window.InboxMode == 'LocalVIPView')) {
            if (Array.isArray(data))
                ids.push(...data);
            else
                ids.push(data);
        }
            // from context menu default view // from context menu
            //ids.push(...data);
        else
            vipId = data;
        if (ids.length > 0) {
          
            var table = $('#' + gTableName).DataTable();
            var allRows = table.rows().data();
            for (var i = 0; i < ids.length; i++) {
                var selectedRowData = $.grep(allRows, function (element, index) {
                    return element.id === Number(ids[i]);
                });
                if (selectedValue == 'InboxDefault' || selectedValue == 'LocalInboxDefaultView' ) 
                    selectedDocumentIds.push(selectedRowData[0].documentId);
                else if (selectedValue == "InboxDefaultWithGrouping")
                    selectedDocumentIds.push(selectedRowData[0].documentId); 
            }
        }
        else {
            var dids = new Array();
            if (vipId != null) {
            //  dids.push(...vipId);
                if (Array.isArray(data))
                    dids.push(...data);
                else
                    dids.push(data);
            }
            else {
                var checkedRows = $('#inboxListContainer li').find('input[type="checkbox"]:checked');
                checkedRows.each(function (index, obj) {
                    dids.push(obj.getAttribute('data-id'));
                });
            }
            if (dids.length > 0) {
                for (var i = 0; i < dids.length; i++) {
                    var selectedRowData = $("input[type='hidden'][data-id='" + dids[i] + "']").val();
                    if (selectedRowData && selectedRowData !== "") {
                        selectedRowData = JSON.parse(selectedRowData);
                        selectedDocumentIds.push(selectedRowData.documentId);
                    }
                }
            }
           
        }
        var wrapper = $(".modal-window");
        var modelIndex = new BasketFromNodeIndex.AddBasketFromNodeIndex();
        modelIndex.selectedDocumentIds = selectedDocumentIds;
     
        var grpIndex = new BasketFromNodeIndex.AddBasketFromNodeIndexView(wrapper, modelIndex);
        grpIndex.render();
        $('#modalAddToBasketTitle').html(Resources.Add);
        $('#modalAddToBasket').addClass('modalScroll');
        $('#modalAddToBasket').modal('show');
        $("#modalAddToBasket").off("hidden.bs.modal");
        $("#modalAddToBasket").off("shown.bs.modal");
        $('#modalAddToBasket').on('shown.bs.modal', function () {
        });
        $('#modalAddToBasket').on('hidden.bs.modal', function () {
            $('#indexAddToBasketContainer').show();
            swal.close();
            modelIndex.selectedDocumentIds = [];
            $("#modalAddToBasket").remove();
            swal.close();
        });
    }
    E.AddToBasketContext = function (data) {
        E.addToBasket(data.id);
    }

    E.createFollowUpWithDocuments = function () {
        if (window.location.hash.split("/").length >= 1) {
            //if (window.location.hash.split("/")[0].toLowerCase() == '#inbox') {
                if (window.location.hash.toLowerCase().includes("#inbox")) {

                var selectedDocumentIds = [];
                var gTableName = "grdInboxItems";
                var ids = GridCommon.GetSelectedRows(gTableName);
                if (ids.length > 0) {
                    var table = $('#' + gTableName).DataTable();
                    var allRows = table.rows().data();
                    for (var i = 0; i < ids.length; i++) {
                        var selectedRowData = $.grep(allRows, function (element, index) {
                            return element.id === Number(ids[i]);
                        });
                        if (selectedRowData && selectedRowData[0]) {
                            selectedDocumentIds.push(selectedRowData[0].documentId);
                        }
                    }
                }
                saveDocument(selectedDocumentIds, true);
            }
        }
    };
    E.reAssignTask = function (data) {
        var gTableName;
        var ids = [];
        var nodeName = (window.location.hash.split("/")[0]).toLowerCase();
        switch (nodeName) {
            case '#myrequests':
                gTableName = $("#grdMyRequestsItems").val() != undefined ? gMyRequestsTableName : "myRequestsListContainer";;
                break;
            case '#inbox':
                gTableName = $("#grdInboxItems").val() != undefined ? gInboxTableName : "inboxListContainer";
                break;
            case '#sent':
                gTableName = $("#grdSentItems").val() != undefined ? gSentTableName : "sentListContainer";
                break;
            default:
        }
        ids = $("#" + gTableName + " input:checkbox:checked").map(function () {
            return $(this).attr("data-id");
        }).get();
        if (ids.length > 0) {
            var wrapper = $(".modal-window");
            var modelIndex = new AssigneeIndex.AssigneeIndex();
            modelIndex.ids = data;

            if ((window.location.hash.split("/")[0]).toLowerCase() == '#myrequests') {
                modelIndex.fromMyRequestsNode = true;
            } else {
                modelIndex.fromMyRequestsNode = false;
            }
            modelIndex.fromGrid = true;
            var assigneeIndex = new AssigneeIndex.AssigneeIndexView(wrapper, modelIndex);
            assigneeIndex.render();

            $(assigneeIndex.refs['modalAssigneeTitle']).html(Resources.New);
            $(assigneeIndex.refs['modalAssignee']).modal("show");
            $(assigneeIndex.refs['modalAssignee']).off("hidden.bs.modal");
            $(assigneeIndex.refs['modalAssignee']).off("shown.bs.modal");
            $(assigneeIndex.refs['modalAssignee']).on('hidden.bs.modal', function () {
                $(assigneeIndex.refs['formAssigneePost']).parsley().reset();
                $(assigneeIndex.refs['assigneePostMessage']).html('');
                $(assigneeIndex.refs[assigneeIndex.model.ComponentId]).remove();
                swal.close();
                $('body').addClass('modal-open');
            });
        } else {
            Common.alertMsg(Resources.NoRowSelected);
        }
    };
    E.requestToComplete = function () {
        var gTableName, gridContainerName;
        var nodeName = (window.location.hash.split("/")[0]).toLowerCase();
        switch (nodeName) {
            case '#inbox':
                gTableName = $("#grdInboxItems").val() != undefined ? gInboxTableName : "inboxListContainer";
                gridContainerName = inboxGidContainerName;
                break;
            case '#sent':
                gTableName = $("#grdSentItems").val() != undefined ? gSentTableName : "sentListContainer";
                gridContainerName = sentGridContainerName;
                break;
            case '#managefollowup':
                gTableName = gManageFollowUpTableName;
                gridContainerName = followUpGridContainerName;
                break;
            default:
        }
        var ids = $("#" + gTableName + " input:checkbox:checked").map(function () {
            return $(this).attr("data-id");
        }).get();
        if (ids.length > 0) {
            Common.showConfirmMsg(Resources.CompleteConfirmation, function () {
                requestToCompleteTask(ids, gTableName, gridContainerName);
            });
        }
        else {
            Common.alertMsg(Resources.NoRowSelected);
        }
    };
    E.recallRowsBtn = function () {
        let recallData = [];
        let cannotBeRecalled = [];
        var delegationId = window.location.hash.split("/")[2];
        var gTableName, gridContainerName;
        var fromExported = true;
        var nodeId = window.location.hash.split("/")[1];
        var nodeName = (window.location.hash.split("/")[0]).toLowerCase();
        if (nodeName == '#sent') {
            gTableName = $("#grdSentItems").val() != undefined ? gSentTableName : "sentListContainer";
            gridContainerName = sentGridContainerName;
        } else {
            const nodes = new CoreComponents.Lookup.Nodes().get();
            nodeName = nodes.find(e => e.id == nodeId).name;
            if (nodeName == 'ExportedDocuments') {
                gTableName = 'grdExportedDocumentsItems';
                fromExported = true;
            }
        }
        var ids = $("#" + gTableName + " input:checkbox:checked").map(function () {
            
            return $(this).data("id");
        }).get();
        if (ids.length > 0) {
            Common.showConfirmMsg(Resources.RecallConfirmation, async function () {
                $.each(ids, function (index, id) { // Iterate over the 'ids' array
                    let $checkbox = $("#" + gTableName + " input:checkbox[data-id='" + id + "']");
                    let $row = fromExported == true ? $checkbox.closest('tr'):$checkbox.closest('li');
                    if ($row.find(".recallIcon").length > 0 || (fromExported == true && $row.find(".recall").length > 0)) {
                        recallData.push({ id: id });
                        if (!$(".filterInfoDiv").hasClass("hidden") && !fromExported) {
                            $(".filterInfoDiv").addClass("hidden");

                        }
                    } else {
                        let subject = $checkbox.attr("data-subject");
                        let toStructure = $checkbox.attr("data-toStructure");
                        cannotBeRecalled.push({ toStructure: toStructure, subject: subject })
                    }
                });

                if (recallData.length > 0) {
                    openRecallReasonModal(function (reason) {

                        recallData.forEach(rowData => {
                            recallRows(rowData.id, delegationId, fromExported, cannotBeRecalled, reason);
                        });
                    });
                }

                if (!fromExported || recallData.length === 0) {
                    showCannotBeDoneMessages(cannotBeRecalled,'Recalled')
                }
            });
        }
    }
    E.dismissRejectedDocuments = function () {
        let dismissData = [];
        let cannotBeDismissed = [];
        var delegationId = window.location.hash.split("/")[2];
        var nodeId = window.location.hash.split("/")[1];
        var gTableName = "grdRejectedDocumentsItems";
        var fromExported = true;
        var ids = $("#" + gTableName + " input:checkbox:checked").map(function () {
            return $(this).data("id");;
        }).get();
        var selectedData = $("#" + gTableName + " input:checkbox:checked").map(function () {
            return $(this).data();;
        }).get();

        if (ids.length > 0) {
            Common.showConfirmMsg(Resources.DismissConfirmation, async function () {
                $.each(ids, function (index, id) {
                    let $checkbox = $("#" + gTableName + " input:checkbox[data-id='" + id + "']");
                    let $row = $checkbox.closest('tr');
                    if ($row.find(".dismiss").length > 0) {
                        dismissData.push({ id: id });
                    } else {
                        let subject = $checkbox.attr("data-subject");
                        let toStructure = $checkbox.attr("data-toStructure");
                        cannotBeDismissed.push({ toStructure: toStructure, subject: subject })
                    }
                });
                if (dismissData.length > 0) {
                    dismissData.forEach(rowData => {
                        dismissRows(rowData.id, delegationId, fromExported, cannotBeDismissed, gTableName);
                    });
                }
                else {
                    showCannotBeDoneMessages(cannotBeDismissed,'Dismissed')
                }
            });
        }
    }
    function dismissRows(id, delegationId, fromExported, cannotBeDismissed, gTableName) {
        var gTableName = "grdRejectedDocumentsItems";
            Common.ajaxPost('/Transfer/Dismiss',
                {
                    'id': id, 'delegationId': delegationId, 'fromExported': fromExported, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                },
                function (result) {
                    if (result) {
                        showCannotBeDoneMessages(cannotBeDismissed,'Dismissed')
                        Common.showScreenSuccessMsg()
                        GridCommon.Refresh(gTableName);
                    } else {
                        Common.showScreenErrorMsg();
                    }
                    
                }, function () { Common.showScreenErrorMsg(); }, false
            );
        
    }
    E.resendRejectedDocuments = function () {
        let resendData = [];
        let cannotBeResended = [];
        var delegationId = window.location.hash.split("/")[2];
        var nodeId = window.location.hash.split("/")[1];
        var gTableName = "grdRejectedDocumentsItems";
        var fromExported = true;
        var ids = $("#" + gTableName + " input:checkbox:checked").map(function () {
            return $(this).data("id");;
        }).get();
        var selectedData = $("#" + gTableName + " input:checkbox:checked").map(function () {
            return $(this).data();;
        }).get();

        if (ids.length > 0) {
            Common.showConfirmMsg(Resources.ResendConfirmation, async function () {
                $.each(ids, function (index, id) {
                    let $checkbox = $("#" + gTableName + " input:checkbox[data-id='" + id + "']");
                    let $row = $checkbox.closest('tr');
                    if ($row.find(".resend").length > 0) {
                        resendData.push({ id: id });
                    } else {
                        let subject = $checkbox.attr("data-subject");
                        let toStructure = $checkbox.attr("data-toStructure");
                        cannotBeResended.push({ toStructure: toStructure, subject: subject })
                    }
                });
                if (resendData.length > 0) {
                    resendData.forEach(rowData => {
                        resendRows(rowData.id, delegationId, fromExported, cannotBeResended, gTableName);
                    });
                }
                else {
                    showCannotBeDoneMessages(cannotBeResended,'resent')
                }
            });
        }
    }
    function resendRows(data, delegationId, fromExported, cannotBeResended, gTableName) {
        var gTableName = "grdRejectedDocumentsItems";

            var broadcastModel = {};
            var exportedCategoryModel = new CategoryModel().findFullById(data.exportedDocumentCategoryId);
            if (typeof exportedCategoryModel !== 'undefined' && exportedCategoryModel !== "" && exportedCategoryModel !== null) {
                if (exportedCategoryModel.basicAttribute !== "" && exportedCategoryModel.basicAttribute !== null) {
                    let basicAttributes = JSON.parse(exportedCategoryModel.basicAttribute);
                    if (basicAttributes.length > 0) {
                        let receivingEntityObj = $.grep(basicAttributes, function (e) {
                            return e.Name === "ReceivingEntity";
                        });
                        if (receivingEntityObj[0].BroadcastReceivingEntity && receivingEntityObj[0].Type === "internal") {
                            broadcastModel.isInternalBroadcast = true;
                            broadcastModel.isBroadcast = true;
                        } else if (receivingEntityObj[0].BroadcastReceivingEntity) {
                            broadcastModel.isBroadcast = true;
                        }
                    }
                }
            }
            var receivingEntities = data.receivingEntityId;
            delegationId = null;
            var transferToType = broadcastModel.isInternalBroadcast ? TransferType.BroadcastSend : TransferType.Send;
            var transferToTxt = data.fromUserId;

            let modalWrapper = $(".modal-window");
            var modelIndex = new ExportOptions.ExportOptions();
           // let allPurposes = new Helper().get();
            var defaultPurposeForExport = window.ToViewPurpose /*allPurposes.filter(p => p.id === 13 || p.text.includes("To View") || p.text.includes("للعرض"))*/;
            modelIndex.purposes = new Helper().getPurpose();
            modelIndex.receivingEntities = receivingEntities;
            modelIndex.transferToType = transferToType;
            modelIndex.transferToUser = transferToTxt;
            modelIndex.transferToStructure = data.toStructureId;
            modelIndex.customAttributeDueDate = data.dueDate;
            modelIndex.documentId = data.exportedDocumentId;
            modelIndex.transferId = data.signedTransferId;
            modelIndex.fromVip = false;
            modelIndex.enableSendingRules = window.EnableSendingRules === "True";
            modelIndex.enableTransferToUsers = window.EnableTransferToUsers === "True";
            modelIndex.isStructureSender = window.IsStructureSender === "True";
            modelIndex.delegationId = delegationId;
            modelIndex.structureIds = $("#hdStructureIds").val().split(window.Seperator);
            modelIndex.fromStructureId = data.fromStructureId;
            modelIndex.signatureTemplate = null
            modelIndex.withSign = false;
            modelIndex.fromExport = false;
            modelIndex.fromResend = true;
            modelIndex.incomingDocumentId = data.documentId;
            modelIndex.isSpecific = false;
            if (transferToType == TransferType.BroadcastSend || transferToType == TransferType.BroadcastComplete) {
                modelIndex.isBroadcast = true;
                modelIndex.broadcastIds = data.SignedTransferId;
            }
            modelIndex.action = "Attribute.Export";
            var ExportOptionsView = new ExportOptions.ExportOptionsView(modalWrapper, modelIndex, function () {

                GridCommon.Refresh(gTableName);
                TreeNode.refreshTreeNodeCounts(TreeNode.Custom);
            });
            modelIndex.defaultPurposeForExport = defaultPurposeForExport ? defaultPurposeForExport[0].id : null;
            ExportOptionsView.render();
            $('#modalExportOptions').modal('show');
        
    }
    E.completeFollowUp = function () {
        
        var gTableName = "grdFollowUpItems";
        var ids = GridCommon.GetSelectedRows(gTableName);
        if (ids.length > 0) {
            var allSelectedData = [];
            var table = $('#' + gTableName).DataTable();
            var allRows = table.rows().data();
            var allCced = true;
            for (var i = 0; i < ids.length; i++) {
                var selectedRowData = $.grep(allRows, function (element, index) {
                    return element.id === Number(ids[i]);
                });
                allSelectedData.push(selectedRowData[0]);
            }
        }
        var paramData = []
        for (var i = 0; i < allSelectedData.length; i++) {
            paramData.push({ 'followUpId': allSelectedData[i].id, 'subject': allSelectedData[i].subject })
        }

        if (paramData.length > 0) {
            Common.showConfirmMsg(Resources.FollowUpCompleteConfirmation, function () {
                completeFollowUp(paramData);
            });
        }
        else {
            Common.alertMsg(Resources.NoRowSelected);
        }
    }
    E.cancelFollowUp = function () {
        
        var gTableName = "grdFollowUpItems";
        var ids = GridCommon.GetSelectedRows(gTableName);
        if (ids.length > 0) {
            var allSelectedData = [];
            var table = $('#' + gTableName).DataTable();
            var allRows = table.rows().data();
            var allCced = true;
            for (var i = 0; i < ids.length; i++) {
                var selectedRowData = $.grep(allRows, function (element, index) {
                    return element.id === Number(ids[i]);
                });
                allSelectedData.push(selectedRowData[0]);
            }
        }
        var paramData = []
        for (var i = 0; i < allSelectedData.length; i++) {
            paramData.push({ 'followUpId': allSelectedData[i].id, 'subject': allSelectedData[i].subject })
        }

        if (paramData.length > 0) {
            Common.showConfirmMsg(Resources.FollowUpCancelConfirmation, function () {
                cancelFollowUp(paramData);
            });
        }
        else {
            Common.alertMsg(Resources.NoRowSelected);
        }
    }

    E.postponeFollowUp = function () {
        
        var gTableName = "grdFollowUpItems";
        var ids = GridCommon.GetSelectedRows(gTableName);
        if (ids.length > 0) {
            var allSelectedData = [];
            var table = $('#' + gTableName).DataTable();
            var allRows = table.rows().data();
            var allCced = true;
            for (var i = 0; i < ids.length; i++) {
                var selectedRowData = $.grep(allRows, function (element, index) {
                    return element.id === Number(ids[i]);
                });
                allSelectedData.push(selectedRowData[0]);
            }
        }
     
        if (allSelectedData.length > 0) {
            if (allSelectedData.some(item => item.followUpUserRole === FollowUpRoles.Owner)) {
                if (window.EnableConfirmationMessage === "True") {
                    Common.showConfirmMsg(Resources.FollowUpPostponeConfirmation, function () {
                        openPostponeFollowUp(allSelectedData)

                    });
                } else
                {
                    openPostponeFollowUp(allSelectedData)
                }
                
            }
            else {
                let msg = "";
                for (var i = 0; i < allSelectedData.length; i++) {
                    if (msg == "") {
                        msg = '○ ' + Resources.UserCantPostponeFollowUp + " : " + allSelectedData[i].subject;
                    }
                    else {
                        msg += "\n ○ " + Resources.UserCantPostponeFollowUp + " : " + allSelectedData[i].subject;
                    }

                }
                if (msg !== "") {
                    setTimeout(function () {
                        Common.alertMsg(msg);
                    }, 300);
                }
            }
        }
        else {
            Common.alertMsg(Resources.NoRowSelected);
        }
    }

    E.Reopen = function () {
        var documentIds = [];
        var gTableName = $("#grdClosedItems").val() != undefined ? 'grdClosedItems' : "closedListContainer";

        documentIds = $("#" + gTableName + " input:checkbox:checked").map(function () {
            return $(this).attr("data-id");
        }).get();
        if (documentIds.length > 0) {
            Common.showConfirmMsg(Resources.ReopenConfirmation, function () {
                Reopen(documentIds);
            });
        }
        else {
            Common.alertMsg(Resources.NoRowSelected);
        }
    }


    E.GenerateBarcode = function () {
        try {
            Common.showConfirmMsg(Resources.GenerateBarcodeMsg, function () {
                Common.mask(document.body, "body-mask");
                Common.ajaxGet("/Document/GenerateReferenceNumber?documentId=" + $("[ref=hdDocumentId]").val() + "&transferId=" + $("[ref=hdId]").val(), null, function (response) {
                    if (response != null) {
                        $(".documentHeader span").before(" - " + response.referenceNumber + " ");
                        Common.alertMsg(Common.format(Resources.ReferenceNumberMsg, response.referenceNumber));
                        $('[ref=documentBarcodeContainer]').removeClass('hidden');
                        $('[ref=lnkDocumentBarcodeView]').html("<i class='fa fa-barcode mr-sm'></i>" + response.referenceNumber);
                        $("#documentWithViewerContainer").removeClass('hidden');

                        $('[id*="_refNumberPanel"]').removeClass('hidden');
                        $('[id*="_refNumberPanelHeader"]').html(response.referenceNumber);

                        if (window.InboxMode == "InboxVIPView" || window.InboxMode === 'LocalVIPView')
                        {
                            const reference = $(`[data-transfer='${$("[ref=hdId]").val()}'] span[data-ref]`)[0];
                            const tabRef = $("[ref=documentDetailsContainerDiv] [id$=taskPanelHeader]")[0];
                            if(reference)
                            {
                                reference.title = response.referenceNumber;
                                reference.innerText = response.referenceNumber;
                            }
                            if(tabRef)
                            {
                                tabRef.title = response.referenceNumber;
                                tabRef.innerText = response.referenceNumber;
                            }
                        }
                        Common.unmask("body-mask");

                        if ($("#grdInboxItems").val() != undefined) {
                            GridCommon.Refresh("grdInboxItems");
                            Common.unmask("body-mask");
                        }
                        else if ($("#grdInboxItems").val() != undefined) {
                            window.location.href = '/';
                            Common.unmask("body-mask");
                        }
                    }
                    else {
                        Common.alertMsg(Resources.ReferenceAlreadyExists);
                        Common.unmask("body-mask");
                    }
                }, function () {
                    Common.showScreenErrorMsg();
                    Common.unmask("body-mask");
                });
            });
        }
        catch (e) {
        }
    }

    E.Export = function () {
        
    }

    function Reopen(documentIds) {
        Common.ajaxPost('/Document/Reopen',
            {
                'documentIds': documentIds, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
            },
            function (result) {
                swal.close()
                if (!result) {
                    Common.alertMsg(Resources.ErrorOccured);
                }
                else {
                    Common.showScreenSuccessMsg();

                    if ($("#grdClosedItems").val() != undefined) {
                        GridCommon.Refresh("grdClosedItems");
                    }
                    else {
                        for (var i = 0; i < documentIds.length; i++) {
                            var li = $($("input[data-id='" + documentIds[i] + "']").parents("li")[0]);
                            li.fadeOut().remove();
                            if (Number(gSelectedRowId) === Number(documentIds[i])) {
                                $(".withBorders-o").addClass("waitingBackground");
                                $("#closedListContainer").empty();
                            }
                        }
                    }
                    TreeNode.refreshTreeNodeCounts(TreeNode.Closed);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
                    TreeNode.refreshTreeNodeCounts(TreeNode.MyRequests);
                }
            }, null, false);
    }
    E.OpenCopyOptionsModal = function () {
        OpenCopyOptionsModal(data, false);
    }
    E.OpenCopyOptionsModal = function (data) {
        OpenCopyOptionsModal(data, false);
    }
    E.OpenCopySpecificVersionOptionsModal = function (data) {
        OpenCopyOptionsModal(data, true);
    }

    E.OpenCreateFollowUpModal = function (data) {
        var followUpWrapper = $(".modal-window");
        followUpWrapper.find("#followUpIndexModal").remove();
        var model = new followUpIndex.FollowUpIndexIndex();
        model.transferId = data.id;
        model.documentId = data.documentId;
        model.dueDate = data.documentDueDate;
        var View = new followUpIndex.FollowUpIndexView(followUpWrapper, model);
        View.render();

        $('#followUpIndexModal').modal('show');
        $("#followUpIndexModal").off("hidden.bs.modal");
        $("#followUpIndexModal").off("shown.bs.modal");

        $('#followUpIndexModal').on('hidden.bs.modal', function () {
            $("#followUpIndexModal").remove()
        });
    }

   

    
    function OpenCopyOptionsModal(data, isSpecific) {
        var CopyOptionsWrapper = $(".modal-window");
        CopyOptionsWrapper.find("#modalCopyOptions").remove();
        var CopyOptionsModel = new CopyOptions.CopyOptions();
        CopyOptionsModel.isSpecific = isSpecific;
        CopyOptionsModel.fileId = data.originalDocumentId;
        CopyOptionsModel.transferId = data.id;
        CopyOptionsModel.documentId = data.documentId;
        CopyOptionsModel.categoryId = data.categoryId;
        CopyOptionsModel.delegationId = data.delegationId;
        //CopyOptionsModel.hasEditAccess = hasEditAccess;
        //CopyOptionsModel.allowRestore = isSigned ? false : true;
        var CopyOptionsView = new CopyOptions.CopyOptionsView(CopyOptionsWrapper, CopyOptionsModel);
        //let callback = selfModel.fromVip && window.OpenCorrespondenceMode === "OpenCorrespondenceWithViewer" ? refreshPreviewTab : null;
        CopyOptionsView.render(/*callback*/);

        $('#modalCopyOptions').modal('show');
        $("#modalCopyOptions").off("hidden.bs.modal");
        $("#modalCopyOptions").off("shown.bs.modal");

        $('#modalCopyOptions').on('hidden.bs.modal', function () {
            $('#modalCopyOptions').remove();
        });
    }

    E.ApproveDraft = function (data) {
        updateDraftStatus(data.id, 3);
    }

    E.RejectDraft = function (data) {
        updateDraftStatus(data.id, 4);
    }
    function addFiltersDraft(d) {
        d.structureId = window.EnablePerStructure ? $('#hdLoggedInStructureId').val() : $("#hdStructureId").val();
        if (gFromSearch) {
            d.CategoryId = $("#cmbFilterDraftCategory").val() !== null && typeof $("#cmbFilterDraftCategory").val() !== "undefined" ? $("#cmbFilterDraftCategory").val() : "0";
            d.FromDate = $("#filterDraftFromDate").val() !== "" && typeof $("#filterDraftFromDate").val() !== "undefined" ? $("#filterDraftFromDate").val() : "";
            d.ToDate = $("#filterDraftToDate").val() !== "" && typeof $("#filterDraftToDate").val() !== "undefined" ? $("#filterDraftToDate").val() : "";
            d.Subject = $("#txtFilterDraftSubject").val() !== "" && typeof $("#txtFilterDraftSubject").val() !== "undefined" ? $("#txtFilterDraftSubject").val() : "";
        }
    }
   
    function createDraftListData(data) {
        var html = '';
        if (data.length === 0 && gPageIndex === 0) {
            html = '<ul class="mdl-ul"><div class="nodata dataTables_empty pt-lg">' + Resources.NoDataToDisplay + '</div></ul>';
            $('#draftListContainer').html(html);
        } else if (data.length > 0) {
            var priorities = new CoreComponents.Lookup.Priorities().get(window.language);
            var privacies = new CoreComponents.Lookup.Privacies().get(window.language);
            html = '<ul class="mdl-ul" style=" margin: 0px 5px 0 0;">';
            var htmlLi = '';
            var color = "";
            var draftstatuses = new DraftStatuses().get();
            for (var i = 0; i < data.length; i++) {
                var document = data[i];
                var draftstatus;
                for (var j = 0; j < draftstatuses.length; j++) {
                    if (draftstatuses[j].id === document.draftStatusId) {
                        draftstatus = draftstatuses[j].text;
                    }
                }
                var htmlIcons = "";
                if (document.importanceId) {
                    var importances = new CoreComponents.Lookup.Importances().get(window.language);
                    for (var j = 0; j < importances.length; j++) {
                        if (importances[j].id === document.importanceId) {
                            htmlIcons += "<i class='fa fa-exclamation fa-lg mr-sm' title='" + importances[j].text + "' style='color:" + importances[j].color + "'></i>";
                        }
                    }
                }

                if (window.PriorityPrivacyAction == "2") {
                    for (var j = 0; j < privacies.length; j++) {
                        if (privacies[j].id === document.privacyId) {
                            color = privacies[j].color;
                            break;
                        }
                    }
                } else {
                    for (var j = 0; j < priorities.length; j++) {
                        if (priorities[j].id === document.priorityId) {
                            color = priorities[j].color;
                            break;
                        }
                    }
                }
                document.referenceNumber = document.referenceNumber ?? "";


                //var to = document.sendingEntity !== "" ? document.toStructure + (document.toUser !== "" ? '/' + document.toUser : document.toUser) : document.toUser;
                htmlLi += '<li class="mdl-li" data-transfer="' + document.id + '">';
                htmlLi += '<div class="mdl-container-document">';
                htmlLi += '<div id="leftbox" class="pull-left">';
                htmlLi += '<input data-id=' + document.id + ' data-categoryid=' + document.categoryId + ' type="checkbox" class="pointer m-sm ml-10px" onclick="event.stopPropagation();">';
                htmlLi += "<input type='hidden' data-id=" + document.id + " value='" + JSON.stringify(document) + "'/>";
                htmlLi += "<span class='mdl-span' style='margin-left:10px; margin-right: 10px;'><i class='fa fa-circle icon-primary'></i></span>"
                htmlLi += "<span class='mdl-span' style='margin-left:10px; margin-right: 10px;'><i class='fa fa-arrow-left'></i></span>"
                htmlLi += '</div>';
                htmlLi += '<div id="middlebox" class="mdl-text-msg pull-left pl-sm card-max-width">';
                //htmlLi += '<span class="dot"></span>';
                htmlLi += '<span class="mdl-span light_grey_color" data-field="referenceNumber" style="color:' + color + '" title="' + (document.referenceNumber || "") + '">' + (document.referenceNumber || "") + '</span>';
                htmlLi += '<span class="mdl-span text-primary bold" data-field="subject" style="color:' + color + '" title="' + (document.subject || "") + '">' + (document.subject || "") + '</span>';
                htmlLi += '<span class="mdl-span light_grey_color" style="color:' + color + '" title="' + (document.sendingEntity || "") + '">' + (document.sendingEntity || "") + '</span>';
                htmlLi += '<span class="mdl-span light_grey_color" data-field="draftstatus" style="color:' + color + '" title="' + (draftstatus || "") + '">' + (draftstatus || "") + '</span>';
                htmlLi += '</div>';
                htmlLi += '<div id="rightbox" class="pull-right text-right"><div class="mdl-time mr-sm" title="' + Resources.CreatedDate + '">' + dateFormat(document.createdDate) + '</div>';

                if (htmlIcons !== "") {
                    htmlLi += '<div class="mdl-action">' + htmlIcons + '</div>';
                }
                htmlLi += '</div>';
                htmlLi += '</div>';
                htmlLi += '</div>';
                htmlLi += '</li>';
            }
            html += htmlLi;
            html += '</ul>';
            if (gPageIndex === 15) {
                $('#draftListContainer').html(html);
            } else {
                $('#draftListContainer ul').append(htmlLi);
            }
        }
    }
    function loadDraftList() {
        if (!gNoMoreData) {
            var delegationId = window.location.hash.split("/")[2];
            if (delegationId == undefined)
                delegationId = null;
            var nodeId = window.location.hash.split("/")[1];
            if (nodeId == undefined)
                nodeId = null;
            Common.mask(document.getElementById('draftListContainer'), "draftListContainer-mask");
            var params = {};
            addFiltersDraft(params);
            params.NodeId = nodeId;
            params.DelegationId = delegationId;
            params.start = gPageIndex;
            params.structureId = window.EnablePerStructure ? $('#hdLoggedInStructureId').val() : $("#hdStructureId").val();
            Common.ajaxPost('/Document/ListDraftVip', params, function (response) {
                if (response.length > 0) {
                    gPageIndex += window.Paging;
                    if (response.length < window.Paging) {
                        gNoMoreData = true;
                    }
                } else {
                    gNoMoreData = true;
                }
                createDraftListData(response);
                gLocked = false;
                Common.unmask("draftListContainer-mask");
                if (gFromSearch) {
                    $("#divSearchDraft").fadeOut();
                }
            }, function () { gLocked = false; Common.showScreenErrorMsg(); });
        } else {
            gLocked = false;
        }
    }
  

    function updateDraftStatus(documentId, draftStatusId) {
        var params = {
            documentId: documentId,
            draftStatus: draftStatusId
        }
        Common.ajaxPost('/Document/ChangeDraftStatus', params, function (response) {
            if ((window.InboxMode == "InboxVIPView" || window.InboxMode === 'LocalVIPView') ) {
                if (draftStatusId == 4) {
                    Common.unmask("vipContainer-mask");
                    $(".withBorders-o").addClass("waitingBackground");
                    $("#inboxDocumentDetailsContainer").empty();
                    $($("input[data-id='" + documentId + "']").parents("li")[0]).fadeOut().remove();
                    gPageIndex = 0;
                    gNoMoreData = false;
                    loadDraftList();
                }
                else {
                    var draftstatuses = new DraftStatuses().get();
                    var draftstatus;
                    for (var j = 0; j < draftstatuses.length; j++) {
                        if (draftstatuses[j].id === draftStatusId) {
                            draftstatus = draftstatuses[j].text;
                        }
                    }
                    var $li = $('li[data-transfer="' + documentId + '"]');
                
                    $li.find('[data-field="draftstatus"]').text(draftstatus);
                    gPageIndex = 0;
                    gNoMoreData = false;
                    loadDraftList();
                }
               
            }
            else {
                Common.showScreenSuccessMsg();
                GridCommon.RefreshCurrentPage("grdDraftItems", false);
                TreeNode.refreshTreeNodeCounts(TreeNode.Draft);

            }
            }, function () { gLocked = false; Common.showScreenErrorMsg(); }, true);
    }

    E.G2GAction = function () {
        var model = new G2GExport.G2GExport();
        model.documentId = parseInt($('[ref=hdDocumentId]').val());
        model.g2gInternalId = isNaN(parseInt($('[ref=hdG2GInternalId]').val())) ? null : parseInt($('[ref=hdG2GInternalId]').val());
        let modalWrapper = $(".modal-window");
        modalWrapper.find("#modalg2gExport").remove();
        var g2gExportView = new G2GExport.G2GExportView(modalWrapper, model);
        g2gExportView.render();


        $('#modalg2gExport').modal('show');
        $("#modalg2gExport").off("hidden.bs.modal");
        $("#modalg2gExport").off("shown.bs.modal");
        $('#modalg2gExport').on('shown.bs.modal', function () { });
        $('#modalg2gExport').on('hidden.bs.modal', function () { });
    }
   
    return E;
}(CustomActions));
var CustomMenus = (function (E) {
    E = {};
    E.CheckMenueAccessibility = function (menuName) {

        if (window.userMenus.menus.find(d => d.url == null ? false : d.url.toLowerCase() == menuName.toLowerCase())) {
            return true;
        }
        return false;
    }

    //E.privacyComponent = function () {

    //    Common.setActiveSidebarMenu("liPrivacy");
    //    $(".delegation").removeClass("active");

    //    let view = new CoreComponents.privacyComponent();
    //    view.render();
    //},
    E.PrivacyComponent = function () {
        
        Common.setActiveSidebarMenu("liPrivacy");

        $("a[href='#Privacy']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let model = new Privacy.Privacy();
        let view = new Privacy.PrivacyView(wrapper, model);
        view.render();
    };
    E.systemDelegation = function () {
        if (window.location.href.indexOf("systemdelegation") < 0) {
            window.location.href = window.location.origin + "/#systemdelegation";
        }
        $('.sidebar li[class="active"]').removeClass("active");
        $("a[href='#systemdelegation']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");

        let view = new CTSSystemDelegation.SystemDelegationView(wrapper);
        view.render();

    };
    E.advanceSearchRoute = function (delegationId) {
    
        Common.ajaxGet('/AdvanceSearchConfiguration/GetConfiguration', null, function (response) {
            $('.sidebar li[class="active"]').removeClass("active");

            $("a[href='#advanceSearch']").parent().addClass("active");
            $(".delegation").removeClass("active");
            let wrapper = $(".content-wrapper");
            let model = new DocumentAdvanceSearch.DocumentAdvanceSearchModel();
            model.delegationId = delegationId;
            model.configuration = response;
            model.delegationUsers = new DelegationUsers().get(window.language);
            if (response && response.content != null && response.content != "") {
                if (JSON.parse(response.content).components.length > 0) {
                    model.drawContentBuilder = true;
                }
            }
            let view = new DocumentAdvanceSearch.DocumentAdvanceSearchView(wrapper, model);
            view.render();
        });

    }
    E.delegationRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#delegation']").parent().addClass("active");
        $(".delegation").removeClass("active");

        var userId = $("#hdUserId").val();
        var structureIds = $("#hdStructureIds").val().split(window.Seperator);
        var hdHasManager = $("#hdHasManager").val();
        let wrapper = $(".content-wrapper");
        let view = new CTSDelegation.DelegationView(wrapper, userId, structureIds, hdHasManager);//params userId,structureIds,hasManager,"DocumentTypes" pass as param to change categories label, default is "Categories"
        view.render();

    }
    E.rootRoute = function () {
        window.onload = function () {
            let view = new Home.HomeView($(".content-wrapper"));
            view.render();
        };
    }
    E.exceptionLogRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#exceptionlog']").parent().addClass("active");
        $(".delegation").removeClass("active");

        let view = new CoreComponents.ExceptionLogComponent();
        view.render();
    }
    E.purposeRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#purpose']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let model = new Purpose.Purpose();
        model.showCced = true;
        let view = new Purpose.PurposeView(wrapper, model);
        //let view = new CoreComponents.PurposeComponent(true);//showCced param pass to show cced column default is true
        view.render();
    }
    E.privacyRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#privacy']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let model = new Privacy.Privacy();
        model.showCced = true;
        let view = new Privacy.PrivacyView(wrapper, model);
        view.render();
    }
    E.statusRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#status']").parent().addClass("active");
        $(".delegation").removeClass("active");

        let view = new CoreComponents.StatusComponent();
        view.render();
    }
    E.priorityRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#priority']").parent().addClass("active");
        $(".delegation").removeClass("active");

        let view = new CoreComponents.PriorityComponent();
        view.render();
    }
    E.applicationServerRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#applicationserver']").parent().addClass("active");
        $(".delegation").removeClass("active");

        let view = new CoreComponents.ApplicationServerComponent();
        view.render();
    }
    E.parameterRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#parameter']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let documentView = new Parameter.ParameterView(wrapper, null);
        documentView.render();
        let modalWrapper = $(".modal-window");
        modalWrapper.empty();

        let modelIndex = new ParameterIndex.ParameterIndex();
        let parameterIndexView = new ParameterIndex.ParameterIndexView(modalWrapper, modelIndex);
        parameterIndexView.render();

        let modelEventIndex = new EventReceiverIndex.EventReceiverIndex();
        let eventIndexView = new EventReceiverIndex.EventReceiverIndexView(modalWrapper, modelEventIndex);
        eventIndexView.render();

        var exportModel = new Export.Export();
        var viewexport = new Export.ExportView(modalWrapper, exportModel);
        viewexport.render();

        var importModel = new Import.Import();
        var viewimport = new Import.ImportView(modalWrapper, importModel);
        viewimport.render();
    }
    E.translatorDictionaryRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#translatordictionary']").parent().addClass("active");
        $(".delegation").removeClass("active");

        let view = new CoreComponents.DictionaryComponent();
        view.render();
    }
    E.notificationTemplateRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#notificationtemplate']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let view = new NotificationTemplate.NotificationTemplateView(wrapper);
        view.render();

    }
    E.scanConfigurationRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#scanconfiguration']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let documentView = new ScanConfiguration.ScanConfigurationView(wrapper, null);
        documentView.render();
        let modalWrapper = $(".modal-window"); modalWrapper.empty();
        let model = new ScanConfigurationIndex.ScanConfigurationIndex();
        model.categories = new ScanConfigurationIndex.ScanConfigurationIndex().get();
        let scanConfigurationIndexView = new ScanConfigurationIndex.ScanConfigurationIndexView(modalWrapper, model);
        scanConfigurationIndexView.render();
    }

    // This file is about all the CTS menu pages routes like the /#organizationManagement page route
    E.organizationManagementRoute = function () {              
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#organizationManagement']").parent().addClass("active");
        $(".delegation").removeClass("active");
        var isSendingRulesEnabled = window.EnableSendingRules === "True";
        var searchAssignedSecurity = { enabled: true, showStructures: true, showUsers: false, showGroups: false };

        var dashboardAssignedStructures = { enabled: true, showStructures: true, showGroups: false };

        //params: isSendingRulesEnabled default false,showSendingRules default false, searchAssignedSecurity default {enabled:false,showStructures:false,showUsers:false,showGroups:false}
        let wrapper = $(".content-wrapper");
        let view = new OrganizationManagement.OrganizationManagementView(wrapper, isSendingRulesEnabled, true, searchAssignedSecurity, dashboardAssignedStructures);
        view.render();
    }

    E.manageDepartmentUsersRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#manageDepartmentUsers']").parent().addClass("active");
        $(".delegation").removeClass("active");
        var isSendingRulesEnabled = window.EnableSendingRules === "True";
        var searchAssignedSecurity = { enabled: true, showStructures: true, showUsers: false, showGroups: false };
        //params: isSendingRulesEnabled default false,showSendingRules default false, searchAssignedSecurity default {enabled:false,showStructures:false,showUsers:false,showGroups:false}
        let wrapper = $(".content-wrapper");
        let view = new ManageDepartmentUsers.ManageDepartmentUsersView(wrapper, isSendingRulesEnabled, true, searchAssignedSecurity);
        view.render();
    }
    E.manageTemplateRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#manageTemplate']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let documentView = new Template.TemplateView(wrapper, null);
        documentView.render();
        let modalWrapper = $("#contentDiv");


        let modelIndex = new TemplateIndex.TemplateIndex();
        let categoriesObj = new CategoryModel().get();
        let categoriesByTemplates = [];
        for (var i = 0; i < categoriesObj.length; i++) {
            if (categoriesObj[i].byTemplate) {
                var catName = window.language === "en" ? categoriesObj[i].name : window.language === "ar" ? categoriesObj[i].nameAr : categoriesObj[i].nameFr;
                categoriesByTemplates.push({ id: categoriesObj[i].id, text: catName });
            }
        }
        modelIndex.categories = categoriesByTemplates;
        let templateIndexView = new TemplateIndex.TemplateIndexView(modalWrapper, modelIndex);
        templateIndexView.render();

        let modalTreeWrapper = $(".modal-window"); modalTreeWrapper.empty();
        let treeNodeActionModel = new TreeNodeActions.TreeNodeActionsModel();
        treeNodeActionModel.structureTreeId = 'structureTree';
        treeNodeActionModel.templateTreeId = 'templateTree';

        let treeNodeActionsView = new TreeNodeActions.TreeNodeActions(modalTreeWrapper, treeNodeActionModel);
        treeNodeActionsView.render();
    }
    E.manageCategoryRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#manageCategory']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");

        let model = new Category.Category();
        model.categories = new Categories().get(window.language);
        let documentView = new Category.CategoryView(wrapper, model);
        documentView.render();

        let modalWrapper = $(".modal-window");
        modalWrapper.empty();

        let categoryindexIndexView = new CategoryIndex.CategoryIndexView(modalWrapper, null);
        categoryindexIndexView.render();
        let categoryManagement = new CategoryManagementView.CategoryManagementView(modalWrapper, null);
        categoryManagement.render();
        let categoryindexImportView = new CategoryImport.CategoryImportView(modalWrapper, null);
        categoryindexImportView.render();

        //let modelSecurityMatrix = new SecurityMatrix.SecurityMatrix();
        //modelSecurityMatrix.isCategory = true;
        //let modalView = new SecurityMatrix.SecurityMatrixView(modalWrapper, modelSecurityMatrix);
        //modalView.render();
    }
    E.categoryReferenceNumberRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#categoryreferencenumber']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let documentView = new CategoryReferenceNumber.CategoryReferenceNumberView(wrapper, null);
        documentView.render();
        let modalWrapper = $(".modal-window"); modalWrapper.empty();
        let model = new CategoryReferenceNumberIndex.CategoryReferenceNumberIndex();
        model.categories = new CategoryReferenceNumberIndex.CategoryReferenceNumberIndex().get();
        let categoryReferenceNumberIndexView = new CategoryReferenceNumberIndex.CategoryReferenceNumberIndexView(modalWrapper, model);
        categoryReferenceNumberIndexView.render();
    }
    E.categoryReferenceCounterRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#categoryreferencecounter']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let view = new CategoryReferenceCounter.CategoryReferenceCounterView(wrapper, null);
        view.render();
        let modalWrapper = $(".modal-window"); modalWrapper.empty();
        let referenceCounterView = new CategoryReferenceCounterIndex.CategoryReferenceCounterIndexView(modalWrapper, null);
        referenceCounterView.render();
    }
    E.classificationRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#classification']").parent().addClass("active");
        $(".delegation").removeClass("active");

        let view = new CoreComponents.ClassificationComponent();
        view.render();
    }
    E.importanceRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#importance']").parent().addClass("active");
        $(".delegation").removeClass("active");

        let view = new CoreComponents.ImportanceComponent();
        view.render();
    }
    E.templatesManagementRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#TemplatesManagement']").parent().addClass("active");
        $(".delegation").removeClass("active");

        let view = new CoreComponents.TemplatesManagementComponent();
        view.render();
    }
    //E.privacyRoute = function () {
    //    $('.sidebar li[class="active"]').removeClass("active");

    //    $("a[href='#privacy']").parent().addClass("active");
    //    $(".delegation").removeClass("active");
    //    let view = new CoreComponents.PrivacyComponent();
    //    view.render();
    //}
    E.toDoListRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#toDoList']").parent().addClass("active");
        $(".delegation").removeClass("active");

        let view = new CoreComponents.ToDoListComponent();
        view.render();
    }
    E.assemblyRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#assembly']").parent().addClass("active");
        $(".delegation").removeClass("active");

        let view = new CoreComponents.AssemblyComponent();
        view.render();
    }
    E.activityLogRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#activitylog']").parent().addClass("active");
        $(".delegation").removeClass("active");

        let wrapper = $(".content-wrapper");

        let model = new ActivityLog.ActivityLog();
        model.categories = new Categories().get(window.language);
        model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
        if (model.statuses.length > 0) {
            model.statuses = model.statuses.filter(function (obj) {
                return obj.id !== SystemStatus.Draft;
            });
        }
        let documentView = new ActivityLog.ActivityLogView(wrapper, model);
        documentView.render();

        //let modalWrapper = $(".modal-window");
        //modalWrapper.empty();
        //let auditTrailValuesView = new AuditTrailValues.AuditTrailValuesView(modalWrapper, null);
        //auditTrailValuesView.render();

    }
    E.menuRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#menu']").parent().addClass("active");
        $(".delegation").removeClass("active");

        let view = new CoreComponents.MenuComponent();
        view.render();
    }
    E.actionRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#action']").parent().addClass("active");
        $(".delegation").removeClass("active");

        let wrapper = $(".content-wrapper");
        // let view = new CoreComponents.ActionComponent();//"DocumentTypes" pass as param to change categories label, default is "Categories"
        let view = new Action.ActionView(wrapper);//"DocumentTypes" pass as param to change categories label, default is "Categories"
        view.render();
    }
    E.tabRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#tab']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let view = new Tab.TabView(wrapper); //"DocumentTypes" pass as param to change categories label, default is "Categories"
        //let view = new CoreComponents.TabComponent();
        view.render();
    }
    E.userRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#user']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let view = new User.UserView(wrapper);
        view.render();
    }
    E.roleRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");
        $("a[href='#role']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let view = new Role.RoleView(wrapper);
        view.render();
    }
    E.nodeRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#node']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");

        let model = new Node.Node();
        let view = new Node.NodeView(wrapper, model);
        view.render();
    }
    E.categoryRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#category']").parent().addClass("active");
        $(".delegation").removeClass("active");

        let view = new CoreComponents.CategoryComponent();//"DocumentTypes" pass as param to change categories label, default is "Categories"
        view.render();
    }
    E.attachmentfolderRoute = function () {

        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#attachmentfolder']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let model = new AttachmentFolder.AttachmentFolder();
        model.categories = new Categories().get(window.language);
        let documentView = new AttachmentFolder.AttachmentFolderView(wrapper, model);
        documentView.render();
        let modalWrapper = $(".modal-window");
        modalWrapper.empty();
        let modelIndex = new AttachmentFolderIndex.AttachmentFolderIndex();
        let attachmentFolderIndexView = new AttachmentFolderIndex.AttachmentFolderIndexView(modalWrapper, modelIndex);
        attachmentFolderIndexView.render();
    }
    E.lookupRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#lookup']").parent().addClass("active");
        $(".delegation").removeClass("active");

        let view = new CoreComponents.LookupComponent();
        view.render();
    }
    E.documenttypeRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#documenttype']").parent().addClass("active");
        $(".delegation").removeClass("active");

        let view = new CoreComponents.DocumentTypeComponent();
        view.render();
    }
    E.searchRoute = function (term) {
        $('.sidebar li[class="active"]').removeClass("active");

        Common.setActiveSidebarMenu("liSearch");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let model = new DocumentSearch.DocumentSearch();
        model.categories = new Categories().get(window.language);
        var statuses = new CoreComponents.Lookup.Statuses().get(window.language);
        if (statuses.length > 0) {
            statuses = statuses.filter(function (obj) {
                return obj.id !== SystemStatus.Draft;
            });
        }
        model.statuses = statuses;
        model.priorities = new CoreComponents.Lookup.Priorities().get(window.language);
        model.privacies = new CoreComponents.Lookup.Privacies().get(window.language);
        model.importances = new CoreComponents.Lookup.Importances().get(window.language);
        model.delegationUsers = new DelegationUsers().get(window.language);
        let view = new DocumentSearch.DocumentSearchView(wrapper, model, term != null ? term : '');
        view.render();
    }
    E.advanceSearchConfigurationRoute = function () {
       
        Common.ajaxGet('/AdvanceSearchConfiguration/GetConfiguration', null, function (response) {
            $('.sidebar li[class="active"]').removeClass("active");

            $("a[href='#advanceSearchConfiguration']").parent().addClass("active");
            $(".delegation").removeClass("active");

            let wrapper = $(".content-wrapper");

            let model = new DocumentAdvanceSearchConfiguration.DocumentAdvanceSearchConfiguration();
            model.configuration = response;
            let view = new DocumentAdvanceSearchConfiguration.DocumentAdvanceSearchConfigurationView(wrapper, model);
            view.render();

            let modalWrapper = $(".modal-window");
            modalWrapper.empty();
            let documentAdvanceSearchConfigurationColumnsView = new DocumentAdvanceSearchConfigurationColumns.AdvanceSearchConfigurationColumnsView(modalWrapper, null);
            documentAdvanceSearchConfigurationColumnsView.render();
        });

    }
    E.nonArchivedAttachmentsTypesRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#nonarchivedattachmentstypes']").parent().addClass("active");
        let wrapper = $(".content-wrapper");
        let model = new NonArchivedAttachmentsTypes.NonArchivedAttachmentsTypes();
        model.types = new Types().get(window.language);
        let documentView = new NonArchivedAttachmentsTypes.NonArchivedAttachmentsTypesView(wrapper, model);
        documentView.render();
        let modalWrapper = $(".modal-window");
        modalWrapper.empty();
        let documenttypeIndexView = new NonArchivedAttachmentsTypesIndex.NonArchivedAttachmentsTypesIndexView(modalWrapper, null);
        documenttypeIndexView.render();
    }
    E.barcodeConfigurationRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");
        $("a[href='#barcodeconfiguration']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let modelIndex = new BarcodeConfigurationIndex.BarcodeConfigurationIndex();
        modelIndex.categories = new Categories().get(window.language);
        let documentView = new BarcodeConfiguration.BarcodeConfigurationView(wrapper, modelIndex);
        documentView.render();
        let modalWrapper = $(".modal-window");
        modalWrapper.empty();
        let model = new BarcodeConfigurationIndex.BarcodeConfigurationIndex();
        model.categories = new BarcodeConfigurationIndex.BarcodeConfigurationIndex().get();
        let barcodeConfigurationIndexView = new BarcodeConfigurationIndex.BarcodeConfigurationIndexView(modalWrapper, model);
        barcodeConfigurationIndexView.render();

        let modelBarcodeIndex = new BarcodeIndex.BarcodeIndex();
        let barcodeIndexView = new BarcodeIndex.BarcodeIndexView(modalWrapper, modelBarcodeIndex);
        barcodeIndexView.render();
    }
    E.filingPlanRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        //Common.setActiveSidebarMenu("liFilingPlan");
        $('a[href*="#filingPlan"]').parent().addClass("active")

        $(".delegation").removeClass("active");

        let wrapper = $(".content-wrapper");

        let model = new FilingPlan.FilingPlan();

        let view = new FilingPlan.FilingPlanView(wrapper, model);
        view.render();

        let modalWrapper = $("#addEditPanel");

        let modelIndex = new FilingPlanIndex.FilingPlanIndex();
        let menuIndexView = new FilingPlanIndex.FilingPlanIndexView(modalWrapper, modelIndex);
        menuIndexView.render();
    }
    E.nodeListRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#nodelist']").parent().addClass("active");
        $(".delegation").removeClass("active");

        let wrapper = $(".content-wrapper");

        let model = new NodeList.NodeList();

        let view = new NodeList.NodeListView(wrapper, model);
        view.render();

        let modalWrapper = $("#addEditPanel");
        modalWrapper.empty();

        let modelIndex = new NodeIndex.NodeIndex();
        modelIndex.roles = new IdentityService().getRoles();
        let menuIndexView = new NodeIndex.NodeIndexView(modalWrapper, modelIndex);
        menuIndexView.render();
    }
    E.bookmarksRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#bookmarks']").parent().addClass("active");
        $(".delegation").removeClass("active");

        let wrapper = $(".content-wrapper");

        let model = new Bookmark.Bookmark();
        model.categories = new Categories().get(window.language);
        let bookmarkView = new Bookmark.BookmarkView(wrapper, model);
        bookmarkView.render();
    }
    E.systemDashboardRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#systemDashboard']").parent().addClass("active");
        $(".delegation").removeClass("active");

        $(".modal-window").empty();

        let wrapper = $(".content-wrapper");
        let model = new SystemDashboard.SystemDashboard();
        model.purposes = new CoreComponents.Lookup.Purposes().get(window.language);
        model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
        model.categories = new Categories().get(window.language);

        let view = new SystemDashboard.SystemDashboardView(wrapper, model);
        view.render();
    }
    E.userDashboardRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#userDashboard']").parent().addClass("active");
        $(".delegation").removeClass("active");

        $(".modal-window").empty();

        let wrapper = $(".content-wrapper");
        let view = new UserDashboard.UserDashboardView(wrapper);
        view.render();
    }
    E.averageDurationForCorrespondenceCompletionRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#averageDurationForCorrespondenceCompletion']").parent().addClass("active");
        $(".delegation").removeClass("active");

        $(".modal-window").empty();

        let wrapper = $(".content-wrapper");

        let view = new KPI.KPIView(wrapper, "AverageDurationForCorrespondenceCompletion", true);
        view.render();
    }
    E.averageDurationForCorrespondenceDelayRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#averageDurationForCorrespondenceDelay']").parent().addClass("active");
        $(".delegation").removeClass("active");

        $(".modal-window").empty();

        let wrapper = $(".content-wrapper");

        let view = new KPI.KPIView(wrapper, "AverageDurationForCorrespondenceDelay", true);
        view.render();
    }
    E.averageDurationForTransferCompletionRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#averageDurationForTransferCompletion']").parent().addClass("active");
        $(".delegation").removeClass("active");

        $(".modal-window").empty();

        let wrapper = $(".content-wrapper");

        let view = new KPI.KPIView(wrapper, "AverageDurationForTransferCompletion");
        view.render();
    }
    E.averageDurationForTransferDelayRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#averageDurationForTransferDelay']").parent().addClass("active");
        $(".delegation").removeClass("active");

        $(".modal-window").empty();

        let wrapper = $(".content-wrapper");

        let view = new KPI.KPIView(wrapper, "AverageDurationForTransferDelay");
        view.render();
    }
    E.reportInProgressTransfersRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#reportInProgressTransfers']").parent().addClass("active");
        //$(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let documentView = new ReportInProgressTransfers.ReportInProgressTransfersView(wrapper, null);
        documentView.render();
    }
    E.reportTransfersSentToStructureRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#reportTransfersSentToStructure']").parent().addClass("active");
        //$(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let documentView = new ReportTransfersSentToStructure.ReportTransfersSentToStructureView(wrapper, null);
        documentView.render();
    }
    E.reportOutgoingFromDepartmentRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#reportOutgoingFromDepartment']").parent().addClass("active");
        //$(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let OutgoingFromDepartment = new ReportOutgoingFromDepartment.ReportOutgoingFromDepartmentView(wrapper, null);
        OutgoingFromDepartment.render();
    }

    E.reportCompletedTransfersRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#reportCompletedTransfers']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let documentView = new ReportCompletedTransfers.ReportCompletedTransfersView(wrapper, null);
        documentView.render();
    }
    E.reportOperationByUserRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#reportOperationByUser']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let model = new ReportOperationByUser.ReportOperationByUser();
        model.categories = new Categories().get(window.language);
        let documentView = new ReportOperationByUser.ReportOperationByUserView(wrapper, model);
        documentView.render();
    }
    E.reportOperationByCorrespondenceRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#reportOperationByCorrespondence']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let model = new ReportOperationByCorrespondence.ReportOperationByCorrespondence();
        let documentView = new ReportOperationByCorrespondence.ReportOperationByCorrespondenceView(wrapper, null);
        documentView.render();
    }
    E.reportStatisticalCorrespondencesRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#reportstatisticalcorrespondences']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let model = new ReportStatisticalCorrespondences.ReportStatisticalCorrespondences();
        model.categories = new Categories().get(window.language);
        let documentView = new ReportStatisticalCorrespondences.ReportStatisticalCorrespondencesView(wrapper, model);
        documentView.render();
    }
    E.reportCorrespondenceDetailFilterRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#reportcorrespondencedetailfilter']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let model = new ReportCorrespondenceDetailFilter.ReportCorrespondenceDetailFilter();
        let documentView = new ReportCorrespondenceDetailFilter.ReportCorrespondenceDetailFilterView(wrapper, model);
        documentView.render();
    }
    E.reportInProgressCorrespondencesRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#reportInProgressCorrespondences']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let documentView = new ReportInProgressCorrespondences.ReportInProgressCorrespondencesView(wrapper, null);
        documentView.render();
    }
    E.reportCompletedCorrespondencesRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#reportCompletedCorrespondences']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let documentView = new ReportCompletedCorrespondences.ReportCompletedCorrespondencesView(wrapper, null);
        documentView.render();
    }
    E.entityGroupRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#entitygroup']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let documentView = new EntityGroup.EntityGroupView(wrapper, null);
        documentView.render();
        let modalWrapper = $(".modal-window");
        modalWrapper.empty();

        //let modelIndex = new EntityGroupIndex.EntityGroupIndex();
        //let entityGroupIndexView = new EntityGroupIndex.EntityGroupIndexView(modalWrapper, modelIndex);
        //entityGroupIndexView.render();
    }

    E.adminlogRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#adminlog']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let documentView = new AdminLog.AdminLogView(wrapper, null);
        documentView.render();
        let modalWrapper = $(".modal-window");
        modalWrapper.empty();

    }
    E.autoForwardRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#autoForward']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let model = new AutoForward.AutoForward();
        model.enableTransferToUsers = window.EnableTransferToUsers;
        model.itemsNames = [];
        let documentView = new AutoForward.AutoForwardView(wrapper, model);
        documentView.render();
        let modalWrapper = $(".modal-window");
        modalWrapper.empty();

        //let modelIndex = new EntityGroupIndex.EntityGroupIndex();
        //let entityGroupIndexView = new EntityGroupIndex.EntityGroupIndexView(modalWrapper, modelIndex);
        //entityGroupIndexView.render();
    }
    E.favoriteStructuresRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#favoriteStructures']").parent().addClass("active");
        $(".delegation").removeClass("active");

        let wrapper = $(".content-wrapper");


        let favoriteStructuresView = new FavoriteStructures.FavoriteStructuresView(wrapper, null);
        favoriteStructuresView.render();
    }
    E.distributionListRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#distributionList']").parent().addClass("active");
        $(".delegation").removeClass("active");

        let wrapper = $(".content-wrapper");

        let model = new DistributionList.DistributionList();

        let distributionListView = new DistributionList.DistributionListView(wrapper, model);
        distributionListView.render();

        let modalWrapper = $(".modal-window");
        modalWrapper.empty();

        let distributionIndexIndexView = new DistributionIndex.DistributionIndexView(modalWrapper, null);
        distributionIndexIndexView.render();

    }
    E.AutoForwardRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#AutoForward']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let model = new AutoForward.AutoForward();
        model.enableTransferToUsers = window.EnableTransferToUsers;
        let view = new AutoForward.AutoForwardView(wrapper, model);
        view.render();
        let modalWrapper = $(".modal-window");
        modalWrapper.empty();

    }
    E.manageCorrespondenceRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");
        $("a[href='#manageCorrespondence']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let model = new DocumentManageCorrespondence.DocumentManageCorrespondence();
        model.categories = new Categories().get(window.language);
        model.delegationUsers = new DelegationUsers().get(window.language);
        model.priorities = new CoreComponents.Lookup.Priorities().get(window.language);
        model.fromManageCorrespondance = true;
        var statuses = new CoreComponents.Lookup.Statuses().get(window.language);
        if (statuses.length > 0) {
            statuses = statuses.filter(function (obj) {
                return obj.id !== SystemStatus.Draft;
            });
        }
        model.statuses = statuses;
        let view = new DocumentManageCorrespondence.DocumentManageCorrespondenceView(wrapper, model);
        view.render();
    }
    E.manageStructureUsersCorrespondencesRoute = function () {
        $('.sidebar li[class="active"]').removeClass("active");
        $("a[href='#manageStructureUsersCorrespondences']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let model = new DocumentManageStructureUsersCorrespondences.DocumentManageStructureUsersCorrespondences();
        let view = new DocumentManageStructureUsersCorrespondences.DocumentManageStructureUsersCorrespondencesView(wrapper, model);
        view.render();
    }
    E.committee = function () {
        if (window.location.href.indexOf("committee") < 0) {
            window.location.href = window.location.origin + "/#committee";
        }
        $('.sidebar li[class="active"]').removeClass("active");
        $("a[href='#committee']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");

        let view = new Committee.CommitteeView(wrapper);
        view.render();

    };

    E.createMeeatinAgenda = function () {

        if (window.location.href.indexOf("createMeetingAgenda") < 0) {
            window.location.href = window.location.origin + "/#createMeetingAgenda";
        }
        $('.sidebar li[class="active"]').removeClass("active");
        // $("a[href='#committee']").parent().addClass("active");
        $(".delegation").removeClass("active");

        var statuses = new CoreComponents.Lookup.Statuses().get(window.language);
        var wrapper = $(".content-wrapper");
        var modelIndex = new MeetingAgendaIndex.MeetingAgendaIndex();
        // modelIndex.transferId = self.model.transferId;
        //modelIndex.documentId = self.model.documentId;
        //modelIndex.delegationId = self.model.delegationId;
        var linkedCorrespondenceIndex = new MeetingAgendaIndex.MeetingAgendaIndexView(wrapper, modelIndex);
        linkedCorrespondenceIndex.render();
        var wrapper = $("#linkSearchDiv");
        let model = new DocumentSearch.DocumentSearch();
        model.categories = new Categories().get(window.language).filter(x => x.id != window.MeetingResolutionId && x.id != window.MeetingAgendaId && x.id != window.MeetingMinutesId && x.id != window.SepResolutionId );
        model.statuses = statuses.filter(function (el) { return el.text !== Resources.Draft; });
        model.priorities = new CoreComponents.Lookup.Priorities().get(window.language);
        model.delegationUsers = new DelegationUsers().get(window.language);
        model.privacies = new CoreComponents.Lookup.Privacies().get(window.language);
        model.importances = new CoreComponents.Lookup.Importances().get(window.language);
        model.fromMeeting = true;
        // model.documentId = self.model.documentId;
        model.fromLink = true;
        let view = new DocumentSearch.DocumentSearchView(wrapper, model);
        view.render();
        $('#searchContainerDiv').find("h3").hide();
        setTimeout(function () { $('#cmbSearchFilterCategory').focus(); }, 200);
    };

    E.movetransfers = function () {
        if (window.location.href.indexOf("movetransfers") < 0) {
            window.location.href = window.location.origin + "/#movetransfers";
        }
        $('.sidebar li[class="active"]').removeClass("active");
        $("a[href='#movetransfers']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        var model = new MoveTransfers.MoveTransfers();
        model.categories = new CoreComponents.Lookup.Categories().get(window.language);
        var statuses = new CoreComponents.Lookup.Statuses().get(window.language);
        if (statuses.length > 0) {
            statuses = statuses.filter(function (obj) {
                return obj.id !== SystemStatus.Draft;
            });
        }
        model.statuses = statuses;
        model.priorities = new CoreComponents.Lookup.Priorities().get(window.language);
        model.privacies = new CoreComponents.Lookup.Privacies().get(window.language);
        model.importances = new CoreComponents.Lookup.Importances().get(window.language);
        let view = new MoveTransfers.MoveTransfersView(wrapper, model);
        view.render();

    };

    E.manageFollowUpRoute = function () {
        if (window.location.href.indexOf("manageFollowUp") < 0) {
            window.location.href = window.location.origin + "/#manageFollowUp";
        }
        $('.sidebar li[class="active"]').removeClass("active");
        $("a[href='#manageFollowUp']").parent().addClass("active");
        $(".delegation").removeClass("active");

        let wrapper = $(".content-wrapper");
        let model = new ManageFollowUp.ManageFollowUp();

        model.nodeId = window.AssignedToMeNode;
        //model.delegationId = delegationId;
        //model.categories = new CoreComponents.Lookup.Categories().get(window.language, delegationId);
        model.categories = new CoreComponents.Lookup.Categories().get(window.language);
        model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
        model.priorities = new CoreComponents.Lookup.Priorities().get(window.language);
        model.privacies = new CoreComponents.Lookup.Privacies().get(window.language);
        model.isFollowUp = true;
        let manageFollowUpView = new ManageFollowUp.ManageFollowUpView(wrapper, model);
        manageFollowUpView.render();
    };


    E.userNodeRoute = function () {
       

        let wrapper = $(".content-wrapper");

        let model = new UserNodeList.UserNodeList();

        let view = new UserNodeList.UserNodeListView(wrapper, model);
        view.render();

        let modalWrapper = $("#addEditPanel");
        modalWrapper.empty();

        let modelIndex = new UserNodeIndex.UserNodeIndex();
        
        let menuIndexView = new UserNodeIndex.UserNodeIndexView(modalWrapper, modelIndex);
        menuIndexView.render();
    };

    E.liststructureusers = function () {
        if (window.location.href.indexOf("liststructureusers") < 0) {
            window.location.href = window.location.origin + "/#liststructureusers";
        }
        $('.sidebar li[class="active"]').removeClass("active");
        $("a[href='#liststructureusers']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let view = new ListStructureUsers.ListStructureUsersView(wrapper);
        view.render();
    }

    E.listallstructureusers = function () {
        if (window.location.href.indexOf("listallstructureusers") < 0) {
            window.location.href = window.location.origin + "/#listallstructureusers";
        }
        $('.sidebar li[class="active"]').removeClass("active");
        $("a[href='#listallstructureusers']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let view = new ListAllStructureUsers.ListAllStructureUsersView(wrapper);
        view.render();
    }
    
    E.listSecureUsers = function()
    {
        $('.sidebar li[class="active"]').removeClass("active");

        $("a[href='#listsecureusers']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");
        let view = new ListSecureUsersView(wrapper);
        view.render();
    }

    E.followUpPanel = function () {
        if (window.location.href.indexOf("followUpPanel") < 0) {
            window.location.href = window.location.origin + "/#followUpPanel";
        }
        $('.sidebar li[class="active"]').removeClass("active");
        $("a[href='#followUpPanel']").parent().addClass("active");
        $(".delegation").removeClass("active");
        let wrapper = $(".content-wrapper");

        let view = new FollowUpPanel.FollowUpPanelView(wrapper);
        view.render();

    };

    return E;
}(CustomMenus));
var CustomNodes = (function (E) {
    E = {};
    E.CheckNodeAccessibility = function (nodeId, name, delegationId, type) {
        switch (type) {
            case "Basket":
                if (window.userMenus.baskets.find(d => d.id == nodeId)) {
                    return true;
                }
                break;
            case "UserNode":
                if (window.userMenus.userNodes.find(d => d.id == nodeId)) {
                    return true;
                }
                break;
            case "Node":
                if (delegationId != undefined && window.userMenus.delegatedNodes.find(d => d.id == nodeId && d.inherit == name && d.visible)) {
                    return true;
                }
                if (window.userMenus.nodes.find(d => d.id == nodeId && d.inherit == name && d.visible)) {
                    return true;
                }
                break;
            default:
        }
        return false;
    }
    E.g2g = function (ViewName) {
        window.location.href = window.location.origin + "/#g2g/" + ViewName ;
        var nodes = new CoreComponents.Lookup.Nodes().getTreeNodes(window.language);
        var node = $.grep(nodes, function (element, index) {
            if (element.customFunctions != null && element.customFunctions.includes('g2g("' + ViewName + '")')) {
                return element.customFunctions.includes(ViewName);
            }
        })[0];
        if (node != null) {
            $('.sidebar li[class="active"]').removeClass("active");
            Common.setActiveSidebarMenu("liCustom" + node.id);
            $(".delegation").removeClass("active");
            let wrapper = $(".content-wrapper");
            let model = new g2g.Documentg2g();
            model.nodeId = node.id;
            model.viewName = ViewName;
            model.nodeName = node.name;
            model.categories = new Categories().get(window.language);
            let documentView = new g2g.Documentg2gView(wrapper, model);
            documentView.render();
        }
    };

    E.exportedDocuments = function (nodeId) {
        //Common.setActiveSidebarMenu(item);
        $(".delegation").removeClass("active");

        let wrapper = $(".content-wrapper");

        let model = new ExportedDocuments.ExportedDocuments();
        model.title = Resources.ExportedDocuments;
        model.nodeId = nodeId;
        model.categories = new Categories().get(window.language, null);
        model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
        model.priorities = new CoreComponents.Lookup.Priorities().get(window.language);
        model.privacies = new CoreComponents.Lookup.Privacies().get(window.language);
        let exportedDocumentsView = new ExportedDocuments.ExportedDocumentsView(wrapper, model);
        exportedDocumentsView.render();

    };
    E.RejectedDocuments = function (nodeId) {
        //Common.setActiveSidebarMenu(item);
        $(".delegation").removeClass("active");

        let wrapper = $(".content-wrapper");

        let model = new RejectedDocuments.RejectedDocuments();
        model.title = Resources.RejectedDocuments;
        model.nodeId = nodeId;
        model.categories = new Categories().get(window.language, null);
        model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
        model.priorities = new CoreComponents.Lookup.Priorities().get(window.language);
        model.privacies = new CoreComponents.Lookup.Privacies().get(window.language);
        let rejectedDocumentsView = new RejectedDocuments.RejectedDocumentsView(wrapper, model);
        rejectedDocumentsView.render();

    };
    return E;
}(CustomNodes));

globalThis.CTSCoreComponents = new class {
    constructor() {
        this.CustomTabs = CustomTabs;
        this.CustomActions = CustomActions;
        this.CustomMenus = CustomMenus;
        this.CustomNodes = CustomNodes;
    }
}();

export {
    Widget, Home, MyTransfer, DocumentMetadata, DocumentAttachment, DocumentNote, DocumentLinkCorrespondence, DocumentNonArchivedAttachment, VisualTracking,
    TransferHistory, ActivityLogs, Parameter, ParameterIndex, ScanConfiguration, ScanConfigurationIndex, Template, TemplateIndex, TreeNodeActions, Category, CategoryIndex,
    CategoryReferenceNumber, CategoryReferenceNumberIndex, CategoryReferenceCounter, CategoryReferenceCounterIndex, EventReceiverIndex, ActivityLog, AttachmentFolder,
    AttachmentFolderIndex, CategoryManagementView, DocumentSearch, DocumentAdvanceSearch, DocumentAdvanceSearchConfiguration, DocumentAdvanceSearchConfigurationColumns, CreateByTemplate, CreateByFile, NonArchivedAttachmentsTypes, NonArchivedAttachmentsTypesIndex,
    DocumentDetails, BarcodeConfiguration, BarcodeConfigurationIndex, FilingPlan, FilingPlanIndex, BarcodeIndex, DocumentSent, CategoryImport, AuditTrailValues, NodeList, NodeIndex, Bookmark,
    DocumentClosed, Export, Import, SystemDashboard, UserDashboard, KPI, ReportInProgressTransfers, ReportTransfersSentToStructure, ReportCompletedTransfers, ReportOperationByUser, ReportOperationByCorrespondence,
    ReportStatisticalCorrespondences, ReportCorrespondenceDetailFilter, ReportInProgressCorrespondences, ReportCompletedCorrespondences, EntityGroup, DocumentManageCorrespondence, CTSDelegation, CTSSystemDelegation
    , Purpose, AutoForward, ActivityLogTimeline, UserNodeIndex, UserNodeList, Privacy, BasketFromNodeIndex, DocumentManageStructureUsersCorrespondences, ReportOutgoingFromDepartment, AdminLog,EventList
    , followUpIndex, FollowUpDetails, FollowUpUsers, ListSecureUsersView, followUpPostpone, FollowUpPanel, ExportedDocuments, RejectedDocuments, g2g
};
