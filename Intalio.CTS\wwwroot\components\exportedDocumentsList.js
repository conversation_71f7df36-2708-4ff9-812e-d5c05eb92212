﻿import Intalio from './common.js'
import { RequestStatuses,Categories, CategoryModel, DelegationUsers } from './lookup.js'
import CustomizeNodeColomns from '../components/customizeNodeColomns.js'
import LinkedCorrespondenceDocument from './linkedCorrespondenceDocument.js'
import DocumentDetails from './documentDetails.js'


class ExportedDocuments extends Intalio.Model
{
    constructor()
    {
        super();
        this.title = null;
        this.nodeId = null;
        this.delegationId = null;
        this.categories = null;
        this.statuses = null;
        this.priorities = null;
        this.privacies = null;
        this.transferId = null;
        this.isExported = false;
    }
}
var gTableName = "grdExportedDocumentsItems";
var gLocked = false;


function openRecallReasonModal(callback) {
    const modal = $(`
    <div data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-hidden="true" class="modal fade modalRecall" id="recallModal">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="border-bottom:0px; padding-bottom:3px;">
                    <button type="button" ref="recallClose" id="recallActionModalClose" class="close" data-dismiss="modal">&times;</button>
                    <h4 ref="modalRecallTitle" class="modal-title"></h4>
                </div> 
                <div class="modal-body" style="padding-top: 2px;">
                    <form ref="formPost" method="post" data-parsley-validate="" novalidate="">
                        <div class="row">
                            <div class="col-md-12" ref="recallReasonContainer">
                                <label class="control-label field-required" style="font-size: medium;">${Resources.RecallReason}</label>
                                <textarea id="recallReason" rows="3" class="form-control" required></textarea>
                                <div class="invalid-feedback" style="display:none; color:red;">
                                    ${Resources.RequiredField}
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer" style="border-top:0px;">
                    <button type="button" class="btn btn-primary" id="submitReason">${Resources.Recall}</button>
                    <button type="button" class="btn btn-secondary" id="cancelRecall" data-bs-dismiss="modal">${Resources.Cancel}</button>
                </div>
            </div>
        </div>
    </div>
    `);

    $('body').append(modal);
    modal.modal('show');

    modal.find('#submitReason').on('click', function () {
        const textarea = modal.find('#recallReason');
        const reason = textarea.val().trim();                 // Removes any leading or trailing whitespace from the recall reason input value
        const errorMsg = textarea.siblings('.invalid-feedback');

        if (!reason) {
            textarea.addClass('is-invalid');
            errorMsg.show();  // Shows the error message
            //modal.find('form').addClass('was-validated');
            return;
        } else {
            textarea.removeClass('is-invalid');
            errorMsg.hide(); // Hides error message
            // modal.modal('hide').remove();           // Once there is added text  removes the error msg and red border automatically.
            $("#recallActionModalClose").trigger("click");
            callback(reason);
        }
    });

    // Remove required validation styles once there is an input change or input added
    modal.find('#recallReason').on('input', function () {
        $(this).removeClass('is-invalid');
        $(this).siblings('.invalid-feedback').hide();
    });
    modal.find('#cancelRecall').on('click', function () {
        modal.find('#recallReason').val('');
        modal.modal('hide');

    });
    modal.on('hidden.bs.modal', function () {
        modal.remove();
    });
}
function recall(id, delegationId) {
    openRecallReasonModal(function (reason) {
        Common.ajaxPost('/Transfer/Recall',
            {
                'id': id, 'delegationId': delegationId, 'note': reason, 'fromExported': true, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
            },
            function (result) {
                TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
                TreeNode.refreshTreeNodeCounts(TreeNode.Custom);

                GridCommon.RefreshCurrentPage(gTableName, false);
            }, function () { Common.showScreenErrorMsg(); }, false
        );
    });
    
}
function openSearchDocument(id, delegationId, fromLink) {
    gLocked = false;
    var params = { id: id };
    if (delegationId !== null) {
        params.delegationId = delegationId;
    }
    Common.ajaxGet('/Document/GetSearchDocument', params, function (response) {
        if (response && response === "NoAccess") {
            Common.alertMsg(Resources.NoPermission);
        } else {
            if (!response.id) {
                return;
            }
            var wrapper = $(".modal-documents");
            var model = new LinkedCorrespondenceDocument.LinkedCorrespondenceDocument();
            model.reference = response.referenceNumber;
            model.subject = response.subject;
            model.documentId = response.id;
            var linkedCorrespondenceDocument = new LinkedCorrespondenceDocument.LinkedCorrespondenceDocumentView(wrapper, model);
            linkedCorrespondenceDocument.render();

            model = new DocumentDetails.DocumentDetails();
            model.documentModel = response;
            model.readonly = true;
            model.delegationId = delegationId;
            model.documentId = response.id;
            model.referenceNumber = response.referenceNumber;
            model.categoryName = response.categoryName;
            model.statusId = response.status;
            model.createdByUser = response.createdByUser;
            model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
            model.showMyTransfer = false;
            model.showVisualTrackingOnly = false;
            model.attachmentVersion = response.attachmentVersion;
            var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));
            var tabs = [];
            var nodeId = /*$('[data-inherit="' + TreeNode.Inbox+ '"]').first().data("id");*/ TreeNodes.Search;
            if (nodeId !== undefined && $.isNumeric(nodeId)) {
                tabs = securityMatrix.SecurityNodes[nodeId].SecurityCategories[response.categoryId].Tabs;
                model.tabsActions = securityMatrix.SecurityNodes[nodeId].SecurityCategories[response.categoryId].SecurityTabs;
            }
            model.tabs = $.grep(tabs, function (element, index) {
                return !element.Name.includes("myTransfer") && !element.Name.includes("documentMetadata") &&
                    !element.Name.includes("notes") &&
                    !element.Name.includes("visualTracking") && !element.Name.includes("activityLog") &&
                    !element.Name.includes("linkedDocument") && !element.Name.includes("transferHistory") && !element.Name.includes("attachments");
            });
            model.tabsWithStatic = tabs;
            model.showBackButton = false;
            model.isModal = true;
            model.attachmentId = response.attachmentId;
            model.fromRejectedDocument = true;

            wrapper = $(linkedCorrespondenceDocument.refs['linkDocumentDiv']);
            var view = new DocumentDetails.DocumentDetailsView(wrapper, model);
            view.render();
            $($(view.refs['documentDetailsContainerDiv']).find('.div-flex')[0]).addClass('width100pc-ml0');

            var title = response.categoryName;
            if (response.referenceNumber) {
                title += ' - ' + response.referenceNumber;
            }
            if (response.createdByUser) {
                title += ' - ' + response.createdByUser;
            }
            $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocumentTitle']).html(title);
            $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).off("hidden.bs.modal");
            $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).off("shown.bs.modal");
            $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).on('shown.bs.modal', function () {
                $("ul[ref='tabDocumentDetails']").scrollingTabs('refresh');
            });
            $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).on('hidden.bs.modal', function () {
                if ($(this).data("remove") != true)
                    return;
                $(linkedCorrespondenceDocument.refs[linkedCorrespondenceDocument.model.ComponentId]).remove();
                swal.close();
                if ($(".modal-documents").children().length > 0) {
                    $('body').addClass('modal-open');
                }
            });
            $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).modal("show");

        }
    }, function () { Common.showScreenErrorMsg(); }, true);

}
function UpdateButtonVisibility(tableId,self) {
    var selectedRows = GridCommon.GetSelectedRows(tableId).length;

    // Show buttons if at least one row is selected
    if (selectedRows > 0 && !self.model.isExported) {
        //$(".html5buttons").removeClass("hidden");
        $(".conditional-buttons").removeClass("hidden");

        //table.buttons.add(allButtons);

    } else if (selectedRows == 0) {
        $(".conditional-buttons").addClass("hidden");

        //    $(".html5buttons").addClass("hidden");
    }
}
function buildColumns(gridcolumns) {

    gridcolumns.push({
        title: Resources.ReferenceNumber, data: "referenceNumber",
        render: function (data, type, full, meta) {
            if (full.referenceNumber != undefined && full.referenceNumber != "" && full.referenceNumber != null) {
                var linkedCorresRefNumList = full.referenceNumber.split(',');
                var html = '';
                $(linkedCorresRefNumList).each(function () {
                    html += "<div>" + this + "</div>";
                });
                return html;
            }
            return "";
        },
        "orderable": false, "className": "min-max-width-150-250 copyToClipboard"
    });

    gridcolumns.push({
        title: Resources.Subject, data: "subject",
        render: function (data, type, full, meta) {
            if (full.subject != undefined && full.subject != "" && full.subject != null) {
                var html = '';
                html += "<div>" + full.subject + "</div>";
                return html;
            }
            return "";
        },
        "orderable": false, "className": "min-max-width-150-250 copyToClipboard"
    });

    gridcolumns.push({ title: Resources.ReceivingEntity, data: "toStructure", "orderable": false, width: "150px" });
    gridcolumns.push({
        title: Resources.ExportedUser, data: "fromUser",
        render: function (data, type, full, meta) {
            if (full.fromUser != undefined && full.fromUser != "" && full.fromUser != null) {
                var html = '';
                html += "<div>" + full.fromUser + "</div>";
                return html;
            }
            return "";
        },
        "orderable": false, "className": "min-max-width-150-250 copyToClipboard"
    });
    gridcolumns.push({


        title: Resources.ExportedDate, data: "exportedDate",
        render: function (data, type, full, meta) {
            return DateConverter.toHijriFormated(full.exportedDate, null, window.CalendarType);
        }, width: "150px",
        "orderable": false, "className": "min-max-width-150-250"
    });
  
    gridcolumns.push({ title: Resources.ExportedNumberOfDays, data: "exportedNumberOfDays", "orderable": false, width: "150px" });
 
    gridcolumns.push({
        title: Resources.Status, data: "requestStatus", "orderable": false, width: "50px",
        "render": function (data, type, full, meta) {
            var statuses = new RequestStatuses().get();
            for (var i = 0; i < statuses.length; i++) {
                if (statuses[i].Name === data) {

                    var color = "#27c24c";
                    if (statuses[i].color !== undefined && statuses[i].color !== null) {
                        color = statuses[i].color;
                    }
                    return "<div class='label' style='background-color:" + color + "'>" + statuses[i].text + "</div>";
                }
            }
            return "";
        }
    });
    gridcolumns.push({
        data: "cced", "orderable": false, width: "50px",
        "render": function (data, type, full, meta) {
            if (full.document.documentReceiverEntity[0].exportIsOriginal == false || full.document.documentReceiverEntity[0].exportIsOriginal == null) {
                return "<div class='mr-sm' title='" + Resources.CarbonCopy + "'><i class='fa fa-cc text-warning'></i></div>";
            }
            return "";
        }
    });

}
function buildFilters() {
    
        var html = '<div class="row">';
        html += '<div class="col-lg-3 col-md-3 col-sm-6"><div class="form-group"><label class="control-label">' + Resources.ReferenceNumber + '</label>' +
            '<input type="text" id="txtFilterExportedDocumentsReferenceNumber" class="form-control" autocomplete="off" tabindex="1" aria-hidden="true"></div></div>';
        html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 list-action-filter"><button id="btnFilterExportedDocumentsSearch" tabindex="9" type="button" class="btn btn-primary">' + Resources.Search + '</button>' +
            ' <button id="btnFilterExportedDocumentsClear" tabindex="10" type="button" class="btn btn-clear">' + Resources.Clear + '</button></div>';
        html += '</div>';
        $('#filtersContainer').html(html);

        $('#txtFilterExportedDocumentsReferenceNumber').keydown(function (e) {
            var code = e.keyCode || e.which;
            if (code === 9) {
                e.preventDefault();
                if (e.shiftKey) {
                    $('#btnFilterExportedDocumentsSearch').focus();
                }
            }
        });
        $('#btnFilterExportedDocumentsClear').keydown(function (e) {
            var code = e.keyCode || e.which;
            if (code === 9) {
                e.preventDefault();
                if (e.shiftKey) {
                    $('#btnFilterExportedDocumentsSearch').focus();
                }
                else {
                    $('#txtFilterExportedDocumentsReferenceNumber').focus();
                }
            }
        });
        $("#btnFilterExportedDocumentsSearch").on('click', function () {
            GridCommon.Refresh(gTableName);
        });
        $("#btnFilterExportedDocumentsClear").on('click', function () {
            
            $("#txtFilterExportedDocumentsReferenceNumber").val('');
            GridCommon.Refresh(gTableName);
        });
}

class ExportedDocumentsView extends Intalio.View
{
    constructor(element, model)
    {
        super(element, "exportedDocuments", model);
    }
    render()
{
        var self = this;
        var model = this.model;
        $.fn.select2.defaults.set("theme", "bootstrap");
        var categoryModels = new CategoryModel().getFullCategories();
        if (categoryModels.length > 0)
        {
            for (var i = 0; i < categoryModels.length; i++)
            {
                if (categoryModels[i].basicAttribute !== "" && categoryModels[i].basicAttribute !== null)
                {
                    let basicAttributes = JSON.parse(categoryModels[i].basicAttribute);
                    if (basicAttributes.length > 0)
                    {
                        let receivingEntityObj = $.grep(basicAttributes, function (e)
                        {
                            return e.Name === "ReceivingEntity";
                        });
                        if (receivingEntityObj[0].BroadcastReceivingEntity)
                        {
                            for (var j = 0; j < self.model.categories.length; j++)
                            {
                                if (self.model.categories[j].id == categoryModels[i].id)
                                {
                                    self.model.categories[j].isBroadcast = true;
                                    self.model.categories[j].isInternalBroadcast = receivingEntityObj[0].Type === "internal";
                                }
                            }
                        }
                    }
                }
            }
        }

        buildFilters();
        Common.gridCommon();
        var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));
        const duplicateIds = new Set();

        var buttons = SecurityMatrix.getToolbarActions(securityMatrix, self.model.nodeId, gTableName)
            .filter(button => {
                if (duplicateIds.has(button.attr.id)) {
                    return false;
                }
                duplicateIds.add(button.attr.id);
                return true;
            })
            .map(button => {
                button.className = (button.className || '') + ' hidden conditional-buttons';
                return button;
            });

        var exportButton = [{
            className: 'btn-sm btn-primary',

            extend: 'print',
            text: Resources.Print,
            title: function () {
                return Resources.reportExportedDocuments;
            },
            filename: function () {
                var currentDate = new Date();

                var formattedDate = currentDate.toISOString().split('T')[0];

                var formattedTime = currentDate.toTimeString().split(' ')[0].replace(/:/g, '-');

                return Resources.reportExportedDocuments + '_' + formattedDate + '_' + formattedTime;
            },

            exportOptions: {
                columns: [ ':visible',2,3]

            },
            customize: CTSReportDatatblesCustomComponents.CustomDatatableReport.reportExportedDocuments.print
        },
        {
            className: 'btn-sm btn-primary',

            extend: 'excelHtml5',
            title: function () {
                return Resources.reportExportedDocuments;
            },
            filename: function () {
                var currentDate = new Date();

                var formattedDate = currentDate.toISOString().split('T')[0];

                var formattedTime = currentDate.toTimeString().split(' ')[0].replace(/:/g, '-');

                return Resources.reportExportedDocuments + '_' + formattedDate + '_' + formattedTime;
            },


            exportOptions: {
                columns: [':visible',2, 3]

            },
            customize: CTSReportDatatblesCustomComponents.CustomDatatableReport.reportExportedDocuments.excelHTML5
        },
            {
                className: 'btn-sm btn-primary',

            extend: 'pdfHtml5',
            title: function () {
                return Resources.reportExportedDocuments;
            },
            filename: function () {
                var currentDate = new Date();

                var formattedDate = currentDate.toISOString().split('T')[0];

                var formattedTime = currentDate.toTimeString().split(' ')[0].replace(/:/g, '-');

                return Resources.reportExportedDocuments + '_' + formattedDate + '_' + formattedTime;
            },
           
            exportOptions: {
                columns: [':visible',2,3] 
            },
            customize: CTSReportDatatblesCustomComponents.CustomDatatableReport.reportExportedDocuments.pdfHTML5
            }
        ];

        var allButtons = [exportButton, ...buttons];

        var columns = [
            {
                title: "", visible: true, width: '0px', "orderable": false,
                "render": function (data, type, row) {
                    return "<div class='hidden'  data-id=" + row.id + " />";

                }
            },
            {
                title: '<input id="chkAll" type="checkbox" />',
                width: '16px',
                orderable: false,
                render: function (data, type, row) {
                    return "<input type='checkbox' onclick='event.stopPropagation();' data-id=" + row.id + " data-subject='" + row.subject + "' data-toStructure='" + row.toStructure + "' />";
                }
            },
            { title: "Id", data: "id", visible: false, "orderable": false }
        ];


        buildColumns(columns, self.model.nodeId, false);
        columns.push({

            "className": "text-right",
            "autoWidth": false,
            "bAutoWidth": false,
            width: "16px",
            'orderable': false,
            'sortable': false,
            'render': function (data, type, full, meta) {
                var html = '';
                if (full.requestStatus == "Pending") {

                    let btnRecall = document.createElement("button");
                    btnRecall.setAttribute("class", "btn btn-xs btn-success mr-sm recall");
                    btnRecall.setAttribute("title", Resources.Recall);
                    btnRecall.setAttribute("clickattr", "recall(" + full.id + ", " + self.model.delegationId + ")");
                    btnRecall.innerHTML = "<i class='fa fa-repeat fa-white'/>";
                    html += btnRecall.outerHTML;
                }

                let btnView = document.createElement("button");
                btnView.setAttribute("class", "btn btn-xs btn-warning mr-sm view")
                btnView.setAttribute("title", Resources.View);
                btnView.setAttribute("clickattr", "openSearchDocument(" + full.exportedDocumentId + "," + self.model.delegationId + "," +false + ")");
                btnView.innerHTML = "<i class='fa fa-eye fa-white'/>";
                html += btnView.outerHTML;

                return "<div style='display: inline-flex;'>" + html + "</div>";
            }
        });

        let table = $("#" + gTableName)
            .DataTable({
                "createdRow": function (row, data, dataIndex , textColor)
                {
                    var color = "";
                    var priorities = new CoreComponents.Lookup.Priorities().get(window.language);
              
                        for (var i = 0; i < priorities.length; i++) {
                            if (priorities[i].id === data.priorityId) {
                                color = priorities[i].color;
                            }
                        }

                    if (color !== "")
                    {
                        $(row).attr('style', "color:" + color);
                    }
                    $(row).css('cursor', 'pointer');

                },
                processing: true,
                ordering: false,
                serverSide: true,
                // The attributes data of this datatable dropdown comes by default from the appComponent file from the ($.fn.dataTable.defaults >> values) and we can override any datatable attribute value here
                "paging": true, // Enable pagination
                pageLength: 10,  // The default number of transfer rows per page
                //lengthMenu: [10, 25, 50, 100, 200],  // Dropdown options coming by default from the appComponent for rows per page
                "ajax": {
                    "url": "/Transfer/ListExportedDocumentsGrid",
                    "type": "POST",
                    "datatype": "json",
                    data: function (d)
                    {
                        d.__RequestVerificationToken = $('input[name="__RequestVerificationToken"]').val();
                        d.ReferenceNumber = $("#txtFilterExportedDocumentsReferenceNumber").val() !== "" && typeof $("#txtFilterExportedDocumentsReferenceNumber").val() !== "undefined" ? $("#txtFilterExportedDocumentsReferenceNumber").val() : "";
                        d.structureId = window.EnablePerStructure ? $('#hdLoggedInStructureId').val() : $("#hdStructureId").val();
                        return d;
                    }, "dataSrc": function (response) {
                        return response.data;
                    }
                },
                "order": [],
                "columns": columns,
                dom: '<"html5buttons "B>ltr<"pull-left"p><"pull-right"i>',

                "fnInitComplete": function (settings, json)
                {
                    $('[data-toggle="tooltip"]').tooltip();
                },
                buttons: allButtons,

            });
        table.on('draw.dt', function () {
           
            $('#grdItems tbody tr td').each(function () {
                this.setAttribute('title', $(this).text());
            });
            GridCommon.CheckSelectedRows(gTableName);

            if ($('#expandAll')[0] != undefined) {
                $('#expandAll')[0].classList.add("expand");
                $('#expandAll')[0].classList.remove("colllapse");
            }

            $('#' + gTableName + " td input[type='checkbox']").on('click', function () {

                if ($(this).is(":checked"))
                    $(".conditional-buttons").removeClass("hidden");

                else if (GridCommon.GetSelectedRows(gTableName).length == 1)
                    $(".conditional-buttons").addClass("hidden");

            });

        })
        table.$('td').tooltip({
            "delay": 0,
            "track": true,
            "fade": 250
        });
        GridCommon.AddCheckBoxEvents(gTableName);
        if (window.AllowRowSelection == "True") {
            $('#' + gTableName + ' tbody').on('click', 'tr', function () {

                UpdateButtonVisibility(gTableName,self);
            });
        }
        $('#' + gTableName + ' tbody').on('click', ".recall,.edit,.view,.unlock,.visualtracking,.Readbtn,.accept,.reject", function () {
            var onclick = $(this).attr("clickattr");
            eval(onclick);
        });
        $('#' + gTableName + ' tbody').on('dblclick', 'tr', function () {
                
                if (!gLocked) {
                    gLocked = true;
                    try {
                        var onclick = $(this).find(".edit").attr("clickattr");
                        if (!onclick) {
                            onclick = $(this).find(".view").attr("clickattr");
                        }
                        eval(onclick);
                    } catch (e) {
                        gLocked = false;
                    }
                }
            });
        $('#' + gTableName + ' tbody').on('click', 'td.parentCheckAll', function (event) {
            event.stopPropagation();
            let tr = $(this).closest('tr');
            var data = table.row(tr).data();
            $('[parent=' + data.id + ']').prop('checked', $('[data-id=' + data.id + ']').prop('checked'));
            $('[parent=' + data.id + ']').trigger("change");
        });
        $('#filtersContainer input').off('keydown', function (e) { });
        $('#filtersContainer input').on('keydown', function (e)
        {
            var code = e.keyCode || e.which;            
            if (code === 13)
            {
                $("#btnFilterExportedDocumentsSearch").trigger('click');
            }
        });
        $('#' + gTableName + ' #chkAll').on('click', function () {
            if ($(this).is(":checked") && !self.model.isExported)
            $(".conditional-buttons").removeClass("hidden");

            else
                $(".conditional-buttons").addClass("hidden");

        });   
        $('#' + gTableName).on('change', 'input[type="checkbox"]', function () {
            let table = $('#' + gTableName).DataTable();
            let pageNodes = table.rows({ page: 'current' }).nodes();
            let total = $('input[type="checkbox"]', pageNodes).length;
            let checked = $('input[type="checkbox"]:checked', pageNodes).length;

            $('#chkAll').prop('checked', total === checked);

            if (checked > 0 && !self.model.isExported) {
                $(".conditional-buttons").removeClass("hidden");
            } else {
                $(".conditional-buttons").addClass("hidden");
            }
        });
        SecurityMatrix.InitContextMenu(securityMatrix, self.model.nodeId);
    }

    
}

export default { ExportedDocuments, ExportedDocumentsView };