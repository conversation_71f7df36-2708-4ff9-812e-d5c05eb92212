﻿using Intalio.CTS.Core.DAL;
using Intalio.CTS.Core.Model;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Headers;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace Intalio.CTS.Core.API
{
    /// <summary>
    /// A class To manage the security of the attachments
    /// Add,Update Or Delete
    /// </summary>
    /// <returns></returns>
    public static class ManageAttachmentSecurity
    {
        #region Public Methods
        /// <summary>
        /// Add the attachment Securities
        /// UserId and StructureId
        /// </summary>
        /// <param name="model"></param>
        /// <param name="AttachmentId"></param>
        /// <returns></returns>
        public static async Task<bool> AddAsync(List<AttachmentSecurityModel> model, long? AttachmentId)
        {
            await RemoveSecurityByAttachmentId(AttachmentId);
            if (model.Count() > 0)
            {
                List<AttachmentSecurity> list = new List<AttachmentSecurity>();
                foreach (var item in model)
                {
                    list.Add(new AttachmentSecurity()
                    {
                        AttachmentId = AttachmentId,
                        UserId = item.toUserId,
                        StructureId = item.toStructureId
                    });
                }
                new AttachmentSecurity().InsertMultiple(list);
                return true;
            }
            return false;
        }

        public static async Task<bool> AppendAsync(List<AttachmentSecurityModel> model, long? AttachmentId)
        {
            try
            {
                if (model.Count() > 0)
                {
                   
                    List<AttachmentSecurity> existingPermissions = GetExistingPermissions(AttachmentId);

                    
                    List<AttachmentSecurity> list = new List<AttachmentSecurity>();

                    foreach (var item in model)
                    {
                       
                        bool exists = existingPermissions.Any(p => p.UserId == item.toUserId && p.StructureId == item.toStructureId);

                        if (!exists)
                        {
                            list.Add(new AttachmentSecurity()
                            {
                                AttachmentId = AttachmentId,
                                UserId = item.toUserId,
                                StructureId = item.toStructureId
                            });
                        }
                    }

                   
                    if (list.Any())
                    {
                        new AttachmentSecurity().InsertMultiple(list);
                        return true;
                    }
                }
                return false;
        }
            catch (Exception ex)
            {
                return false;
            }
}
        public static List<AttachmentSecurity> GetExistingPermissions(long? AttachmentId)
        {
            using (var context = new CTSContext())
            {
                return context.AttachmentSecurity
                              .Where(x => x.AttachmentId == AttachmentId)
                              .ToList();
            }
        }

        /// <summary>
        /// delete the attachment Securities to update with new user and structure ids
        /// 
        /// </summary>
        /// <param name="AttachmentId"></param>
        /// <returns></returns>
        public static async Task<bool> RemoveSecurityByAttachmentId(long? AttachmentId)
        {
            List<AttachmentSecurity> list = await new AttachmentSecurity().FindByAttachmentId(AttachmentId);
            if (list.Count()>0)
            {
                new AttachmentSecurity().DeleteRange(list);
                return true;
            }
            return true;
        }
        /// <summary>
        /// check if the user included in the attachment securities
        /// </summary>
        /// <param name="attachment">must included attachment securities</param>
        /// <param name="UserId"></param>
        /// <param name="StructureIds"></param>
        /// <returns></returns>
        public static bool ChackAttachmentSecurity(Attachment attachment,long UserId, List<long> StructureIds)
        {
            return (attachment.AttachmentSecurities.Count() > 0 ? 
                (attachment.CreatedByUserId == UserId 
                || attachment.AttachmentSecurities.Select(x => x.UserId).Contains(UserId) 
                || attachment.AttachmentSecurities.Where(x => StructureIds.Contains(x.StructureId.Value) && x.UserId == null).Count() > 0) 
                : true);
        }
        /// <summary>
        /// discard Changes And CheckOut File
        /// </summary>
        /// <param name="AttachmentId"></param>
        /// <param name="version"></param>
        /// <param name="usertoken"></param>
        /// <returns></returns>
        public static async Task<bool> discardChangesAndCheckOutFile(long AttachmentId, string version, string usertoken = null)
        {
            if (string.IsNullOrEmpty(usertoken))
            {
                usertoken = Core.Configuration.IdentityAccessToken;
            }

            //viewer API document/{documentId}/version/{version}/checkout
            string viewerUrl = $"{Configuration.ViewerUrl}/api/document/{AttachmentId}/version/{version}/checkout";
            var result = await HttpDeleteAsync<object>(viewerUrl, usertoken);
            if (result != null && ((Newtonsoft.Json.Linq.JContainer)result)?.Count > 0)
                return true;
            return false;
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="url"></param>
        /// <param name="token"></param>
        /// <returns></returns>
        public static async Task<T> HttpDeleteAsync<T>(string url, string token)
        {
            HttpClient client = new HttpClient();
            client.BaseAddress = new Uri(url);
            client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
            HttpResponseMessage response = await client.DeleteAsync(url);
            if (response.IsSuccessStatusCode)
            {
                return JsonConvert.DeserializeObject<T>(await response.Content.ReadAsStringAsync());
            }

            return default(T);
        }

        #endregion
    }
}
