#ignore thumbnails created by windows
Thumbs.db
#Ignore files build by Visual Studio
*.user
*.aps
*.pch
*.vspscc
*.orig
*_i.c
*_p.c
*.ncb
*.suo
*.cache
*.ilk
*.log

# ignore a file type
*.pdb
*.cache

# ignore an entire directory
.vs/
node_modules/

## Ignore Visual Studio temporary files, build results, and
## files generated by popular Visual Studio add-ons.

# User-specific files
*.suo
*.user
*.userosscache
*.sln.docstates

# User-specific files (MonoDevelop/Xamarin Studio)
*.userprefs

# Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
build/
bld/
[Bb]in/
[Oo]bj/

# Visual Studio 2015 cache/options directory
.vs/
#construct the below from the debug version using jscompress.com
[Bb]in
[Dd]ebug*/
*.sbr
obj/
[Rr]elease*/
_ReSharper*/
/Backup/17 Aug/CodeBase
*.config
!nlog.config
!/Hangfire/Hangfire-master/*
bin/
.pdb
appsettings.json
Intalio.CTS/node_modules/
ApiDocumentation/

# js build files
Intalio.CTS/wwwroot/js/templates.min.js
Intalio.CTS/wwwroot/js/bundle.min.js
Intalio.CTS/appsettings.Development.json
Intalio.CTS/wwwroot/js/bundle.min.js
Intalio.CTS/wwwroot/js/app.min.js
Intalio.CTS/wwwroot/js/main.min.js

# css build files
Intalio.CTS/wwwroot/css/main.min.css
Intalio.CTS/wwwroot/css/main-rtl.min.css
Intalio.CTS/wwwroot/css/style.min.css
Intalio.CTS/wwwroot/css/style-rtl.min.css
/Intalio.CTS.Core/Model/UserStructureViewModel.cs
/Intalio.CTS/Properties/PublishProfiles/FolderProfile7.pubxml
/docs/CTS-Architecture-Overview.md
/docs/CTS-Database-Management.md
/docs/CTS-Development-Guidelines.md
/docs/CTS-Frontend-Architecture.md
/docs/CTS-Project-Structure.md
/docs/CTS-Translation-System.md
/docs/README.md
/.augment/rules/CTS-Architecture-Overview.md
/.augment/rules/CTS-Database-Management.md
/.augment/rules/CTS-Development-Guidelines.md
/.augment/rules/CTS-Frontend-Architecture.md
/.augment/rules/CTS-Project-Structure.md
/.augment/rules/CTS-Translation-System.md
/.augment/rules/README.md
/bash.exe.stackdump
/.augment/rules/CTS-SQL-UTF8-Encoding.md
