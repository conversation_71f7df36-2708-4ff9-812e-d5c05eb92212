.workingDaysLabel {
    left: 70px;
    right: auto !important;
}

.ResetIcon svg {
    left: calc(50% - 85px) !important;
}

.DisableMouseZoom svg, .EnableMouseZoom svg {
    left: calc(50% - 105px) !important;
}

*::-ms-backdrop, .list-action-filter {
    margin-top: 20px !important;
}
/* IE11 */

.floating {
    float: right !important;
}

#ImgSizeEditor {
    left: auto !important;
    right: 65px;
    text-align: right !important;
}

    #ImgSizeEditor input[type='text'] {
        padding-left: initial !important;
        padding-right: 3px;
    }

    #ImgSizeEditor ul {
        padding-right: 0px;
    }

#attachmentContainer > .col-lg-3 {
    padding-right: 0px;
    padding-left: 5px;
}

#attachmentContainer > .col-lg-9 {
    padding-left: 0px;
    padding-right: 0px;
}

#documentWithViewerContainer {
    padding-right: 0px;
    padding-left: 5px;
}

#documentViewerContainer {
    padding-left: 0px;
    padding-right: 0px;
}

.input-group > .select2-hidden-accessible:first-child + .select2-container--bootstrap > .selection > .select2-selection,
.input-group > .select2-hidden-accessible:first-child + .select2-container--bootstrap > .selection > .select2-selection.form-control {
    border-bottom-left-radius: 0;
    border-top-left-radius: 0;
    border-bottom-right-radius: 4px;
    border-top-right-radius: 4px;
}

.margin-5 {
    margin-left: 5px !important;
}

.transferInstruction {
    right: 30%;
    left: auto !important;
}

.padding-left-0 {
    padding-right: 0px;
    padding-left: initial !important;
}

.ml-1-25em {
    margin-right: 1.25em;
    margin-left: initial !important;
}

.ml-0-7em {
    margin-right: 0.7em;
    margin-left: initial !important;
}

#purposeContainer span.select2-dropdown.select2-dropdown--below {
    margin-left: -1px;
    margin-right: initial !important;
}

#setPurposeToAll {
    margin-left: 15px;
    margin-right: initial !important;
}

#parsleyIcon {
    right: 115px;
}

.classInfoDiv {
    left: auto !important;
    right: 80px;
    box-shadow: 0px 3px 3px 0px #ccc;
}

.classInfoDivIcon {
    padding-right: 5px;
    padding-left: initial !important;
}


.mr-10px {
    margin-left: 10px !important;
}

.width100pc-ml0 {
    width: 100% !important;
    margin-right: 0px !important;
}

.unread .dot {
    left: auto !important;
    right: -10px;
}

.ml-10px {
    margin-left: auto !important;
    margin-right: 10px !important;
}

.inside_color_line {
    left: auto !important;
    right: 0;
}

.mdl-text-msg {
    font-size: 13px;
}

.mdl-time {
    font-size: 11px;
}

.dropdown-margin {
    margin-right: -113px;
}

.select2-selection__clear {
    float: left !important;
    margin-left: 10px !important;
}

@media (min-width:768px) {
    .vipDetailsPanel {
        padding-left: 15px;
        padding-right: 15px;
    }

    .vipCorrLeftPanel {
        
    }

    .vipCorrPanel {
        padding-right: 15px;
    }
}

.typeahead.dropdown-menu {
    right: auto !important;
    direction: ltr !important;
}

.flatpickr-current-month {
    padding: 0 0 0 0 !important;
}

.bootstrap-tagsinput > .label-tag {
    font-size: 85% !important;
    padding: 0.3em 0.6em 0.3em;
}

.bootstrap-tagsinput .tag [data-role="remove"] {
    margin-right: 8px;
    margin-left: auto !important;
    float: left;
}

@media (max-width: 768px) {
    #containerBarcodeDiv {
        padding-right: 0px;
    }
    #containerPreviewBarcode {
        margin-right: 15px;
    }
}
/*.topnavbar .navbar-form {
    right: 220px !important;
   
}*/
@media (min-width: 768px) {
    .topnavbar .navbar-form {
        right: 220px !important;
        left: 0px !important ;
    }
}

.navbar-form-close {
    left: 0 !important;
    right: auto !important;

}

.tabContent {
    padding: 0px 45px 0px 10px;
}
.ctstooltip {
    position: relative;
    display: inline-block;
}

    .ctstooltip .tooltiptext {
        width: 100px;
        visibility: hidden;
        background-color: #555;
        color: #fff;
        text-align: center;
        padding: 5px 0;
        border-radius: 6px;
        /* Position the tooltip text */
        position: absolute;
        z-index: 1;
        top: 3px;
        left: 105%;
        right: auto;
        /* Fade in tooltip */
        opacity: 0;
        transition: opacity 0.3s;
    }

    /* Tooltip arrow */

    .ctstooltip .tooltiptext::after {
        content: " ";
        position: absolute;
        top: 50%;
        right: 100%; /* To the left of the tooltip */
        margin-top: -5px;
        border-width: 5px;
        border-style: solid;
        border-color: transparent black transparent transparent;
    }

    /* Show the tooltip text when you mouse over the tooltip container */
    .ctstooltip:hover .tooltiptext {
        visibility: visible;
        opacity: 1;
    }

.VIPContainerItem {
    margin: 0px 0px 0px 5px;
}


.defaultview {
    padding: 0px 5px 0px 29px;
}
.panel-header-primary {
    background-color: #37BC9B !important;
    color: white !important;
}

.panel-header-default {
    background-color: white !important;
    color: black !important;
}
.mdl-container-document {
    width: 100%;
    height: 95px;
}
.min-max-width-150-250 {
    min-width: 150px !important;
    max-width: 250px !important;
    word-break: break-word;
}
.task-panel {
    box-sizing: border-box !important;
    left: 0px !important;
    color: white !important;
    padding-top: 0px !important;
    position: absolute !important;
    top: 100% !important;
    z-index: 10 !important;
    width: 100% !important;
    background-color: #37BC9B !important
}

.card-max-width {
    max-width: 900px !important
}

.card-min-width {
    max-width: 175px !important
}

.scrtabs-rtl {
	display: flex;
	justify-content: revert;
}

.scrtabs-rtl > .scrtabs-tab-scroll-arrow-left,
.scrtabs-rtl > .scrtabs-tab-scroll-arrow-right {
    rotate: 180deg;
}

.navbar-nav .nav-link.btn-transparent {
    background: transparent;
    color: #fff;
    padding: 12px;
    outline: 0;
    font-size: 18px;
}

table#grdFollowupPanelItems thead th {
    white-space: nowrap;
}

.list-container > .card {
    max-height: 470px
}

.aside-collapsed .sidebar .arrow-menu {
    display: none !important;
}

label.control-label {
    font-size: inherit !important;
}

.table.dataTable th {
    font-size: inherit !important;
}

div.dataTables_wrapper div.dataTables_length label {
    font-size: inherit !important;
}

.btn-excel,
.btn-pdf {
    font-size: inherit !important;
}