﻿using Intalio.Core;
using Intalio.Core.DAL;
using Intalio.Core.Model;
using Intalio.CTS.Core.Model;
using NPOI.POIFS.Crypt.Dsig;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;

namespace Intalio.CTS.Core.API
{
    public static class ManageUser
    {
        #region Public Methods

        /// <summary>
        /// Mapping users from IAM
        /// </summary>
        /// <param name="ids"></param>
        public static void Provision(List<long> ids)
        {
            var userIds = new User().FindNotExistent(ids);
            if (userIds.Count > 0)
            {
                var result = Intalio.Core.API.IdentityHelper.GetUsers(userIds, Configuration.IdentityAccessToken);
                if (!result.IsNullOrEmpty())
                {
                    foreach (Intalio.Core.Model.UserModel userModel in result)
                    {
                        new User
                        {
                            Id = userModel.Id,
                            Firstname = userModel.FirstName,
                            Lastname = userModel.LastName,
                            RoleId = userModel.ApplicationRoleId
                        }.Insert();
                    }
                }
            }
        }

        public static void Provision(UserViewModel model, bool updateAll = true)
        {
            User user = new User().Find(Convert.ToInt32(model.Id));
            if (user == null)
            {
                user = new User
                {
                    Id = model.Id,
                    Firstname = model.Firstname,
                    Lastname = model.Lastname,
                    RoleId = model.RoleId
                };
                user.Insert();
            }
            else if (updateAll)
            {
                user.Firstname = model.Firstname;
                user.Lastname = model.Lastname;
                user.RoleId = model.RoleId;
                user.Update();
            }
        }


        /// <summary>
        /// Get user from iam
        /// </summary>
        /// <param name="id">user id</param>
        public static Intalio.Core.Model.UserModel GetUser(long id)
        {
            return Intalio.Core.API.IdentityHelper.GetUser(id, Configuration.IdentityAccessToken);
        }

        public static Intalio.Core.Model.UserModel GetUser(long userId, Language language)
        {
            string url = $"{Configuration.IdentityAuthorityUrl}/Api/GetUser?id={userId}&language={language}";
            return Intalio.Core.Helper.HttpGet<Intalio.Core.Model.UserModel>(url,
               Configuration.IdentityAccessToken);
        }
        /// <summary>
        /// Update user configuration
        /// </summary>
        /// <param name="id">User id</param>
        /// <param name="configuration">user configuration (json)</param>
        /// <returns></returns>
        public static void UpdateConfiguration(long id, string configuration)
        {
            DAL.User user = new DAL.User { Id = id };
            user.Configuration = configuration;
            user.UpdateConfiguration();
        }

        /// <summary>
        /// Get user configuration
        /// </summary>
        /// <param name="id">User id</param>
        /// <returns></returns>
        public static string GetConfiguration(long id)
        {
            return new DAL.User().FindConfiguration(id);
        }

        /// <summary>
        /// Get logged in structure
        /// </summary>
        /// <param name="id">User id</param>
        /// <returns></returns>
        public static long? GetLoggedInStructure(long id)
        {
            return new DAL.UserStructure().getLoggedInStrucureId(id);
        }

        /// <summary>
        /// Get User Structure by Strucure Id
        /// </summary>
        /// <param name="id">User id</param>
        /// <returns></returns>
        public static DAL.UserStructure GetUserStructureByStructureId(long structureId, long userId)
        {
            return new DAL.UserStructure().FindByStructureIdAndUserId(structureId, userId);
        }

        /// <summary>
        /// Update user logged in structure
        /// </summary>
        /// <param name="id">User id</param>
        /// <param name="loggedInStrucureId">user logged In Strucure Id</param>
        /// <returns></returns>
        public static void UpdateLoggedInStructure(long userId, long StrucureId, bool isLoggedIn)
        {

            DAL.UserStructure userStructure = new DAL.UserStructure().FindByStructureIdAndUserId(StrucureId, userId);
            userStructure.IsLoggedInStructure = isLoggedIn;
            userStructure.UpdateLoggedInStructure();
        }

        public static void InsertLoggedInStructure(long userId, long StructureId, bool isLoggedIn)
        {
            User user = new User().Find(userId);
            CTS.Core.DAL.Structure structure = new CTS.Core.DAL.Structure().Find(StructureId);

            if (user == null)
                Provision(new List<long>() { userId });
            if (structure == null)
                ManageStructure.Provision(new List<long>() { StructureId });

            DAL.UserStructure userStructure = new DAL.UserStructure { UserId = userId, StructureId = StructureId, IsLoggedInStructure = isLoggedIn, PrivacyId = 1};
            if (userStructure.FindByStructureIdAndUserId(StructureId, userId) == null)
            {
                userStructure.Insert();
            }
        }

        public static List<Intalio.Core.Model.UserModel> GetUsers(List<long> usersId)
        {
            string url = $"{Configuration.IdentityAuthorityUrl}/api/GetUsers";
            MultipartFormDataContent data = new MultipartFormDataContent();
            foreach (long id in usersId)
            {
                data.Add(new StringContent(id.ToString()), "ids");
            }
            return Intalio.Core.Helper.HttpPost<List<Intalio.Core.Model.UserModel>>(url, Configuration.IdentityAccessToken, data);
        }
        public static UserName GetUserName(long userId, Language lang)
        {
            var user = API.ManageUser.GetUser(userId, lang);
            UserName userName = GetUserName(lang, user);
            return userName;
        }

        public static UserName GetUserName(Language lang, Intalio.Core.Model.UserModel user)
        {
            var userName = new UserName();
            var userAttributes = user?.Attributes;
            if (userAttributes?.Count > 0)
            {
                if (lang == Language.AR)
                {

                    userName.FirstName = userAttributes.SingleOrDefault(x => x.Text == Configuration.FirstNameAr)?.Value ?? String.Empty;
                    userName.MiddleName = userAttributes.SingleOrDefault(x => x.Text == Configuration.MiddleNameAr)?.Value ?? String.Empty;
                    userName.LastName = userAttributes.SingleOrDefault(x => x.Text == Configuration.LastNameAr)?.Value ?? String.Empty;
                }
                else if (lang == Language.FR)
                {
                    userName.FirstName = userAttributes.SingleOrDefault(x => x.Text == Configuration.FirstNameFr)?.Value ?? String.Empty;
                    userName.MiddleName = userAttributes.SingleOrDefault(x => x.Text == Configuration.MiddleNameFr)?.Value ?? String.Empty;
                    userName.LastName = userAttributes.SingleOrDefault(x => x.Text == Configuration.LastNameFr)?.Value ?? String.Empty;


                }
            }
            if (string.IsNullOrEmpty(userName.FirstName))
            {
                userName.FirstName = user.FirstName;
                userName.LastName = user.LastName;
            }

            return userName;
        }

        public static string GetFullName(long userId, Language lang)
        {
            var userName = GetUserName(userId, lang);
            return $"{userName.FirstName} {userName.LastName}";
        }
        public static string GetTrippleFullName(long userId, Language lang)
        {
            var userName = GetUserName(userId, lang);
            return $"{userName.FirstName} {userName.MiddleName} {userName.LastName}";
        }
        public static string GetFullNameByUser(Intalio.CTS.Core.DAL.User user, Language lang)
        {
            var userFullName = $"{user.Firstname} {user.Lastname}";
            if (lang == Language.AR)
            {
                userFullName = $"{user.FirstnameAr} {user.LastnameAr}";               
            }
            else if (lang == Language.FR)
            {
                userFullName = $"{user.FirstnameFr} {user.LastnameFr}";                
            }
            return userFullName ;
        }
        public static string GetFullNameByUser(Intalio.Core.DAL.User user, Language lang)
        {
            var userFullName = $"{user.Firstname} {user.Lastname}";
            if (lang == Language.AR)
            {
                userFullName = user.FirstnameAr != null && user.LastnameAr != null?  $"{user.FirstnameAr} {user.LastnameAr}": userFullName;               
            }
            else if (lang == Language.FR)
            {
                userFullName = user.FirstnameFr != null && user.LastnameFr != null ? $"{user.FirstnameFr} {user.LastnameFr}": userFullName ;                
            }
            return userFullName ;
        }

        public static bool CheckBreakInheritanceInStructure(long userId, long? StructureId)
        {
            var userstructure = new DAL.UserStructure().FindByStructureIdAndUserId((long)StructureId, userId);
            if (userstructure == null) 
                return false;
            return userstructure.SecurityBreakedInheritance;
        }
        public static List<long> FindNotExistent(List<long> userIds)
        {
            return new Intalio.Core.DAL.User().FindNotExistent(userIds);
        }

        public static List<DAL.User> GetUsersFromCTS(string userName, long? delegationId, bool enableSendingRules, bool isStructureSender, List<long> structureIds, long userid
            , long structureId, long? startIndex, long? pageSize, Language language)
        {
            var structures = GetUsersStructuresFromCTS(userName, delegationId, enableSendingRules, true, isStructureSender, structureIds, userid, structureId, startIndex, pageSize, StructureType.Internal, null, null, false, language);
            //var users = structures
            //    .SelectMany(x => x.UserStructure.Select(us => us.User)).DistinctBy(y => y.Id)
            //    .ToList();
            var users = structures
               .SelectMany(x => x.UserStructure.Select(us => us.User))
               .Where(user => user.Active == true) 
               .DistinctBy(user => user.Id)
               .ToList();
                    return users;
                }

        public static List<Intalio.Core.DAL.User> ListAllActiveUsers()
        {
            return new Intalio.Core.DAL.User().ListAllActiveUsers();
        }

        public static List<DAL.Structure> GetUsersStructuresFromCTS(string searchText, long? delegationId, 
            bool enableSendingRules, bool enableTransferToUsers, bool isStructureSender, List<long> structureIds, long userId,
            long structureId, long? startIndex, long? pageSize, StructureType structureType, string searchStructure, string searchUser,
            bool fromSendingandReceiving , Language language, long? categoryId = null, bool fromSendingEntity = false,bool AllowTransferToMe=false,long filterByLoggedInStructure=default)
        {
            List<long> structureSendingRulesIds = new List<long>();
            List<DAL.Structure> structureUsers = new List<DAL.Structure>();
            // only For Lekhwya Buiseness
            if (fromSendingandReceiving)
            {
                isStructureSender = true;
                enableSendingRules = false;
                enableTransferToUsers = false;
            }

            if (delegationId.HasValue)
            {
                var delegationData = ManageDelegation.GetByDelegationId(userId, delegationId.Value);
                isStructureSender = delegationData.IsStructureSender;
                structureIds = delegationData.StructureIds;
            }

            if (!isStructureSender)
            {
                structureSendingRulesIds = structureIds;
            }
            else if (enableSendingRules)
            {
                structureSendingRulesIds = ManageSendingRule.ListSendToStructures(userId, structureId);
            }

            if (enableSendingRules && structureSendingRulesIds.Count == 0)
            {
                return null;
            }

            else if (!enableSendingRules && structureSendingRulesIds.Count == 0)
            {
                structureUsers = ManageStructure.GetUsersAndStructures(searchText, enableTransferToUsers, userId, structureId, new List<long>(), structureType, searchStructure, searchUser, fromSendingandReceiving, AllowTransferToMe, filterByLoggedInStructure:filterByLoggedInStructure);
            }
            else
            {
                structureUsers = ManageStructure.GetUsersAndStructures(searchText, enableTransferToUsers, userId, structureId, structureSendingRulesIds, structureType, searchStructure, searchUser, AllowTransferToMe: AllowTransferToMe, filterByLoggedInStructure:filterByLoggedInStructure);
            }

            if (structureUsers == null || !structureUsers.Any())
                return new List<DAL.Structure>();
            var result = new List<string>();

            foreach (var structureUser in structureUsers)
            {
                
                if (structureUser != null)
                {
                    if (structureUser.UserStructure != null)
                    {
                        List<Intalio.CTS.Core.DAL.UserStructure> AddedUserStructers = new List<Intalio.CTS.Core.DAL.UserStructure>();
                        foreach (var UserStructure in structureUser.UserStructure)
                        {
                            if (UserStructure.User.Active)
                                AddedUserStructers.Add(UserStructure);
                        }
                        structureUser.UserStructure = AddedUserStructers;
                    }
                    var code = structureUser.Code ?? string.Empty; // Use empty string if code is null
                    var parentname = structureUser.Parent?.Name;
                    var structurename = structureUser.Name ?? string.Empty; // Use empty string if structurename is null
                    if (language == Language.AR)
                    {
                        structurename = !string.IsNullOrEmpty(structureUser.NameAr) ? structureUser.NameAr : structureUser.Name;
                        if (!string.IsNullOrEmpty(structureUser.Parent?.NameAr))
                        {
                            parentname = structureUser.Parent.NameAr;
                        }
                    }
                    else if (language == Language.FR)
                    {
                        structurename = !string.IsNullOrEmpty(structureUser.NameFr) ? structureUser.NameFr : structureUser.Name;
                        if (!string.IsNullOrEmpty(structureUser.Parent?.NameFr))
                        {
                            parentname = structureUser.Parent.NameFr;
                        }
                    }
                    else
                    {
                        structurename = structureUser.Name;
                    }
                    // Concatenate code, parentname, and structurename with separators
                    var updatedStructureName = $"{parentname}/{structurename}";
                    if (!string.IsNullOrEmpty(structurename) && !string.IsNullOrEmpty(parentname) && !string.IsNullOrEmpty(code))
                    {
                        updatedStructureName = $"{parentname}/{structurename}";
                    }
                    else if(!string.IsNullOrEmpty(structurename) && !string.IsNullOrEmpty(parentname))
                    {
                        updatedStructureName = $"{parentname}/{structurename}";
                    }
                    else if(!string.IsNullOrEmpty(structurename) && !string.IsNullOrEmpty(code))
                    {
                        updatedStructureName = $"{structurename}";
                    }
                    else
                    {
                        updatedStructureName = $"{structurename}";
                    }

                    structureUser.Name = updatedStructureName;
                    structureUser.NameAr = updatedStructureName;
                    //var newStructure = new DAL.Structure
                    //{
                    //    Code = code,
                    //    Name = updatedStructureName, 
                    //    ParentId = structureUser.ParentId
                    //};
                    //   newStructureUsers.Add(newStructure);

                }
            }
            if (categoryId == 1 && fromSendingEntity || !fromSendingEntity && categoryId == 2)
            {
                structureUsers = structureUsers.Where(s => s.Id != structureId).ToList();
            }
            return structureUsers;
        }

        
        public static List<Intalio.Core.Model.UserModel> GetSearchUsers(string name, bool showOnlyActiveUsers, int language = 1) 
        {
             return Intalio.Core.API.IdentityHelper.GetSearchUsers(name,showOnlyActiveUsers,language, Configuration.IdentityAccessToken);
        }
        #endregion
    }
}