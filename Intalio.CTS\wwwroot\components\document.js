﻿import Intalio from './common.js'
import SendTransferModal from './sendTransfer.js'
import { SignatureTemplates, SignatureTemplatesView } from './signatureTemplate.js'

import SendToReceivingEntityIndex from './sendToReceivingEntity.js'
import DocumentCompleteIndex from './documentComplete.js'
import DocumentBarcode from './documentBarcode.js'
import { IdentityService, Helper } from './lookup.js'
import EntityGroupBook from './entityGroupBook.js'
import Transfer from './transfer.js'
import TaskEmailReminder from './taskEmailReminder.js'
import Workflow from './Workflow.js'
import OrganizationManagement from './organizationManagement.js'
import AddressBook from './ctsaddressBook.js'
import CopyOptions from '../components/copyOptions.js'
import ExportOptions from '../components/exportOptions.js'
import VipDocumentInbox from './vipInboxList.js'
import { Categories } from '../components/lookup.js'

var gInboxTableName = "grdInboxItems";
var inboxGidContainerName = "inboxListContainer-mask";
var gSentTableName = "grdSentItems";
var sentGridContainerName = "sentListContainer-mask";
var prioritiesList = [];
function signDocument(self, signType, templateId, model=null) {
    if (templateId == 0) {
        var templateId = $('input[name="rdbSignature"]:checked').val();

    }
    if (templateId == undefined) {
        Common.alertMsg(Resources.SignatureRequired);
        Common.unmask("body-mask");
        return;
    }
    if (signType == 0) {
        let parms = {
            'templateId': templateId,
            'documentId': self.model.id,
            'transferId': self.model.transferId,
            'delegationId': self.model.delegationId
        }
        Common.mask(document.body, "body-mask");
        $('#btnSubmitSign').attr("disabled", true);
        $('#btnSubmitSign').button('reset');
        var headers = {};
        headers.Authorization = 'Bearer ' + window.IdentityAccessToken;

        var BookMarkscallBack = () => {

            $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", false);
            $('#' + self.model.ComponentId + '_btnComplete').button('reset');
            $('#' + self.model.ComponentId + '_btnCompleteAndArchive').attr("disabled", false);
            $('#' + self.model.ComponentId + '_btnCompleteAndArchive').button('reset');
            $('#' + self.model.ComponentId + '_btnReplyToStructure').attr("disabled", false);
            $('#' + self.model.ComponentId + '_btnReplyToStructure').button('reset');
            $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", false);
            $('#' + self.model.ComponentId + '_btnReplyToUser').button('reset');
            $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
            $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
            $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", false);
            $('#' + self.model.ComponentId + '_btnSignTemplate').button('reset');
            $('#btnSubmitSign').attr("disabled", false);
            $('#btnSubmitSign').button('reset');
            $('#btnGroupDropup').attr('disabled', false);
            $('#btnGroupDropup').button('reset');

            $('#modalSignatureTemplates').modal("hide");

            tryCloseModal(self.model.actionComponentId);
            Common.showScreenSuccessMsg(Resources.SigningDocument);
            Common.unmask("body-mask");
            if (self.model.fromVip) {
                $(".withBorders-o").addClass("waitingBackground");
                $("#draftDocumentDetailsContainer").empty();
                $($("input[data-id='" + self.model.id + "']").parents("li")[0]).fadeOut().remove();
            } else {
                GridCommon.Refresh("grdDraftItems");
            }
            TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
            TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
            TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);
            $('.btn-SignOperations').remove();

            Common.ajaxPostWithHeaders('Transfer/SignDocument', parms, function (returnedData) {
                var data = returnedData.message;
                if (data == '') {
                    Common.showScreenSuccessMsg(Resources.SigningSuccess);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                    TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);
                }
                else {
                    if (self.model.fromVip) {
                        $(".withBorders-o").addClass("waitingBackground");
                        $("#draftDocumentDetailsContainer").empty();
                        $($("input[data-id='" + self.model.id + "']").parents("li")[0]).fadeOut().remove();
                    } else {
                        GridCommon.Refresh("grdDraftItems");
                    }
                    TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                    TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);
                    var errorMessage = Resources.SigningFailed;
                    if (Resources[data] !== undefined)
                        errorMessage += ": " + Resources[data];
                    else if (data = "Can't render barcode: not enough space")
                        errorMessage += ": " + data;
                    Common.showScreenErrorMsg(errorMessage);
                }
            }, function (error, object) {
                var msg = error ? error : "";
                if (object && object.responseText) {
                    msg = object.responseText;
                }
                Common.showScreenErrorMsg(msg);

            }, false, null, headers, true)
        };
        CheckUserCanSign(self.model.id, self.model.transferId, self.model.delegationId, ["signature", "barcode"], BookMarkscallBack);
        //CheckBookMarksAreExist(self.model.id, self.model.transferId, ["signature"], BookMarkscallBack); //"barcode" // remove barcode bookmark check for now
    }
    else if (signType == 1) {// sign and send 
        signType = 0;
        sendToReceivingEntityMyTransfer(self.model.replyToEntity, self.model.delegationId, TransferType.Send, self.model.fromUser, self, templateId, true);
    }
    else if (signType == 2) { // sign and transfer
        signType = 0;
        Common.mask(document.body, "body-mask");
        self.model.signDocument = false;
        self.model.signatureTemplate = templateId;
        //$('#' + self.model.ComponentId + '_btnSignTemplate').click();


        let parms = {
            'templateId': templateId,
            'documentId': self.model.id,
            'transferId': self.model.transferId,
            'delegationId': self.model.delegationId
        }
        $('#btnSubmitSign').attr("disabled", true);
        $('#btnSubmitSign').button('reset');
        var BookMarkscallBack = () => {
            Common.ajaxPost('Transfer/SignDocument', parms, function (returnedData) {
                var data = returnedData.message;
                $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnComplete').button('reset');
                $('#' + self.model.ComponentId + '_btnReplyToStructure').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnReplyToStructure').button('reset');
                $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnReplyToUser').button('reset');
                $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnSignTemplate').button('reset');
                $('#btnGroupDropup').attr('disabled', false);
                $('#btnGroupDropup').button('reset');
                $('#btnSubmitSign').attr("disabled", false);
                $('#btnSubmitSign').button('reset');

                $("#modalSignatureTemplates").modal("hide");
                if (data == '') {
                    self.model.fromSignAndTransfer = true;
                    if (returnedData.signedTransferId) {
                        self.model.transferId = returnedData.signedTransferId;
                    }
                    openTransfer(model.privacies, model.delegationId, self);
                    Common.showScreenSuccessMsg();

                    $('[id$=viewerFrame]').attr("src", $('[id$=viewerFrame]').attr("src"))



                }
                else if (data == 'NoSignatureConfigured') {
                    Common.alertMsg(Resources.NoSignatureConfigured);
                    Common.unmask("body-mask");
                }
                else if (data == 'NoAttachmentToSign') {
                    Common.alertMsg(Resources.NoAttachmentToSign);
                    Common.unmask("body-mask");
                }
                else if (data === "CheckoutFaild") {
                    Common.alertMsg(Resources.CheckoutFaild);
                    Common.unmask("body-mask");
                }
                else if (data === "SignFailed") {
                    Common.alertMsg(Resources.SignFailed);
                    Common.unmask("body-mask");
                }
                else if (data === "HasNoAccess") {
                    Common.alertMsg(Resources.HasNoAccess);
                    Common.unmask("body-mask");
                }
                else if (data === "CheckinFailed") {
                    Common.alertMsg(Resources.CheckinFailed);
                    Common.unmask("body-mask");
                }
                else if (data === "FileInUse") {
                    Common.alertMsg(Resources.FileInUse);
                    Common.unmask("body-mask");
                }
                else if (data === "Can't render barcode: not enough space") {
                    Common.alertMsg(data);
                    Common.unmask("body-mask");
                }
                else {
                    Common.showScreenErrorMsg();
                    Common.unmask("body-mask");
                }
                Common.unmask("body-mask");
                //Common.showScreenSuccessMsg();
                //sendToReceivingEntity(self.model.replyToEntity, self.model.delegationId, TransferType.SignAndSend, self.model.fromStructure, self);
            } , function (error, object) {
                var msg = error ? error : "";
                if (object && object.responseText) {
                    msg = object.responseText;
                }
                Common.showScreenErrorMsg(msg);

            });
        };
        CheckBookMarksAreExist(self.model.id, self.model.transferId, ["signature"], BookMarkscallBack);//"barcode" // remove barcode bookmark check for now
        //Common.unmask("body-mask");
        //$('#' + self.model.ComponentId + '_btnMyTransfer').click();

    }
    else if (signType == 3) { // sign and reply to structure
        signType = 0;
        sendToReceivingEntityMyTransfer(self.model.replyToEntity, self.model.delegationId, TransferType.Send, self.model.fromUser, self, templateId, true);


    //    sendToReceivingEntity(self.model.replyToEntity, self.model.delegationId, TransferType.ReplyToStructure, self.model.fromStructure, self, templateId, true);
    }
    else if (signType == 4) { //sign and reply to user
        signType = 0;
        sendToReceivingEntityMyTransfer(self.model.replyToEntity, self.model.delegationId, TransferType.ReplyToUser, self.model.fromUser, self, templateId, true);
    }
    else if (signType == 5) { // proceed 
        signType = 0;
        self.model.signDocument = true;
        self.model.signatureTemplate = templateId;
        proceed(self);
    }
    else if (signType == 6) { // preview before sign
        signType = 0;
        getDocumentIdInOriginalMail(self.model.id, function (attachmentId) {
            validateAndPreviewAttachment(attachmentId, templateId,self);
        });
        $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", false);
        $('#' + self.model.ComponentId + '_btnSignTemplate').button('reset');
        $('#btnGroupDropup').prop('disabled', false);

        $('#btnGroupDropup').attr('reset');
        $('#btnSubmitSign').attr("disabled", false);
        $('#btnSubmitSign').button('reset');


        $("#modalSignatureTemplates").modal('hide'); // Close the modal after submission
    }
    else if (signType == 7) { // sign and Export
        signType = 0;
        Common.mask(document.body, "body-mask");
        self.model.signDocument = false;
        self.model.signatureTemplate = templateId;

        let parms = {
            'templateId': templateId,
            'documentId': self.model.id,
            'transferId': self.model.transferId,
            'delegationId': self.model.delegationId
        }
        $('#btnSubmitSign').attr("disabled", true);
        $('#btnSubmitSign').button('reset');
        var BookMarkscallBack = () => {
            Common.ajaxPost('Transfer/SignDocument', parms, function (returnedData) {
                var data = returnedData.message;
                $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnComplete').button('reset');
                $('#' + self.model.ComponentId + '_btnReplyToStructure').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnReplyToStructure').button('reset');
                $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnReplyToUser').button('reset');
                $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnSignTemplate').button('reset');
                $('#btnSubmitSign').attr("disabled", false);
                $('#btnSubmitSign').button('reset');
                $('#btnGroupDropup').attr('disabled', false);
                $('#btnGroupDropup').button('reset');
                $("#modalSignatureTemplates").modal("hide");
                if (data == '') {
                    self.model.fromSignAndExport = true;
                    if (returnedData.signedTransferId) {
                        self.model.transferId = returnedData.signedTransferId;
                    }
                    //Common.unmask("body-mask");
                    //$('#' + self.model.ComponentId + '_btnTransferExport').click();
                    TransferExport(actionArray, self);
                    $('[id$=viewerFrame]').attr("src", $('[id$=viewerFrame]').attr("src"))
                }
                else if (data == 'NoSignatureConfigured') {
                    Common.alertMsg(Resources.NoSignatureConfigured);
                    Common.unmask("body-mask");
                }
                else if (data == 'NoAttachmentToSign') {
                    Common.alertMsg(Resources.NoAttachmentToSign);
                    Common.unmask("body-mask");
                }
                else if (data === "CheckoutFaild") {
                    Common.alertMsg(Resources.CheckoutFaild);
                    Common.unmask("body-mask");
                }
                else if (data === "SignFailed") {
                    Common.alertMsg(Resources.SignFailed);
                    Common.unmask("body-mask");
                }
                else if (data === "HasNoAccess") {
                    Common.alertMsg(Resources.HasNoAccess);
                    Common.unmask("body-mask");
                }
                else if (data === "CheckinFailed") {
                    Common.alertMsg(Resources.CheckinFailed);
                    Common.unmask("body-mask");
                }
                else if (data === "FileInUse") {
                    Common.alertMsg(Resources.FileInUse);
                    Common.unmask("body-mask");
                }
                else if (data === "Can't render barcode: not enough space") {
                    Common.alertMsg(data);
                    Common.unmask("body-mask");
                }
                else {
                    Common.showScreenErrorMsg();
                    Common.unmask("body-mask");
                }
                Common.unmask("body-mask");

            }, function (error, object) {
                var msg = error ? error : "";
                if (object && object.responseText) {
                    msg = object.responseText;
                }
                Common.showScreenErrorMsg(msg);

            });
        };
        CheckBookMarksAreExist(self.model.id, self.model.transferId, ["signature"], BookMarkscallBack);//"barcode" // remove barcode bookmark check for now

    }
}

function CheckBookMarksAreExist(documentId, transferId, bookmarks, BookMarkscallBack) {
    Common.ajaxPost('Transfer/CheckBookMarksAreExist', { documentId: documentId, transferId: transferId, BookMarks: bookmarks }
        , function (res) {
            if (res.result) {
                if (typeof (BookMarkscallBack) === "function") {
                    BookMarkscallBack();
                }
            }
            else {
                var bookmarksText = "\n";
                $.each(res.bookmarks, (i, value) => {
                    if (res.bookmarks.length == i + 1)
                        bookmarksText += value;
                    else
                        bookmarksText += value + "\n";
                });
                Common.unmask("body-mask");
                $('#modalSignatureTemplates').modal("hide");
                $('#btnSubmitSign').attr("disabled", false);
                $('#btnSubmitSign').button('reset');
                Common.alertMsg(Resources.BookmarksDosenotExist + ":" + bookmarksText);
            }
        }
        , (err) => {
            Common.unmask("body-mask");
            $('#modalSignatureTemplates').modal("hide");
            $('#btnSubmitSign').attr("disabled", false);
            $('#btnSubmitSign').button('reset');
            Common.showScreenErrorMsg();
        }
    );

}

function CheckUserCanSign(documentId, transferId, delegationId, bookmarks, successCallBack) {
    Common.ajaxPost('Transfer/CheckUserCanSign', { documentId, transferId, delegationId, bookmarks }//"barcode" // remove barcode bookmark check for now
        , function (res) {
            if (res.result) {
                if (typeof (successCallBack) === "function") {
                    successCallBack();
                }
            }
            else {
                var bookmarksText = "\n";
                $.each(res.bookmarks, (i, value) => {
                    if (res.bookmarks.length == i + 1)
                        bookmarksText += value;
                    else
                        bookmarksText += value + "\n";
                });
                Common.unmask("body-mask");
                $('#modalSignatureTemplates').modal("hide");
                $('#btnSubmitSign').attr("disabled", false);
                $('#btnSubmitSign').button('reset');
                Common.alertMsg(Resources[res.message] + ": " + res.details);
            }
        }
        , (err) => {
            Common.unmask("body-mask");
            $('#modalSignatureTemplates').modal("hide");
            $('#btnSubmitSign').attr("disabled", false);
            $('#btnSubmitSign').button('reset');
            Common.showScreenErrorMsg();
        }
    );
}
async function CheckUserCertificate() {
    try {
        const responseArray = await $.ajax({
            method: "GET",
            url: window.DSURL + "/api/userCertificate",
            headers: {
                "Authorization": "Bearer " + IdentityAccessToken
            },
            dataType: 'json',
        });

        if (!Array.isArray(responseArray) || responseArray.length === 0) {
            return Resources.NoCertificate; 
        }
        const certificate = responseArray[0];

        if (!certificate.activated) {
            return Resources.CertificateNotActivated;
        }
        if (new Date(certificate.endDate) < Date.now()) {
            return Resources.CertificateExpired;
        }
        return null;

    } catch (error) {
        return Resources.FailedToGetCertificate;
    }

}
function disablebuttons(disableBtns, self) {
    var $form = $(self.refs['formDocumentAttributePost']);
    $form.parsley().reset();
    var isValid = $form.parsley().isValid();
    if (disableBtns && isValid && formioFrom) {
        $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
        $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
        $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
        $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
        $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
        $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
        $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
        $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
        $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
        $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
        $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
        $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
    }
}
function saveDocument(saveOptions, documentViewModel, self, disableBtns)
{
    Common.ajaxPost(saveOptions.url, documentViewModel, function (response) {
        gLocked = false;
        if (response === "OriginalFileInUse") {
            Common.alertMsg(Resources.OriginalFileInUse);
            $(self.refs['btnAttributeSave']).attr("disabled", false);
            $(self.refs['btnAttributeSave']).button('reset');
            disablebuttons(disableBtns, self)
        }
        else if (response === "ExternalReferencedNumberInUse") {
            Common.alertMsg(Resources.ExternalReferencedNumberInUse);
            $(self.refs['btnAttributeSave']).attr("disabled", false);
            $(self.refs['btnAttributeSave']).button('reset');
            disablebuttons(disableBtns, self)
        }
        else if (response === "MustBeAllowEditAttributeAndNotSigned") {
            Common.alertMsg(Resources.MustBeAllowEditAttributeAndNotSigned);
            $(self.refs['btnAttributeSave']).attr("disabled", false);
            $(self.refs['btnAttributeSave']).button('reset');
            disablebuttons(disableBtns, self)
        }
        else if (response === "InvalidSubject") {
            Common.alertMsg(Resources.InvalidSubject);
            $(self.refs['btnAttributeSave']).attr("disabled", false);
            $(self.refs['btnAttributeSave']).button('reset');
            disablebuttons(disableBtns, self)
        }
        else if (response === "FollowUpTeamEmptyAndNotPrivate") {
            Common.alertMsg(Resources.FollowUpTeamEmptyAndNotPrivate);
            $(self.refs['btnAttributeSave']).attr("disabled", false);
            $(self.refs['btnAttributeSave']).button('reset');
            disablebuttons(disableBtns, self)
        }
        else {
            EventReceiver.OnDocumentSaved(documentViewModel).then(function () {
                Common.showScreenSuccessMsg();
                //if ($('[ref="activityLogGridContainer"]').length > 0) {
                //    var activitylogTable = $('[ref="activityLogGridContainer"]').find('#grdItems');
                //    if (activitylogTable.length > 0) {
                //        activitylogTable.DataTable().ajax.reload();
                //    }
                //}
                if (typeof saveOptions.callback === 'function') {
                    saveOptions.callback(response);
                }
                var viewerFrame = $("#" + self.model.parentComponentId + "_viewerFrame");
                if (viewerFrame.length > 0) {
                    if (window.viewOnlyMode === 'full') {
                        Common.ajaxGet('/Attachment/GetCurrentVersionNumber?attachmentId=' + self.model.attachmentId, null,
                            function (attachmentVersion) {
                                var originalUrl = $(viewerFrame).attr("src");
                                var updatedUrl = '';
                                if (originalUrl.includes("version=")) {
                                    updatedUrl = originalUrl.replace(/version=\d+(\.\d+)?/, `version=${attachmentVersion}`);
                                }
                                if (updatedUrl == '') {
                                    updatedUrl = originalUrl;
                                } else {
                                    $(viewerFrame).attr("src", updatedUrl);
                                }
                            }, function (err) {
                                Common.showScreenErrorMsg();
                            });
                    }
                    else {
                        $(viewerFrame).attr("src", $(viewerFrame).attr("src"));
                    }
                }

                $(self.refs['btnAttributeSave']).attr("disabled", false);
                $(self.refs['btnAttributeSave']).button('reset');
                disablebuttons(disableBtns, self)

                if ($("#grdClosedItems").val() != undefined) {
                    if (self.model.fromVip) {
                        $(".withBorders-o").addClass("waitingBackground");
                        $("#closedListContainer").empty();
                        $($("input[data-id='" + self.model.transferId + "']").parents("li")[0]).fadeOut().remove();
                    } else {
                        GridCommon.Refresh("grdClosedItems");
                    }
                    TreeNode.refreshTreeNodeCounts(TreeNode.Closed);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                    TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox); TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
                    TreeNode.refreshTreeNodeCounts(TreeNode.MyRequests);
                    window.location.href = '/';
                }
                else if ($("#closedListContainer").val() != undefined) {
                    var li = $($("input[data-id='" + self.model.id + "']").parents("li")[0]);
                    li.fadeOut().remove();
                    $(".withBorders-o").addClass("waitingBackground");
                    $("#closedListContainer").empty();
                    TreeNode.refreshTreeNodeCounts(TreeNode.Closed);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                    TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox); TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
                    TreeNode.refreshTreeNodeCounts(TreeNode.MyRequests);
                    window.location.href = '/';
                }

            });
            if (self.model.fromVip) {
                
                updateListItem(documentViewModel);
            }
        }
    }, function (data) {
        $(self.refs['btnAttributeSave']).attr("disabled", false);
        $(self.refs['btnAttributeSave']).button('reset');
        disablebuttons(disableBtns, self)

        Common.showScreenErrorMsg();
        gLocked = false;
    });

}
function deleteOption(element) {

    $("#" + element.id).remove();

}
function sendTransfer(data, privacies, delegationId, self, successCallback, ParentTransferId) {
    var transferToStructures = [], transferToStructureIds = [];
    var ccedTransfer = 0;
    //////////////////////////////////////////////
    var purposes = new Helper().getPurpose();
    ///////////////////////////////////////////
    var hasPrivacyLevel = true;
    var htmlPrivacy = Resources.NoUserWithSelectedPrivacy;
    var structureExist = data.some(a => a.toStructureId !== null && a.toUserId === null);
    for (var i = 0; i < data.length; i++) {
        data[i].FromStructureId = $(self.refs['cmbUserStructures']).val();
        data[i].ParentTransferId = self.model.transferId ? self.model.transferId : null;
        data[i].IsStructure = data[i].toUserId === null;
        data[i].DocumentId = self.model.id;
        data[i].DocumentPrivacyId = $(self.refs['cmbCustomAttributePrivacy']).val();
        var currentPurpose = $.grep(purposes, function (e) {
            return e.id.toString() === data[i].purposeId;
        });
        if (currentPurpose[0].cCed === true) {
            ccedTransfer++;
        }
        if (data[i].toUserId === null) {
            transferToStructures.push({ dataId: data[i].toStructureId, text: data[i].name });
            transferToStructureIds.push(data[i].toStructureId);
        } else {
            data[i].PrivacyId = 0;
            var userObj = new IdentityService().getFullUser(data[i].toUserId);
            if (userObj !== null) {
                var attributePrivacy = $.grep(userObj.attributes, function (e) {
                    return e.text === window.UserPrivacy ? e.value : 0;
                });
                if (attributePrivacy.length > 0) {
                    data[i].PrivacyId = attributePrivacy[0].value === "" ? 0 : attributePrivacy[0].value;
                }
            }
            var currentPrivacy = $.grep(privacies, function (e) {
                return e.id.toString() === data[i].PrivacyId.toString();
            });
            if (currentPrivacy !== null && currentPrivacy.length > 0) {
                if (data[i].DocumentPrivacyId > currentPrivacy[0].level) {
                    hasPrivacyLevel = false;
                    htmlPrivacy += ' \n ○ ' + data[i].name;
                }
            } else {
                hasPrivacyLevel = false;
                htmlPrivacy += ' \n ○ ' + data[i].name;
            }
        }
    }
    var userStructureIds = $("#hdStructureIds").val();
    if (successCallback == null) {
        successCallback = function () {
            Common.tryCloseDocumentModal(self.model.actionComponentId);
        };
    }

    var message = SendTransferModal.checkAbilityToSend(transferToStructureIds.length === 0 ? userStructureIds.split(window.Seperator) : transferToStructureIds, transferToStructures, $(self.refs['cmbCustomAttributePrivacy']).val(), privacies, null, false);
    if (message === "error") {
        $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
        $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
        $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
        $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
        $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
        $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
        $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
        $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
        $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
        $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
        $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
        $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
        return;
    }
    if (!hasPrivacyLevel) {
        message += (message !== "" ? " \n " : "") + htmlPrivacy;
    }
    if (message !== "") {
        if (!structureExist) {
            setTimeout(function () {
                Common.showConfirmMsg(message + " \n " + Resources.ContinueConfirmation, function () {
                    SendTransferModal.transfer(transferComponent, data, false, false, false, delegationId);
                }, function () {
                    $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                    $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
                    $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
                    $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                    $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
                    $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
                });
            }, 300);
        }
        else {

            if (window.SendWithoutStructureReceiverOrPrivacyLevel === "True") {
                setTimeout(function () {
                    Common.showConfirmMsg(message + " \n " + Resources.ContinueConfirmation, function () {
                        SendTransferModal.transfer(transferComponent, data, false, false, false, delegationId);
                    }, function () {
                        $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                        $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                        $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                        $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
                        $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                        $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
                        $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                        $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                        $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                        $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
                        $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                        $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
                    });
                }, 300);
            } else {
                $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
                $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
                $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
                $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
                setTimeout(function () {
                    Common.alertMsg(message);
                }, 300);
            }
        }
    } else {
        if (ccedTransfer === data.length) {
            setTimeout(function () {
                Common.alertMsg(Resources.SelectAtLeastOneNonCCTransfer);
            }, 300);
        } else {
            SendTransferModal.transfer(transferComponent, data, false, false, false, delegationId,  successCallback);
         }
    }
}

function getDocumentIdInOriginalMail(documentId, callback) {
    Common.mask(document.body, "body-mask");
    Common.ajaxGet("/Attachment/List", { "documentId": documentId }, function (data) {
        const childIds = getChildIdsFromFolder("folder_originalMail", data);
        if (childIds && childIds.length > 0) {
            callback(childIds[0].split("_")[1]);
        } else {
            Common.unmask("body-mask");
            callback(null);

        }
        Common.unmask("body-mask");
    },
        function (error) {
            console.error("Error fetching data:", error);
            Common.unmask("body-mask");
            callback(null);

        }
    );
}

function openPdfInModal(url) {
    // Set the iframe source to the PDF URL
    $('#pdfIframe').attr('src', url);
    // Show the modal
    $('#pdfModal').modal('show');

}
function getChildIdsFromFolder(folderId, data) {
    for (const item of data) {
        if (item.id === folderId && item.children) {
            return item.children.map(child => child.id);
            
        }
        if (item.children) {
            const result = getChildIdsFromFolder(folderId, item.children);
            if (result) {
                return result;
            }
        }
    }
    return null;
}
function validateAndPreviewAttachment(attachmentId, selectedSignature, self) {
    Common.unmask("body-mask");
    Common.mask(document.body, "body-mask");
    Common.ajaxGet(
        "/Attachment/PreviewAttachmentValidation",
        { "id": attachmentId },
        function (response) {
            if (response === "OriginalFileInUse") {
                setTimeout(() => Common.alertMsg(Resources.OriginalFileInUse), 400);
                Common.unmask("body-mask");

            } else if (response === "AttachmentNotComplete") {
                setTimeout(() => Common.alertMsg(Resources.AttachmentNotComplete), 400);
                Common.unmask("body-mask");
            }
            else if (self.model.referenceNumber===null)
            {
                let url = `/Attachment/PreviewBeforeSign?attachmentId=${attachmentId}&documentId=${self.model.id}&delegationId=${self.model.delegationId}&signature=${selectedSignature}&GenerateReference=true`;
                openPdfInModal(url);
                Common.unmask("body-mask");

            }
            else
            {
                if (attachmentId == null && attachmentId == '0') return;
                let url = `/Attachment/PreviewBeforeSign?attachmentId=${attachmentId}&documentId=${self.model.id}&delegationId=${self.model.delegationId}&signature=${selectedSignature}&GenerateReference=false`;
                openPdfInModal(url);
                Common.unmask("body-mask");
            }

        }
    );
}
function checkAddSelectValue(select2Id, value, text, self) {
    var dataArray = $(self.refs[select2Id]).find("option");
    var exists = false;
    for (var i = 0; i < dataArray.length; i++) {
        if (dataArray[i].value === value.toString()) {
            exists = true;
            break;
        }
    }
    if (exists) {
        $(self.refs[select2Id]).val(value).trigger("change");
    } else {
        var option = new Option(text, value, false, false);
        $(self.refs[select2Id]).append(option);
        $(self.refs[select2Id]).val(value).trigger("change");
    }
}

function TransferExport(actionArray,self) {
    try {
        $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", true);
        $('#' + self.model.ComponentId + '_btnCompleteAndArchive').attr("disabled", true);
        $('#' + self.model.ComponentId + '_btnSend').attr("disabled", true);
        //var actionArray = this.model.actionName.split("_");
        sendToReceivingEntityMyTransfer(self.model.replyToEntity, self.model.delegationId, self.model.isInternalBroadcast ? TransferType.BroadcastSend : TransferType.Send, self.model.fromUser, self, null, false, actionArray);

    } catch (e) {
        $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", false);
        $('#' + self.model.ComponentId + '_btnComplete').button('reset');
        $('#' + self.model.ComponentId + '_btnCompleteAndArchive').attr("disabled", false);
        $('#' + self.model.ComponentId + '_btnCompleteAndArchive').button('reset');
        $('#' + self.model.ComponentId + '_btnSend').attr("disabled", false);
        $('#' + self.model.ComponentId + '_btnSend').button('reset');
    }
}
function createStructuresSelect2(basicAttributes, model, self) {
    var url = 'User/GetUsersStructuresFromCTS';
    var sendingEntityUrl = url, receivingEntityUrl = url;
    var sendingStructuretype = 1, receivingStructuretype = 1;
    var sendingUseCurrentStructure = false, receivingUseCurrentStructure = false;
    var currentStructure;
    var structureSenderDataForSendingEntity = [];
    var structureSenderDataForReceivingEntity = [];
    var structureSenderDataForCarbonCopy = [];
    let structureType;
    if (basicAttributes.length > 0) {
        var sendingRulesIds = 0;
        if (window.EnableSendingRules === "True") {
            sendingRulesIds = new CoreComponents.Lookup.SendingRules().get($(self.refs['cmbUserStructures']).val());
        }
        var sendingEntityObj = $.grep(basicAttributes, function (e) {
            return e.Name === "SendingEntity";
        });
        if (sendingEntityObj[0].Type === "external") {
            //sendingEntityUrl = window.IdentityUrl + '/api/SearchExternalStructuresWithSearchAttributes';
            sendingStructuretype = 2;
            if (document.getElementById("hdEditDesignatedPerson").value == 'True')
                $(self.refs["openSenderDesignatedPerson"]).removeClass("hidden");
        }
        else if (sendingEntityObj[0].Type === "internal" /*&& window.IsStructureSender === "False"*/) {
            //structureSenderDataForSendingEntity = new IdentityService().getUserStructures(window.language);
            sendingStructuretype = 1;
            sendingUseCurrentStructure = sendingEntityObj[0].UseCurrentStructure;
        }
        else if (sendingEntityObj[0].Type === "both" /*&& window.IsStructureSender === "False"*/) {
            //structureSenderDataForSendingEntity = new IdentityService().getUserStructures(window.language);
            sendingStructuretype = 3;
            sendingUseCurrentStructure = sendingEntityObj[0].UseCurrentStructure;
        }
        //else if (window.EnableSendingRules === "True") {
        //    sendingEntityUrl = window.IdentityUrl + '/api/ListStructuresByIds';
        //    sendingUseCurrentStructure = sendingEntityObj[0].UseCurrentStructure;
        //}
        else {
            sendingEntityUrl = window.IdentityUrl + '/api/SearchStructuresWithSearchAttributes';
            sendingUseCurrentStructure = sendingEntityObj[0].UseCurrentStructure;
        }
        var receivingEntityObj = $.grep(basicAttributes, function (e) {
            return e.Name === "ReceivingEntity";
        });
        if (receivingEntityObj[0].Type === "external") {
            $('#' + self.model.ComponentId + '_btnAttributeSend').hide();
            if (document.getElementById("hdEditDesignatedPerson").value == 'True')
                $(self.refs["openRecieverDesignatedPerson"]).removeClass("hidden");
            //receivingEntityUrl = window.IdentityUrl + '/api/SearchExternalStructuresWithSearchAttributes';
            receivingStructuretype = 2;
        }
        else if (receivingEntityObj[0].Type === "internal" /*&& window.IsStructureSender === "False"*/) {
            //structureSenderDataForReceivingEntity = new IdentityService().getUserStructures(window.language);
            receivingStructuretype = 1;
            receivingUseCurrentStructure = receivingEntityObj[0].UseCurrentStructure;
        }
        else if (receivingEntityObj[0].Type === "both" /*&& window.IsStructureSender === "False"*/) {
            //structureSenderDataForReceivingEntity = new IdentityService().getUserStructures(window.language);
            receivingStructuretype = 3;
            receivingUseCurrentStructure = receivingEntityObj[0].UseCurrentStructure;
        }
        //else if (window.EnableSendingRules === "True") {
        //    receivingEntityUrl = window.IdentityUrl + '/api/ListStructuresByIds';
        //    receivingUseCurrentStructure = receivingEntityObj[0].UseCurrentStructure;
        //}
        else {
            receivingEntityUrl = window.IdentityUrl + '/api/SearchStructuresWithSearchAttributes';
            receivingUseCurrentStructure = receivingEntityObj[0].UseCurrentStructure;
        }

        if (receivingStructuretype != 3)
            $(".receiver-both-options").hide();
        var carbonCopyObj = $.grep(basicAttributes, function (e) {
            return e.Name === "CarbonCopy";
        });
        if (carbonCopyObj !== null && carbonCopyObj.length > 0) {
            structureType = 1
            if (carbonCopyObj[0].Type === "external") {
                url = '/User/GetUsersStructuresFromCTS';
                structureType = 2;
            }
            else if (carbonCopyObj[0].Type === "internal" /*&& window.EnableSendingRules === "True"*/) {
                //if (window.IsStructureSender === "False") {
                //    structureSenderDataForCarbonCopy = new IdentityService().getUserStructures(window.language);
                //}
                //else {
                //    url = window.IdentityUrl + '/api/ListStructuresByIds';
                //}
            }
            //else if (carbonCopyObj[0].Type === "internal" && window.EnableSendingRules === "False" && window.IsStructureSender === "True") {
            //    url = '/User/GetUsersStructuresFromCTS';
            //}
            else if (carbonCopyObj[0].Type === "both" /*&& window.EnableSendingRules === "False" && window.IsStructureSender === "False"*/) {
                /*structureSenderDataForCarbonCopy = new IdentityService().getUserStructures(window.language);*/
                structureType = 3;
            }
        }
    }
    if (structureSenderDataForSendingEntity.length > 0) {
        $(self.refs['cmbCustomAttributeSender']).select2({
            minimumInputLength: 0,
            allowClear: false,
            placeholder: "",
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            dropdownParent: $(self.refs['customAttributeSenderContainer']),
            width: "100%",
            data: structureDataForSelect2(structureSenderDataForSendingEntity),
            sorter: data => data.sort((a, b) => a.text.localeCompare(b.text))
        }).on('change', function () {
            $(self.refs["cmbCustomAttributeSenderPerson"]).val('').trigger('change');
            $(this).trigger('input');
        });
    }
    else {
        var headers = {};
        headers.Authorization = 'Bearer ' + window.IdentityAccessToken;
        $(self.refs['cmbCustomAttributeSender']).select2({
            minimumInputLength: 0,
            allowClear: false,
            placeholder: "",
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            dropdownParent: $(self.refs['customAttributeSenderContainer']),
            width: "100%",
            ajax: {
                delay: 250,
                url: sendingEntityUrl,
                type: "Post",
                dataType: 'json',
                headers: typeof headers !== "undefined" ? headers : "",
                data: function (term) {
                    //if (sendingEntityObj != undefined && sendingEntityObj[0].Type === "internal" && window.EnableSendingRules === "True") {
                    //    sendingRulesIds = new CoreComponents.Lookup.SendingRules().get($(self.refs['cmbUserStructures']).val());
                    //    return { "text": term.term ? term.term : "", language: window.language, "ids": sendingRulesIds.length > 0 ? sendingRulesIds : 0, "attributes": [window.StructureNameAr, window.StructureNameFr] };
                    //}
                    //else {
                    return { "searchText": term.term ? term.term : "", structureType: sendingStructuretype, delegationId: self.model.delegationId, categoryId: model.categoryId, fromSendingEntity: true,fromSendingandReceiving: true };
                    //}
                },
                processResults: function (data) {
                    return {
                        results: structureDataForSelect2(data || [])
                    };
                }
            },
            sorter: data => data.sort((a, b) => a.text.localeCompare(b.text))
        }).on('change', function () {
            $(self.refs["cmbCustomAttributeSenderPerson"]).val('').trigger('change');
            $(this).trigger('input');
        });
    }
    if (structureSenderDataForReceivingEntity.length > 0) {
        $(self.refs['cmbCustomAttributeReceiver']).select2({
            minimumInputLength: 0,
            multiple: model.multipleReceivingEntity,
            allowClear: false,
            placeholder: "",
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            dropdownParent: $(self.refs['customAttributeReceiverContainer']),
            width: "100%",
            data: structureDataForSelect2(structureSenderDataForReceivingEntity),
            sorter: data => data.sort((a, b) => a.text.localeCompare(b.text))
        }).on('change', function () {
            $(self.refs["cmbCustomAttributeReceiverPerson"]).val('').trigger('change');
            $(this).trigger('input');
        });
    }
    else {
        var headers = {};
        headers.Authorization = 'Bearer ' + window.IdentityAccessToken;
        $(self.refs['cmbCustomAttributeReceiver']).select2({
            minimumInputLength: 0,
            multiple: model.multipleReceivingEntity,
            allowClear: false,
            placeholder: "",
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            dropdownParent: $(self.refs['customAttributeReceiverContainer']),
            width: "100%",
            ajax: {
                delay: 250,
                url: receivingEntityUrl,
                type: "Post",
                dataType: 'json',
                headers: typeof headers !== "undefined" ? headers : "",
                data: function (term) {
                    //if (receivingEntityObj != undefined && receivingEntityObj[0].Type === "internal" && window.EnableSendingRules === "True") {
                    //    sendingRulesIds = new CoreComponents.Lookup.SendingRules().get($(self.refs['cmbUserStructures']).val());
                    //    return { "text": term.term ? term.term : "", language: window.language, "ids": sendingRulesIds.length > 0 ? sendingRulesIds : 0, "attributes": [window.StructureNameAr, window.StructureNameFr] };
                    //}
                    //else {
                    return { "searchText": term.term ? term.term : "", structureType: $(`input[name="${self.model.ComponentId}_structureSenderSearchType"]:checked`).val(), delegationId: self.model.delegationId, categoryId: model.categoryId, fromSendingEntity: false, fromSendingandReceiving: true };
                    //}
                },
                processResults: function (data) {
                        return {
                        results: structureDataForSelect2(data || [])
                        };
                }
            },
            templateResult: function (data) {
                if (data.isExternal) {
                    // The container is a jQuery object for the <option> tag
                    const $result = $('<span>' + data.text + '</span>');
                    $result.attr('data-external', data.isExternal);
                    return $result;
                }
                return data.text;
            },
            templateSelection: function (item) {
                return item.text;
            },
            sorter: data => data.sort((a, b) => a.text.localeCompare(b.text))
        }).on('change', function () {
            $(self.refs["cmbCustomAttributeReceiverPerson"]).val('').trigger('change');
            $(this).trigger('input');
        });
    }
    if (structureSenderDataForCarbonCopy.length > 0) {
        $(self.refs['cmbCustomAttributeCarbonCopy']).select2({
            minimumInputLength: 0,
            multiple: true,
            allowClear: true,
            placeholder: Resources.CarbonCopy,
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            dropdownParent: $(self.refs['customAttributeCarbonCopyContainer']),
            width: "100%",
            data: structureDataForSelect2(structureSenderDataForCarbonCopy),
            sorter: data => data.sort((a, b) => a.text.localeCompare(b.text))
        });
    }
    else {
        var headers = {};
        headers.Authorization = 'Bearer ' + window.IdentityAccessToken;
        $(self.refs['cmbCustomAttributeCarbonCopy']).select2({
            minimumInputLength: 0,
            multiple: true,
            allowClear: true,
            placeholder: Resources.CarbonCopy,
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            dropdownParent: $(self.refs['customAttributeCarbonCopyContainer']),
            width: "100%",
            ajax: {
                delay: 250,
                url: url,
                type: "Post",
                dataType: 'json',
                headers: typeof headers !== "undefined" ? headers : "",
                data: function (term) {
                    //if (carbonCopyObj != undefined && carbonCopyObj[0].Type === "internal" && window.EnableSendingRules === "True") {
                    //    sendingRulesIds = new CoreComponents.Lookup.SendingRules().get($(self.refs['cmbUserStructures']).val());
                    //    return { "text": term.term ? term.term : "", language: window.language, "ids": sendingRulesIds.length > 0 ? sendingRulesIds : 0,  "attributes": [window.StructureNameAr, window.StructureNameFr] };
                    //}
                    /*else {*/
                    return { "searchStructure": term.term ? term.term : "", structureType: $(`input[name="${self.model.ComponentId}_carbonCopySearchType"]:checked`).val(), categoryId: model.categoryId, fromSendingEntity: false, fromSendingandReceiving: true };
                    //return { "searchText": term.term ? term.term : "", structureType: receivingStructuretype, delegationId: self.model.delegationId, categoryId: model.categoryId, fromSendingEntity: false, fromSendingandReceiving: true };

                    /*}*/
                },
                processResults: function (data) {
                    return {
                        results: structureDataForSelect2(data || [])
                    };
                }
            },
            sorter: data => data.sort((a, b) => a.text.localeCompare(b.text))
        });
    }
    if (model.sendingEntity !== null) {
        checkAddSelectValue("cmbCustomAttributeSender", model.sendingEntity.id.toString(), model.sendingEntity.text, self);
        if (sendingEntityObj[0].DisableField || model.readonly ) {
            $(self.refs["cmbCustomAttributeSender"]).prop('disabled', true);
            $(self.refs["openSenderAddressBook"]).css('cursor', 'default');
        } else {

            if (model.requestStatus == 0 && !model.isFollowUp) {
                $(self.refs["cmbCustomAttributeSender"]).prop('disabled', false);
                $(self.refs["openSenderAddressBook"]).css('cursor', 'pointer');
            }
            else {
                sendingEntityObj[0].DisableField = true;
                $(self.refs["cmbCustomAttributeSender"]).prop('disabled', true);
                $(self.refs["openSenderAddressBook"]).css('cursor', 'default');
            }
          
        }
    } else if (/*sendingUseCurrentStructure === "CurrentStructure" &&*/ model.userStructures.length > 0) {
        if (window.EnableSendingRules === "False" || (window.EnableSendingRules === "True" && sendingRulesIds !== 0 && sendingRulesIds.includes(parseInt(window.EnablePerStructure ? $('#hdLoggedInStructureId').val() : $("#hdStructureId").val())))) {
            currentStructure = $.grep(model.userStructures, function (e) {
                return (e.id === (window.EnablePerStructure ? parseInt($('#hdLoggedInStructureId').val()) : parseInt($("#hdStructureId").val())));
            });
            if (currentStructure.length > 0) {
                var result;
                switch (sendingUseCurrentStructure) {
                    case "CurrentStructure":
                        result = structureDataForSelect2(currentStructure);
                        break;
                    case "CurrentDepartment":
                        if (window.IsStructureSender === "True") {
                            Common.ajaxGet("CTS/Structure/GetDepartmentByStructureByid?Id=" + currentStructure[0].id, null, function (data) {
                                if (data) {
                                    result = structureDataForSelect2([data]);
                                }
                            }, function () { Common.showScreenErrorMsg(); }, null, null, false);
                        }
                        break;
                    default:
                }
                if (result) {
                    checkAddSelectValue("cmbCustomAttributeSender", result[0].id.toString(), result[0].text, self);
                    if (sendingEntityObj[0].DisableField || model.readonly) {
                        $(self.refs["cmbCustomAttributeSender"]).prop('disabled', true);
                        $(self.refs["openSenderAddressBook"]).css('cursor', 'default');
                    } else {
                        if (model.requestStatus == 0 && !model.isFollowUp) {
                            $(self.refs["cmbCustomAttributeSender"]).prop('disabled', false);
                            $(self.refs["openSenderAddressBook"]).css('cursor', 'pointer');
                        }
                        else {
                            sendingEntityObj[0].DisableField = true;
                            $(self.refs["cmbCustomAttributeSender"]).prop('disabled', true);
                            $(self.refs["openSenderAddressBook"]).css('cursor', 'default');
                        }
                    }
                }
            }
        } else {
            $(self.refs['cmbCustomAttributeSender']).val('').trigger('change');
        }
    } else {
        $(self.refs['cmbCustomAttributeSender']).val('').trigger('change');
    }
    if (model.receivingEntities !== null && model.receivingEntities.length > 0) {
        var receivingArray = model.receivingEntities;
        for (var i = 0; i < receivingArray.length; i++) {
            if (receivingArray[i].isEntityGroup) {
                receivingArray[i].id = 'EGroup-' + receivingArray[i].id;
            }
        }
        //if (window.IsStructureSender === "False") {
        //    var receivingIds = receivingArray.map(a => a.id);
        //    $(self.refs['cmbCustomAttributeReceiver']).val(receivingIds).trigger("change");
        //} else {
            for (var i = 0; i < receivingArray.length; i++) {
                if ($(self.refs['cmbCustomAttributeReceiver']).find("option[value = " + receivingArray[i].id + "]").length <= 0) {
                    let text = receivingArray[i].text;
                    if(receivingArray[i].parentName)
                    {
                        text = receivingArray[i].parentName + window.Seperator + text;
                    }
                    var option = new Option(text, receivingArray[i].id, true, true);
                    $(option).data('external', receivingArray[i].isExternal);
                    $(self.refs['cmbCustomAttributeReceiver']).append(option).trigger("change");
                } else {
                    var selectedIds = $(self.refs['cmbCustomAttributeReceiver']).val() != null ? $(self.refs['cmbCustomAttributeReceiver']).val() : [];
                    if (selectedIds.indexOf(receivingArray[i].id) < 0) {
                        selectedIds.push(receivingArray[i].id);
                    }
                    $(self.refs['cmbCustomAttributeReceiver']).val(selectedIds).trigger("change");
                }
            }
        //}
        if (receivingEntityObj[0].DisableField || model.readonly) {
            $(self.refs["cmbCustomAttributeReceiver"]).prop('disabled', true);
            $(self.refs["openReceiverAddressBook"]).css('cursor', 'default');
        } else {
            if (model.requestStatus == 0 && !model.isFollowUp) {
                $(self.refs["cmbCustomAttributeReceiver"]).prop('disabled', false);
                $(self.refs["openReceiverAddressBook"]).css('cursor', 'pointer');
            }
            else {
                receivingEntityObj[0].DisableField = true;
                $(self.refs["cmbCustomAttributeReceiver"]).prop('disabled', true);
                $(self.refs["openReceiverAddressBook"]).css('cursor', 'default');
            }
          
        }
    } else if (/*receivingUseCurrentStructure === "CurrentStructure" &&*/ model.userStructures.length > 0) {
        if (window.EnableSendingRules === "False" || (window.EnableSendingRules === "True" && sendingRulesIds !== 0 && sendingRulesIds.includes(parseInt(window.EnablePerStructure ? $('#hdLoggedInStructureId').val() : $("#hdStructureId").val())))) {
            currentStructure = $.grep(model.userStructures, function (e) {
                return (e.id === (window.EnablePerStructure ? parseInt($('#hdLoggedInStructureId').val()) : parseInt($("#hdStructureId").val())));
            });
            if (currentStructure.length > 0) {
                var receivingresult;
                switch (receivingUseCurrentStructure) {
                    case "CurrentStructure":
                        receivingresult = structureDataForSelect2(currentStructure);
                        break;
                    case "CurrentDepartment":
                        if (window.IsStructureSender === "True") {
                            Common.ajaxGet("CTS/Structure/GetDepartmentByStructureByid?Id=" + currentStructure[0].id, null, function (data) {
                                if (data) {
                                    receivingresult = structureDataForSelect2([data]);
                                }
                            }, function () { Common.showScreenErrorMsg(); }, null, null, false);
                        }
                        break;
                    default:
                }
                if (receivingresult) {
                    checkAddSelectValue("cmbCustomAttributeReceiver", receivingresult[0].id.toString(), receivingresult[0].text, self);
                    if (receivingEntityObj[0].DisableField || model.isFollowUp || model.readonly) {
                        $(self.refs["cmbCustomAttributeReceiver"]).prop('disabled', true);
                        $(self.refs["openReceiverAddressBook"]).css('cursor', 'default');
                    } else {
                        $(self.refs["cmbCustomAttributeReceiver"]).prop('disabled', false);
                        $(self.refs["openReceiverAddressBook"]).css('cursor', 'pointer');
                    }
                }
            }
        } else {
            $(self.refs['cmbCustomAttributeReceiver']).val('').trigger('change');
        }
    } else {
        $(self.refs['cmbCustomAttributeReceiver']).val('').trigger('change');
    }
    if (model.carbonCopies !== null) {
        var carbonCopiesArray = model.carbonCopies;
        if (window.IsStructureSender === "False") {
            var carbonCopiesIds = carbonCopiesArray.map(a => a.id);
            $(self.refs['cmbCustomAttributeCarbonCopy']).val(carbonCopiesIds).trigger("change");
        } else {
            for (var i = 0; i < carbonCopiesArray.length; i++) {
                if ($(self.refs['cmbCustomAttributeCarbonCopy']).find("option[value = " + carbonCopiesArray[i].id + "]").length <= 0) {
                    let text = carbonCopiesArray[i].text;
                    if(carbonCopiesArray[i].parentName)
                    {
                        text = carbonCopiesArray[i].parentName + window.Seperator + text;
                    }
                    var option = new Option(text, carbonCopiesArray[i].id, true, true);
                    $(self.refs['cmbCustomAttributeCarbonCopy']).append(option).trigger("change");
                } else {
                    var selectedIds = $(self.refs['cmbCustomAttributeCarbonCopy']).val() != null ? $(self.refs['cmbCustomAttributeCarbonCopy']).val() : [];
                    if (selectedIds.indexOf(carbonCopiesArray[i].id) < 0) {
                        selectedIds.push(carbonCopiesArray[i].id);
                    }
                    $(self.refs['cmbCustomAttributeCarbonCopy']).val(selectedIds).trigger("change");
                }
            }
        }
    } else {
        $(self.refs['cmbCustomAttributeCarbonCopy']).val('').trigger('change');
    }
}
function openDocumentCompleteModal(self) {
    
    Common.ajaxGet('/Document/CheckDocumentAttachmentLocked/', { "documentId": self.model.id }, function (retval) {
        if (retval) {
            Common.alertMsg(Resources.FileInUse);
        } else {

            let modalWrapper = $(".modal-window");
            let modelIndex = new DocumentCompleteIndex.DocumentCompleteIndex();
            modelIndex.documentId = self.model.id;
            modelIndex.fromVip = self.model.fromVip;
            modelIndex.toStructureId = $(self.refs['cmbCustomAttributeReceiver']).val() != null ? $(self.refs['cmbCustomAttributeReceiver']).val() : [];
            if ($(self.refs['cmbUserStructures']).val()) {
                sessionStorage.removeItem($("#hdUserId").val() + "SelectedUserStrcuture");
                sessionStorage.setItem($("#hdUserId").val() + "SelectedUserStrcuture", $(self.refs['cmbUserStructures']).val());
            }
            let documentCompleteIndexView = new DocumentCompleteIndex.DocumentCompleteIndexView(modalWrapper, modelIndex, function () { tryCloseModal(self.model.actionComponentId) });

            documentCompleteIndexView.render({
                url: '/Document/Save',
                params: getFormData(self.model.categoryId, false, self.model.multipleReceivingEntity, self)

            });
            $("#modalDocumentComplete").off("shown.bs.modal");
            $("#modalDocumentComplete").on('shown.bs.modal');

        }
    })
}
function structureDataForSelect2(data) {
    var retVal = [];
    for (var i = 0; i < data.length; i++) {
        var structureName = data[i].name;
        if (data[i].attributes != null && data[i].attributes.length > 0) {
            var attributeLang = $.grep(data[i].attributes, function (e) {
                return e.text === window.StructureNameAr && window.language === "ar" ? e.value : (e.text === window.StructureNameFr && window.language === "fr" ? e.value : "");
            });
            if (attributeLang.length > 0) {
                structureName = attributeLang[0].value === "" ? structureName : attributeLang[0].value;
            }
        }
        else {
            structureName = window.language === "ar" && data[i].nameAr != null && data[i].nameAr != "" ? data[i].nameAr : structureName;
        }
        retVal.push({
            id: data[i].id.toString(),
            text: structureName,
            isExternal: data[i].isExternal
        });
    }
    return retVal;
}
function getFormData(categoryId, register, multipleReceivingEntity, self) {
  
    var documentViewModel = {};
    var receivers = [];
    var receiverPersons = [];
    if ($(self.refs['cmbCustomAttributeReceiver']).val() !== null && $(self.refs['cmbCustomAttributeReceiver']).val() !== undefined) {
        if (multipleReceivingEntity) {
            var recEntity = $(self.refs['cmbCustomAttributeReceiver']).val();
            for (var i = 0; i < recEntity.length; i++) {
                if (recEntity[i].split('-').length == 2) {
                    receivers.push({ Id: recEntity[i].split('-')[1], IsEntityGroup: true });
                } else {
                    receivers.push({ Id: recEntity[i], IsEntityGroup: false });
                }
            }
            //receivers = $(self.refs['cmbCustomAttributeReceiver']).val();
        } else {
            if ($(self.refs['cmbCustomAttributeReceiver']).val().split('-').length == 2) {
                receivers.push({ Id: $(self.refs['cmbCustomAttributeReceiver']).val().split('-')[1], IsEntityGroup: true });
            } else {
                receivers.push({ Id: $(self.refs['cmbCustomAttributeReceiver']).val(), IsEntityGroup: false });
            }
        }
    }
    if ($(self.refs['cmbCustomAttributeReceiverPerson']).val() !== null && $(self.refs['cmbCustomAttributeReceiverPerson']).val() !== undefined) {
        var recPersonJson = "";
        var recPerson = $(self.refs['cmbCustomAttributeReceiverPerson']).val();
        var receiverPersons = [];

        for (var i = 0; i < recPerson.length; i++) {
            receiverPersons.push(recPerson[i]);
        }
        var joinedIds = receiverPersons.join('-');
        recPersonJson = `{"Id":"${joinedIds}"}`;
    }
    var isExternalSender = false;
    var isExternalReceiver = false;
    var sendingEntityObj = $.grep(self.model.basicAttributes, function (e) {
        return e.Name === "SendingEntity";
    });
    if (sendingEntityObj[0].Type === "external") {
        isExternalSender = true;
    }
    var receivingEntityObj = $.grep(self.model.basicAttributes, function (e) {
        return e.Name === "ReceivingEntity";
    });
    if (receivingEntityObj[0].Type === "external") {
        isExternalReceiver = true;
    }
    if (categoryId == window.MeetingAgendaId
        || categoryId == window.MeetingMinutesId
        || categoryId == window.MeetingResolutionId
        || categoryId == window.SepResolutionId) {
        var documentReceivers = [];

        var selectedOptions = $('[id*=receivers]').parent().children("div").first().children();
        selectedOptions.each(function () {
            documentReceivers.push($(this).attr('data-value'))
        });
        gFormData.receivers = documentReceivers;
    }
    documentViewModel.id = self.model.id;
    documentViewModel.followUpId = self.model.followUpId;
    documentViewModel.categoryId = categoryId;
    documentViewModel.subject = $(self.refs['txtCustomAttributeSubject']).val();
    documentViewModel.formData = JSON.stringify(gFormData);
    documentViewModel.receivers = receivers;
    documentViewModel.sendingEntityId = $(self.refs['cmbCustomAttributeSender']).val();
    documentViewModel.dueDate = $(self.refs['customAttributeDueDate']).val();
    documentViewModel.documentDate = $(self.refs['customAttributeDocumentDate']).val();
    documentViewModel.priorityId = $(self.refs['cmbCustomAttributePriority']).val();
    documentViewModel.sender = $(self.refs['cmbCustomAttributeSenderPerson']).val();
    documentViewModel.receiver = recPersonJson;
    documentViewModel.isExternalReceiver = isExternalReceiver;
    documentViewModel.isExternalSender = isExternalSender;
    documentViewModel.privacyId = $(self.refs['cmbCustomAttributePrivacy']).val();
    documentViewModel.carbonCopy = $(self.refs['cmbCustomAttributeCarbonCopy']).val() ? $(self.refs['cmbCustomAttributeCarbonCopy']).val() : [];
    documentViewModel.importanceId = $(self.refs['cmbCustomAttributeImportance']).val();
    documentViewModel.classificationId = $(self.refs['cmbCustomAttributeClassification']).val();
    documentViewModel.documentTypeId = $(self.refs['cmbCustomAttributeDocumentType']).val();
    documentViewModel.body = $(self.refs['txtCustomAttributeBody']).val();
    documentViewModel.keyword = $(self.refs['txtCustomAttributeKeyword']).val();
    documentViewModel.register = register;
    documentViewModel.createdByStructureId = $(self.refs['cmbUserStructures']).val() != null ? $(self.refs['cmbUserStructures']).val() : self.model.createdByStructureId;
    documentViewModel.externalReferenceNumber = $(self.refs['txtCustomAttributeExternalReferenceNumber']).val() ? $(self.refs['txtCustomAttributeExternalReferenceNumber']).val().trim() : $(self.refs['txtCustomAttributeExternalReferenceNumber']).val();
    documentViewModel.transferId = self.model.transferId;
    documentViewModel.isFollowUp = self.model.isFollowUp;
    documentViewModel.delegationId = self.model.delegationId;
    documentViewModel.fromRejectedDocument = self.model.fromRejectedDocument;
    documentViewModel.referenceNumber = self.model.referenceNumber;
    return documentViewModel;
}
function openAddressBook(selection, mode, callback, structureType, self, fromSendingEntity) {
    let modalWrapper = $(".modal-window");
    let view = new AddressBook($(self.refs['cmbUserStructures']).val(), window.EnableSendingRules === "True", window.IsStructureSender === "True", window.EnableTransferToUsers === "True", mode, selection, self.model.delegationId, structureType, true, callback, modalWrapper, self.model.categoryId, fromSendingEntity);
    view.render();
    $('.modalAddressBook').modal('show');
    $(".modalAddressBook").off("hidden.bs.modal");
    $(".modalAddressBook").off("shown.bs.modal");
    $('.modalAddressBook').on('hidden.bs.modal', function () {
        $(".modalAddressBook").parent().remove();
        swal.close();
    });
}
function openSendingAndReceivingAddressBook(selection, mode, callback, self, structureType, fromSendingEntity) {
    let modalWrapper = $(".modal-window");
    //let view = new CoreComponents.AddressBookComponent($(self.refs['cmbUserStructures']).val(), window.EnableSendingRules === "True", window.IsStructureSender === "True", window.EnableTransferToUsers === "True", mode, selection, self.model.delegationId, callback, modalWrapper);
    let view = new AddressBook($(self.refs['cmbUserStructures']).val(), window.EnableSendingRules === "True", window.IsStructureSender === "True", window.EnableTransferToUsers === "True", mode, selection, self.model.delegationId, structureType, true, callback, modalWrapper, self.model.categoryId, fromSendingEntity);
    view.render();
    $('.modalAddressBook').modal('show');
    $(".modalAddressBook").off("hidden.bs.modal");
    $(".modalAddressBook").off("shown.bs.modal");
    $('.modalAddressBook').on('hidden.bs.modal', function () {
        $(".modalAddressBook").parent().remove();
        swal.close();
    });
}
function openEntityGroupBook(selection, mode, callback) {
    var wrapper = $(".modal-window");
    var modelIndex = new EntityGroupBook.EntityGroupBook(callback);
    modelIndex.mode = mode;
    modelIndex.isMultiple = selection == AddressBookSelectionMode.Multiple;
    var EntityGroupIndex = new EntityGroupBook.EntityGroupBookView(wrapper, modelIndex);
    EntityGroupIndex.render();

    $('#modalEntityGroup').modal("show");
    $('#modalEntityGroup').off("hidden.bs.modal");
    $('#modalEntityGroup').off("shown.bs.modal");
    $('#modalEntityGroup').on('hidden.bs.modal', function () {
        $('#modalEntityGroup').remove();
        swal.close();
    });
}
function sendToReceivingEntityMyTransfer(receivingEntities, delegationId, transferToType, transferToTxt, self, signatureTemplate, withSign, actionArray) {
    var documentCarbonCopy = self.model.documentCarbonCopy;

    if (!actionArray?.includes("Attribute.Export") && !actionArray?.includes("Transfer.SignAndExport")) {
        receivingEntities = $("[ref='cmbCustomAttributeReceiver']").length > 0
            ? $("[ref='cmbCustomAttributeReceiver']").select2("data")
            : receivingEntities;

        documentCarbonCopy = $("[ref='cmbCustomAttributeCarbonCopy']").length > 0
            ? $("[ref='cmbCustomAttributeCarbonCopy']").select2("data")
            : self.model.documentCarbonCopy;
    }

    $.each(documentCarbonCopy, (index, x) => {
        x.isCC = true;
    });
    $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", false);
    $('#' + self.model.ComponentId + '_btnComplete').button('reset');
    $(self.refs['btnSignTemplate']).attr("disabled", false);
    $(self.refs['btnGroupDropup']).attr("disabled", false);
    $(self.refs['btnSignTemplate']).button('reset');
    $(self.refs['btnGroupDropup']).button('reset');
    $('#' + self.model.ComponentId + '_btnCompleteAndArchive').attr("disabled", false);
    $('#' + self.model.ComponentId + '_btnCompleteAndArchive').button('reset');
    $('#' + self.model.ComponentId + '_btnReplyToStructure').attr("disabled", false);
    $('#' + self.model.ComponentId + '_btnReplyToStructure').button('reset');
    $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", false);
    $('#' + self.model.ComponentId + '_btnReplyToUser').button('reset');
    $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
    $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
    $('#' + self.model.ComponentId + '_btnSend').attr("disabled", false);
    $('#' + self.model.ComponentId + '_btnSend').button('reset');
    $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", false);
    $('#' + self.model.ComponentId + '_btnSignTemplate').button('reset');
    $('#btnGroupDropup').attr("disabled", false);
    $('#btnGroupDropup').button('reset');
    $('#' + self.model.ComponentId + '_btnProceed').attr("disabled", false);
    $('#' + self.model.ComponentId + '_btnProceed').button('reset');
    $('#' + self.model.ComponentId + '_btnReturn').attr("disabled", false);
    $('#' + self.model.ComponentId + '_btnReturn').button('reset');
    $('#' + self.model.ComponentId + '_btnResume').attr("disabled", false);
    $('#' + self.model.ComponentId + '_btnResume').button('reset');
    $('#' + self.model.ComponentId + '_btnReinitiate').attr("disabled", false);
    $('#' + self.model.ComponentId + '_btnReinitiate').button('reset');

    let modalWrapper = $(".modal-window");
    if (actionArray != undefined) {
        if (actionArray.includes("Attribute.Export") || actionArray.includes("Transfer.SignAndExport"))
            var modelIndex = new ExportOptions.ExportOptions();

    }
    else {
        var modelIndex = new SendToReceivingEntityIndex.SendToReceivingEntityIndex();

    }
    //let allPurposes = new Helper().get();
    var defaultPurposeForExport = window.ToViewPurpose /*allPurposes.filter(p => p.id === 13 || p.text.includes("To View") || p.text.includes("للعرض"))*/;
    modelIndex.purposes = new Helper().getPurpose();
    modelIndex.receivingEntities = receivingEntities;
    modelIndex.transferToType = transferToType;
    modelIndex.transferToUser = transferToTxt;
    modelIndex.transferToStructure = transferToTxt;
    modelIndex.customAttributeDueDate = self.model.dueDate;
    modelIndex.documentId = self.model.id;
    modelIndex.transferId = self.model.transferId;
    modelIndex.fromVip = gFromVip;
    modelIndex.enableSendingRules = window.EnableSendingRules === "True";
    modelIndex.enableTransferToUsers = window.EnableTransferToUsers === "True";
    modelIndex.isStructureSender = window.IsStructureSender === "True";
    modelIndex.delegationId = delegationId;
    modelIndex.structureIds = $("#hdStructureIds").val().split(window.Seperator);
    modelIndex.fromStructureId = self.model.fromStructureId;
    modelIndex.signatureTemplate = signatureTemplate
    modelIndex.withSign = withSign;
    modelIndex.fromExport = true;
    modelIndex.isSpecific = false;
    modelIndex.documentCarbonCopy = documentCarbonCopy;

    if (transferToType == TransferType.BroadcastSend || transferToType == TransferType.BroadcastComplete) {
        modelIndex.isBroadcast = true;
        modelIndex.broadcastIds = self.model.transferId;
    }
    if (actionArray != undefined) {
        if (actionArray.includes("Attribute.Export") || actionArray.includes("Transfer.SignAndExport"))
            modelIndex.action = "Attribute.Export";
    }
    modelIndex.actionsComponentId = self.model.actionsComponentId;
    let sendToReceivingEntityIndexView = new SendToReceivingEntityIndex.SendToReceivingEntityIndexView(modalWrapper, modelIndex, function () {
        tryCloseModal(self.model.actionsComponentId);
        if (self.model.fromVip) {
            TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
            TreeNode.refreshTreeNodeCounts(TreeNode.Draft);

            $(".withBorders-o").addClass("waitingBackground");
            $("#draftDocumentDetailsContainer").empty();
            $($("input[data-id='" + self.model.id + "']").parents("li")[0]).fadeOut().remove();
        } else {
            GridCommon.Refresh("grdDraftItems");
        }
        TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
        TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);
    } /*self.model.callback*/);

    if (purposeIdForSignature != 0) {
        Common.mask(document.body, "body-mask");
        modelIndex.purposeIdForSignature = purposeIdForSignature;
        sendToReceivingEntityIndexView.sendToReceivingEntitySubmit();
        $('#signatureTemplatesClose').click(function () {
            $('#modalSignatureTemplates').modal("hide");
        });
        Common.unmask(document.body, "body-mask");
    }
    else {
        if (actionArray != undefined && (actionArray.includes("Attribute.Export") || actionArray.includes("Transfer.SignAndExport")) && defaultPurposeForExport.length != 0) {
            var ExportOptionsView = new ExportOptions.ExportOptionsView(modalWrapper, modelIndex, function () { tryCloseModal(self.model.actionsComponentId) });
            modelIndex.defaultPurposeForExport = defaultPurposeForExport ? defaultPurposeForExport[0].id : null;
            ExportOptionsView.render();
            Common.unmask(document.body, "body-mask");
            $('#modalExportOptions').modal('show');
            $("#modalExportOptions").off("hidden.bs.modal");
            $("#modalExportOptions").off("shown.bs.modal");

            $('#modalExportOptions').on('hidden.bs.modal', function () {
                $('#modalExportOptions').remove();
                if (self.model.fromSignAndExport) {
                    Common.tryCloseDocumentModal(self.model.actionComponentId);
                    if (self.model.fromVip) {
                        $(".withBorders-o").addClass("waitingBackground");
                        $("#draftDocumentDetailsContainer").empty();
                        $($("input[data-id='" + self.model.id + "']").parents("li")[0]).fadeOut().remove();
                    } else {
                        GridCommon.Refresh("grdDraftItems");
                    }
                }
            });
        }
        else {
            Common.unmask("body-mask");
            sendToReceivingEntityIndexView.render();

        }
    }
    $(document).on('click', '#signatureTemplatesClose', function () {
        $('#modalSignatureTemplates').modal("hide");
    });
    $('#btnSignTemplateClose').click();

    $("#modalSendToReceivingEntity").off("shown.bs.modal");
    $('#modalSendToReceivingEntity').on('shown.bs.modal', function () {
        $("#hdSendDeligationId").val(delegationId);
        CKEDITOR.instances.txtAreaInstruction.focus();
    });
}

function sendToReceivingEntity(privacies, delegationId, self, transferType, actionArray) {
    var receivingEntities = $(self.refs['cmbCustomAttributeReceiver']).select2("data");
    var documentCarbonCopy = $(self.refs['cmbCustomAttributeCarbonCopy']).select2("data");
    $.each(documentCarbonCopy, (index, x) => {
        x.isCC = true;
    });
    var carbonCopy = $(self.refs['cmbCustomAttributeCarbonCopy']).select2("data").map(function (e) {
        return parseInt(e.id);
    });
    //const filteredList = receivingEntities.filter(item => !item.isExternal);
    //var receiversIdsArray = receivingEntities.map(function (e) {
    //    return parseInt(e.id);
    //});
    var receiversIdsArray = [];
    for (var i = 0; i < receivingEntities.length; i++) {
        if ($(receivingEntities[i].element).data("external"))
            continue;

        receiversIdsArray.push(receivingEntities[i].id);
    }

    var message = SendTransferModal.checkAbilityToSend(receiversIdsArray, receivingEntities, $(self.refs['cmbCustomAttributePrivacy']).val(), privacies, null, false);
    if (message === "error") {
        $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
        $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
        $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
        $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
        $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
        $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
        $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
        $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
        $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
        $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
        $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
        $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
    } else if (message !== "") {
        if (window.SendWithoutStructureReceiverOrPrivacyLevel === "True") {
            Common.showConfirmMsg(message + " \n " + Resources.ContinueConfirmation, function () {
                $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
                $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
                $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
                $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
                let modalWrapper = $(".modal-window");
                let modelIndex = new SendToReceivingEntityIndex.SendToReceivingEntityIndex();
                modelIndex.purposes = new Helper().getPurpose();
                modelIndex.transferToType = TransferType.Send;
                modelIndex.customAttributeDueDate = $(self.refs['customAttributeDueDate']).val();

                modelIndex.documentId = self.model.id;
                modelIndex.fromVip = self.model.fromVip;
                if ($(self.refs['cmbUserStructures']).val()) {
                    sessionStorage.setItem($("#hdUserId").val() + "SelectedUserStrcuture", $(self.refs['cmbUserStructures']).val());
                }
                let sendToReceivingEntityIndexView = new SendToReceivingEntityIndex.SendToReceivingEntityIndexView(modalWrapper, modelIndex, self.model.callback);
                sendToReceivingEntityIndexView.render();
                $("#modalSendToReceivingEntity").off("shown.bs.modal");
                $("#modalSendToReceivingEntity").on('shown.bs.modal', function () {
                    $("#hdSendDeligationId").val(delegationId);
                    CKEDITOR.instances.txtAreaInstruction.focus();
                });
            }, function () {
                $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
                $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
                $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
                $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
            });
        } else {
            //$('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
            //$('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
            //$('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
            //$('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
            //$('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
            //$('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
            //$('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
            //$('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
            //$('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
            //$('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
            //$('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
            //$('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
            //Common.alertMsg(message);

            const filteredList = receivingEntities.filter(item => !item.isExternal);
            if (filteredList.length > 0) {
                Common.showConfirmMsg(message + " \n " + Resources.ContinueConfirmation, function () {
                    $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                    //$('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                    $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
                    $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
                    $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                    $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
                    $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
                    let modalWrapper = $(".modal-window");
                    if (actionArray != undefined) {
                        if (actionArray.includes("Attribute.Export"))
                            var modelIndex = new ExportOptions.ExportOptions();

                    }
                    else {
                        var modelIndex = new SendToReceivingEntityIndex.SendToReceivingEntityIndex();

                    }

                  //  let allPurposes = new Helper().get();
                    var defaultPurposeForExport = window.ToViewPurpose/*allPurposes.filter(p => p.id === 13 || p.text.includes("To View") || p.text.includes("للعرض"))*/;

                    ///////////////////////////////////////////////////
                    modelIndex.purposes = new Helper().getPurpose();
                    //////////////////////////////////////////////////
                    //receivingEntities.forEach(item => {
                    //    // Fetch the actual <option> by value
                    //    const option = $(self.refs['cmbCustomAttributeReceiver']).find(`option[value="${item.id}"]`);

                    //    // Assign isExternal manually from data attribute
                    //    item.isExternal = option.data('external');
                    //});
                    modelIndex.receivingEntities = receivingEntities;
                    modelIndex.documentCarbonCopy = documentCarbonCopy;
                    modelIndex.transferToType = transferType;
                    modelIndex.documentId = self.model.id;
                    modelIndex.transferId = self.model.transferId;
                    modelIndex.fromVip = self.model.fromVip;
                    modelIndex.carbonCopy = carbonCopy;
                    if (actionArray != undefined) {
                        if (actionArray.includes("Attribute.Export"))
                            modelIndex.action = "Attribute.Export";
                        //if (defaultPurposeForExport.length !=0 )
                        //    modelIndex.defaultPurposeForExport = defaultPurposeForExport ? defaultPurposeForExport[0].id : null;
                    }
                    modelIndex.fromExport = true;
                    modelIndex.isSpecific = false;
                    if (transferType == TransferType.BroadcastSend || transferType == TransferType.BroadcastComplete) {
                        modelIndex.isBroadcast = true;
                        modelIndex.isDraft = true;
                        modelIndex.broadcastIds = self.model.transferId;
                    }
                    modelIndex.customAttributeDueDate = $(self.refs['customAttributeDueDate']).val();

                    if ($(self.refs['cmbUserStructures']).val()) {
                        sessionStorage.setItem($("#hdUserId").val() + "SelectedUserStrcuture", $(self.refs['cmbUserStructures']).val());
                    }
                    let sendToReceivingEntityIndexView = new SendToReceivingEntityIndex.SendToReceivingEntityIndexView(modalWrapper, modelIndex, function () { tryCloseModal(self.model.actionsComponentId) } /*self.model.callback*/);
                    if (actionArray != undefined && actionArray.includes("Attribute.Export") && defaultPurposeForExport.length != 0) {
                        var ExportOptionsView = new ExportOptions.ExportOptionsView(modalWrapper, modelIndex);
                        modelIndex.defaultPurposeForExport = defaultPurposeForExport ? defaultPurposeForExport[0].id : null;
                        //sendToReceivingEntityIndexView.sendToReceivingEntitySubmit();
                        ExportOptionsView.render(/*callback*/);
                        $('#modalExportOptions').modal('show');
                        $("#modalExportOptions").off("hidden.bs.modal");
                        $("#modalExportOptions").off("shown.bs.modal");

                        $('#modalExportOptions').on('hidden.bs.modal', function () {
                            $('#modalExportOptions').remove();
                            if (self.model.fromSignAndExport) {
                                Common.tryCloseDocumentModal(self.model.actionComponentId);
                                if (self.model.fromVip) {
                                    $(".withBorders-o").addClass("waitingBackground");
                                    $("#draftDocumentDetailsContainer").empty();
                                    $($("input[data-id='" + self.model.id + "']").parents("li")[0]).fadeOut().remove();
                                } else {
                                    GridCommon.Refresh("grdDraftItems");
                                }
                            }
                        });
                        Common.unmask("body-mask");
                    }
                    else {
                        sendToReceivingEntityIndexView.render();
                        $("#modalSendToReceivingEntity").off("shown.bs.modal");
                        $("#modalSendToReceivingEntity").on('shown.bs.modal', function () {
                            $("#hdSendDeligationId").val(delegationId);
                            CKEDITOR.instances.txtAreaInstruction.focus();
                        });
                    }





                }, function () {
                    $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                    $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
                    $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
                    $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                    $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
                    $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
                });
            }

            else {
                $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
                $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
                $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
                $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
                Common.alertMsg(message);
            }
        }
    } else {
        $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
        //$('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
        $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
        $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
        $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
        $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
        $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
        $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
        $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
        $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
        $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
        $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
        let modalWrapper = $(".modal-window");
        if (actionArray != undefined) {
            if (actionArray.includes("Attribute.Export"))
                var modelIndex = new ExportOptions.ExportOptions();

        }
        else {
             var modelIndex = new SendToReceivingEntityIndex.SendToReceivingEntityIndex();

        }

        //let allPurposes = new Helper().get();
        var defaultPurposeForExport = window.ToViewPurpose /*allPurposes.filter(p => p.id === 13 || p.text.includes("To View") || p.text.includes("للعرض"))*/;

        ///////////////////////////////////////////////////
        modelIndex.purposes = new Helper().getPurpose();
        //////////////////////////////////////////////////
        receivingEntities.forEach(item => {
            // Fetch the actual <option> by value
            const option = $(self.refs['cmbCustomAttributeReceiver']).find(`option[value="${item.id}"]`);

            // Assign isExternal manually from data attribute
            item.isExternal = option.data('external');
        });
        modelIndex.receivingEntities = receivingEntities;
        modelIndex.documentCarbonCopy = documentCarbonCopy;
        modelIndex.transferToType = transferType;
        modelIndex.documentId = self.model.id;
        modelIndex.transferId = self.model.transferId;
        modelIndex.fromVip = self.model.fromVip;
        modelIndex.carbonCopy = carbonCopy;
        if (actionArray != undefined) {
            if (actionArray.includes("Attribute.Export"))
                modelIndex.action = "Attribute.Export";
            //if (defaultPurposeForExport.length !=0 )
            //    modelIndex.defaultPurposeForExport = defaultPurposeForExport ? defaultPurposeForExport[0].id : null;
        }
        modelIndex.fromExport = true;
        modelIndex.isSpecific = false;
        if (transferType == TransferType.BroadcastSend || transferType == TransferType.BroadcastComplete) {
            modelIndex.isBroadcast = true;
            modelIndex.isDraft = true;
            modelIndex.broadcastIds = self.model.transferId;
        }
        modelIndex.customAttributeDueDate = $(self.refs['customAttributeDueDate']).val();
        modelIndex.actionsComponentId = self.model.actionComponentId;
        if ($(self.refs['cmbUserStructures']).val()) {
            sessionStorage.setItem($("#hdUserId").val() + "SelectedUserStrcuture", $(self.refs['cmbUserStructures']).val());
        }
        var ExportOptionsView = new ExportOptions.ExportOptionsView(modalWrapper, modelIndex);
        let sendToReceivingEntityIndexView = new SendToReceivingEntityIndex.SendToReceivingEntityIndexView(modalWrapper, modelIndex, function () { tryCloseModal(self.model.actionsComponentId) } /*self.model.callback*/);
        if (actionArray != undefined && actionArray.includes("Attribute.Export") && defaultPurposeForExport.length != 0) {
            modelIndex.defaultPurposeForExport = defaultPurposeForExport ? defaultPurposeForExport[0].id : null;
            //sendToReceivingEntityIndexView.sendToReceivingEntitySubmit();
            ExportOptionsView.render(/*callback*/);
            $('#modalExportOptions').modal('show');
            $("#modalExportOptions").off("hidden.bs.modal");
            $("#modalExportOptions").off("shown.bs.modal");

            $('#modalExportOptions').on('hidden.bs.modal', function () {
                $('#modalExportOptions').remove();
                if (self.model.fromSignAndExport) {
                    Common.tryCloseDocumentModal(self.model.actionComponentId);
                    if (self.model.fromVip) {
                        $(".withBorders-o").addClass("waitingBackground");
                        $("#draftDocumentDetailsContainer").empty();
                        $($("input[data-id='" + self.model.id + "']").parents("li")[0]).fadeOut().remove();
                    } else {
                        GridCommon.Refresh("grdDraftItems");
                    }
                }
            });
            Common.unmask("body-mask");
        }
        else {
            sendToReceivingEntityIndexView.render();
            $("#modalSendToReceivingEntity").off("shown.bs.modal");
            $("#modalSendToReceivingEntity").on('shown.bs.modal', function () {
                $("#hdSendDeligationId").val(delegationId);
                CKEDITOR.instances.txtAreaInstruction.focus();
            });
            Common.unmask("body-mask");
        }
      
    }
}
function openTransfer(privacies, delegationId, self) {
    var callback = function (data, isFollowUpTransfer) {
        
        if (window.EnableConfirmationMessage === "True" & !isFollowUpTransfer) {
            Common.showConfirmMsg(Resources.ProceedConfirmation, function () {
                sendTransfer(data, privacies, delegationId, self);
            });
        } else {
            sendTransfer(data, privacies, delegationId, self);
        }
    };
    $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
    $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
    $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
    $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
    $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
    // $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
    $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
    $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
    $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
    $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
    $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
    $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
    let modalWrapper = $(".modal-window");
    
    transferComponent = new Transfer($(self.refs['cmbUserStructures']).val(), $("#hdUserId").val(), $("#hdStructureIds").val().split(window.Seperator),
        window.IsStructureSender === "True", window.EnableSendingRules === "True", window.EnableTransferToUsers === "True", $(self.refs['customAttributeDueDate']).val(),
        self.model.delegationId, callback, modalWrapper, false, null, gIsDueDateRelatedToPriority, prioritiesList, self.model.id);
    transferComponent.render();
    if (self.model.fromSignAndTransfer && self.model.categoryName == "Outgoing") {
        $('#chkMaintainTransfer').prop('checked', true);
    }
    $('.modalTransfer').modal('show');
    $(".modalTransfer").off("hidden.bs.modal");
    $(".modalTransfer").off("shown.bs.modal");
    $('.modalTransfer').on('hidden.bs.modal', function () {
        $(".modalTransfer").parent().remove();
        if (self.model.fromSignAndTransfer && self.model.categoryName == "Outgoing") {
            Common.tryCloseDocumentModal(self.model.actionComponentId);
            if (self.model.fromVip) {
                $(".withBorders-o").addClass("waitingBackground");
                $("#draftDocumentDetailsContainer").empty();
                $($("input[data-id='" + self.model.id + "']").parents("li")[0]).fadeOut().remove();
            } else {
                GridCommon.Refresh("grdDraftItems");
            }
        }
        swal.close();
    });
}
class Document extends Intalio.Model {
    constructor() {
        super();
        this.id = null;
        this.categoryId = null;
        this.referenceNumber = null;
        this.subject = null;
        this.categoryName = null;
        this.createdByUserId = null;
        this.basicAttributes = null;
        this.customAttributes = null;
        this.customAttributesTranslation = null;
        this.formData = null;
        this.receivers = null;
        this.sendingEntityId = null;
        this.dueDate = null;
        this.createdDate = null;
        this.documentDate = null;
        this.createdByUser = null;
        this.priorityId = null;
        this.privacyId = null;
        this.carbonCopy = null;
        this.importanceId = null;
        this.classificationId = null;
        this.documentTypeId = null;
        this.delegationId = null;
        this.readonly = false;
        this.userStructures = [];
        this.privacies = [];
        this.importances = [];
        this.sendingEntity = null;
        this.receivingEntities = null;
        this.carbonCopies = null;
        this.classification = null;
        this.documentType = null;
        this.createdByStructureId = null;
        this.multipleReceivingEntity = true;
        this.body = null;
        this.fromVip = false;
        this.keyword = null;
        this.enableEdit = false;
        this.isDocumentEdit = false;
        this.transferId = null;
        this.senderPerson = null;
        this.isExternalSender = null;
        this.receiverPerson = null;
        this.isExternalReceiver = null;
        this.isFollowUp = null;
        this.documentDate = null;
        this.isCompleted = null;
        this.actionComponentId = null;
        this.parentComponentId = null;
        this.fromDraft = null;
        this.fromRejectedDocument = false;
        this.followUpId = null;
        this.byTemplate = false;
        this.documentCarbonCopy = [];
        this.g2gInternalId = null;
    }
}
function viewDocumentBarcode(documentId, referenceNumber, delegationId) {
    if (referenceNumber) {
        let param = {
            'documentId': documentId,
            'delegationId': delegationId
        };
        var barcodeData = sessionStorage.getItem(documentId + "_BarcodeData");
        if (barcodeData !== null) {
            openBarcode(documentId, barcodeData, true);

        } else {
            Common.ajaxPost("Document/PreviewBarcodeByDocument", param, function (data) {
                if (data !== "") {
                    openBarcode(documentId, data, false);
                }
            }, function (error, object) { Common.showScreenErrorMsg(object.responseText); }, true);
        }
    }
}
function getUsersOnStructures(structureIds, isExternalUser, callback) {
    var url = window.IdentityUrl + '/api/SearchUsersByStructureIds';
    if (isExternalUser) {
        url = 'CTS/DesignatedPerson/GetDesignatedPerson';
    }
    $.ajax({
        type: 'POST',
        url: url,
        headers: {
            "Authorization": 'Bearer ' + window.IdentityAccessToken
        },
        dataType: 'json',
        data: {
            'ids': structureIds,
            'text': "",
            'language': "",
            'showOnlyActiveUsers': false
        },
        success: function (data) {
            var userList = [];
            $.each(data, function (key, val) {
                var item = {
                    id: val.id,
                    text: val.fullName
                };
                userList.push(item);
            });
            callback(userList);
        },
        error: function (xhr, status, error) {
            console.error("Error fetching user data:", error);
            callback([]);
        }
    });
}
function openBarcode(documentId, data, isCached) {
    let modelIndex = new DocumentBarcode.DocumentBarcode();
    if (typeof data !== "undefined") {
        modelIndex.imageSrc = "data:image/png;base64," + data;
    } else {
        modelIndex.imageSrc = "";
    }
    if (!isCached && typeof data !== "undefined") {
        sessionStorage.setItem(documentId + "_BarcodeData", data);
    }
    let contentDiv = $(".modal-window");
    let documentBarcode = new DocumentBarcode.DocumentBarcodeView(contentDiv, modelIndex);
    
    documentBarcode.render();
    
    $(documentBarcode.refs['modalDocumentBarcode']).modal('show');
    $(documentBarcode.refs['modalDocumentBarcode']).off("hidden.bs.modal");
    $(documentBarcode.refs['modalDocumentBarcode']).off("shown.bs.modal");
    $(documentBarcode.refs['modalDocumentBarcode']).on('hidden.bs.modal', function () {
        swal.close();
        $(documentBarcode.refs[documentBarcode.model.ComponentId]).remove();
        if ($(".modal-window").children().length > 0 || $(".modal-documents").children().length > 0) {
            $('body').addClass('modal-open');
        }
    });
    $(documentBarcode.refs['modalDocumentBarcode']).on('shown.bs.modal', function () {

    });
}
function loadSendEmailReminderModal(delegationId, self) {
    $(self.refs['btnComplete']).attr("disabled", false);
    $(self.refs['btnComplete']).button('reset');
    $(self.refs['btnReplyToStructure']).attr("disabled", false);
    $(self.refs['btnReplyToStructure']).button('reset');
    $(self.refs['btnReplyToUser']).attr("disabled", false);
    $(self.refs['btnReplyToUser']).button('reset');
    $(self.refs['btnMyTransfer']).attr("disabled", false);
    $(self.refs['btnMyTransfer']).button('reset');
    $(self.refs['btnSend']).attr("disabled", false);
    $(self.refs['btnSend']).button('reset');
    let modalWrapper = $(".modal-window");
    let modelIndex = new TaskEmailReminder.TaskEmailReminder();
    modelIndex.documentId = self.model.id;
    modelIndex.transferId = self.model.transferId;
    modelIndex.delegationId = delegationId;
    modelIndex.subject = self.model.subject == '' ? $(self.refs['txtCustomAttributeSubject']).val() : self.model.subject;
    modelIndex.createdByUser = self.model.createdByUser;
    modelIndex.createdByUserId = self.model.createdByUserId;
    modelIndex.dueDate = self.model.dueDate;
   

    Common.ajaxGet("/Attachment/ListAttachments?documentId=" + self.model.id + "&transferId=" + self.model.transferId + "&delegationId=" + self.model.delegationId, null, function (data) {
        $("#modalTaskEmailReminder").remove();

        modelIndex.attachments = data.data;
        let taskEmailReminderView = new TaskEmailReminder.TaskEmailReminderView(modalWrapper, modelIndex);
        taskEmailReminderView.render();
        $('#modalTaskEmailReminder').modal("show");
        $("#modalTaskEmailReminder").off("hidden.bs.modal");
        $("#modalTaskEmailReminder").off("shown.bs.modal");
        $("#modalTaskEmailReminder").on('hidden.bs.modal', function () {
            $("#modalTaskEmailReminder").remove();
            swal.close();
            $('body').addClass('modal-open');
        });
        //$('#modalTaskEmailReminder').on('shown.bs.modal', function () {
        //    CKEDITOR.instances.txtAreaEmailBody.focus();
        //});
    }, function () { Common.showScreenErrorMsg(); }, null, null, false);
}
function updateListItem(transfer) {
    if (transfer.transferId == null)
        var $li = $('li[data-transfer="' + transfer.id + '"]');
    else
        var $li = $('li[data-transfer="' + transfer.transferId + '"]');
    if (!$li.length) return;
    // Update subject, referenceNumber.
    $li.find('[data-field="subject"]').text(transfer.subject);
    $li.find('[data-field="referenceNumber"]').text(transfer.referenceNumber);
    
}

var gFormData;
var templateId = 0;
var purposeIdForSignature = 0;
var signType = 0;
var gLocked = false;
var gIsDueDateRelatedToPriority = false;
var transferComponent;
var formioFrom = true;
var gMaxTagsCharCount = 749;
var gFromVip = false;
var fromSignOnly = false;
var fromSignAndTransfer = false;
var fromSignAndExport = false;
var fromPeview = false;
var actionArray = [];

$(document).on('click', '.input-group.date .input-group-addon.dum', function () {

    const $container = $(this).closest('.input-group.date');
    const $inputs = $container.find('input.flatpickr-input[type="hidden"]');
    const $matchedInput = $inputs.filter(function () {
        return this._flatpickr;
    }).first();

    if ($matchedInput.length && $matchedInput[0]._flatpickr) {
        const fp = $matchedInput[0]._flatpickr;

        //$matchedInput[0]._flatpickr.toggle();

        if (fp && !fp.isOpen) {
            fp.open();
        }
    } 
});

class DocumentView extends Intalio.View {
    constructor(element, model) {
        super(element, "document", model);
    }
    render(saveOptions, sendOptions) {
        Common.ajaxPost('/StructureSignature/GetTemplateIdByStructureIdAndUserId/', { "structureId": $('#hdLoggedInStructureId').val() }, function (retval) {
            if (retval) {
                templateId = retval.data;
            }
        });
        var self = this;
        // make attributes tab not reload 
        var tabs = $("div[ref='" + self.model.parentComponentId + "'] ul[ref='tabDocumentDetails'] li a")
        var refreshtab = $.grep(tabs, function (element) {
            return $(element).data("id") == self.model.tabId;
        })[0];

        $(refreshtab).data("loaded", true)
        $(refreshtab).data("customloaded", true)

        let Signaturemodel = new SignatureTemplates();
        let wrapper = $(".modal-window");
        //wrapper.empty();
        let view = new SignatureTemplatesView(wrapper, Signaturemodel);
        view.render();

        $.fn.select2.defaults.set("theme", "bootstrap");
        if ((self.model.basicAttributes.find(obj => obj.Name == "SendingEntity" && obj.Type == "internal")) && (self.model.basicAttributes.find(obj => obj.Name == "ReceivingEntity" && obj.Type == "internal")))
        {
            $(self.refs['customAttributeSenderContainer']).hide();
            $(self.refs['customAttributeReceiverContainer']).hide();

        }
        gFromVip = this.model.fromVip;
        var actionsPanel = "#" + self.model.parentComponentId + "_ActionsPanel";
        if (!gFromVip)
            actionsPanel = "#" + (self.model.actionComponentId !== 'null' && self.model.actionComponentId !== undefined ? self.model.actionComponentId : self.model.parentComponentId) + "_ActionsPanel";

        var builder, model = this.model, basicAttributes = this.model.basicAttributes, customAttributes = this.model.customAttributes;
        gFormData = model.formData;
        //var actionsPanel = "#" + self.model.actioncomponentId + "_ActionsPanel";
       
        if (this.model.actionName) {
            actionArray = this.model.actionName.split("_");
            if (self.model.fromDraft) {

                $(actionsPanel).empty();
                $(actionsPanel).append('<button ref="btnAttributeComplete" id ="' + self.model.ComponentId + '_btnAttributeComplete" type = "button" transferAction = "Transfer.Complete" class="btn btn-vip-toolbar " ><i class="fa fa-check-square mr-sm"></i>' + Resources.Complete + '</button>');
                if(actionArray.includes("Transfer.StartWorkflow"))
                {
                    $(actionsPanel).append('<button ref="btnStartWorkflow1" id="' + self.model.ComponentId + '_btnStartWorkflow1" type="button" class="btn btn-vip-toolbar "><i class="fa fa-tasks mr-sm"></i>' + Resources.StartWorkflow + '</button>');
                }
                if(actionArray.includes("Attribute.Send"))
                {
                    $(actionsPanel).append('<button ref="btnAttributeSend" id ="' + self.model.ComponentId + '_btnAttributeSend" type = "button" transferAction = "Transfer.Complete" class="btn btn-vip-toolbar " ><i class="fa fa-check-square mr-sm"></i>' + Resources.Send + '</button>');
                }
                $(actionsPanel).append('<button ref="btnAttributeTransfer" id="' + self.model.ComponentId + '_btnAttributeTransfer" type="button" class="btn btn-vip-toolbar " data-loading-text="<i class="fa fa-spinner fa-spin"><i class="fa fa-send mr-sm"></i>' + Resources.TransferDraft + '</button>');
                $(actionsPanel).append('<button ref="btnAttributeProceed" id="' + self.model.ComponentId + '_btnAttributeProceed" type="button" class="btn btn-vip-toolbar " data-loading-text="<i class="fa fa-spinner fa-spin"><i class="fa fa-send mr-sm"></i>' + Resources.Proceed + '</button>');
                if (actionArray.includes("Attribute.Export")) {
                    $(actionsPanel).append('<button ref="btnAttributeExport" style="display: none;" id="' + self.model.ComponentId + '_btnAttributeExport" type="button" class="btn btn-vip-toolbar " data-loading-text="<i class="fa fa-spinner fa-spin"><i class="fa fa-check-square mr-sm"></i>' + Resources.Export + '</button>');
                    $('#' + self.model.ComponentId + '_btnAttributeExport').show();
                }
                //if ((actionArray.includes("Transfer.Sign") || actionArray.some(action => action.startsWith("Transfer.Sign."))) &&
                //    !document.getElementById(self.model.ComponentId + '_btnSignTemplate')
                //    && !self.model.isCompleted && !self.model.readonly &&  this.model.hasAttachments
                //    && (this.model.hasUserCofigureSignature || (this.model.byTemplate && this.model.templateHasSignature && this.model.attachmentExtention == "docx"))
                //    && self.model.allowSign && !self.model.isSigned) {
                    //hasAnyPermission = true;
                    //signmenu += '<li><a class="dropdown-item" id="' + self.model.ComponentId + '_btnSignTemplate" ref="btnSignTemplate" style="cursor: pointer">' + Resources.SignOnly + '</a></li>';
                if (/*!self.model.isCompleted && !self.model.readonly && (this.model.hasAttachments || this.model.byTemplate)*/
                    !document.getElementById(self.model.ComponentId + '_btnSignTemplate')
                    && !self.model.isCompleted && !self.model.readonly && this.model.hasAttachments
                    && (this.model.hasUserCofigureSignature || (/*this.model.byTemplate &&*/ this.model.templateHasSignature && this.model.attachmentExtention == "docx"))
                    && self.model.allowSign && !self.model.isSigned) {
                    if (actionArray.includes("Transfer.Sign")) {
                        $(actionsPanel).append('<button id="' + self.model.ComponentId + '_btnSignTemplate" type="button" class="btn btn-vip-toolbar " disabled="disabled"><i class="fa fa-fw fa-pencil-square-o mr-sm"></i>' + Resources.SignOnly + '</button>');

                    }
                }
                /*}*/
                //if (!self.model.isCompleted && !self.model.readonly 
                //    && this.model.hasAttachments && (this.model.hasUserCofigureSignature ||
                //    (this.model.byTemplate && this.model.templateHasSignature && this.model.attachmentExtention == "docx")) &&
                //    self.model.allowSign && !self.model.isSigned) {

                if (/*!self.model.isCompleted && !self.model.readonly && (this.model.hasAttachments || this.model.byTemplate )*/
                     !self.model.isCompleted && !self.model.readonly && this.model.hasAttachments
                    && (this.model.hasUserCofigureSignature || (/*this.model.byTemplate &&*/ this.model.templateHasSignature && this.model.attachmentExtention == "docx"))
                    && self.model.allowSign && !self.model.isSigned) {
                    
                        var hasAnyPermission = false;
                var signmenu = '<div class="btn-group btn-SignOperations" role="group" aria-label="Button group with nested dropdown">'
                    + '<button id="btnGroupDropup" ref="btnGroupDropup" type="button" class="btn btn-sm btn-secondary dropdown-toggle btn-vip-toolbar" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" >' + Resources.SignOperations + '<i class="fa fa-caret-down ml-sm" aria-hidden="true"></i></button>'
                    + '<ul class="dropdown-menu dropdown-margin" aria-labelledby="btnGroupDropup">';

                        //if (actionArray.includes("Transfer.SignAndSend") || actionArray.some(action => action.startsWith("Transfer.SignAndSend.")) && !document.getElementById(self.model.ComponentId + '_btnSignAndSend')) {
                        //    hasAnyPermission = true;
                        //    signmenu += '<li><a class="dropdown-item" id="' + self.model.ComponentId + '_btnSignAndSend" ref="btnSignAndSend" style="cursor: pointer">' + Resources.SignAndSend + '</a></li>';
                        //}
                        if (actionArray.includes("Transfer.SignAndTransfer") || actionArray.some(action => action.startsWith("Transfer.SignAndTransfer.")) && !document.getElementById(self.model.ComponentId + '_btnSignAndTransfer')) {
                            hasAnyPermission = true;
                            signmenu += '<li><a class="dropdown-item" id="' + self.model.ComponentId + '_btnSignAndTransfer" ref="btnSignAndTransfer" style="cursor:pointer">' + Resources.SignAndTransfer + '</a></li>';

                        }
                        if (actionArray.includes("Transfer.SignAndExport") || actionArray.some(action => action.startsWith("Transfer.SignAndExport.")) && !document.getElementById(self.model.ComponentId + '_btnSignAndExport')) {
                            hasAnyPermission = true;
                            signmenu += '<li><a class="dropdown-item" id="' + self.model.ComponentId + '_btnSignAndExport" ref="btnSignAndExport" style="cursor:pointer">' + Resources.SignAndExport + '</a></li>';

                        }
                        if (actionArray.includes("Transfer.PreviewBeforeSign") || actionArray.some(action => action.startsWith("Transfer.PreviewBeforeSign.")) && !document.getElementById(self.model.ComponentId + '_btnPreviewBeforeSign')) {
                            hasAnyPermission = true;
                            signmenu += '<li><a class="dropdown-item" id="' + self.model.ComponentId + '_btnPreviewBeforeSign" ref="btnPreviewBeforeSign" style="cursor:pointer">' + Resources.PreviewBeforeSign + '</a></li ></ul></div>';
                        }
                        signmenu += '</ul></div></div>';
                        if (hasAnyPermission)

                        $(actionsPanel).append(signmenu);

                    }

            }
            
            var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));

            var activenode = $('li .active').attr("data-id") != undefined ? $('li .active').attr("data-id") : $('.active').attr("data-id");
            activenode = activenode != undefined ? activenode : this.model.categoryId;
            var Actions = this.model.tabId != undefined ? securityMatrix.SecurityNodes[activenode].SecurityCategories[this.model.categoryId].SecurityTabs[this.model.tabId] : securityMatrix.SecurityNodes[activenode].SecurityCategories[this.model.categoryId].Actions;
            var myDocumentActions = Actions != undefined ? Actions.filter(a => a.JsFunction != null && a.Type == TypeAction.Tab):[];//to get all custom actions & tab === TypeAction.Tab = 4: 

            for (var i = 0; i < myDocumentActions.length; i++) {
                var actionName = myDocumentActions[i].Name;
                var icone = myDocumentActions[i].Icon;
                var color = myDocumentActions[i].Color;
                var jsFunction = myDocumentActions[i].JsFunction;
                var showInEditMode = myDocumentActions[i].ShowInEditMode;
                var showInReadMode = myDocumentActions[i].ShowInReadMode;
                                if ($(actionsPanel).find('#' + self.model.parentComponentId + '_' + actionName + '').length == 0) {

                if (!this.model.readonly) {
                    $(actionsPanel).append('<button id="' + self.model.parentComponentId + '_' + actionName + '" type="button" onclick="' + jsFunction + '" class="btn btn-vip-toolbar " data-loading-text="<i class="fa fa-spinner fa-spin"><i class="' + icone + '"></i> ' + actionName + '</button>');
                }
                else {
                    if (showInReadMode) {
                        $(actionsPanel).append('<button id="' + self.model.parentComponentId + '_' + actionName + '" type="button" onclick="' + jsFunction + '" class="btn btn-vip-toolbar " data-loading-text="<i class="fa fa-spinner fa-spin"><i class="' + icone + '"></i> ' + actionName + '</button>');
                    }
                }
                }
            }
        }

        const connection = new signalR.HubConnectionBuilder()
            .withUrl("/notificationhub")
            .withAutomaticReconnect()
            .build();

        connection.on("RefreshInboxGrid", () => {
            if (self.model.fromVip) {
                TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                $(".withBorders-o").addClass("waitingBackground");
                $("#draftDocumentDetailsContainer").empty();
                $($("input[data-id='" + self.model.id + "']").parents("li")[0]).fadeOut().remove();
            }
            $('[ref="modalLinkedCorrespondenceDocument"]').modal('hide');
            $('[ref="modalLinkedCorrespondenceDocument"]').on('hidden.bs.modal', function () {
                $('.modal-documents').empty();
            });
            GridCommon.Refresh("grdDraftItems");
            TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
            TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
            TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);

        });

        connection.start()
            .catch(err => console.error("SignalR error:", err));


        //$('#' + self.model.ComponentId + '_btnAttributeProceed').remove();
        $('#' + self.model.ComponentId + '_btnAttributeProceed').hide();

        if ($(self.refs['txtCustomAttributeKeyword']).length > 0) {
            var tagsInput = $(self.refs['txtCustomAttributeKeyword']).tagsinput({
                maxChars: gMaxTagsCharCount,
                tagClass: 'label label-tag',
                trimValue: true,
                freeInput: true,
                allowDuplicates: false,
                confirmKeys: [13, 44, 188],
                focusClass: 'tag-focus',
                typeahead: {
                    minLength: 2,
                    delay: 500,
                    items: 15,
                    source: function (query) {
                        return $.ajax({
                            url: "/Tags/ListTags",
                            type: 'GET',
                            data: { text: query },
                            dataType: 'json'
                        });
                    },
                    afterSelect: function () {
                        this.$element.val("");
                    }
                }
            });

            $(self.refs['txtCustomAttributeKeyword']).on('itemAdded', function (event) {
                tagsInput[0].options.maxChars = gMaxTagsCharCount - $(self.refs['txtCustomAttributeKeyword']).val().length;
            });
            $(self.refs['txtCustomAttributeKeyword']).on('itemRemoved', function (event) {
                tagsInput[0].options.maxChars = gMaxTagsCharCount - $(self.refs['txtCustomAttributeKeyword']).val().length;
            });
            if ($(self.refs['txtCustomAttributeKeyword']).prev('div').length > 0) {
                if (typeof $(self.refs['txtCustomAttributeKeyword']).attr("disabled") !== "undefined") {
                    $(self.refs['txtCustomAttributeKeyword']).prev('div').attr("disabled", "disabled");
                } else {
                    $(self.refs['txtCustomAttributeKeyword']).prev('div').removeAttr("disabled");
                }
            }
        }

        if (model.isFollowUp) {
            if ($(self.refs['txtCustomAttributeSubject']).length > 0) {
                var tagsInput = $(self.refs['txtCustomAttributeSubject']).tagsinput({
                    maxChars: gMaxTagsCharCount,
                    tagClass: 'label label-tag',
                    trimValue: true,
                    freeInput: true,
                    allowDuplicates: false,
                    confirmKeys: [13, 44, 188],
                    focusClass: 'tag-focus',
                    typeahead: {
                        minLength: 2,
                        delay: 500,
                        items: 15,
                        source: [],
                        afterSelect: function () {
                            this.$element.val("");
                        }
                    }
                });

                $(self.refs['txtCustomAttributeSubject']).on('itemAdded', function (event) {
                    tagsInput[0].options.maxChars = gMaxTagsCharCount - $(self.refs['txtCustomAttributeSubject']).val().length;
                });
                $(self.refs['txtCustomAttributeSubject']).on('itemRemoved', function (event) {
                    tagsInput[0].options.maxChars = gMaxTagsCharCount - $(self.refs['txtCustomAttributeSubject']).val().length;
                });
                if ($(self.refs['txtCustomAttributeSubject']).prev('div').length > 0) {
                    if (typeof $(self.refs['txtCustomAttributeSubject']).attr("disabled") !== "undefined") {
                        $(self.refs['txtCustomAttributeSubject']).prev('div').attr("disabled", "disabled");
                    } else {
                        $(self.refs['txtCustomAttributeSubject']).prev('div').removeAttr("disabled");
                    }
                }


                if (model.subject !== null) {
                    var tags = model.subject.split(window.Splitter);
                    for (var i = 0; i < tags.length; i++) {
                        $(self.refs['txtCustomAttributeSubject']).tagsinput('add', tags[i]);
                    }
                }
            }

        }
        
        
        if ((window.OpenCorrespondenceMode === CorrespondenceMode.WithViewer) && !this.model.isFollowUp) {
            $(".defaultWithViewer").addClass("col-xl-12");
            $(".defaultWithViewer").addClass("col-lg-12");
            $(".defaultWithViewer").addClass("col-md-12");
            $(".defaultWithViewer").removeClass("col-sm-6");
            $(".defaultWithViewer").removeClass("col-xs-12");

        }
        $(".workingDaysLabel").hide();
        var receivingEntityObj = [];
        if (basicAttributes.length > 0) {
            receivingEntityObj = $.grep(basicAttributes, function (e) {
                return e.Name === "ReceivingEntity";
            });


            if (receivingEntityObj.length > 0) {
                model.multipleReceivingEntity = receivingEntityObj[0].MultipleReceivingEntity;
                if (receivingEntityObj[0].BroadcastReceivingEntity) {
                    if (receivingEntityObj[0].Type === "internal") {
                        //$('#' + self.model.ComponentId + '_btnAttributeTransfer').hide();
                        $('#' + self.model.ComponentId + '_btnAttributeTransfer').hide();
                        //$('#' + self.model.ComponentId + '_btnAttributeProceed').hide();
                        $('#' + self.model.ComponentId + '_btnAttributeProceed').hide();
                    }
                    $(self.refs['openBroadcastReceiverAddressBook']).show();
                } else {
                    $('#' + self.model.ComponentId + '_btnAttributeTransfer').show();
                    //$('#' + self.model.ComponentId + '_btnAttributeTransfer').show();
                    //$('#' + self.model.ComponentId + '_btnAttributeProceed').show();
                    $(self.refs['openBroadcastReceiverAddressBook']).hide();
                }
            }
        }

        /* if (customAttributes != null && customAttributes?.components.length > 0) {*/
        //console.table(model);
        /* if (customAttributes.components[1].key == 'meetingType') {*/
        if (model.categoryId == window.MeetingAgendaId || model.categoryId == window.MeetingResolutionId
            || model.categoryId == window.MeetingMinutesId) {
            //$('#' + self.model.ComponentId + '_btnAttributeTransfer').hide();
            $('#' + self.model.ComponentId + '_btnAttributeTransfer').hide();
            $('#' + self.model.ComponentId + '_btnAttributeSend').hide();

            //$('#' + self.model.ComponentId + '_btnStartWorkflow1').hide(); 
            $('#' + self.model.ComponentId + '_btnStartWorkflow1').hide();
            if ((actionArray.includes("Attribute.AttributeProceed"))) {
                //$('#' + self.model.ComponentId + '_btnAttributeProceed').show();
                $('#' + self.model.ComponentId + '_btnAttributeProceed').show();
            }
        }

        else {
            $('#' + self.model.ComponentId + '_btnAttributeTransfer').show();
            $('#' + self.model.ComponentId + '_btnAttributeTransfer').show();

            $('#' + self.model.ComponentId + '_btnAttributeSend').show();

            $('#' + self.model.ComponentId + '_btnStartWorkflow1').show();
            $('#' + self.model.ComponentId + '_btnStartWorkflow1').show();

            $('#' + self.model.ComponentId + '_btnAttributeProceed').hide();
            $('#' + self.model.ComponentId + '_btnAttributeProceed').hide();
        }
        //}
        if (model.referenceNumber !== null && model.referenceNumber !== "") {

            //$(self.refs['btnAttributeSave']).hide();
            if (!(window.location.hash.includes("#search") || window.location.hash.includes("#manageCorrespondence") || window.location.hash.toLowerCase().includes("#inbox") || window.location.hash.includes("#StructureInbox") || window.location.hash.includes("#Draft"))) {
                $('#' + self.model.ComponentId + '_btnAttributeSave').hide();
                $('#' + self.model.ComponentId + '_btnAttributeRegister').hide();
                $(self.refs['btnAttributeSave']).hide();
                $(self.refs['btnAttributeRegister']).hide();


            }
            else if (window.location.hash.toLowerCase().includes("#inbox") || window.location.hash.includes("#StructureInbox")) {
                $('#' + self.model.ComponentId + '_btnAttributeSave').addClass("show");
                $('#' + self.model.ComponentId + '_btnAttributeRegister').hide();
            }
            else if (window.location.hash.includes("#Draft")) {
                $('#' + self.model.ComponentId + '_btnAttributeSave').addClass("show");
                $('#' + self.model.ComponentId + '_btnAttributeRegister').hide();
            }
            else {
                $('#' + self.model.ComponentId + '_btnAttributeSave').addClass("show");
                $('#' + self.model.ComponentId + '_btnAttributeRegister').addClass("show");
            }

            $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
            $('#' + self.model.ComponentId + '_btnAttributeRegister').addClass("btn-primary");
            $('#' + self.model.ComponentId + '_btnAttributeRegister').removeClass("btn-info");
            $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("data-loading-text", "<i class='fa fa-spinner fa-spin'></i>" + Resources.Saving);
            $('#' + self.model.ComponentId + '_btnAttributeRegister').text(Resources.Save).button("refresh");
            if (window.OpenCorrespondenceMode != "OpenCorrespondenceWithViewer") {
                $(self.refs['documentBarcodeContainer']).removeClass('hidden');
                $(self.refs['documentBarcodeContainer']).find(".defaultWithViewer").removeClass('hidden');
            }
            $(self.refs['lnkDocumentBarcodeView']).html("<i class='fa fa-barcode mr-sm'></i>" + model.referenceNumber);
           
        }
        if (self.model.fromRejectedDocument) {
            $(self.refs['btnAttributeSave']).show();
        }
        if (model.userStructures.length === 1) {
            $(self.refs['customAttributeStructureContainer']).hide();
            $(".barcodeDiv").removeClass("mt-33");
        }
        $(self.refs['cmbUserStructures']).select2({
            minimumInputLength: 0,
            allowClear: false,
            placeholder: "",
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            dropdownParent: $(self.refs['customAttributeStructureContainer']),
            width: "100%",
            data: structureDataForSelect2(model.userStructures),
            sorter: data => data.sort((a, b) => a.text.localeCompare(b.text))
        });

        if (window.EnablePerStructure) {
            $(self.refs['customAttributeStructureContainer']).hide();
        }
        else {
            $(self.refs['customAttributeStructureContainer']).show();
        }

        $(self.refs['cmbUserStructures']).on("select2:select", function (e) {
            createStructuresSelect2(basicAttributes, model, self);
            if (window.EnableSendingRules === "True") {
                $(self.refs['cmbCustomAttributeSender']).val('').trigger('change');
                $(self.refs['cmbCustomAttributeReceiver']).val('').trigger('change');
                $(self.refs['cmbCustomAttributeCarbonCopy']).val('').trigger('change');
            }
        });
        if (!window.EnablePerStructure && sessionStorage.getItem($("#hdUserId").val() + "SelectedUserStrcuture") !== null) {
            $(self.refs['cmbUserStructures']).val(sessionStorage.getItem($("#hdUserId").val() + "SelectedUserStrcuture")).trigger("change");
            $(self.refs['cmbCustomAttributeSender']).val(sessionStorage.getItem($("#hdUserId").val() + "SelectedUserStrcuture")).trigger("change");
            $(self.refs['cmbCustomAttributeReceiver']).val(sessionStorage.getItem($("#hdUserId").val() + "SelectedUserStrcuture")).trigger("change");
        } else if (model.createdByStructureId !== null) {
            $(self.refs['cmbUserStructures']).val(model.createdByStructureId).trigger("change");
            $(self.refs['cmbCustomAttributeSender']).val(model.createdByStructureId).trigger("change");
            $(self.refs['cmbCustomAttributeReceiver']).val(model.createdByStructureId).trigger("change");

        }
        if (basicAttributes.length > 0) {
            var dueDateRelatedToPriority = $.grep(basicAttributes, function (e) {
                return e.Name === "DueDate";
            });
            gIsDueDateRelatedToPriority = dueDateRelatedToPriority[0].RelatedToPriority;
        }
        $(self.refs['cmbCustomAttributePriority']).select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            dropdownParent: $(self.refs['customAttributePriorityContainer'])
        }).on('change', function () {
            $(this).trigger('input');
        });
        $(self.refs['cmbCustomAttributePriority']).on('select2:open', function (e) {
            const evt = "scroll.select2";
            $(e.target).parents().off(evt);
            $(window).off(evt);
        });


        var customAttributeDueDate = $(self.refs['customAttributeDueDate']).flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            minDate: model.categoryId == window.FollowUpCategory ? model.dueDate:"today"
        });
        $(self.refs["customAttributeDueDate_img"]).on('click', function () {
            customAttributeDueDate.toggle();

        });
        var documentDate = this.model.documentDate;
        $(document).ready(function () {

            var formattedDate = documentDate ? documentDate : new Date().toLocaleDateString('en-GB');
            var customAttributedd = $(self.refs['customAttributeDocumentDate']).flatpickr({
                noCalendar: false,
                enableTime: false,
                dateFormat: 'd/m/Y',
                defaultDate: formattedDate
            });


            var customAttributeDocumentDate = $(self.refs['customAttributeDocumentDate']);
            customAttributeDocumentDate.val(formattedDate);
            $(self.refs["customAttributeDocumentDate_img"]).on('click', function () {
                customAttributedd.toggle();

            });

        });
       
        var createdDate = this.model.createdDate;
        var customAttributeCreatedDate = createdDate ? createdDate : new Date().toLocaleDateString('en-GB');
   
        $(self.refs['customAttributeCreatedDate']).val(customAttributeCreatedDate);


 
        //if (this.model.transferId == null) {
        //    var headers = {};
        //    headers.Authorization = 'Bearer ' + window.IdentityAccessToken;
        //    var userid = parseInt($("#hdUserId").val());
        //    Common.ajaxGetWithHeaders(window.IdentityUrl + '/api/GetUser', { "id": userid }, function (response) {

        //        $("#createdBy").val(response.fullName);

        //    }, function (msg) {
        //        console.log(msg);
        //    }, false, "", headers, false);


        //} else {
            var createdByUser = this.model.createdByUser;
            $("#createdBy").val(createdByUser);
        //}
     

        if (gIsDueDateRelatedToPriority) {
            $(self.refs['cmbCustomAttributePriority']).on("change", function (e) {
                var dueDays = $('option:selected', this).attr('data-duedays');
                if (typeof dueDays !== "undefined") {
                    $(".workingDaysLabel").text(dueDays);
                    customAttributeDueDate.clear();
                    customAttributeDueDate.setDate(addBusinessDays(new Date(), dueDays));
                    $(".workingDaysLabel").show();
                } else {
                    customAttributeDueDate.clear();
                    $(".workingDaysLabel").hide();
                }
            });
        } else {

            $(self.refs['customAttributeDueDate_img']).click(function () {
                customAttributeDueDate.toggle();
            });
        }
        Common.ajaxGet("Priority/List", null, function (data) {
            prioritiesList = data;
            for (var i = 0; i < data.length; i++) {
                var text = data[i].name;
                if (window.language === "ar" && data[i].nameAr !== "") {
                    text = data[i].nameAr;
                } else if (window.language === "fr" && data[i].nameFr !== "") {
                    text = data[i].nameFr;
                }
                prioritiesList[i].text = text;
                $("<option></option>").attr("value", data[i].id).attr("data-dueDays", data[i].numberOfDueDays).text(text).appendTo(self.refs['cmbCustomAttributePriority']);
            }
        }, function () { Common.showScreenErrorMsg(); }, null, null, false);
        $(self.refs['cmbCustomAttributePrivacy']).select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            dropdownParent: $(self.refs['customAttributePrivacyContainer'])
        }).on('change', function () {
            $(this).trigger('input');
        });
        $(self.refs['cmbCustomAttributePrivacy']).on('select2:open', function (e) {
            const evt = "scroll.select2";
            $(e.target).parents().off(evt);
            $(window).off(evt);
        });
        createStructuresSelect2(basicAttributes, model, self);
        if ($(self.refs['cmbCustomAttributeImportance']).length > 0) {
            $(self.refs['cmbCustomAttributeImportance']).select2({
                dir: window.language === "ar" ? "rtl" : "ltr",
                language: window.language,
                width: "100%",
                allowClear: true,
                placeholder: "",
                dropdownParent: $(self.refs['customAttributeImportanceContainer']),
                data: model.importances
            });

            $(self.refs['cmbCustomAttributeImportance']).on('select2:open', function (e) {
                const evt = "scroll.select2";
                $(e.target).parents().off(evt);
                $(window).off(evt);
            });
        }
        var SendingStructureUrl = '';
        var sendingEntityObjj = $.grep(basicAttributes, function (e) {
            return e.Name === "SendingEntity";
        });
        if (sendingEntityObjj[0].Type === "external") {
            SendingStructureUrl = 'CTS/DesignatedPerson/GetDesignatedPerson';
        }
        else {
            SendingStructureUrl = window.IdentityUrl + '/api/SearchUsersByStructureIds';
        }
        var ReceivingStructureUrl = '';
        var receivingEntityObjj = $.grep(basicAttributes, function (e) {
            return e.Name === "ReceivingEntity";
        });
        if (receivingEntityObjj[0].Type === "external") {
            ReceivingStructureUrl = 'CTS/DesignatedPerson/GetDesignatedPerson';
        }
        else {
            ReceivingStructureUrl = window.IdentityUrl + '/api/SearchUsersByStructureIds';
        }
        $(self.refs['cmbCustomAttributeSenderPerson']).select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: true,
            placeholder: "",
            dropdownParent: $(self.refs['customAttributeSenderPersonContainer']),
            ajax: {
                type: 'POST',
                url: SendingStructureUrl,
                headers: {
                    "Authorization": 'Bearer ' + window.IdentityAccessToken
                },
                dataType: 'json',
                delay: 400,
                data: function (params) {
                    var query = {
                        'ids': $(self.refs['cmbCustomAttributeSender']).val(),
                        'searchText': params.term || "",
                        'language': "",
                        'showOnlyActiveUsers': false
                    };
                    return query;
                },
                processResults: function (data) {
                    var listitemsMultiList = [];
                    $.each(data, function (key, val) {
                        let isExist = listitemsMultiList.some(function (usr) { return usr.id === val.id; });
                        if (!isExist) {
                            var item = {};
                            item.id = val.id;
                            item.text = val.fullName;
                            if (item.id !== Number(self.model.userId)) {
                                listitemsMultiList.push(item);
                            }
                        }
                    });
                    return {
                        results: listitemsMultiList
                    };
                }
            }
        });
        $(self.refs["cmbCustomAttributeSenderPerson"]).val('').trigger('change');
        $(self.refs['cmbCustomAttributeReceiverPerson']).select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            multiple: true,
            width: "100%",
            allowClear: true,
            placeholder: "",
            dropdownParent: $(self.refs['customAttributeReceiverPersonContainer']),
            ajax: {
                type: 'POST',
                url: ReceivingStructureUrl,
                headers: {
                    "Authorization": 'Bearer ' + window.IdentityAccessToken
                },
                dataType: 'json',
                delay: 400,
                data: function (params) {
                    var query = {
                        'ids': $(self.refs['cmbCustomAttributeReceiver']).val(),
                        'searchText': params.term || "",
                        'language': "",
                        'showOnlyActiveUsers': false
                    };
                    return query;
                },
                processResults: function (data) {
                    var listitemsMultiList = [];
                    $.each(data, function (key, val) {
                        let isExist = listitemsMultiList.some(function (usr) { return usr.id === val.id; });
                        if (!isExist) {
                            var item = {};
                            item.id = val.id;
                            item.text = val.fullName;
                            if (item.id !== Number(self.model.userId)) {
                                listitemsMultiList.push(item);
                            }
                        }
                    });
                    return {
                        results: listitemsMultiList
                    };
                }
            }
        });
        $(self.refs["cmbCustomAttributeReceiverPerson"]).val('').trigger('change');

        if ($(self.refs['cmbCustomAttributeClassification']).length > 0) {
            $(self.refs['cmbCustomAttributeClassification']).select2({
                dir: window.language === "ar" ? "rtl" : "ltr",
                language: window.language,
                width: "100%",
                allowClear: true,
                placeholder: "",
                dropdownParent: $(self.refs['customAttributeClassificationContainer']),
                ajax: {
                    url: '/Classification/ListClassifications',
                    dataType: 'json',
                    'type': 'GET',
                    delay: 250,
                    data: function (params) {
                        var query = {
                            '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val(),
                            'searchText': params.term || ""
                        };
                        return query;
                    },
                    processResults: function (data) {
                        return {
                            results: data
                        };
                    }
                },
                sorter: data => data.sort((a, b) => a.text.localeCompare(b.text))
            });
            $(self.refs['cmbCustomAttributeClassification']).on('select2:open', function (e) {
                const evt = "scroll.select2";
                $(e.target).parents().off(evt);
                $(window).off(evt);
            });
        }
        if ($(self.refs['cmbCustomAttributeDocumentType']).length > 0) {
            $(self.refs['cmbCustomAttributeDocumentType']).select2({
                dir: window.language === "ar" ? "rtl" : "ltr",
                language: window.language,
                width: "100%",
                allowClear: true,
                placeholder: "",
                dropdownParent: $(self.refs['customAttributeDocumentTypeContainer']),
                ajax: {
                    url: '/DocumentType/GetDocumentType',
                    dataType: 'json',
                    'type': 'GET',
                    delay: 250,
                    data: function (params) {
                        var query = {
                            '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val(),
                            'text': params.term || ""
                        };
                        return query;
                    },
                    processResults: function (data) {
                        return {
                            results: data
                        };
                    }
                },
                sorter: data => data.sort((a, b) => a.text.localeCompare(b.text))
            });
            $(self.refs['cmbCustomAttributeDocumentType']).on('select2:open', function (e) {
                const evt = "scroll.select2";
                $(e.target).parents().off(evt);
                $(window).off(evt);
            });
        }
        if (model.subject !== null) {
            $(self.refs['txtCustomAttributeSubject']).val(model.subject);
        } else if (basicAttributes.length > 0) {
            var subjectDefaultValue = $.grep(basicAttributes, function (e) {
                return e.Name === "Subject";
            });
            $(self.refs['txtCustomAttributeSubject']).val(subjectDefaultValue[0].DefaultValue);

            var documentTypeEnable = $.grep(basicAttributes, function (e) {
                return e.Name === "DocumentType";
            });
         
            var documentTypeContainer = self.refs['customAttributeDocumentTypeContainer'];

            if (documentTypeContainer) {
                var labelElement = documentTypeContainer.querySelector('label.control-label');

                var documentTypeEnable = $.grep(basicAttributes, function (e) {
                    return e.Name === "DocumentType";
                });
   
                if (documentTypeEnable.length > 0) {
                    var requiredDocumentType = $.grep(basicAttributes, function (e) {
                        return e.required === true;
                    });
                    const selectElement = $(self.refs['cmbCustomAttributeDocumentType']);
                    if (requiredDocumentType.length > 0) {
                        labelElement.classList.add('field-required'); 
                        selectElement.attr('data-parsley-required', 'true');
                    }
                    else {
                        labelElement.classList.remove('field-required');
                    }
                }

            }
        }

        if (model.priorityId !== null) {
            $(self.refs['cmbCustomAttributePriority']).val(model.priorityId).trigger("change");
        }
        if (self.model.senderPerson !== 0) {
            var structureIds = $(self.refs['cmbCustomAttributeSender']).val();
            getUsersOnStructures(structureIds, self.model.isExternalSender, function (userList) {
                if (userList.length > 0) {
                    for (var i = 0; i < userList.length; i++) {
                        if (userList[i].id == self.model.senderPerson) {
                            var option = new Option(userList[i].text, self.model.senderPerson.toString(), true, true);
                            $(self.refs['cmbCustomAttributeSenderPerson']).append(option).trigger("change");
                            break;
                        }
                    }
                }

            });
        }
        if (self.model.receiverPerson !== null && self.model.receiverPerson !== "") {
            var structureIds = $(self.refs['cmbCustomAttributeReceiver']).val();
            getUsersOnStructures(structureIds, self.model.isExternalReceiver, function (userList) {
                var JsonIds = JSON.parse(self.model.receiverPerson);
                var idsList = JsonIds.Id.split('-').map(Number);
                if (userList.length > 0) {
                    for (var i = 0; i < userList.length; i++) {
                        if (idsList.includes(userList[i].id)) {
                            var option = new Option(userList[i].text, userList[i].id.toString(), true, true);
                            $(self.refs['cmbCustomAttributeReceiverPerson']).append(option).trigger("change");
                        }
                    }
                }

            });
        }
        else if (model.priorityId !== null) {
            $(self.refs['cmbCustomAttributePriority']).val(model.priorityId).trigger("change");
        }
        else if (basicAttributes.length > 0) {
            var priorityDefaultValue = $.grep(basicAttributes, function (e) {
                return e.Name === "Priority";
            });
            $(self.refs['cmbCustomAttributePriority']).val(priorityDefaultValue[0].DefaultValue).trigger("change");
        }
        if (model.privacyId !== null) {
            $(self.refs['cmbCustomAttributePrivacy']).val(model.privacyId).trigger("change");
        } else if (basicAttributes.length > 0) {
            var privacyDefaultValue = $.grep(basicAttributes, function (e) {
                return e.Name === "Privacy";
            });
            $(self.refs['cmbCustomAttributePrivacy']).val(privacyDefaultValue[0].DefaultValue).trigger("change");
        }
        if (model.dueDate !== null && model.dueDate != "") {
            if (customAttributeDueDate.length != 0)
                customAttributeDueDate.setDate(model.dueDate);
        }

      
        if (model.externalReferenceNumber !== null) {
            $(self.refs['txtCustomAttributeExternalReferenceNumber']).val(model.externalReferenceNumber);
        }
        if (model.importanceId !== null) {
            $(self.refs['cmbCustomAttributeImportance']).val(model.importanceId).trigger("change");
        } else {
            $(self.refs['cmbCustomAttributeImportance']).val('').trigger("change");
        }
        if (model.classification !== null && model.classification.id !== 0) {
            checkAddSelectValue("cmbCustomAttributeClassification", model.classification.id, model.classification.text, self);
        }
        if (model.documentType !== null && model.documentType.id !== 0) {
            checkAddSelectValue("cmbCustomAttributeDocumentType", model.documentType.id, model.documentType.text, self);
        }
        if (model.body !== null) {
            $(self.refs['txtCustomAttributeBody']).val(model.body);
        }
        if (model.keyword !== null) {
            var tags = model.keyword.split(window.Splitter);
            for (var i = 0; i < tags.length; i++) {
                $(self.refs['txtCustomAttributeKeyword']).tagsinput('add', tags[i]);
            }
        }
        var $form = $(self.refs['formDocumentAttributePost']);
        $form.parsley().reset();
        if (!$form.parsley().isValid()) {
            $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", true);
            $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", true);
            $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", true);
            $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", true);
            $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", true);
            $('#' + self.model.ComponentId + '_btnAttributeExport').attr("disabled", true);
            $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", true)
            $('#btnGroupDropup').attr('disabled', true);
           
        } else {
            $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
            $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
            $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
            $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
            $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
            $('#' + self.model.ComponentId + '_btnAttributeExport').attr("disabled", false);
            $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", false)

            $('#btnGroupDropup').attr('disabled', false);


        }

        $('#btnSubmitSign').click(function () {
            Common.unmask("body-mask");
            templateId = 0;
            if (fromSignOnly || fromSignAndTransfer) {
                fromSignOnly = false;
                fromSignAndTransfer = false;
                Common.mask(document.body, "body-mask");
            }
            const selectedSignature = $('input[name="rdbSignature"]:checked');
            templateId = selectedSignature.val();

            if (fromPeview) {
                getDocumentIdInOriginalMail(self.model.id, function (attachmentId) {
                    if (templateId == undefined || templateId == 0) {
                        Common.alertMsg(Resources.SignatureRequired);
                        Common.unmask("body-mask");
                        return;
                    }
                    validateAndPreviewAttachment(attachmentId, templateId,self);
                    fromPeview = false;
                    templateId = 0;
                    $("#modalSignatureTemplates").modal("hide");
                });
            }
            else {
                signDocument(self, signType, templateId);
                templateId = 0;// if not in the else it will run before the callback of   getDocumentIdInOriginalMail(self.model.documentId, function (attachmentId) {
            }                    //if (templateId == undefined || templateId == 0) { and it will be zero so it will not preview attachment

        })


        $('#' + self.model.ComponentId + '_btnSignTemplate').click( async function () {
            Common.mask(document.body, "body-mask");
            const certMessage = await CheckUserCertificate();
            if (certMessage) {
                Common.unmask("body-mask");
                Common.alertMsg(certMessage);
                $(self.refs['btnSignTemplate']).attr("disabled", false);
                $(self.refs['btnGroupDropup']).attr("disabled", false);
                $(self.refs['btnSignTemplate']).button('reset');
                $(self.refs['btnGroupDropup']).button('reset');
                $('#btnGroupDropup').prop('disabled', false);
                return;
            }
            
            var documentViewModel = getFormData(model.categoryId, false, model.multipleReceivingEntity, self);
            Common.ajaxPost(saveOptions.url, documentViewModel, function (response) {
                gLocked = false;
                if (response === "OriginalFileInUse") {
                    Common.unmask("body-mask");
                    Common.alertMsg(Resources.OriginalFileInUse);
                    $(self.refs['btnAttributeSave']).attr("disabled", false);
                    $(self.refs['btnAttributeSave']).button('reset');
                    $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
                    $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
                    $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                    $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                    $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
                    $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
                } else if (response === "FileInUse") {
                    Common.unmask("body-mask");
                    Common.alertMsg(Resources.FileInUse);
                    $(self.refs['btnAttributeSave']).attr("disabled", false);
                    $(self.refs['btnAttributeSave']).button('reset');
                    $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
                    $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
                    $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                    $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                    $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
                    $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
                } else if (response === "ExternalReferencedNumberInUse") {
                    Common.unmask("body-mask");
                    Common.alertMsg(Resources.ExternalReferencedNumberInUse);
                    $(self.refs['btnAttributeSave']).attr("disabled", false);
                    $(self.refs['btnAttributeSave']).button('reset');
                    $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
                    $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
                    $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                    $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                    $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
                    $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
                }
                else if (response == "") { 
                        $(".documentHeader span").before(" - " + response + " ");
                        $(self.refs['btnAttributeSave']).hide();
                        $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                        $('#' + self.model.ComponentId + '_btnAttributeRegister').addClass("btn-primary");
                        $('#' + self.model.ComponentId + '_btnAttributeRegister').removeClass("btn-info");
                        $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("data-loading-text", "<i class='fa fa-spinner fa-spin'></i>" + Resources.Saving);
                        $('#' + self.model.ComponentId + '_btnAttributeRegister').text(Resources.Save).button("refresh");
                    if (window.OpenCorrespondenceMode != "OpenCorrespondenceWithViewer") {

                        $(self.refs['documentBarcodeContainer']).removeClass('hidden');
                        $(self.refs['documentBarcodeContainer']).find(".defaultWithViewer").removeClass('hidden');
                    }
                    $(self.refs['lnkDocumentBarcodeView']).html("<i class='fa fa-barcode mr-sm'></i>" + response);
                        model.referenceNumber = response;
                        $('[id*="_refNumberPanel"]').removeClass('hidden');
                    $('[id*="_refNumberPanelHeader"]').text(model.referenceNumber);
                    if (self.model.fromVip) {
                        $(".withBorders-o").addClass("waitingBackground");
                        $("#draftDocumentDetailsContainer").empty();
                        $($("input[data-id='" + self.model.id + "']").parents("li")[0]).fadeOut().remove();
                    } else {
                        GridCommon.Refresh("grdDraftItems");
                    }
                    if (fromSignAndTransfer) {
                        signType = 2;
                        fromSignAndTransfer = false;
                    }
                    else if (fromSignAndExport) {
                        signType = 7;
                        fromSignAndExport = false;
                    }
                    else
                        signType = 0;
                    $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", true);
                    $('#' + self.model.ComponentId + '_btnCompleteAndArchive').attr("disabled", true);
                    $('#' + self.model.ComponentId + '_btnReplyToStructure').attr("disabled", true);
                    $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", true);
                    $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", true);
                    $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", true);
                    $('#' + self.model.ComponentId + '_btnStartWorkflow').attr("disabled", true);
                    $('#btnGroupDropup').attr('disabled', true);

                    if (templateId != 0) {
                        //$('#btnSubmitSign').click();
                        signDocument(self, signType, templateId,model);
                    }
                    else {

                        $.ajax({
                            method: "GET",
                            url: window.DSURL + "/api/signature/template",
                            headers: {
                                "Authorization": "Bearer " + IdentityAccessToken
                            },
                            success: function (res) {
                                $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnComplete').button('reset');
                                $('#' + self.model.ComponentId + '_btnCompleteAndArchive').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnCompleteAndArchive').button('reset');
                                $('#' + self.model.ComponentId + '_btnReplyToStructure').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnReplyToStructure').button('reset');
                                $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnReplyToUser').button('reset');
                                $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                                $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnSignTemplate').button('reset');
                                $('#' + self.model.ComponentId + '_btnStartWorkflow').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnStartWorkflow').button('reset');
                                $(self.refs['btnSignTemplate']).attr("disabled", false);
                                $(self.refs['btnGroupDropup']).attr("disabled", false);
                                $(self.refs['btnSignTemplate']).button('reset');
                                $(self.refs['btnGroupDropup']).button('reset');
                                $('#btnGroupDropup').prop('disabled', false);

                                Common.unmask("body-mask");
                                if (res.length == 0) {
                                    Common.alertMsg(Resources.NoTemplatesFound);

                                }
                                else if (res.length == 1) {
                                    signDocument(self, signType, res[0].id,model);
                                }
                                else {
                                    $('#modalSignatureTemplates').modal('show');
                                    $("#modalSignatureTemplates").off("hidden.bs.modal");
                                    $("#modalSignatureTemplates").off("shown.bs.modal");
                                    $("#tblSignature tbody").html("");

                                    $(self.refs['modalCTSPurpose']).on('hidden.bs.modal', function () {
                                        swal.close();
                                    });

                                    for (let j = 0; j < res.length / 2; j++) {
                                        $("#tblSignature tbody").append('<tr id="tr' + j + '"></tr>');
                                    }
                                    var count = 0, k = 0;

                                    $.each(res, function (i, val) {
                                        $("#tr" + k).append('<td style="width: 250px;"><div class="form-check"><input class="form-check-input" style="margin:6px;" type="radio" name="rdbSignature" id="rdb' + val.id + '" value="' + val.id + '"><label for="rdb' + val.id + '"><img for="rdb' + val.id + '" id="img' + val.id + '"src=""  class="rounded mx-auto d-block" alt="' + val.name + '" width="75%"></label></div></td>');

                                        $("#img" + val.id).attr("src", window.DSURL + "/api/signature/template/" + val.id + "/image?token=" + IdentityAccessToken);

                                        if ((count + 1) % 2 == 0)
                                            k++;

                                        count++;
                                    });
                                }
                                //Common.unmask("body-mask");
                            },
                            error: function (res) {
                                $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnComplete').button('reset');
                                $('#' + self.model.ComponentId + '_btnReplyToStructure').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnReplyToStructure').button('reset');
                                $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnReplyToUser').button('reset');
                                $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                                $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnSignTemplate').button('reset');
                                $(self.refs['btnSignTemplate']).attr("disabled", false);
                                $(self.refs['btnGroupDropup']).attr("disabled", false);
                                $(self.refs['btnSignTemplate']).button('reset');
                                $(self.refs['btnGroupDropup']).button('reset');
                                $('#btnGroupDropup').prop('disabled', false);
                                Common.showScreenErrorMsg();
                                Common.unmask("body-mask");
                            }
                        });

                    }

                }
            }, function () {
                $(self.refs['btnAttributeSave']).attr("disabled", false);
                $(self.refs['btnAttributeSave']).button('reset');
                $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
                $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
                $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
                $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
                Common.showScreenErrorMsg();
                gLocked = false;
            });

        
        });

        $('#' + self.model.ComponentId + '_btnSignAndSend').click( async function () {
            Common.mask(document.body, "body-mask");
            const certMessage = await CheckUserCertificate();
            if (certMessage) {
                Common.unmask("body-mask");
                Common.alertMsg(certMessage);
                $(self.refs['btnSignTemplate']).attr("disabled", false);
                $(self.refs['btnGroupDropup']).attr("disabled", false);
                $(self.refs['btnSignTemplate']).button('reset');
                $(self.refs['btnGroupDropup']).button('reset');
                $('#btnGroupDropup').prop('disabled', false);
                return;
            }
            if (templateId != 0) {

                Common.ajaxGet('/Document/CheckDocumentAttachmentLocked/', { "documentId": self.model.id }, function (retval) {
                    if (retval) {
                        Common.unmask("body-mask");
                        Common.alertMsg(Resources.FileInUse);

                    } else {
                        signType = 1;
                        $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnReplyToStructure').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", true);
                        $('#btnGroupDropup').attr('disabled', true);    

                        signDocument(self, signType, templateId);

                    }
                })
            } else {
                Common.ajaxGet('/Document/CheckDocumentAttachmentLocked/', { "documentId": self.model.id }, function (retval) {
                    if (retval) {
                        Common.unmask("body-mask");
                        Common.alertMsg(Resources.FileInUse);

                    } else {
                        signType = 1;
                        $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnReplyToStructure').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", true);
                        $('#btnGroupDropup').attr('disabled', true);    


                        $.ajax({
                            method: "GET",
                            url: window.DSURL + "/api/signature/template",
                            headers: {
                                "Authorization": "Bearer " + IdentityAccessToken
                            },
                            success: function (res) {
                                $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnComplete').button('reset');
                                $('#' + self.model.ComponentId + '_btnReplyToStructure').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnReplyToStructure').button('reset');
                                $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnReplyToUser').button('reset');
                                $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                                $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnSignTemplate').button('reset');
                                $('#btnGroupDropup').attr("disabled", false);
                                $('#btnGroupDropup').button('reset');
                                
                                if (res.length == 0) {
                                    Common.alertMsg(Resources.NoTemplatesFound);
                                    Common.unmask("body-mask");
                                }
                                else if (res.length == 1) {

                                    sendToReceivingEntityMyTransfer(self.model.replyToEntity, self.model.delegationId, TransferType.Send, self.model.fromUser, self, res[0].id, true);

                                }
                                else {
                                    $('#modalSignatureTemplates').modal('show');
                                    $("#modalSignatureTemplates").off("hidden.bs.modal");
                                    $("#modalSignatureTemplates").off("shown.bs.modal");
                                    $("#tblSignature tbody").html("");

                                    $(self.refs['modalCTSPurpose']).on('hidden.bs.modal', function () {
                                        swal.close();
                                    });

                                    for (let j = 0; j < res.length / 2; j++) {
                                        $("#tblSignature tbody").append('<tr id="tr' + j + '"></tr>');
                                    }
                                    var count = 0, k = 0;

                                    $.each(res, function (i, val) {
                                        $("#tr" + k).append('<td style="width: 250px;"><div class="form-check"><input class="form-check-input" type="radio" style="margin:6px;" name="rdbSignature" id="rdb' + val.id + '" value="' + val.id + '"><label for="rdb' + val.id + '"><img for="rdb' + val.id + '" id="img' + val.id + '"src=""  class="rounded mx-auto d-block" alt="' + val.name + '" width="75%"></label></div></td>');

                                        $("#img" + val.id).attr("src", window.DSURL + "/api/signature/template/" + val.id + "/image?token=" + IdentityAccessToken);

                                        if ((count + 1) % 2 == 0)
                                            k++;

                                        count++;
                                    });
                                    Common.unmask("body-mask");
                                }

                            },
                            error: function (res) {
                                $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnComplete').button('reset');
                                $('#' + self.model.ComponentId + '_ReplyToStructure').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_ReplyToStructure').button('reset');
                                $('#' + self.model.ComponentId + '_ReplyToUser').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_ReplyToUser').button('reset');
                                $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                                $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnSignTemplate').button('reset');
                                $('#btnGroupDropup').attr("disabled", false);
                                $('#btnGroupDropup').button('reset');
                             
         
                                Common.showScreenErrorMsg();
                                Common.unmask("body-mask");
                            }

                        });

                    }
                });
            }


        });

        $('#' + self.model.ComponentId + '_btnSignAndTransfer').click( async function () {
            Common.mask(document.body, "body-mask");
            const certMessage = await CheckUserCertificate();
            if (certMessage) {
                Common.unmask("body-mask");
                Common.alertMsg(certMessage);
                $(self.refs['btnSignTemplate']).attr("disabled", false);
                $(self.refs['btnGroupDropup']).attr("disabled", false);
                $(self.refs['btnSignTemplate']).button('reset');
                $(self.refs['btnGroupDropup']).button('reset');
                $('#btnGroupDropup').prop('disabled', false);
                return;
            }
            fromSignAndTransfer = true;
            if (templateId != 0) {
                Common.ajaxGet('/Document/CheckDocumentAttachmentLocked/', { "documentId": self.model.id }, function (retval) {
                    //Common.unmask("body-mask");
                    if (retval) {
                        Common.unmask("body-mask");
                        Common.alertMsg(Resources.FileInUse);

                    } else {
                        signType = 2;
                        $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnReplyToStructure').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", true);
                        $('#btnGroupDropup').attr('disabled', true);
                        //signDocument(self, signType, templateId);
                        $('#' + self.model.ComponentId + '_btnSignTemplate').click();
                    }
                })
            }
            else {
                Common.mask(document.body, "body-mask");
                Common.ajaxGet('/Document/CheckDocumentAttachmentLocked/', { "documentId": self.model.id }, function (retval) {
                    //Common.unmask("body-mask");
                    if (retval) {
                        Common.unmask("body-mask");
                        Common.alertMsg(Resources.FileInUse);
                    } else {
                        signType = 2;
                        $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnReplyToStructure').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", true);
                        $('#btnGroupDropup').attr('disabled', true);
                        $('#' + self.model.ComponentId + '_btnSignTemplate').click();

                    }
                })
            }

        });

        $('#' + self.model.ComponentId + '_btnSignAndExport').click(async function () {
            Common.mask(document.body, "body-mask");
            const certMessage = await CheckUserCertificate();
            if (certMessage) {
                Common.unmask("body-mask");
                Common.alertMsg(certMessage);
                $(self.refs['btnSignTemplate']).attr("disabled", false);
                $(self.refs['btnGroupDropup']).attr("disabled", false);
                $(self.refs['btnSignTemplate']).button('reset');
                $(self.refs['btnGroupDropup']).button('reset');
                $('#btnGroupDropup').prop('disabled', false);
                return;
            }
            fromSignAndExport = true;
            if (templateId != 0) {
                Common.ajaxGet('/Document/CheckDocumentAttachmentLocked/', { "documentId": self.model.id }, function (retval) {
                    
                    if (retval) {
                        Common.unmask("body-mask");
                        Common.alertMsg(Resources.FileInUse);

                    } else {
                        signType = 7;
                        $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnReplyToStructure').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", true);
                        $('#btnGroupDropup').attr('disabled', true); 
                        signDocument(self, signType, templateId);
                    }
                })
            }
            else {
                Common.mask(document.body, "body-mask");
                Common.ajaxGet('/Document/CheckDocumentAttachmentLocked/', { "documentId": self.model.id }, function (retval) {
                        Common.unmask("body-mask");
                    if (retval) {
                        Common.alertMsg(Resources.FileInUse);

                    } else {
                        signType = 7;
                        $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnReplyToStructure').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", true);
                        $('#btnGroupDropup').attr('disabled', true); 
                        $('#' + self.model.ComponentId + '_btnSignTemplate').click();

                    }
                })
            }

        });

        $('#' + self.model.ComponentId + '_btnPreviewBeforeSign').click(function () {
            Common.mask(document.body, "body-mask");
            if (templateId != 0) {
                Common.ajaxGet('/Document/CheckDocumentAttachmentLocked/', { "documentId": self.model.id }, function (retval) {
                        Common.unmask("body-mask");
                    if (retval) {
                        Common.alertMsg(Resources.FileInUse);

                    } else {
                        signType = 6;
                        $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnReplyToStructure').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", true);
                        $('#btnGroupDropup').attr('disabled', true); 

                        getDocumentIdInOriginalMail(self.model.id, function (attachmentId) {
                            validateAndPreviewAttachment(attachmentId, templateId,self);
                        });

                    }
                })



            } else {
                Common.ajaxGet('/Document/CheckDocumentAttachmentLocked/', { "documentId": self.model.id }, function (retval) {
                        Common.unmask("body-mask");
                    if (retval) {
                        Common.alertMsg(Resources.FileInUse);

                    }
                    else {
                        signType = 6;
                        $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnReplyToStructure').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", true);
                        $('#btnGroupDropup').attr('disabled', true); 


                        $.ajax({
                            method: "GET",
                            url: window.DSURL + "/api/signature/template",
                            headers: {
                                "Authorization": "Bearer " + IdentityAccessToken
                            },
                            success: function (res) {
                                $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnComplete').button('reset');
                                $('#' + self.model.ComponentId + '_btnReplyToStructure').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnReplyToStructure').button('reset');
                                $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnReplyToUser').button('reset');
                                $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                                $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnSignTemplate').button('reset');
                                $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnSignTemplate').button('reset');
                                $('#btnGroupDropup').attr('disabled', false);
                                $('#btnGroupDropup').button('reset');
                                $('#btnSubmitSign').attr("disabled", false);
                                $('#btnSubmitSign').button('reset');

                                if (res.length == 0) {
                                    Common.alertMsg(Resources.NoTemplatesFound);
                                    Common.unmask("body-mask");
                                }
                                else if (res.length == 1) {
                                    Common.mask(document.body, "body-mask");
                                    let selectedTemplateId = res[0].id;
                                    getDocumentIdInOriginalMail(self.model.id, function (attachmentId) {
                                        validateAndPreviewAttachment(attachmentId, selectedTemplateId,self);
                                    });
                                }
                                else {
                                    fromPeview = true;
                                    $("#modalSignatureTemplates").modal('show');
                                    $("#modalSignatureTemplates").off("hidden.bs.modal");
                                    $("#modalSignatureTemplates").off("shown.bs.modal");
                                    $("#tblSignature tbody").html("");

                                    $(self.refs['modalCTSPurpose']).on('hidden.bs.modal', function () {
                                        swal.close();
                                    });

                                    for (let j = 0; j < res.length / 2; j++) {
                                        $("#tblSignature tbody").append('<tr id="tr' + j + '"></tr>');
                                    }
                                    var count = 0, k = 0;

                                    $.each(res, function (i, val) {
                                        $("#tr" + k).append('<td style="width: 250px;"><div class="form-check"><input class="form-check-input" type="radio" style="margin:6px;" name="rdbSignature" id="rdb' + val.id + '" value="' + val.id + '"><label for="rdb' + val.id + '"><img for="rdb' + val.id + '" id="img' + val.id + '"src=""  class="rounded mx-auto d-block" alt="' + val.name + '" width="75%"></label></div></td>');

                                        $("#img" + val.id).attr("src", window.DSURL + "/api/signature/template/" + val.id + "/image?token=" + IdentityAccessToken);

                                        if ((count + 1) % 2 == 0)
                                            k++;

                                        count++;
                                    });
                                }
                            },
                            error: function (res) {
                                $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnComplete').button('reset');
                                $('#' + self.model.ComponentId + '_ReplyToStructure').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_ReplyToStructure').button('reset');
                                $('#' + self.model.ComponentId + '_ReplyToUser').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_ReplyToUser').button('reset');
                                $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                                $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnSignTemplate').button('reset');
                                $('#btnGroupDropup').attr('disabled', false);
                                $('#btnGroupDropup').button('reset');
                                Common.showScreenErrorMsg();
                              
                            }

                        });
                    }

                })

            }
        })
        if (this.model.customAttributes !== null && this.model.customAttributes.components.length > 0) {
            var translationObject = this.model.customAttributesTranslation;
            var translationEn = {}, translationAr = {}, translationFr = {};
            if (translationObject !== null) {
                for (var i = 0; i < translationObject.length; i++) {
                    translationEn[translationObject[i].Keyword] = translationObject[i].En;
                    translationAr[translationObject[i].Keyword] = translationObject[i].Ar;
                    translationFr[translationObject[i].Keyword] = translationObject[i].Fr;
                }
            }
            var formDesigner = this.model.customAttributes;
            if (self.model.isFollowUp && !self.model.isTaskCreator) {
                var isPrivateAttribute = $.grep(formDesigner.components, function (attribute) {
                    return attribute.key =='isPrivate';
                })[0];
                if (isPrivateAttribute) {
                    isPrivateAttribute.customConditional="show=false;"
                }
            }
            if (self.model.isFollowUp && self.model.followUpUserRole != FollowUpRoles.Owner) {
                var teamAttribute = $.grep(formDesigner.components, function (attribute) {
                    return attribute.key =='team';
                })[0];
                if (teamAttribute) {
                    teamAttribute.customConditional="show=false;"
                }
            }

            Formio.createForm(self.refs['builder'], formDesigner, {
                noAlerts: true, template: 'bootstrap3', readOnly: model.readonly, language: window.language,
                i18n: {
                    'en': translationEn,
                    'ar': translationAr,
                    'fr': translationFr
                },
                buttonSettings: {
                    showCancel: false,
                    showPrevious: true,
                    showNext: true,
                    showSubmit: false
                }
            }).then(function (form) {
                builder = form;
                form.nosubmit = true;
                var $form = $(self.refs['formDocumentAttributePost']);
                $form.parsley().reset();
                var isValid = $form.parsley().isValid();
                if (model.formData !== null) {
                    form.submission = { data: model.formData };
                }
                if (model.readonly === false) {
                    form.on('submit', function (submission) {
                        delete submission.data.submit;
                        var documentViewModel = getFormData(model.categoryId, false, model.multipleReceivingEntity, self);
                        Common.ajaxPost(saveOptions.url, documentViewModel, function (response) {
                            if (response === "OriginalFileInUse") {
                                Common.alertMsg(Resources.OriginalFileInUse);
                                $(self.refs['btnAttributeSave']).attr("disabled", false);
                                $(self.refs['btnAttributeSave']).button('reset');
                                $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
                                $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
                                $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                                $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                                $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
                                $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
                                $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", false)

                                $('#btnGroupDropup').attr('disabled', false);
                                $('#btnGroupDropup').button('reset');

                                gLocked = false;
                            } else if (response === "FileInUse") {
                                Common.alertMsg(Resources.FileInUse);
                                $(self.refs['btnAttributeSave']).attr("disabled", false);
                                $(self.refs['btnAttributeSave']).button('reset');
                                $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
                                $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
                                $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                                $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                                $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
                                $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
                                $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnSignTemplate').button('reset');

                                $('#btnGroupDropup').attr('disabled', false);
                                $('#btnGroupDropup').button('reset');
                                gLocked = false;
                            } else if (response === "ExternalReferencedNumberInUse") {
                               
                                Common.alertMsg(Resources.ExternalReferencedNumberInUse);
                                $(self.refs['btnAttributeSave']).attr("disabled", false);
                                $(self.refs['btnAttributeSave']).button('reset');
                                $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
                                $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
                                $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                                $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                                $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
                                $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
                                $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnSignTemplate').button('reset');
                                $('#btnGroupDropup').attr('disabled', false);
                                $('#btnGroupDropup').button('reset');
                                gLocked = false;
                            }
                            else {
                                if (response !== "") {
                                    $(".documentHeader span").before(" - " + response + " ");
                                    $(self.refs['btnAttributeSave']).hide();
                                    $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                                    $('#' + self.model.ComponentId + '_btnAttributeRegister').addClass("btn-primary");
                                    $('#' + self.model.ComponentId + '_btnAttributeRegister').removeClass("btn-info");
                                    $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("data-loading-text", "<i class='fa fa-spinner fa-spin'></i>" + Resources.Saving);
                                    $('#' + self.model.ComponentId + '_btnAttributeRegister').text(Resources.Save).button("refresh");
                                    if (window.OpenCorrespondenceMode != "OpenCorrespondenceWithViewer") {

                                        $(self.refs['documentBarcodeContainer']).removeClass('hidden');
                                        $(self.refs['documentBarcodeContainer']).find(".defaultWithViewer").removeClass('hidden');
                                    }
                                    $(self.refs['lnkDocumentBarcodeView']).html("<i class='fa fa-barcode mr-sm'></i>" + response);
                                    model.referenceNumber = response;
                                    $('[id*="_refNumberPanel"]').removeClass('hidden');
                                    $('[id*="_refNumberPanelHeader"]').html(response);
                                    
                                }
                                if (typeof saveOptions.callback === 'function') {
                                    saveOptions.callback(response);
                                }
                                EventReceiver.OnDocumentRegistered(documentViewModel).then(function () {
                                    form.emit('submitDone', submission);
                                });
                            }
                        }, function () {
                            $(self.refs['btnAttributeSave']).attr("disabled", false);
                            $(self.refs['btnAttributeSave']).button('reset');
                            $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                            $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
                            $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                            $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
                            $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                            $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                            $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                            $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                            $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                            $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
                            Common.showScreenErrorMsg();
                            gLocked = false;
                        });
                    });
                    form.on('change', function (value) {
                        if (value && value.changed && value.changed.component && value.changed.component.key && value.changed.component.key == 'isPrivate') {
                            if (value.data && value.data.isPrivate == false && value.data.team == 0) {
                                value.data.team = '';
                            }
                        }
                        var $form = $(self.refs['formDocumentAttributePost']);
                        $form.parsley().reset();
                        var isValid = $form.parsley().isValid();
                        gFormData = form.submission.data;
                        if (value || (value && value.changed)) {
                            formioFrom = form.checkValidity();
                        }
                        if (value.changed && isValid) {
                            $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", !value.isValid);
                            $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", !value.isValid);
                            $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", !value.isValid);
                            $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", !value.isValid);
                            $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", !value.isValid);
                            $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", !value.isValid);
                            $('#btnGroupDropup').attr("disabled", !value.isValid);
                        }
                    });
                }
                if (form.checkValidity() && isValid) {
                    $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", false);
                    $('#btnGroupDropup').attr("disabled",  false);

                } else {
                    $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", true);
                    $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", true);
                    $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", true);
                    $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", true);
                    $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", true);
                    $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", true);
                    $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", true);
                    $('#btnGroupDropup').attr("disabled", true);

                }
            });
        }
        else {
            $("#hrLine").hide();
        }
        if (this.model.readonly === false) {

            let isBroadcastInternal = false;
            if (receivingEntityObj[0].BroadcastReceivingEntity && receivingEntityObj[0].Type === "internal") {
                isBroadcastInternal = true;
            }
            $(self.refs['btnAttributeSave']).click(function () {
                var disableBtns = false;
                var $form = $(self.refs['formDocumentAttributePost']);
                $form.parsley().reset();
                if (!gLocked) {
                    gLocked = true;
                    try {
                        if ($('#' + self.model.ComponentId + '_btnAttributeSend').attr('disabled') === false || typeof $('#' + self.model.ComponentId + '_btnAttributeSend').attr('disabled') === "undefined") {
                            disableBtns = true;
                            $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", true);
                            $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", true);
                            $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", true);
                            $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", true);
                            $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", true);
                        }
                        $(this).button('loading');
                        var documentViewModel = getFormData(model.categoryId, false, model.multipleReceivingEntity, self);
                        if ($(self.refs['cmbUserStructures']).val()) {
                            sessionStorage.setItem($("#hdUserId").val() + "SelectedUserStrcuture", $(self.refs['cmbUserStructures']).val());
                        }
                            saveDocument(saveOptions, documentViewModel, self, disableBtns)
                        
                    } catch (e) {
                        gLocked = false;
                    }
                }
            });
            $('#' + self.model.ComponentId + '_btnAttributeRegister').click(function () {
                var $form = $(self.refs['formDocumentAttributePost']);
                $form.parsley().reset();
                var isValid = $form.parsley().validate();
                if (isValid) {
                    if (!gLocked) {
                        gLocked = true;
                        try {
                            $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", true);
                            $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", true);
                            $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", true);
                            $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", true);
                            $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", true);
                            $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", true);
                            $(this).button('loading');
                            $(this).button('loading');
                            var documentViewModel = getFormData(model.categoryId, true, model.multipleReceivingEntity, self);
                            if ($(self.refs['cmbUserStructures']).val()) {
                                sessionStorage.setItem($("#hdUserId").val() + "SelectedUserStrcuture", $(self.refs['cmbUserStructures']).val());
                            }
                            Common.ajaxPost(saveOptions.url, documentViewModel, function (response) {
                                if (response === "OriginalFileInUse") {
                                    Common.alertMsg(Resources.OriginalFileInUse);

                                    $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                                    $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                                    $(self.refs['btnAttributeSave']).attr("disabled", false);
                                    $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                                    $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                                    $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                                    gLocked = false;
                                    if (typeof model.referenceNumber !== "undefined" &&
                                        model.referenceNumber != null &&
                                        model.referenceNumber != "") {
                                        $('#' + self.model.ComponentId + '_btnAttributeRegister').text(Resources.Save).button("refresh");
                                    } else {
                                        $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                                    }
                                } else if (response === "FileInUse") {
                                    Common.alertMsg(Resources.FileInUse);
                                    $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                                    $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                                    $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                                    $(self.refs['btnAttributeSave']).attr("disabled", false);
                                    $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                                    $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                                    $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                                    gLocked = false;
                                } else if (response === "CantGenerateReferenceNumber") {
                                    Common.alertMsg(Resources.CantGenerateReferenceNumber);
                                    $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                                    $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                                    $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                                    $(self.refs['btnAttributeSave']).attr("disabled", false);
                                    $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                                    $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                                    gLocked = false;
                                } else if (response === "ExternalReferencedNumberInUse") {
                                    Common.alertMsg(Resources.ExternalReferencedNumberInUse);
                                    $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                                    $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                                    $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                                    $(self.refs['btnAttributeSave']).attr("disabled", false);
                                    $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                                    $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                                    $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                                    gLocked = false;
                                } else if (response === "MustBeAllowEditAttributeAndNotSigned") {
                                    Common.alertMsg(Resources.MustBeAllowEditAttributeAndNotSigned);
                                    $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                                    $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                                    $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                                    $(self.refs['btnAttributeSave']).attr("disabled", false);
                                    $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                                    $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                                    $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                                    gLocked = false;
                                }
                                else {
                                    if (response !== "") {
                                        $(".documentHeader span").before(" - " + response + " ");
                                        Common.alertMsg(Common.format(Resources.ReferenceNumberMsg, response));
                                        $(self.refs['btnAttributeSave']).hide();
                                        $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                                        $('#' + self.model.ComponentId + '_btnAttributeRegister').addClass("btn-primary");
                                        $('#' + self.model.ComponentId + '_btnAttributeRegister').removeClass("btn-info");
                                        $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("data-loading-text", "<i class='fa fa-spinner fa-spin'></i>" + Resources.Saving);
                                        $('#' + self.model.ComponentId + '_btnAttributeRegister').text(Resources.Save).button("refresh");
                                        if (window.OpenCorrespondenceMode != "OpenCorrespondenceWithViewer") {

                                            $(self.refs['documentBarcodeContainer']).removeClass('hidden');
                                            $(self.refs['documentBarcodeContainer']).find(".defaultWithViewer").removeClass('hidden');
                                        }
                                        $(self.refs['lnkDocumentBarcodeView']).html("<i class='fa fa-barcode mr-sm'></i>" + response);
                                        $("#documentWithViewerContainer").removeClass('hidden');

                                        $('[id*="_refNumberPanel"]').removeClass('hidden');
                                        $('[id*="_refNumberPanelHeader"]').html(response);
            
                                        var viewerFrame = $("#" + self.model.parentComponentId + "_viewerFrame");
                                        if (viewerFrame.length > 0) {
                                            $(viewerFrame).attr("src", $(viewerFrame).attr("src"));
                                        }

                                        model.referenceNumber = response;
                                        documentViewModel.referenceNumber = response;
                                        if (self.model.fromVip) {

                                            updateListItem(documentViewModel);
                                        }
                                    } else {
                                        $(self.refs['btnAttributeSave']).hide();
                                        $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                                        $('#' + self.model.ComponentId + '_btnAttributeRegister').text(Resources.Save).button("refresh");
                                    }
                                    EventReceiver.OnDocumentRegistered(documentViewModel).then(function () {
                                        Common.showScreenSuccessMsg();
                                        if (typeof saveOptions.callback === 'function') {
                                            saveOptions.callback(response);
                                        }
                                        $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                                        $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
                                        $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                                        $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
                                        $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                                        $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                                        $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                                        $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
                                        $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                                        $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
                                        gLocked = false;
                                    });
                                }
                            }, function (error,object) {
                                var msg = error ? error: "";
                                if (object && object.responseText) {
                                    msg = object.responseText;
                                }
                                $(self.refs['btnAttributeSave']).attr("disabled", false);
                                $(self.refs['btnAttributeSave']).button('reset');
                                $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
                                $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
                                $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                                $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                                $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
                                $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
                                Common.showScreenErrorMsg(msg);
                                gLocked = false;
                            });
                        } catch (e) {
                            gLocked = false;
                            $(self.refs['btnAttributeSave']).attr("disabled", false);
                            $(self.refs['btnAttributeSave']).button('reset');
                            $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                            $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
                            $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                            $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
                            $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                            $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                            $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                            $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                            $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                            $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
                            $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                            $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
                        }
                    }
                }
            });
            $('#' + self.model.ComponentId + '_btnAttributeExport').click(function () {
                Common.mask(document.body, "body-mask");
                Common.ajaxGet('/Document/CheckDocumentAttachmentLocked/', { "documentId": self.model.id }, function (retval) {
                    if (retval) {
                        Common.alertMsg(Resources.FileInUse);
                        Common.unmask("body-mask");
                    } else {

                        var $form = $(self.refs['formDocumentAttributePost']);
                        $form.parsley().reset();
                        var isValid = $form.parsley().validate();
                        
                        if (isValid) {
                            if (!gLocked) {
                                gLocked = true;
                                try {
                                    $(self.refs['btnAttributeSave']).attr("disabled", true);
                                    $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", true);
                                    $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", true);
                                    $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", true);
                                    $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", true);
                                    $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", true);
                                    $('#btnGroupDropup').attr('disabled', true);
                                    $('#' + self.model.ComponentId + '_btnAttributeSend').button('loading');
                                    if (typeof builder !== "undefined") {
                                        builder.off('submitDone');
                                        builder.on('submitDone', function () {
                                            gLocked = false;
                                            /*if (actionArray.includes("Transfer.Export")) {*/
                                            Common.unmask("body-mask");

                                                sendToReceivingEntity(model.privacies, model.delegationId, self, isBroadcastInternal ? TransferType.BroadcastSend : TransferType.Send, actionArray);

                                            /*}*/
                                            //else
                                            //    sendToReceivingEntity(model.privacies, model.delegationId, self, isBroadcastInternal ? TransferType.BroadcastSend : TransferType.Send);


                                        });
                                        builder.submit();
                                    } else {
                                        var documentViewModel = getFormData(model.categoryId, false, model.multipleReceivingEntity, self);
                                        Common.ajaxPost(saveOptions.url, documentViewModel, function (response) {
                                            gLocked = false;
                                            if (response === "OriginalFileInUse") {
                                                Common.unmask("body-mask");
                                                Common.alertMsg(Resources.OriginalFileInUse);
                                                $(self.refs['btnAttributeSave']).attr("disabled", false);
                                                $(self.refs['btnAttributeSave']).button('reset');
                                                $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                                                $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
                                                $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                                                $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
                                                $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                                                $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                                                $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                                                $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                                                $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                                                $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
                                                $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                                                $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
                                                $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", false);
                                                $('#' + self.model.ComponentId + '_btnSignTemplate').button('reset');
                                                $('#btnGroupDropup').attr('disabled', false);
                                                $('#btnGroupDropup').button('reset');
                                                Common.unmask("body-mask");
                                            } else if (response === "FileInUse") {
                                                Common.unmask("body-mask");
                                                Common.alertMsg(Resources.FileInUse);
                                                $(self.refs['btnAttributeSave']).attr("disabled", false);
                                                $(self.refs['btnAttributeSave']).button('reset');
                                                $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                                                $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
                                                $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                                                $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
                                                $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                                                $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                                                $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                                                $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                                                $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                                                $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
                                                $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                                                $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
                                                $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", false);
                                                $('#' + self.model.ComponentId + '_btnSignTemplate').button('reset');
                                                $('#btnGroupDropup').attr('disabled', false);
                                                $('#btnGroupDropup').button('reset');
                                                Common.unmask("body-mask");
                                            }
                                            else if (response === "ExternalReferencedNumberInUse") {
                                                Common.unmask("body-mask");
                                                Common.alertMsg(Resources.ExternalReferencedNumberInUse);
                                                $(self.refs['btnAttributeSave']).attr("disabled", false);
                                                $(self.refs['btnAttributeSave']).button('reset');
                                                $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                                                $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
                                                $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                                                $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
                                                $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                                                $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                                                $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                                                $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                                                $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                                                $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
                                                $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                                                $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
                                                $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", false);
                                                $('#' + self.model.ComponentId + '_btnSignTemplate').button('reset');
                                                $('#btnGroupDropup').attr('disabled', false);
                                                $('#btnGroupDropup').button('reset');
                                                Common.unmask("body-mask");
                                            }
                                            else {
                                                Common.unmask("body-mask");
                                                if (response !== "") {
                                                    $(".documentHeader span").before(" - " + response + " ");
                                                    $(self.refs['btnAttributeSave']).hide();
                                                    $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                                                    $('#' + self.model.ComponentId + '_btnAttributeRegister').addClass("btn-primary");
                                                    $('#' + self.model.ComponentId + '_btnAttributeRegister').removeClass("btn-info");
                                                    $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("data-loading-text", "<i class='fa fa-spinner fa-spin'></i>" + Resources.Saving);
                                                    $('#' + self.model.ComponentId + '_btnAttributeRegister').text(Resources.Save).button("refresh");
                                                    if (window.OpenCorrespondenceMode != "OpenCorrespondenceWithViewer") {

                                                        $(self.refs['documentBarcodeContainer']).removeClass('hidden');
                                                        $(self.refs['documentBarcodeContainer']).find(".defaultWithViewer").removeClass('hidden');
                                                    }
                                                    $(self.refs['lnkDocumentBarcodeView']).html("<i class='fa fa-barcode mr-sm'></i>" + response);
                                                    $("#documentWithViewerContainer").removeClass('hidden');
                                                    $('[id*="_refNumberPanel"]').removeClass('hidden');
                                                    model.referenceNumber = response;
                                                    $('[id*="_refNumberPanelHeader"]').text(response);
                                                    Common.unmask("body-mask");

                                                }
                                                else {
                                                    if ((actionArray.includes("Attribute.Save"))) {
                                                        $(self.refs['btnAttributeSave']).attr("disabled", false);
                                                    }

                                                }
                                                if (typeof saveOptions.callback === 'function') {
                                                    saveOptions.callback(response);
                                                }
                                                
                                                sendToReceivingEntity(model.privacies, model.delegationId, self, isBroadcastInternal ? TransferType.BroadcastSend : TransferType.Send, actionArray);
                                                EventReceiver.OnDocumentRegistered(documentViewModel).then(function () { });
                                            }
                                        }, function () {
                                            Common.unmask("body-mask");
                                            $(self.refs['btnAttributeSave']).attr("disabled", false);
                                            $(self.refs['btnAttributeSave']).button('reset');
                                            $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                                            $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
                                            $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                                            $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
                                            $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                                            $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                                            $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                                            $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                                            $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                                            $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
                                            $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                                            $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
                                            $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", false);
                                            $('#' + self.model.ComponentId + '_btnSignTemplate').button('reset');
                                            $('#btnGroupDropup').attr('disabled', false);
                                            $('#btnGroupDropup').button('reset');
                                            Common.showScreenErrorMsg();
                                            gLocked = false;
                                            Common.unmask("body-mask");
                                        });
                                    }
                                } catch (e) {
                                    Common.unmask("body-mask");
                                    $(self.refs['btnAttributeSave']).attr("disabled", false);
                                    $(self.refs['btnAttributeSave']).button('reset');
                                    $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                                    $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
                                    $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                                    $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
                                    $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                                    $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                                    $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                                    $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                                    $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                                    $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
                                    $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                                    $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
                                    $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", false);
                                    $('#' + self.model.ComponentId + '_btnSignTemplate').button('reset');
                                    $('#btnGroupDropup').attr('disabled', false);
                                    $('#btnGroupDropup').button('reset');
                                    Common.unmask("body-mask");
                                    gLocked = false;
                                }
                            }
                        }

                    }
                });
                //$('#' + self.model.ComponentId + '_btnAttributeSend').trigger("click");
            });
            $('#' + self.model.ComponentId + '_btnAttributeSend').click(function () {

                Common.ajaxGet('/Document/CheckDocumentAttachmentLocked/', { "documentId": self.model.id }, function (retval) {
                    if (retval) {
                        Common.alertMsg(Resources.FileInUse);
                    } else {

                        var $form = $(self.refs['formDocumentAttributePost']);
                        $form.parsley().reset();
                        var isValid = $form.parsley().validate();
                        if (isValid) {
                            if (!gLocked) {
                                gLocked = true;
                                try {
                                    $(self.refs['btnAttributeSave']).attr("disabled", true);
                                    $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", true);
                                    $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", true);
                                    $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", true);
                                    $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", true);
                                    $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", true);
                                    $('#' + self.model.ComponentId + '_btnAttributeSend').button('loading');
                                    if (typeof builder !== "undefined") {
                                        builder.off('submitDone');
                                        builder.on('submitDone', function () {
                                            gLocked = false;
                                           
                                                sendToReceivingEntity(model.privacies, model.delegationId, self, isBroadcastInternal ? TransferType.BroadcastSend : TransferType.Send);


                                        });
                                        builder.submit();
                                    } else {
                                        var documentViewModel = getFormData(model.categoryId, false, model.multipleReceivingEntity, self);
                                        Common.ajaxPost(saveOptions.url, documentViewModel, function (response) {
                                            gLocked = false;
                                            if (response === "OriginalFileInUse") {
                                                Common.alertMsg(Resources.OriginalFileInUse);
                                                $(self.refs['btnAttributeSave']).attr("disabled", false);
                                                $(self.refs['btnAttributeSave']).button('reset');
                                                $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                                                $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
                                                $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                                                $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
                                                $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                                                $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                                                $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                                                $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                                                $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                                                $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
                                                $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                                                $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
                                            } else if (response === "FileInUse") {
                                                Common.alertMsg(Resources.FileInUse);
                                                $(self.refs['btnAttributeSave']).attr("disabled", false);
                                                $(self.refs['btnAttributeSave']).button('reset');
                                                $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                                                $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
                                                $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                                                $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
                                                $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                                                $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                                                $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                                                $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                                                $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                                                $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
                                                $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                                                $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
                                            }
                                            else if (response === "ExternalReferencedNumberInUse") {
                                                Common.alertMsg(Resources.ExternalReferencedNumberInUse);
                                                $(self.refs['btnAttributeSave']).attr("disabled", false);
                                                $(self.refs['btnAttributeSave']).button('reset');
                                                $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                                                $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
                                                $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                                                $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
                                                $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                                                $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                                                $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                                                $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                                                $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                                                $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
                                                $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                                                $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
                                            }
                                            else {
                                                if (response !== "") {
                                                    $(".documentHeader span").before(" - " + response + " ");
                                                    $(self.refs['btnAttributeSave']).hide();
                                                    $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                                                    $('#' + self.model.ComponentId + '_btnAttributeRegister').addClass("btn-primary");
                                                    $('#' + self.model.ComponentId + '_btnAttributeRegister').removeClass("btn-info");
                                                    $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("data-loading-text", "<i class='fa fa-spinner fa-spin'></i>" + Resources.Saving);
                                                    $('#' + self.model.ComponentId + '_btnAttributeRegister').text(Resources.Save).button("refresh");
                                                    if (window.OpenCorrespondenceMode != "OpenCorrespondenceWithViewer") {

                                                        $(self.refs['documentBarcodeContainer']).removeClass('hidden');
                                                        $(self.refs['documentBarcodeContainer']).find(".defaultWithViewer").removeClass('hidden');
                                                    }
                                                    $(self.refs['lnkDocumentBarcodeView']).html("<i class='fa fa-barcode mr-sm'></i>" + response);
                                                    $("#documentWithViewerContainer").removeClass('hidden');
                                                    $('[id*="_refNumberPanel"]').removeClass('hidden');
                                                    model.referenceNumber = response;
                                                    $('[id*="_refNumberPanelHeader"]').text(response);


                                                }
                                                else {
                                                    if ((actionArray.includes("Attribute.Save"))) {
                                                        $(self.refs['btnAttributeSave']).attr("disabled", false);
                                                    }

                                                }
                                                if (typeof saveOptions.callback === 'function') {
                                                    saveOptions.callback(response);
                                                }
                                                sendToReceivingEntity(model.privacies, model.delegationId, self, isBroadcastInternal ? TransferType.BroadcastSend : TransferType.Send);
                                                EventReceiver.OnDocumentRegistered(documentViewModel).then(function () { });
                                            }
                                        }, function () {
                                            $(self.refs['btnAttributeSave']).attr("disabled", false);
                                            $(self.refs['btnAttributeSave']).button('reset');
                                            $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                                            $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
                                            $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                                            $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
                                            $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                                            $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                                            $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                                            $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                                            $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                                            $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
                                            $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                                            $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
                                            Common.showScreenErrorMsg();
                                            gLocked = false;
                                        });
                                    }
                                } catch (e) {
                                    $(self.refs['btnAttributeSave']).attr("disabled", false);
                                    $(self.refs['btnAttributeSave']).button('reset');
                                    $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                                    $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
                                    $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                                    $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
                                    $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                                    $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                                    $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                                    $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                                    $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                                    $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
                                    $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                                    $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
                                    gLocked = false;
                                }
                            }
                        }

                    }
                });
            });

            $('#' + self.model.ComponentId + '_btnAttributeTransfer').click(function () {
                Common.mask(document.body, "body-mask");
                var $form = $(self.refs['formDocumentAttributePost']);
                $form.parsley().reset();
                var isValid = $form.parsley().validate();
                if (isValid) {
                    if (!gLocked) {
                        gLocked = true;
                        try {
                            $(self.refs['btnAttributeSave']).attr("disabled", true);
                            $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", true);
                            $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", true);
                            $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", true);
                            $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", true);
                            $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr('data-loading-text', `<i class='fa fa-spinner fa-spin'></i>${Resources.TransferButton} `);
                            $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('loading');
                          
                            if (typeof builder !== "undefined") {
                                builder.off('submitDone');
                                builder.on('submitDone', function () {
                                    gLocked = false;
                                    Common.unmask("body-mask");
                                    openTransfer(model.privacies, model.delegationId, self);
                                });
                                builder.submit();
                            } else {
                                //gLocked = false;
                                var documentViewModel = getFormData(model.categoryId, false, model.multipleReceivingEntity, self);
                               
                               // $("[ref='btnAttributeSave']").trigger("click");
                                //$(self.refs['btnAttributeSave']).click();
                                //Common.unmask("body-mask");
                                //openTransfer(model.privacies, model.delegationId, self);
                                //EventReceiver.OnDocumentRegistered(documentViewModel).then(function () { });
                                Common.ajaxPost(saveOptions.url, documentViewModel, function (response) {
                                    gLocked = false;
                                    if (response === "OriginalFileInUse") {
                                        Common.unmask("body-mask");
                                        Common.alertMsg(Resources.OriginalFileInUse);
                                        $(self.refs['btnAttributeSave']).attr("disabled", false);
                                        $(self.refs['btnAttributeSave']).button('reset');
                                        $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                                        $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
                                        $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                                        $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
                                        $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                                        $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                                        $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                                        $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                                        $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                                        $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
                                        $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                                        $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
                                    } else if (response === "FileInUse") {
                                        Common.unmask("body-mask");
                                        Common.alertMsg(Resources.FileInUse);
                                        $(self.refs['btnAttributeSave']).attr("disabled", false);
                                        $(self.refs['btnAttributeSave']).button('reset');
                                        $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                                        $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
                                        $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                                        $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
                                        $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                                        $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                                        $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                                        $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                                        $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                                        $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
                                        $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                                        $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
                                    } else if (response === "ExternalReferencedNumberInUse") {
                                        Common.unmask("body-mask");
                                        Common.alertMsg(Resources.ExternalReferencedNumberInUse);
                                        $(self.refs['btnAttributeSave']).attr("disabled", false);
                                        $(self.refs['btnAttributeSave']).button('reset');
                                        $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                                        $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
                                        $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                                        $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
                                        $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                                        $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                                        $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                                        $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                                        $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                                        $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
                                        $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                                        $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
                                    }
                                    else {
                                        Common.unmask("body-mask");
                                        if (response !== "") {
                                            $(".documentHeader span").before(" - " + response + " ");
                                            $(self.refs['btnAttributeSave']).hide();
                                            $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                                            $('#' + self.model.ComponentId + '_btnAttributeRegister').addClass("btn-primary");
                                            $('#' + self.model.ComponentId + '_btnAttributeRegister').removeClass("btn-info");
                                            $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("data-loading-text", "<i class='fa fa-spinner fa-spin'></i>" + Resources.Saving);
                                            $('#' + self.model.ComponentId + '_btnAttributeRegister').text(Resources.Save).button("refresh");
                                            if (window.OpenCorrespondenceMode != "OpenCorrespondenceWithViewer") {

                                                $(self.refs['documentBarcodeContainer']).removeClass('hidden');
                                                $(self.refs['documentBarcodeContainer']).find(".defaultWithViewer").removeClass('hidden');
                                            }
                                            $(self.refs['lnkDocumentBarcodeView']).html("<i class='fa fa-barcode mr-sm'></i>" + response);
                                            model.referenceNumber = response;
                                            $('[id*="_refNumberPanel"]').removeClass('hidden');
                                            $('[id*="_refNumberPanelHeader"]').text(model.referenceNumber);
                                            
                                        }
                                        var viewerFrame = $("#" + self.model.parentComponentId + "_viewerFrame");
                                        if (viewerFrame.length > 0) {
                                            $(viewerFrame).attr("src", $(viewerFrame).attr("src"));
                                        }
                                        if ((actionArray.includes("Attribute.Save"))) {
                                            $(self.refs['btnAttributeSave']).attr("disabled", false);
                                        }
                                        if (typeof saveOptions.callback === 'function') {
                                            saveOptions.callback(response);
                                        }
                                        openTransfer(model.privacies, model.delegationId, self);
                                        EventReceiver.OnDocumentRegistered(documentViewModel).then(function () { });
                                    }
                                }, function () {
                                    Common.unmask("body-mask");
                                    $(self.refs['btnAttributeSave']).attr("disabled", false);
                                    $(self.refs['btnAttributeSave']).button('reset');
                                    $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                                    $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
                                    $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                                    $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
                                    $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                                    $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                                    $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                                    $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                                    $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                                    $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
                                    $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                                    $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
                                    Common.showScreenErrorMsg();
                                    gLocked = false;
                                });
                            }
                        } catch (e) {
                            gLocked = false;
                        }
                    }
                }
            })

            $('#' + self.model.ComponentId + '_btnStartWorkflow1').click(function () {

                Common.ajaxGet('/Document/CheckDocumentAttachmentLocked/', { "documentId": self.model.id }, function (retval) {
                    if (retval) {
                        Common.alertMsg(Resources.FileInUse);
                    } else {

                        var documentViewModel = getFormData(model.categoryId, false, model.multipleReceivingEntity, self);
                        Common.ajaxPost(saveOptions.url, documentViewModel, function (response) {
                            gLocked = false;
                            if (response === "OriginalFileInUse") {
                                Common.alertMsg(Resources.OriginalFileInUse);
                                $(self.refs['btnAttributeSave']).attr("disabled", false);
                                $(self.refs['btnAttributeSave']).button('reset');
                                $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
                                $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
                                $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                                $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                                $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
                                $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
                            } else if (response === "FileInUse") {
                                Common.alertMsg(Resources.FileInUse);
                                $(self.refs['btnAttributeSave']).attr("disabled", false);
                                $(self.refs['btnAttributeSave']).button('reset');
                                $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
                                $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
                                $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                                $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                                $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
                                $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
                            } else if (response === "ExternalReferencedNumberInUse") {
                                Common.alertMsg(Resources.ExternalReferencedNumberInUse);
                                $(self.refs['btnAttributeSave']).attr("disabled", false);
                                $(self.refs['btnAttributeSave']).button('reset');
                                $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
                                $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
                                $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                                $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                                $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
                                $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
                            }
                            else {
                                let getParams = {
                                    "documentId": documentViewModel.id,
                                    'delegationId': documentViewModel.delegationId
                                }
                                Common.ajaxGet('/Workflow/GetWorkflow/', getParams, function (retval) {
                                    if (retval.workflowId != null && retval.workflowId != 0) {
                                        let modalWrapper = $(".modal-window");
                                        modalWrapper.find("#modalWorkflow").remove();
                                        var modalWorkflow = new Workflow.Workflow();
                                        modalWorkflow.documentId = self.model.id;
                                        modalWorkflow.transferId = null;
                                        modalWorkflow.enableTransferToUsers = window.EnableTransferToUsers;
                                        modalWorkflow.workflowSteps = retval.workflowUsers;
                                        modalWorkflow.workflowId = retval.workflowId;
                                        modalWorkflow.isDraft = true;
                                        modalWorkflow.delegationId = self.model.delegationId;
                                        modalWorkflow.callBack = function () {
                                            tryCloseModal(self.model.actionsComponentId);
                                        }
                                        var WorkflowView = new Workflow.WorkflowView(modalWrapper, modalWorkflow);
                                        WorkflowView.render();

                                        $('#modalWorkflow').modal('show');
                                        $("#modalWorkflow").off("hidden.bs.modal");
                                        $("#modalWorkflow").off("shown.bs.modal");
                                        $('#modalWorkflow').on('shown.bs.modal', function () {
                                            document.getElementById('cmbUsers').focus();
                                        });
                                        $('#modalWorkflow').on('hidden.bs.modal', function () {
                                        });
                                    }

                                    else {
                                        let postParams = {
                                            "documentId": documentViewModel.id,
                                            'delegationId': self.model.delegationId,
                                        }
                                        Common.ajaxPost('/Workflow/CreateWorkflow', postParams, function (retVal) {
                                            let modalWrapper = $(".modal-window");
                                            modalWrapper.find("#modalWorkflow").remove();
                                            var modalWorkflow = new Workflow.Workflow();
                                            modalWorkflow.documentId = self.model.id;
                                            modalWorkflow.transferId = self.model.transferId;
                                            modalWorkflow.enableTransferToUsers = window.EnableTransferToUsers;
                                            modalWorkflow.workflowId = retVal.data;
                                            modalWorkflow.isDraft = true;
                                            modalWorkflow.delegationId = self.model.delegationId;

                                            modalWorkflow.callBack = function () {
                                                tryCloseModal(self.model.actionsComponentId);
                                            }
                                            var WorkflowView = new Workflow.WorkflowView(modalWrapper, modalWorkflow);
                                            WorkflowView.render();

                                            $('#modalWorkflow').modal('show');
                                            $("#modalWorkflow").off("hidden.bs.modal");
                                            $("#modalWorkflow").off("shown.bs.modal");
                                            $('#modalWorkflow').on('shown.bs.modal', function () {
                                                document.getElementById('cmbUsers').focus();
                                            });
                                            $('#modalWorkflow').on('hidden.bs.modal', function () {
                                            });
                                        })
                                    }


                                })

                            }
                        }, function () {
                            $(self.refs['btnAttributeSave']).attr("disabled", false);
                            $(self.refs['btnAttributeSave']).button('reset');
                            $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                            $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
                            $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                            $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
                            $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                            $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                            $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                            $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                            $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                            $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
                            $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                            $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
                            Common.showScreenErrorMsg();
                            gLocked = false;
                        });
                    }
                })
            });

            $('#' + self.model.ComponentId + '_btnAttributeProceed').click(function () {
                var $form = $(self.refs['formDocumentAttributePost']);
                $form.parsley().reset();
                var isValid = $form.parsley().validate();
                if (isValid) {
                    if (!gLocked) {
                        gLocked = true;
                        try {
                            $(self.refs['btnAttributeSave']).attr("disabled", true);
                            $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", true);
                            $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", true);
                            $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", true);
                            $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", true);
                            $('#' + self.model.ComponentId + '_btnAttributeProceed').button('loading');
                            if (typeof builder !== "undefined") {
                                builder.off('submitDone');
                                builder.on('submitDone', function () {
                                    gLocked = false;
                                    openTransfer(model.privacies, model.delegationId, self);
                                });
                                builder.submit();
                            } else {
                                var documentViewModel = getFormData(model.categoryId, true, model.multipleReceivingEntity, self);
                                Common.ajaxPost(saveOptions.url, documentViewModel, function (response) {
                                    gLocked = false;
                                    if (response === "OriginalFileInUse") {
                                        Common.alertMsg(Resources.OriginalFileInUse);
                                        $(self.refs['btnAttributeSave']).attr("disabled", false);
                                        $(self.refs['btnAttributeSave']).button('reset');
                                        $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                                        $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
                                        $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                                        $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
                                        $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                                        $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                                        $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                                        $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
                                        $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                                        $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
                                    } else if (response === "FileInUse") {
                                        Common.alertMsg(Resources.FileInUse);
                                        $(self.refs['btnAttributeSave']).attr("disabled", false);
                                        $(self.refs['btnAttributeSave']).button('reset');
                                        $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                                        $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
                                        $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                                        $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
                                        $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                                        $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                                        $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                                        $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
                                        $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                                        $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
                                    } else if (response === "ExternalReferencedNumberInUse") {
                                        Common.alertMsg(Resources.ExternalReferencedNumberInUse);
                                        $(self.refs['btnAttributeSave']).attr("disabled", false);
                                        $(self.refs['btnAttributeSave']).button('reset');
                                        $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                                        $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
                                        $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                                        $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
                                        $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                                        $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                                        $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                                        $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
                                        $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                                        $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
                                    }
                                    else {
                                        if (response !== "") {
                                            $(".documentHeader span").before(" - " + response + " ");
                                            $(self.refs['btnAttributeSave']).hide();
                                            $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                                            $('#' + self.model.ComponentId + '_btnAttributeRegister').addClass("btn-primary");
                                            $('#' + self.model.ComponentId + '_btnAttributeRegister').removeClass("btn-info");
                                            $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("data-loading-text", "<i class='fa fa-spinner fa-spin'></i>" + Resources.Saving);
                                            $('#' + self.model.ComponentId + '_btnAttributeRegister').text(Resources.Save).button("refresh");
                                            if (window.OpenCorrespondenceMode != "OpenCorrespondenceWithViewer") {

                                                $(self.refs['documentBarcodeContainer']).removeClass('hidden');
                                                $(self.refs['documentBarcodeContainer']).find(".defaultWithViewer").removeClass('hidden');
                                            }
                                            $(self.refs['lnkDocumentBarcodeView']).html("<i class='fa fa-barcode mr-sm'></i>" + response);
                                            model.referenceNumber = response;
                                            $('[id*="_refNumberPanel"]').removeClass('hidden');
                                            $('[id*="_refNumberPanelHeader"]').text(model.referenceNumber);
                                           
                                        }
                                        if (typeof saveOptions.callback === 'function') {
                                            saveOptions.callback(response);
                                        }
                                        openTransfer(model.privacies, model.delegationId, self);
                                        EventReceiver.OnDocumentRegistered(documentViewModel).then(function () { });
                                    }
                                }, function () {
                                    $(self.refs['btnAttributeSave']).attr("disabled", false);
                                    $(self.refs['btnAttributeSave']).button('reset');
                                    $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                                    $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
                                    $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                                    $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
                                    $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                                    $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                                    $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                                    $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
                                    $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                                    $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
                                    Common.showScreenErrorMsg();
                                    gLocked = false;
                                });
                            }
                        } catch (e) {
                            gLocked = false;
                        }
                    }
                }
            });

            if (isBroadcastInternal) {
                $('#' + self.model.ComponentId + '_btnAttributeComplete').click(function () {
                    Common.ajaxGet('/Document/CheckDocumentAttachmentLocked/', { "documentId": self.model.id }, function (retval) {
                        if (retval) {
                            Common.alertMsg(Resources.FileInUse);
                        } else {
                            var $form = $(self.refs['formDocumentAttributePost']);
                            $form.parsley().reset();
                            var isValid = $form.parsley().validate();
                            if (isValid) {
                                if (!gLocked) {
                                    gLocked = true;
                                    try {
                                        $(self.refs['btnAttributeSave']).attr("disabled", true);
                                        $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", true);
                                        $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", true);
                                        $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", true);
                                        $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", true);
                                        $('#' + self.model.ComponentId + '_btnAttributeComplete').button('loading');
                                        $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", true);
                                        if (typeof builder !== "undefined") {


                                            builder.off('submitDone');
                                            builder.on('submitDone', function () {
                                                gLocked = false;
                                                sendToReceivingEntity(model.privacies, model.delegationId, self, TransferType.BroadcastComplete);
                                            });
                                            builder.submit();
                                        } else {


                                            var documentViewModel = getFormData(model.categoryId, false, model.multipleReceivingEntity, self);
                                            Common.ajaxPost(saveOptions.url, documentViewModel, function (response) {
                                                gLocked = false;
                                                if (response === "OriginalFileInUse") {
                                                    Common.alertMsg(Resources.OriginalFileInUse);
                                                    $(self.refs['btnAttributeSave']).attr("disabled", false);
                                                    $(self.refs['btnAttributeSave']).button('reset');
                                                    $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                                                    $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
                                                    $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                                                    $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
                                                    $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                                                    $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                                                    $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                                                    $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                                                    $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                                                    $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
                                                    $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                                                    $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
                                                } else if (response === "FileInUse") {
                                                    Common.alertMsg(Resources.FileInUse);
                                                    $(self.refs['btnAttributeSave']).attr("disabled", false);
                                                    $(self.refs['btnAttributeSave']).button('reset');
                                                    $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                                                    $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
                                                    $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                                                    $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
                                                    $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                                                    $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                                                    $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                                                    $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                                                    $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                                                    $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
                                                    $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                                                    $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
                                                }
                                                else if (response === "ExternalReferencedNumberInUse") {
                                                    Common.alertMsg(Resources.ExternalReferencedNumberInUse);
                                                    $(self.refs['btnAttributeSave']).attr("disabled", false);
                                                    $(self.refs['btnAttributeSave']).button('reset');
                                                    $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                                                    $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
                                                    $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                                                    $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
                                                    $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                                                    $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                                                    $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                                                    $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                                                    $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                                                    $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
                                                    $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                                                    $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
                                                }
                                                else {
                                                    if (response !== "") {
                                                        $(".documentHeader span").before(" - " + response + " ");
                                                        $(self.refs['btnAttributeSave']).hide();
                                                        $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                                                        $('#' + self.model.ComponentId + '_btnAttributeRegister').addClass("btn-primary");
                                                        $('#' + self.model.ComponentId + '_btnAttributeRegister').removeClass("btn-info");
                                                        $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("data-loading-text", "<i class='fa fa-spinner fa-spin'></i>" + Resources.Saving);
                                                        $('#' + self.model.ComponentId + '_btnAttributeRegister').text(Resources.Save).button("refresh");
                                                        if (window.OpenCorrespondenceMode != "OpenCorrespondenceWithViewer") {

                                                            $(self.refs['documentBarcodeContainer']).removeClass('hidden');
                                                            $(self.refs['documentBarcodeContainer']).find(".defaultWithViewer").removeClass('hidden');
                                                        }
                                                        $(self.refs['lnkDocumentBarcodeView']).html("<i class='fa fa-barcode mr-sm'></i>" + response);
                                                        model.referenceNumber = response;
                                                        $('[id*="_refNumberPanel"]').removeClass('hidden');
                                                        $('[id*="_refNumberPanelHeader"]').text(model.referenceNumber);

                                                    }
                                                    if (typeof saveOptions.callback === 'function') {
                                                        saveOptions.callback(response);
                                                    }


                                                    sendToReceivingEntity(model.privacies, model.delegationId, self, TransferType.BroadcastComplete);
                                                    EventReceiver.OnDocumentRegistered(documentViewModel).then(function () { });
                                                }
                                            }, function () {
                                                $(self.refs['btnAttributeSave']).attr("disabled", false);
                                                $(self.refs['btnAttributeSave']).button('reset');
                                                $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                                                $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
                                                $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                                                $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
                                                $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                                                $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                                                $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                                                $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                                                $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                                                $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
                                                $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                                                $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
                                                Common.showScreenErrorMsg();
                                                gLocked = false;
                                            });
                                        }
                                    } catch (e) {
                                        $(self.refs['btnAttributeSave']).attr("disabled", false);
                                        $(self.refs['btnAttributeSave']).button('reset');
                                        $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                                        $('#' + self.model.ComponentId + '_btnAttributeSend').button('reset');
                                        $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                                        $('#' + self.model.ComponentId + '_btnStartWorkflow1').button('reset');
                                        $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                                        $('#' + self.model.ComponentId + '_btnAttributeRegister').button('reset');
                                        $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                                        $('#' + self.model.ComponentId + '_btnAttributeTransfer').button('reset');
                                        $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                                        $('#' + self.model.ComponentId + '_btnAttributeComplete').button('reset');
                                        $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                                        $('#' + self.model.ComponentId + '_btnAttributeProceed').button('reset');
                                        gLocked = false;
                                    }
                                }
                            }
                        }
                    })
                });
            }
            else { // if category isBroadcast in internal is not checked (false) which is the case
                $('#' + self.model.ComponentId + '_btnAttributeComplete').click(function () {
                    if (window.EnableConfirmationMessage === "True") {
                        Common.showConfirmMsg(Resources.CompleteCorrespondenceConfirmation, function () {
                            openDocumentCompleteModal(self);
                        });
                    } else {
                        openDocumentCompleteModal(self);
                    }
                });
            }

            $('#' + self.model.ComponentId + '_btnComplete').click(function () {
                Common.showConfirmMsg(Resources.CompleteOneTsfConfirmation, function () {
                    if (!self.model.fromVip) {
                        Common.mask(document.getElementById('grdInboxItems'), "inboxListContainer-mask");
                    } else {
                        Common.mask(document.getElementById('inboxListContainer'), "inboxListContainer-mask");
                    }
                    Common.ajaxPost('/Transfer/Complete',
                        {
                            'ids': self.model.transferId, 'delegationId': self.model.delegationId, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                        },
                        function (result) {
                            if (result[0]) {
                                if (result[0].updated && result[0].uncompletedDocumentReferenceNumber && !result[0].message) {                                   
                                    var msg = Resources.TransferWasCompletedNoOriginalMail;
                                    if (result[0].documentAttachmentIdHasValue) {
                                        msg = Resources.TransferWasCompletedNotLastOpenTransfer;
                                    }
                                    setTimeout(function () {
                                        Common.alertConfirmation(msg, function () {
                                            $("#gridContainerDiv").show();
                                            Common.unmask("inboxListContainer-mask");
                                            $("div [ref=" + self.model.parentComponentId + "]").remove();
                                            TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                                            TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                                            TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                                            TreeNode.refreshTreeNodeCounts(TreeNode.Closed);
                                            if (!gFromVip) {
                                                GridCommon.RefreshCurrentPage("grdInboxItems", true);
                                            } else {
                                                $(".withBorders-o").addClass("waitingBackground");
                                                $("#inboxDocumentDetailsContainer").empty();
                                                $($("input[data-id='" + self.model.transferId + "']").parents("li")[0]).fadeOut().remove();
                                            }
                                        });
                                    }, 300);

                                } else if (result[0].updated) {
                                    Common.unmask("inboxListContainer-mask");
                                    Common.showScreenSuccessMsg();
                                    $("#gridContainerDiv").show();
                                    tryCloseModal(self.model.actionComponentId);
                                    $("div [ref=" + self.model.actionComponentId + "]").remove();
                                    TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                                    TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                                    TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                                    TreeNode.refreshTreeNodeCounts(TreeNode.Closed);
                                    TreeNode.refreshTreeNodeCounts(TreeNode.MyRequests);
                                    TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
                                    if (!gFromVip) {
                                        GridCommon.RefreshCurrentPage("grdInboxItems", true);
                                    } else {
                                        $(".withBorders-o").addClass("waitingBackground");
                                        $("#inboxDocumentDetailsContainer").empty();
                                        $($("input[data-id='" + self.model.transferId + "']").parents("li")[0]).fadeOut().remove();
                                    }
                                } else {
                                    if (result[0].message === "HasLockedAttachmentsByUser") {
                                        setTimeout(function () {
                                            Common.alertMsg(Resources.HasLockedAttachmentsByUser);
                                        }, 300);
                                        Common.unmask("inboxListContainer-mask");
                                    } else if (result[0].message === "OriginalDocumentLockedByUser") {
                                        setTimeout(function () {
                                            Common.alertMsg(Resources.OriginalDocumentLockedByUser);
                                        }, 300);
                                        Common.unmask("inboxListContainer-mask");
                                    } else if (result[0].message === "TransferHasDifferentOwnerOrIsCarbonCopy") {
                                        setTimeout(function () {
                                            Common.alertMsg(Resources.TransferHasDifferentOwnerOrIsCarbonCopy);
                                        }, 300);
                                        Common.unmask("inboxListContainer-mask");
                                    } else {
                                        setTimeout(function () {
                                            Common.alertMsg(Resources.CorrespondenceNotCompleteNoOriginalMail);
                                        }, 300);
                                        Common.unmask("inboxListContainer-mask");
                                    }
                                }
                            } else {
                                Common.showScreenErrorMsg();
                            }
                        }, function () {
                            Common.showScreenErrorMsg();
                            Common.unmask("inboxListContainer-mask");
                        }, false);
                });
            });

            
            $('#' + self.model.ComponentId + '_btnAttributeReopen').click(function () {
                Common.ajaxPost('/Document/Reopen',
                    {
                        'documentIds': self.model.id, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                    },
                    function (result) {
                        swal.close()
                        if (!result) {
                            Common.alertMsg(Resources.ErrorOccured);
                        }
                        else {
                            $("[ref=" + self.model.ComponentId + "] :input").attr("disabled", false);
                            $("[ref=" + self.model.ComponentId + "] div").attr("disabled", false);
                            $("[ref=" + self.model.ComponentId + "] [ref=builder]").css("pointer-events", "auto");

                            $("[ref=" + self.model.ComponentId + "] [ref=grdAssigneeItems] .delete").attr("disabled", false);

                            $("#followUpAssigneesdata :input").attr("disabled", false);
                            $("#followUpLinkedCorrespondencesData :input").attr("disabled", false);

                            $("[ref=" + self.model.ComponentId + "] [ref=btnAttributeReopen]").css("pointer-events", "none");
                            $("[ref=" + self.model.ComponentId + "] [ref=btnAttributeReopen]").attr("disabled", true);

                            $(self.refs['btnAttributeSave']).removeClass("hidden");
                            $('#' + self.model.ComponentId + '_btnAttributeReopen').addClass("hidden");
                        }
                    }, null, false);
            });
        }
        //$(document).on('click', '.barcodePanel', function () {
        //    let refNumber = $('[id*="_refNumberPanelHeader"]').text();
        //    if (refNumber) {
        //        let docId = self.model.id;
        //        viewDocumentBarcode(docId, refNumber, model.delegationId);
        //    }
        //});

        $(self.refs['openSenderAddressBook']).click(function () {
            var mode = AddressBookMode.External;
            var structuretype;
            if (basicAttributes.length > 0) {
                var sendingEntityObj = $.grep(basicAttributes, function (e) {
                    return e.Name === "SendingEntity";
                });
                if (sendingEntityObj /*&& sendingEntityObj[0].Type !== "external"*/) {
                    switch (sendingEntityObj[0].Type) {
                        case "external":
                            mode = AddressBookMode.Internal;
                            structuretype = StructureTypeMode.External;
                            break;
                        case "internal":
                            structuretype = StructureTypeMode.Internal;
                            break;
                        case "both":
                            structuretype = StructureTypeMode.Both;
                            break;
                        default:
                    }
                }
            }
            if (!(sendingEntityObj[0].DisableField)) {
                openSendingAndReceivingAddressBook(AddressBookSelectionMode.Single, mode, function (data) {
                    var option = new Option(data.name, data.structureId, false, false);
                    $(self.refs['cmbCustomAttributeSender']).append(option);
                    $(self.refs['cmbCustomAttributeSender']).val(data.structureId).trigger("change");
                    $('.modalAddressBook').modal('hide');
                }, self, structuretype,true);
            }
        });
        $(self.refs['openSenderDesignatedPerson']).click(function () {
            var isExternal = false;
            if (basicAttributes.length > 0) {
                var sendingEntityObj = $.grep(basicAttributes, function (e) {
                    return e.Name === "SendingEntity";
                });
                if (sendingEntityObj && sendingEntityObj[0].Type == "external") {
                    isExternal = true;
                }
            }
            if (isExternal) {
                var structureIds = $(self.refs['cmbCustomAttributeSender']).val();
                var firstOptionText = $(self.refs['cmbCustomAttributeSender']).find('option[value="' + structureIds + '"]').text();
                var element = document.getElementById('modalOpenDesignatedPerson');
                var wrapper = $(element).find('.modal-body');
                var model = new OrganizationManagement.DesignatedPerson();
                model.nodeId = 0;
                model.nodeText = '';
                model.externalStructureId = structureIds;
                model.firstOptionText = firstOptionText;
                model.externalStructureIds = structureIds;
                var grpIndex = new OrganizationManagement.DesignatedPersonView(wrapper, model);
                grpIndex.render();
                $('#modalOpenDesignatedPerson').modal('show');
                $("#modalOpenDesignatedPerson").off("hidden.bs.modal");
                $("#modalOpenDesignatedPerson").off("shown.bs.modal");
            }
        });
        $(self.refs['openRecieverDesignatedPerson']).click(function () {
            var isExternal = false;
            if (basicAttributes.length > 0) {
                var receivingEntityObj = $.grep(basicAttributes, function (e) {
                    return e.Name === "ReceivingEntity";
                });
                if (receivingEntityObj && receivingEntityObj[0].Type == "external") {
                    isExternal = true;
                }
            }
            if (isExternal) {
                var structureIds = $(self.refs['cmbCustomAttributeReceiver']).val();
                var firstOptionText;
                var externalStructureId;
                if (typeof structureIds === 'string') {
                    firstOptionText = $(self.refs['cmbCustomAttributeReceiver']).find('option[value="' + structureIds + '"]').text();
                    externalStructureId = structureIds;
                } else if (typeof structureIds === 'object' && structureIds !== null) {
                    firstOptionText = $(self.refs['cmbCustomAttributeReceiver']).find('option[value="' + structureIds[0] + '"]').text();
                    externalStructureId = structureIds[0];
                } else {
                    firstOptionText = '';
                    externalStructureId = '';
                }
                var element = document.getElementById('modalOpenDesignatedPerson');
                var wrapper = $(element).find('.modal-body');
                var model = new OrganizationManagement.DesignatedPerson();
                model.nodeId = 0;
                model.nodeText = '';
                model.externalStructureId = externalStructureId;
                model.firstOptionText = firstOptionText;
                model.externalStructureIds = structureIds;
                var grpIndex = new OrganizationManagement.DesignatedPersonView(wrapper, model);
                grpIndex.render();
                $('#modalOpenDesignatedPerson').modal('show');
                $("#modalOpenDesignatedPerson").off("hidden.bs.modal");
                $("#modalOpenDesignatedPerson").off("shown.bs.modal");
            }
        });

        $(self.refs['openReceiverAddressBook']).click(function () {
            var mode = AddressBookMode.External;
            var structuretype;
            var multiple = AddressBookSelectionMode.Multiple;
            if (basicAttributes.length > 0) {
                var receivingEntityObj = $.grep(basicAttributes, function (e) {
                    return e.Name === "ReceivingEntity";
                });
                if (receivingEntityObj /*&& receivingEntityObj[0].Type !== "external"*/) {
                    switch (receivingEntityObj[0].Type) {
                        case "external":
                            mode = AddressBookMode.Internal;
                            structuretype = StructureTypeMode.External;
                            break;
                        case "internal":
                            structuretype = StructureTypeMode.Internal;
                            break;
                        case "both":
                            structuretype = StructureTypeMode.Both;
                            break;
                        default:
                    }
                    //mode = AddressBookMode.Internal;
                }
                if (!receivingEntityObj[0].MultipleReceivingEntity) {
                    multiple = AddressBookSelectionMode.Single;
                }
            }
            if (!(receivingEntityObj[0].DisableField)) {
            openSendingAndReceivingAddressBook(multiple, mode, function (data) {
                if (multiple == AddressBookSelectionMode.Single) {
                    if ($(self.refs['cmbCustomAttributeReceiver']).find("option[value=" + data.structureId + "]").length <= 0) {
                        var option = new Option(data.name, data.structureId, true, true);
                        $(self.refs['cmbCustomAttributeReceiver']).append(option).trigger("change");
                    } else {
                        $(self.refs['cmbCustomAttributeReceiver']).val(data.structureId).trigger("change");
                    }
                } else {
                    for (var i = 0; i < data.length; i++) {
                        if ($(self.refs['cmbCustomAttributeReceiver']).find("option[value=" + data[i].structureId + "]").length <= 0) {
                            var option = new Option(data[i].name, data[i].structureId, true, true);
                            $(self.refs['cmbCustomAttributeReceiver']).append(option).trigger("change");
                        } else {
                            var selectedIds = $(self.refs['cmbCustomAttributeReceiver']).val() != null ? $(self.refs['cmbCustomAttributeReceiver']).val() : [];
                            if (selectedIds.indexOf(data[i].structureId) < 0) {
                                selectedIds.push(data[i].structureId);
                            }
                            $(self.refs['cmbCustomAttributeReceiver']).val(selectedIds).trigger("change");
                        }
                    }
                }
                $('.modalAddressBook').modal('hide');
            }, self, structuretype,false);
            }
        });
        $(self.refs['openBroadcastReceiverAddressBook']).click(function () {
            var mode = AddressBookMode.External;
            var multiple = AddressBookSelectionMode.Multiple;
            if (basicAttributes.length > 0) {
                var receivingEntityObj = $.grep(basicAttributes, function (e) {
                    return e.Name === "ReceivingEntity";
                });
                if (receivingEntityObj && receivingEntityObj[0].Type !== "external") {
                    mode = AddressBookMode.Internal;
                }
                if (!receivingEntityObj[0].MultipleReceivingEntity) {
                    multiple = AddressBookSelectionMode.Single;
                }
            }
            openEntityGroupBook(multiple, mode, function (data) {
                if (multiple == AddressBookSelectionMode.Single) {
                    if ($(self.refs['cmbCustomAttributeReceiver']).find("option[value=EGroup-" + data.id + "]").length <= 0) {
                        var option = new Option(data.name, 'EGroup-' + data.id, true, true);
                        $(self.refs['cmbCustomAttributeReceiver']).append(option).trigger("change");
                    } else {
                        $(self.refs['cmbCustomAttributeReceiver']).val('EGroup-' + data.id).trigger("change");
                    }
                } else {
                    for (var i = 0; i < data.length; i++) {
                        if ($(self.refs['cmbCustomAttributeReceiver']).find("option[value=EGroup-" + data[i].id + "]").length <= 0) {
                            var option = new Option(data[i].name, 'EGroup-' + data[i].id, true, true);
                            $(self.refs['cmbCustomAttributeReceiver']).append(option).trigger("change");
                        } else {
                            var selectedIds = $(self.refs['cmbCustomAttributeReceiver']).val() != null ? $(self.refs['cmbCustomAttributeReceiver']).val() : [];
                            if (selectedIds.indexOf('EGroup-' + data[i].id) < 0) {
                                selectedIds.push('EGroup-' + data[i].id);
                            }
                            $(self.refs['cmbCustomAttributeReceiver']).val(selectedIds).trigger("change");
                        }
                    }
                }
                $('.modalEntityGroup').modal('hide');
            }, self);
        });
        $(self.refs['openCarbonCopyAddressBook']).click(function () {
            var mode = AddressBookMode.External;
            var structuretype;
            if (basicAttributes.length > 0) {
                var carbonCopyObj = $.grep(basicAttributes, function (e) {
                    return e.Name === "CarbonCopy";
                });
                if (carbonCopyObj && carbonCopyObj[0].Type !== "external") {
                    mode = AddressBookMode.Internal;
                }
                if (carbonCopyObj) {
                    switch (carbonCopyObj[0].Type) {
                        case "external":
                            mode = AddressBookMode.External;
                            structuretype = StructureTypeMode.External;
                            break;
                        case "internal":
                            mode = AddressBookMode.Internal;
                            structuretype = StructureTypeMode.Internal;
                            break;
                        case "both":
                            structuretype = StructureTypeMode.Both;
                            break;
                        default:
                    }
                }
            }
            
            openAddressBook(AddressBookSelectionMode.Multiple, mode, function (data) {
                for (var i = 0; i < data.length; i++) {
                    if ($(self.refs['cmbCustomAttributeCarbonCopy']).find("option[value = " + data[i].structureId + "]").length <= 0) {
                        var option = new Option(data[i].name, data[i].structureId, true, true);
                        $(self.refs['cmbCustomAttributeCarbonCopy']).append(option).trigger("change");
                    } else {
                        var selectedIds = $(self.refs['cmbCustomAttributeCarbonCopy']).val() != null ? $(self.refs['cmbCustomAttributeCarbonCopy']).val() : [];
                        if (selectedIds.indexOf(data[i].structureId) < 0) {
                            selectedIds.push(data[i].structureId);
                        }
                        $(self.refs['cmbCustomAttributeCarbonCopy']).val(selectedIds).trigger("change");
                    }
                }
                $('.modalAddressBook').modal('hide');
            }, structuretype, self);
        });
        //setTimeout(function () {
        //    $(".tabDocumentDetails").scrollingTabs('refresh');
        //}, 500);
        $(self.refs['formDocumentAttributePost']).on('change', function () {
            var $form = $(self.refs['formDocumentAttributePost']);
            $form.parsley().reset();
            var isValid = $form.parsley().isValid();
            if (isValid && formioFrom) {
                $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnAttributeExport').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", false);
                $('#btnGroupDropup').attr('disabled', false);

            }
            else if (!isValid) {
                $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", true);
                $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", true);
                $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", true);
                $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", true);
                $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", true);
                $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", true);
                $('#' + self.model.ComponentId + '_btnAttributeExport').attr("disabled", true);
                $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", true);

                $('#btnGroupDropup').attr('disabled', true);

            }
        });
        $(self.refs['txtCustomAttributeSubject']).on("keyup", function (e) {
            var $form = $(self.refs['formDocumentAttributePost']);
            $form.parsley().reset();
            var isValid = $form.parsley().isValid();
            if (isValid && formioFrom) {
                $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnAttributeExport').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", false);
                $('#btnGroupDropup').attr("disabled", false);
            }
            else if (!isValid) {
                $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", true);
                $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", true);
                $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", true);
                $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", true);
                $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", true);
                $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", true);
                $('#' + self.model.ComponentId + '_btnAttributeExport').attr("disabled", true);
                $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", true);
                $('#btnGroupDropup').attr("disabled", true);
            }
        });


        $(self.refs['txtCustomAttributeExternalReferenceNumber']).on("keyup", function (e) {
            var $form = $(self.refs['formDocumentAttributePost']);
            $form.parsley().reset();
            var isValid = $form.parsley().isValid();
            if (isValid && formioFrom) {
                $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnAttributeExport').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", false);
                $('#btnGroupDropup').attr("disabled", false);
            }
            else if (!isValid) {
                $('#' + self.model.ComponentId + '_btnAttributeSend').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnStartWorkflow1').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnAttributeRegister').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnAttributeTransfer').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnAttributeComplete').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnAttributeProceed').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnAttributeExport').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", false);
                $('#btnGroupDropup').attr("disabled", false);
            }
        });



        if ($("[id*='meetingType']").length > 0) {

            $("[id*='meetingType']").on('change', function () {
                $("[id*='receivers'] option").remove();
                $('[id*=receivers]').parent().children("div").first().children().remove()

                setTimeout(function () {
                    Common.ajaxGet('/Committee/GetCommitteeUsers?Id=' + $('[id*=meetingType]').val(), null, function (data) {
                        console.table(data);
                        //var usrerObj = getFullUserObj(2);
                        //var userName = usrerObj.fullName;
                        //var structuerName = usrerObj.structures.find(x => x.id == model.createdByStructureId).name;
                        //var userStructuerName = structuerName + "/ " + userName;
                        //var userStructuerId = "User_" + model.createdByStructureId + "_2";
                        //var currUser = {
                        //    id:2,
                        //    name:userStructuerName,
                        //    userStructureId:userStructuerId
                        //};
                        //var exsitUser = data.some(x => x['userStructureId'] === userStructuerId);   // data.find(x => x.userStructuerId == userStructuerId);
                        //if (!exsitUser) {
                        //    data.push(currUser);
                        //}

                        for (i = 0; i < data.length; i++) {
                            var newDiv = document.createElement("div");
                            // Set attributes for the div
                            newDiv.setAttribute("class", "choices__item choices__item--selectable");
                            newDiv.setAttribute("id", data[i].id);
                            newDiv.setAttribute("data-item", "");
                            //  newDiv.setAttribute("data-id", data[i].id);
                            newDiv.setAttribute("data-value", data[i].userStructureId);
                            newDiv.setAttribute("data-custom-properties", "null");
                            newDiv.setAttribute("aria-selected", "true");
                            newDiv.setAttribute("data-deletable", "");

                            // Create a span element
                            var newSpan = document.createElement("span");
                            newSpan.textContent = data[i].name;

                            // Create a button element
                            var newButton = document.createElement("button");
                            newButton.setAttribute("type", "button");
                            newButton.textContent = "Remove item";
                            newButton.setAttribute("class", "choices__button");
                            newButton.setAttribute("data-button", "");
                            newButton.setAttribute("aria-label", "Remove item: '" + data[i].id + "'");
                            newButton.setAttribute("id", data[i].id);

                            newButton.addEventListener("click", function () {
                                deleteOption(this);
                            });

                            // Append the span and button elements to the div
                            newDiv.appendChild(newSpan);
                            newDiv.appendChild(newButton);

                            // Append the div to the document body (or any other parent element)
                            $('[id*=receivers]').parent().children("div").first().append(newDiv);

                            //var newSpan2 = document.createElement("span");
                            //newSpan2.textContent = data[i].name;

                            //var newOption = document.createElement("option");
                            //newOption.value = data[i].id;
                            //newOption.appendChild(newSpan2);

                            //$('[id*=receivers]').append(newOption);


                            //   $('[id*=receivers]').append(new Option(data[i].name, data[i].id, true, true)).trigger('change');

                        }
                    });
                }, 300);


            });

        }
        EventReceiver.AttributesAfterRender(self);

        if (self.model.isFollowUp == true) {

            if ((window.location.hash.split("/")[0]).toLowerCase() == '#document' || (window.location.hash.split("/")[0]).toLowerCase() == '#myrequests' || (window.location.hash.split("/")[0]).toLowerCase() == '#sent') {
                $('#' + self.model.ComponentId + '_btnRequestToComplete').addClass("hidden");
                //$(self.refs['btnRequestToComplete']).addClass("hidden");

            }

            if ((window.location.hash.split("/")[0]).toLowerCase() == '#closed') {
                $(self.refs['btnAttributeSave']).addClass("hidden");
                $('#' + self.model.ComponentId + '_btnAttributeReopen').removeClass("hidden");
            }

        }
        else {
            if (!(actionArray.includes("Attribute.Save"))) {
                $(self.refs['btnAttributeSave']).addClass("hidden");
                if (!(window.location.hash.includes("#search") || window.location.hash.includes("#manageCorrespondence") || window.location.hash.toLowerCase().includes("#inbox"))) {
                    $('#' + self.model.ComponentId + '_btnAttributeRegister').addClass("hidden");
                    $('#' + self.model.ComponentId + '_btnAttributeSave').addClass("hidden");

                }
                else {
                    $(self.refs['btnAttributeSave']).addClass("show");
                    $('#' + self.model.ComponentId + '_btnAttributeRegister').addClass("hidden");

                }
            }

            if (!(actionArray.includes("Attribute.Register"))) {
                if (!(window.location.hash.includes("#search") || window.location.hash.includes("#manageCorrespondence") || window.location.hash.toLowerCase().includes("#inbox"))) {

                    $('#' + self.model.ComponentId + '_btnAttributeRegister').addClass("hidden");
                    $('#' + self.model.ComponentId + '_btnAttributeSave').addClass("hidden");

                }
                else if (window.location.hash.toLowerCase().includes("#inbox")) {
                    $('#' + self.model.ComponentId + '_btnAttributeSave').addClass("show");
                    $('#' + self.model.ComponentId + '_btnAttributeRegister').addClass("hidden");
                }
                else {
                    $('#' + self.model.ComponentId + '_btnAttributeSave').addClass("show");
                    $('#' + self.model.ComponentId + '_btnAttributeRegister').addClass("show");
                }
            }
        }

        
        if (window.location.hash.split("/")[0] == '#manageFollowUp')
            $(self.refs['btnAttributeSave']).remove();

        //self.model.isTaskCreator == true ? $('.btnCompleteFollowUp').show() : $('.btnCompleteFollowUp').remove();

        if (self.model.isFollowUp && (window.location.hash.split("/")[0]).toLowerCase() == "#closed") {
            $("[ref=" + self.model.ComponentId +"] :input").attr("disabled", true);
            $("[ref=" + self.model.ComponentId +"] div").attr("disabled", true);
            $("[ref=" + self.model.ComponentId +"] [ref=builder]").css("pointer-events", "none");

            $("[ref=" + self.model.ComponentId + "] [ref=grdAssigneeItems] .delete").attr("disabled", true);

            $("#followUpAssigneesdata :input").attr("disabled", true);
            $("#followUpLinkedCorrespondencesData :input").attr("disabled", true);
            
            $("[ref=" + self.model.ComponentId +"] [ref=btnAttributeReopen]").css("pointer-events", "auto");
            $("[ref=" + self.model.ComponentId +"] [ref=btnAttributeReopen]").attr("disabled", false);
        }

        $(self.refs['lnkDocumentBarcodeView']).click(function () {
            if (model.referenceNumber) {
                let docId = self.model.id;
                viewDocumentBarcode(docId, model.referenceNumber, model.delegationId);
            }
        });
    }

    
}
export default { Document, DocumentView };
