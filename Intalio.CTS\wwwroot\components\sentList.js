﻿import Intalio from './common.js'
import DocumentDetails from './documentDetails.js'
import { Categories, Nodes } from './lookup.js'
import ReportCorrespondenceDetailExport from './reportCorrespondenceDetailExport.js'
import LinkedCorrespondenceIndex from './linkedCorrespondenceIndex.js'
import LinkedCorrespondenceDocument from './linkedCorrespondenceDocument.js'
import VipDocumentSent from './vipSentList.js'
import CustomizeNodeColomns from '../components/customizeNodeColomns.js'
import VisualTracking from './visualTracking.js'
import DocumentManageStructureUsersCorrespondence from './manageStructureUsersCorrespondences.js'

class DocumentSent extends Intalio.Model {
    constructor() {
        super();
        this.title = null;
        this.delegationId = null;
        this.nodeId = null;
        this.categories = null;
        this.statuses = null;
        this.priorities = null;
        this.privacies = null;
        this.fromStructureSent = false;

    }
}
var gfirstTime = true;
var gDataChanged = true;
var gAction = "";
var gFilterChanged = false;
var gFilterStructure = "";
var gFilterFromDate = "";
var gFilterToDate = "";
var gFilterUser = "";
var gFilterOverdue = "";
var gOverdue = false;
var clear = false;
var gReportName = "Sent Report";
var fromStructureSent = false;

function format(row, nodeId) {
    return '<table  style="width:100%" cellspacing="0" border="0">' + buildColumnsDetails(row, nodeId) + '</table>';
}
function showCannotRecalledMessages(refresh, cannotBeRecalled) {
    var msg = '';
    cannotBeRecalled.forEach(item => {
        if (item) {
            msg += `\n ○ -` + `${item}` + `\n`;
        }
    });
    if (msg !== "") {
        setTimeout(function () {
            if (refresh) {
                Common.alertMsg(msg, function () {
                    swal.close()
                    TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                    TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);
                    TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                    $('.close').trigger('click');
                    GridCommon.RefreshCurrentPage(gTableName, false);
                })
            } else {
                Common.alertMsg(msg);
            }
        }, 300);
    }
}
function openRecallReasonModal(callback) {
    const modal = $(`
    <div data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-hidden="true" class="modal fade modalRecall" id="recallModal">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="border-bottom:0px; padding-bottom:3px;">
                    <button type="button" ref="recallClose" id="recallActionModalClose" class="close" data-dismiss="modal">&times;</button>
                    <h4 ref="modalRecallTitle" class="modal-title"></h4>
                </div> 
                <div class="modal-body" style="padding-top: 2px;">
                    <form ref="formPost" method="post" data-parsley-validate="" novalidate="">
                        <div class="row">
                            <div class="col-md-12" ref="recallReasonContainer">
                                <label class="control-label field-required" style="font-size: medium;">${Resources.RecallReason}</label>
                                <textarea id="recallReason" rows="3" class="form-control" required></textarea>
                                <div class="invalid-feedback" style="display:none; color:red;">
                                    ${Resources.RequiredField}
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer" style="border-top:0px;">
                    <button type="button" class="btn btn-primary" id="submitReason">${Resources.Recall}</button>
                    <button type="button" class="btn btn-secondary" id="cancelRecall" data-bs-dismiss="modal">${Resources.Cancel}</button>
                </div>
            </div>
        </div>
    </div>
    `);

    $('body').append(modal);
    modal.modal('show');

    modal.find('#submitReason').on('click', function () {
        const textarea = modal.find('#recallReason');
        const reason = textarea.val().trim();                 // Removes any leading or trailing whitespace from the recall reason input value
        const errorMsg = textarea.siblings('.invalid-feedback');

        if (!reason) {
            textarea.addClass('is-invalid');
            errorMsg.show();  // Shows the error message
            //modal.find('form').addClass('was-validated');
            return;
        } else {
            textarea.removeClass('is-invalid');
            errorMsg.hide(); // Hides error message
            // modal.modal('hide').remove();           // Once there is added text  removes the error msg and red border automatically.
            $("#recallActionModalClose").trigger("click");
            callback(reason);
        }
    });

    // Remove required validation styles once there is an input change or input added
    modal.find('#recallReason').on('input', function () {
        $(this).removeClass('is-invalid');
        $(this).siblings('.invalid-feedback').hide();
    });
    modal.find('#cancelRecall').on('click', function () {
        modal.find('#recallReason').val('');
        modal.modal('hide');

    });
    modal.on('hidden.bs.modal', function () {
        modal.remove();
    });
}

// The textarea is required (via data-parsley-required and required attributes), ensuring the user cannot submit without entering text.
// UI Production: The modal is dynamically created using a jQuery template literal and appended to the body.


function formatChild(self, parentRow) {
    var parentData = parentRow.data();
    var childrenData = datatableParentsAndChildren
        .filter(function (item) {
            return item.parent.id === parentData.id;
        }).flatMap(function (item) {
            return item.children;
        });
    var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));
    var Buttons = SecurityMatrix.getToolbarActions(securityMatrix, self.model.nodeId, gTableName);
    var childrenColumns = [
        {
            visible: Buttons.length > 0,
            title: '',
            width: '16px',
            "orderable": false,
            "render": function (data, type, row) {
                return !_isFollowUpNode ? "<input type='checkbox' onclick='event.stopPropagation();' data-id=" + row.id + " />" : "<input type='checkbox' parent=" + row.documentId + " data-id=" + row.id + " checked/>";
            }//return "<input type='checkbox' onclick='event.stopPropagation();' data-id=" + row.id + " />"; }
        },
        { title: "Id", data: "id", visible: false, "orderable": false }];
    buildColumns(childrenColumns, self.model.nodeId, true)
    if (_isFollowUpNode) {
        childrenColumns.push({
            "className": "text-right",
            "autoWidth": false,
            "bAutoWidth": false,
            width: "16px",
            'orderable': false,
            'sortable': false,
            'render': function (data, type, full, meta) {
                var html = "";
                var requestToCompleteStatus = new CoreComponents.Lookup.Statuses().findById(full.transferStatusId, 'en');
                if (requestToCompleteStatus) {
                    if (requestToCompleteStatus.text.replace(/\s/g, '').toLowerCase() === "requesttocomplete") {
                        //if (requestToCompleteStatus.id === 7) {
                        html += "<div class='mr-sm' title='" + Resources.RequestToComplete + "'><i class='fa fa-check-square-o mr-sm text-info'></i></div>&nbsp;";
                    }
                }
                return "<div id='divLock" + full.id + "' style='display: inline-flex;'>" + html + "</div>";
            }
        });
    }

    childrenColumns.push({
        "className": "text-right",
        "autoWidth": false,
        "bAutoWidth": false,
        width: "16px",
        'orderable': false,
        'sortable': false,
        'render': function (data, type, full, meta) {
            var html = '';
            if (!_isFollowUpNode) {
                if (full.openedDate === "") {
                    let btnRecall = document.createElement("button");
                    btnRecall.setAttribute("class", "btn btn-xs btn-success mr-sm recall");
                    btnRecall.setAttribute("title", Resources.Recall);
                    btnRecall.setAttribute("clickattr", "event.stopPropagation(),recall(" + full.id + ", " + self.model.delegationId + ")");
                    btnRecall.innerHTML = "<i class='fa fa-repeat fa-white'/>";
                    html += btnRecall.outerHTML;
                }
            }

            let btnView = document.createElement("button");
            btnView.setAttribute("class", "btn btn-xs btn-warning mr-sm view");
            btnView.setAttribute("title", Resources.View);
            btnView.setAttribute("clickattr", "event.stopPropagation(),openDocument(" + full.id + ", " + self.model.delegationId + ", " + self.model.nodeId + ")");
            btnView.innerHTML = "<i class='fa fa-eye fa-white'/>";
            html += btnView.outerHTML;

            if (!_isFollowUpNode) {
                const visualtrackingBtn = document.createElement("button");
                visualtrackingBtn.setAttribute("class", "btn btn-xs btn-success visualtracking");
                visualtrackingBtn.setAttribute("title", Resources.ViewVisualTracking);
                visualtrackingBtn.setAttribute("clickattr", "VisualTracking.openVisualTracking(" + full.documentId + "," + self.model.delegationId + ")");
                visualtrackingBtn.innerHTML = "<i class='fa fa-sitemap fa-white'/>";
                html += visualtrackingBtn.outerHTML;
            }

            return "<div style='display: inline-flex;'>" + html + "</div>";
        }
    });

    SecurityMatrix.getRowActions(securityMatrix, childrenColumns, self.model.nodeId);

    var childTable = $('<table id="childTable_' + parentData.id + '" class="table table-striped table-hover followUpTasks" style="border: 1px solid black;width: 100%;"></table>');
    childTable.DataTable({
        data: childrenData,
        columns: childrenColumns,
        paging: false,
        searching: false,
        info: false

    });

    childTable.on('click', 'td.details-control', function (event) {
        event.stopPropagation(); // Stop event propagation to parent table
        var tr = $(this).closest('tr');
        var row = childTable.DataTable().row(tr);
        if (row.child.isShown()) {
            row.child.hide();
            tr.removeClass('shown');
        } else {
            row.child(format(row, self.model.nodeId)).show();
            tr.addClass('shown');
        }
    });

    childTable.find('tbody').find('.recall,.view,.visualtracking').on('click', function () {
        var onclick = $(this).attr("clickattr");
        eval(onclick);
    });
    childTable.find('tbody').on('dblclick', 'tr', function (event, d, f) {
        if (!gLocked) {
            gLocked = true;
            try {
                var onclick = $(this).find(".view").attr("clickattr");
                eval(onclick);
            } catch (e) {
                gLocked = false;
            }
        }
    });
    childTable.find('tbody').find('tr').on('click', function (event) {
        event.stopPropagation(); // Stop event propagation to parent table

        if ($(event.target).is('td.details-control')) {
            var tr = $(this).closest('tr');
            var row = childTable.DataTable().row(tr);
            if (row.child.isShown()) {
                row.child.hide();
                tr.removeClass('shown');
            } else {
                row.child(format(row, self.model.nodeId)).show();
                tr.addClass('shown');
            }


        }
        else {
            var tr = $(this)[0];
            var data = childTable.DataTable().row(tr).data();
            if ($('[data-id=' + data.id + ']').prop('checked')) {

                $('[data-id=' + data.id + ']').prop("checked", false);
                $('[data-id=' + data.id + ']').trigger("change");

            }
            else {
                $('[data-id=' + data.id + ']').prop("checked", true);
                $('[data-id=' + data.id + ']').trigger("change");
            }
        }
    });

    childTable.on('click', 'td.followUpTask', function (event) {
        event.stopPropagation();
        let tr = $(this).closest('tr');
        var data = childTable.DataTable().row(tr).data();

        if ($('[data-id=' + data.id + ']').prop('checked')) {
            var isAllChecked = 0;
            $('[parent=' + data.id + ']').each(function () {
                if (!this.checked)
                    isAllChecked = 1;
            });

            if (isAllChecked == 0) {
                $('[data-id=' + data.id + ']').prop("checked", true);
                $('[data-id=' + data.id + ']').trigger("change");
            }
        }

        else {
            $('[data-id=' + data.id + ']').prop("checked", false);
            $('[data-id=' + data.id + ']').trigger("change");
        }
    });
    return childTable
}
function openDocument(id, delegationId, nodeId) {
    var params = { id: id };
    if (delegationId !== null) {
        params.delegationId = delegationId;
    }
    //Common.ajaxGet('/Document/GetDocumentBasicInfoByTransferId', params, function (response) {
    Common.ajaxGet('/Transfer/GetTransferDetailsById', params, function (response) {
        gLocked = false;
        if (response.id !== null) {
            let item = "liSent" + nodeId;
            if (delegationId !== null) {
                item = "sent-" + nodeId;
            }
            Common.setActiveSidebarMenu(item);
            $(".delegation").removeClass("active");
            //$("#gridContainerDiv").hide();
            var wrapper = $(".modal-documents");
            var linkedCorrespondenceModel = new LinkedCorrespondenceIndex.LinkedCorrespondenceIndex();
            linkedCorrespondenceModel.reference = response.referenceNumber;
            linkedCorrespondenceModel.subject = response.subject;
            linkedCorrespondenceModel.documentId = response.documentId;
            //linkedCorrespondenceModel.subject = $("#" + model.ComponentId + "_subjectTaskPanel").html();
            //linkedCorrespondenceModel.from = $("#" + model.ComponentId + "_fromTaskPanel").html();
            //linkedCorrespondenceModel.to = $("#" + model.ComponentId + "_toTaskPanel").html();
            //linkedCorrespondenceModel.transferDate = $("#" + model.ComponentId + "_transferDateTaskPanel").html();
            //linkedCorrespondenceModel.registerDate = $("#" + model.ComponentId + "_registerDateTaskPanel").html();
            //linkedCorrespondenceModel.registeredBy = $("#" + model.ComponentId + "_registerByTaskPanel").html();
            var linkedCorrespondenceDocument = new LinkedCorrespondenceDocument.LinkedCorrespondenceDocumentView(wrapper, linkedCorrespondenceModel);
            linkedCorrespondenceDocument.render();
            var model = new DocumentDetails.DocumentDetails();
            model.readonly = true;
            model.delegationId = delegationId;
            model.id = id;
            model.documentId = response.documentId;
            model.referenceNumber = response.referenceNumber;
            model.categoryName = response.categoryName;
            model.categoryId = response.categoryId;
            model.statusId = response.status;
            model.createdByUser = response.createdByUser;
            model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
            model.showMyTransfer = true;
            model.attachmentId = response.attachmentId;
            model.attachmentVersion = response.attachmentVersion;
            model.categoryId = response.categoryId;
            var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));
            var tabs = securityMatrix.SecurityNodes[nodeId].SecurityCategories[response.categoryId].Tabs;
            model.tabs = $.grep(tabs, function (element, index) {
                return !element.Name.includes("myTransfer") && !element.Name.includes("documentMetadata") &&
                    !element.Name.includes("transferHistory") && !element.Name.includes("visualTracking") &&
                    !element.Name.includes("activityLog") && !element.Name.includes("attachments") && !element.Name.includes("notes") &&
                    !element.Name.includes("linkedDocument") && !element.Name.includes("nonArchivedAttachments");
            });
            model.tabsWithStatic = tabs;
            model.tabsActions = securityMatrix.SecurityNodes[nodeId].SecurityCategories[response.categoryId].SecurityTabs;
            model.fromSent = true;
            model.isDepartmentFollowUpsNode = (nodeId == window.DepartmentFollowUpsNode);
            model.parentComponentId = linkedCorrespondenceDocument.model.ComponentId
            model.isModal = true;
            model.showBackButton = false;
            wrapper = $(linkedCorrespondenceDocument.refs['linkDocumentDiv']);

            var view = new DocumentDetails.DocumentDetailsView(wrapper, model);
            view.render();

            $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).off("hidden.bs.modal");
            $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).off("shown.bs.modal");
            $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).on('shown.bs.modal', function () {
                $("ul[ref='tabDocumentDetails']").scrollingTabs('refresh');

            });
            $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).on('hidden.bs.modal', function () {
                if ($(this).data("remove") != true)
                    return;
                $(linkedCorrespondenceDocument.refs[linkedCorrespondenceDocument.model.ComponentId]).remove();
                swal.close();
                if ($(".modal-documents").children().length > 0 || $(".modal-window").children().length > 0) {
                    $('body').addClass('modal-open');
                }

            });
            $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).modal("show");
            $(document).off('click', '.btn-back');
            $(document).on('click', '.btn-back', function () {
                $("#gridContainerDiv").show();
                view.remove();
                $(".toRemove").remove();
            });
            $(document).off('click', '.btn-export');
            $(document).on('click', '.btn-export', function () {
                var wrapper = $(".modal-window");
                var model = new ReportCorrespondenceDetailExport.ReportCorrespondenceDetailExport();
                model.documentId = response.id;
                model.delegationId = delegationId;
                var reportCorrespondenceDetailExportView = new ReportCorrespondenceDetailExport.ReportCorrespondenceDetailExportView(wrapper, model);
                reportCorrespondenceDetailExportView.render();

                $("#modalReportCorrespondenceDetailExport").off("hidden.bs.modal");
                $("#modalReportCorrespondenceDetailExport").off("shown.bs.modal");
                $("#modalReportCorrespondenceDetailExport").on('shown.bs.modal', function () { });
                $("#modalReportCorrespondenceDetailExport").on('hidden.bs.modal', function () { });
                $("#modalReportCorrespondenceDetailExport").modal("show");
            });
        }
    }, function () { gLocked = false; Common.showScreenErrorMsg(); }, true);
}
function recall(id, delegationId) {
    if (window.EnableConfirmationMessage === "True") {


        Common.showConfirmMsg(Resources.RecallConfirmation, function () {
            openRecallReasonModal(function (reason) {
                recallRows(id, delegationId, reason);
            });

        });
    }
    else {
        openRecallReasonModal(function (reason) {
            recallRows(id, delegationId, reason);
        });
    }

}

function recallRows(id, delegationId, reason) {
    Common.ajaxPost('/Transfer/Recall',
        {
            'id': id, 'delegationId': delegationId, 'note': reason, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
        },
        function (result) {
            if (!result) {
                Common.alertMsg(Resources.CannotBeRecalled, function () {
                    swal.close()
                    TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                    TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);
                    TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                    $('.close').trigger('click');
                    GridCommon.RefreshCurrentPage(gTableName, false);
                });
               
            }
            else {
                swal.close()
                TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
                TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);
                TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                $('.close').trigger('click');
                GridCommon.RefreshCurrentPage(gTableName, false);
            }
            
        }, function () { Common.showScreenErrorMsg(); }, false
    );
}

function singleOrMultiRecallAction(table) {
    openRecallReasonModal(function (reason) {
 
        let recallData = [];
        let cannotBeRecalled = [];
        $('.rowCheckbox:checked').each(function () {
            let row = $(this).closest('tr');
            let rowData = table.row(row).data();
            if (row.find(".recall").length > 0) {
                if (rowData) {
                    recallData.push({
                        id: rowData.id,
                        delegationId: rowData.delegationId,
                        note: rowData.reason,
                        subject: rowData.subject
                    });
                }

            } else {
                cannotBeRecalled.push(Resources.CannotRecallCorrespondenceWithSubject.replace('{0}', rowData.subject));
            }
        });
        if (recallData.length > 0) {
            recallData.forEach(function (data) {
                /*const retValue = recallRows(data.id, data.delegationId, reason);*/
                Common.ajaxPost('/Transfer/Recall',
                    {
                        'id': data.id, 'delegationId': data.delegationId, 'note': reason, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                    },
                    function (result) {
                        if (cannotBeRecalled.length > 0) {

                            showCannotRecalledMessages(true, cannotBeRecalled)
                        }
                        else {
                            swal.close()
                            TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                            TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
                            TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                            TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);
                            TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                            TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                            $('.close').trigger('click');
                            GridCommon.RefreshCurrentPage(gTableName, false);
                        }

                    }, function () { Common.showScreenErrorMsg(); }, false
                );
                
            });
        }
        if (cannotBeRecalled.length > 0 && recallData.length == 0) {
            
            showCannotRecalledMessages(false, cannotBeRecalled)
        }

    });
}
function buildFilters(nodeId, categories) {
    var nodes = new CoreComponents.Lookup.Nodes().getTreeNodes(window.language);
    var node = $.grep(nodes, function (element, index) {
        return element.id.toString() === nodeId;
    })[0];
    var filters = node.filters !== "" && node.filters !== null ? JSON.parse(node.filters) : [];

    if (filters.length > 0) {
        var html = '<div class="row">';
        var clearFix = 0;
        for (var i = 0; i < filters.length; i++) {
            var filter = filters[i];
            switch (filter) {
                case "ReferenceNumber":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6"><div class="form-group"><label class="control-label">' + Resources.ReferenceNumber + '</label>' +
                        '<input type="text" id="txtFilterSentReferenceNumber" class="form-control" autocomplete="off" tabindex="' + i + '" aria-hidden="true"></div></div>';
                    break;
                case "FromDate":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6"><div class="form-group"><label class="control-label">' + Resources.FromDate + '</label>' +
                        '<div class="input-group date"><input id="filterSentFromDate" type="text" tabindex="' + i + '" autocomplete="off" class="form-control" name="Date" data-parsley-errors-container="#filterSentFromDateError">' +
                        '<span class="input-group-addon" id="filterSentFromDate_img" style="cursor:pointer"><i class="fa fa-calendar" aria-hidden="true"></i></span><div id="filterSentFromDateError"></div></div></div></div>';
                    break;
                case "ToDate":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6"><div class="form-group"><label class="control-label">' + Resources.ToDate + '</label>' +
                        '<div class="input-group date"><input id="filterSentToDate" type="text" tabindex="' + i + '" autocomplete="off" class="form-control" name="Date" data-parsley-errors-container="#filterSentToDateError">' +
                        '<span class="input-group-addon" id="filterSentToDate_img" style="cursor:pointer"><i class="fa fa-calendar" aria-hidden="true"></i></span><div id="filterSentToDateError"></div></div></div></div>';
                    break;
                case "Category":
                    clearFix += 3;
                    var div = '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12" id="categoryFilterSentContainer"><div class="form-group"><label class="control-label">' + Resources.Category + '</label>' +
                        '<select id="cmbFilterSentCategory" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterSentCategoryError" class="form-control">';
                    for (var j = 0; j < categories.length; j++) {
                        div += '<option value="' + categories[j].id + '">' + categories[j].text + '</option>';
                    }
                    div += '</select><div id="cmbFilterSentCategoryError"></div></div></div>';
                    html += div;
                    break;
                case "Subject":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12"><div class="form-group"><label class="control-label">' + Resources.Subject +
                        '</label><input type="text" id="txtFilterSentSubject" class="form-control" autocomplete="off" tabindex="' + i + '" aria-hidden="true"></div></div>';
                    break;
                case "Purpose":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12" id="purposeFilterSentContainer"><div class="form-group"><label class="control-label">' + Resources.Purpose + '</label>' +
                        '<select id="cmbFilterSentPurpose" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterSentPurposeError" class="form-control"></select></div></div>';
                    break;
                case "Priority":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12" id="priorityFilterSentContainer"><div class="form-group"><label class="control-label">' + Resources.Priority + '</label>' +
                        '<select id="cmbFilterSentPriority" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterSentPriorityError" class="form-control"></select></div></div>';
                    break;
                case "Privacy":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12" id="privacyFilterSentContainer"><div class="form-group"><label class="control-label">' + Resources.Privacy + '</label>' +
                        '<select id="cmbFilterSentPrivacy" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterSentPrivacyError" class="form-control"></select></div></div>';
                    break;
                case "Structure":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12" id="structureFilterSentContainer"><div class="form-group"><label class="control-label">' + Resources.Structure + '</label>' +
                        '<select id="cmbFilterSentStructure" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterSentStructureError" class="form-control"></select></div></div>';
                    break;
                case "User":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12" id="userFilterSentContainer"><div class="form-group"><label class="control-label">' + Resources.User + '</label>' +
                        '<select id="cmbFilterSentUser" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterSentUserError" class="form-control"></select></div></div>';
                    break;
            }
            if (clearFix === 12 || clearFix === 11) {
                clearFix = 0;
                html += '<div class="clearfix"></div>';
            }
        }
        html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 list-action-filter"><button id="btnFilterSentSearch" tabindex="6" type="button" class="btn btn-primary">' + Resources.Search + '</button>' +
            ' <button id="btnFilterSentClear" tabindex="7" type="button" class="btn btn-clear">' + Resources.Clear + '</button></div>';
        html += '</div>';
        $('#filtersContainer').html(html);

        //var clickedSearch = false;
        //$('#collapseSentIcon').click(function () {
        //    $('#collapseSentIcon').empty();
        //    if (clickedSearch) {
        //        $('#collapseSentIcon').append('<i class="fa fa-angle-up fa-lg"></i>');
        //        $('#collapseSentPanel').attr('class', '');
        //        $('#collapseSentPanel').addClass('panel-body panel-collapse collapse in');
        //        clickedSearch = false;
        //    } else {
        //        $('#collapseSentIcon').append('<i class="fa fa-angle-down fa-lg"></i>');
        //        $('#collapseSentPanel').attr('class', '');
        //        $('#collapseSentPanel').addClass('panel-body panel-collapse collapse');
        //        clickedSearch = true;
        //    }
        //});
        $("#btnFilterSentSearch").on('click', function () {
            GridCommon.Refresh(gTableName);
        });
        $("#btnFilterSentClear").on('click', function () {
            $("#cmbFilterSentPurpose").val('').trigger('change');
            $("#cmbFilterSentPriority").val('').trigger('change');
            $("#cmbFilterSentPrivacy").val('').trigger('change');
            $("#cmbFilterSentCategory").val('').trigger('change');
            $("#cmbFilterSentStatus").val('').trigger('change');
            $("#txtFilterSentReferenceNumber").val('');
            $("#txtFilterSentSubject").val('');
            fromDate.clear();
            toDate.clear();
            $("#cmbFilterSentStructure").val('').trigger('change');
            $("#cmbFilterSentUser").val('').trigger('change');
            GridCommon.Refresh(gTableName);
        });
        $('#cmbFilterSentPurpose').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            data: new CoreComponents.Lookup.Purposes().get(window.language),
            dropdownParent: $('#purposeFilterSentContainer')
        });
        $("#cmbFilterSentPurpose").val('').trigger('change');

        $('#cmbFilterSentPriority').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            data: new CoreComponents.Lookup.Priorities().get(window.language),
            dropdownParent: $('#priorityFilterSentContainer')
        });
        $("#cmbFilterSentPriority").val('').trigger('change');

        $('#cmbFilterSentPrivacy').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            data: new CoreComponents.Lookup.Privacies().get(window.language),
            dropdownParent: $('#privacyFilterSentContainer')
        });
        $("#cmbFilterSentPrivacy").val('').trigger('change');

        $('#cmbFilterSentCategory').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            dropdownParent: $('#categoryFilterSentContainer')
        });
        $("#cmbFilterSentCategory").val('').trigger('change');
        $('#cmbFilterSentStatus').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            dropdownParent: $('#statusFilterSentContainer')
        });
        $("#cmbFilterSentStatus").val('').trigger('change');

        $('#cmbFilterSentStructure').select2({
            minimumInputLength: 0,
            allowClear: false,
            placeholder: "",
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            dropdownParent: $("#structureFilterSentContainer"),
            multiple: true,
            width: "100%",
            ajax: {
                delay: 400,
                url: window.IdentityUrl + '/api/SearchStructuresWithSearchAttributes',
                type: "POST",
                dataType: 'json',
                headers: { Authorization: 'Bearer ' + window.IdentityAccessToken },
                data: function (term) {
                    return {
                        "text": term.term ? term.term : "", "language": window.language, "attributes": [window.StructureNameAr, window.StructureNameFr]
                    };
                },
                processResults: function (data) {
                    var listitemsMultiList = [];
                    $.each(data, function (key, val) {
                        var structureName = val.name;
                        if (val.attributes != null && val.attributes.length > 0) {
                            var attributeLang = $.grep(val.attributes, function (e) {
                                return e.text === window.StructureNameAr && window.language === "ar" ? e.value : (e.text === window.StructureNameFr && window.language === "fr" ? e.value : "");
                            });
                            if (attributeLang.length > 0) {
                                structureName = attributeLang[0].value === "" ? structureName : attributeLang[0].value;
                            }
                        }
                        var item = {};
                        item.id = val.id;
                        item.text = structureName;
                        item.isStructure = true;
                        item.dataId = val.id;
                        listitemsMultiList.push(item);
                    });
                    return {
                        results: listitemsMultiList
                    };
                }
            },
            sorter: data => data.sort((a, b) => a.text.localeCompare(b.text))
        }).on("change", function () {
            if (document.getElementById('cmbFilterSentUser') !== null) {
                var type = "GET";
                var url = '/api/SearchUsers';
                var structures = $('#cmbFilterSentStructure').val();
                //if (structures !== "" && structures !== null)
                //{
                //    type = "POST";
                //    url = '/api/SearchUsersByStructureIds';
                //}
                $('#cmbFilterSentUser').select2({
                    minimumInputLength: 0,
                    allowClear: false,
                    placeholder: "",
                    dir: window.language === "ar" ? "rtl" : "ltr",
                    language: window.language,
                    dropdownParent: $("#userFilterSentContainer"),
                    multiple: true,
                    width: "100%",
                    ajax: {
                        delay: 400,
                        url: window.IdentityUrl + url,
                        type: type,
                        dataType: 'json',
                        headers: { Authorization: 'Bearer ' + window.IdentityAccessToken },
                        data: function (term) {
                            var params = { "text": "", "language": window.language };
                            //var params = { "text": term.term ? term.term : "", "language": window.language };
                            //var structures = $('#cmbFilterSentStructure').val();
                            //if (structures !== "" && structures !== null)
                            //{
                            //    params.ids = $('#cmbFilterSentStructure').val();
                            //}
                            return params;
                        },
                        processResults: function (data, term) {
                            var termSearch = term.term ? term.term : "";

                            var structures = $('#cmbFilterSentStructure').val();
                            var listitemsMultiList = [];
                            $.each(data, function (key, val) {
                                if (structures !== "" && structures !== null &&
                                    !((val.structureIds != null && val.structureIds.some(r => structures.indexOf(String(r)) >= 0)) ||
                                        structures.includes(val.defaultStructureId))) {
                                    return;
                                }
                                var fullName = val.fullName;
                                if (window.language != 'en') {
                                    fullName = getFullNameByLangauge(val);
                                    fullName = fullName.trim() == "" ? val.fullName : fullName;
                                }
                                var allNames = getFullNameInAllLangauge(val);
                                if (allNames.length == 0) allNames.push(fullName);
                                if (termSearch != "" &&
                                    !allNames.some(r => r.toLowerCase().includes(termSearch.toLowerCase()))) {
                                    return;
                                }
                                let isExist = listitemsMultiList.some(function (usr) { return usr.id === val.id; });
                                if (!isExist) {
                                    var item = {};
                                    item.id = val.id;
                                    item.text = fullName;
                                    item.isStructure = false;
                                    item.dataId = val.id;
                                    listitemsMultiList.push(item);
                                }
                            });
                            return {
                                results: listitemsMultiList
                            };
                        }
                    },
                    sorter: data => data.sort((a, b) => a.text.localeCompare(b.text))
                }).val("").trigger("change");
            }
        });
        $("#cmbFilterSentStructure").val('').trigger('change');
        $('#cmbFilterSentUser').select2({
            minimumInputLength: 0,
            allowClear: false,
            placeholder: "",
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            dropdownParent: $("#userFilterSentContainer"),
            multiple: true,
            width: "100%",
            ajax: {
                delay: 400,
                url: window.IdentityUrl + '/api/SearchUsers',
                type: "GET",
                dataType: 'json',
                headers: { Authorization: 'Bearer ' + window.IdentityAccessToken },
                data: function (term) {
                    return { "text": "", "language": window.language };
                    //return { "text": term.term ?term.term : "", "language": window.language };
                },
                processResults: function (data, term) {
                    var termSearch = term.term ? term.term : "";

                    var listitemsMultiList = [];
                    $.each(data, function (key, val) {
                        var fullName = val.fullName;
                        if (window.language != 'en') {
                            fullName = getFullNameByLangauge(val);
                            fullName = fullName.trim() == "" ? val.fullName : fullName;
                        }
                        var allNames = getFullNameInAllLangauge(val);
                        if (allNames.length == 0) allNames.push(fullName);
                        if (termSearch != "" &&
                            !allNames.some(r => r.toLowerCase().includes(termSearch.toLowerCase()))) {
                            return;
                        }
                        let isExist = listitemsMultiList.some(function (usr) { return usr.id === val.id; });
                        if (!isExist) {
                            var item = {};
                            item.id = val.id;
                            item.text = fullName;
                            item.isStructure = false;
                            item.dataId = val.id;
                            listitemsMultiList.push(item);
                        }
                    });
                    return {
                        results: listitemsMultiList
                    };
                }
            },
            sorter: data => data.sort((a, b) => a.text.localeCompare(b.text))
        });
        $("#cmbFilterSentUser").val('').trigger('change');
        var fromDate = $('#filterSentFromDate').flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            onOpen: function (ct) {
                this.set({
                    maxDate: jQuery('#filterSentToDate').val() && jQuery('#filterSentToDate').val() !== "" ? jQuery('#filterSentToDate').val() : moment().format('DD/MM/YYYY')
                });
            }
        });
        $("#filterSentFromDate_img").click(function () {
            fromDate.toggle();
        });
        var toDate = $('#filterSentToDate').flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            onOpen: function (ct) {
                this.set({
                    minDate: jQuery('#filterSentFromDate').val() && jQuery('#filterSentFromDate').val() !== "" ? jQuery('#filterSentFromDate').val() : false,
                    maxDate: moment().format('DD/MM/YYYY')
                });
            }
        });
        $("#filterSentToDate_img").click(function () {
            toDate.toggle();
        });
        $('#txtFilterSentReferenceNumber').keydown(function (e) {
            var code = e.keyCode || e.which;
            if (code === 9) {
                e.preventDefault();
                if (e.shiftKey) {
                    $('#btnFilterSentSearch').focus();
                }
                else {
                    $('#filterSentFromDate').focus();
                }
            }
        });
        $('#btnFilterSentClear').keydown(function (e) {
            var code = e.keyCode || e.which;
            if (code === 9) {
                e.preventDefault();
                if (e.shiftKey) {
                    $('#btnFilterSentSearch').focus();
                }
                else {
                    $('#filterSentFromDate').focus();
                }
            }
        });
    } else {
        $('.searchToRemove > div:first-child').hide();
        $("#gridPanel").removeAttr("style");
        $("#gridPanel").addClass("panel-default");
    }
}
function buildColumns(gridcolumns, nodeId, fromChildTable) {
    gridcolumns.push({
        visible: false,
        title: Resources.Notes,
        data: "notes",
        render: function (data, type, full, meta) {
            if (Array.isArray(full.note) && full.note.length > 0) {
                // Add a bullet point before each note and join with new lines
                return full.note.map(n => `\u2022 ${n.notes} \n`).join('\n');
            }

            return "";
        }
    });

    gridcolumns.push({
        visible: false,
        title: Resources.Instructions,
        data: "instruction",
        render: function (data, type, full, meta) {
            if (full.categoryId == 8) {
                data = full.body;
            }
            // Check if data is null or undefined, return an empty string if so
            if (data == null || data.length === 0) {
                return "";
            }
            // Otherwise, return the data with the text renderer for HTML safety
            return $.fn.dataTable.render.text().display(data);
        },
        orderable: !fromChildTable,
        orderSequence: fromChildTable ? [] : ["asc", "desc"],
        className: "min-max-width-150-250"
    });

    //gridcolumns.push(
    //    {
    //        visible: true,
    //        title: Resources.Instructions,
    //        data: "instruction",
    //        render: function (data, type, full, meta) {

    //            gridcolumns.push({
    //                title: Resources.Instructions, data: "instruction",
    //                render: $.fn.dataTable.render.text(), "orderable": !fromChildTable, orderSequence: fromChildTable == true ? [] :
    //                    ["asc", "desc"], "className": "min-max-width-150-250"
    //            });


    //        }
    //    }

    //);

    var nodes = new CoreComponents.Lookup.Nodes().getTreeNodes(window.language);
    var node = $.grep(nodes, function (element, index) {
        return element.id.toString() === nodeId;
    })[0];
    var newColomns = new Nodes().getCustomcolumns(nodeId);


    if (newColomns) {
        node.columns = newColomns.content;
    }
    var columns = node.columns !== "" && node.columns !== null ? JSON.parse(node.columns) : [];
    var columnDetails = $.grep(columns, function (element, index) {
        return element.isColumnDetail === true;
    });
    if (columnDetails.length > 0 || (IsInboxModeWithGrouping && !fromChildTable)) {
        gridcolumns.push({
            title: fromChildTable ? '' : '<a id="expandAll" class="expand-control expand"></a>',
            "className": 'details-control',
            "orderable": false,
            "data": null,
            "defaultContent": '',
            width: '16px'
        });
    }
    gridcolumns.push(
        {
            "className": "text-left",
            "autoWidth": false,
            "bAutoWidth": false,
            width: "16px",
            'orderable': false,
            'sortable': false,
            data: "importanceId",
            'render': function (data, type, full, meta) {
                var importances = new CoreComponents.Lookup.Importances().get(window.language);
                for (var i = 0; i < importances.length; i++) {
                    if (importances[i].id === data) {
                        let btnView = document.createElement("i");
                        btnView.setAttribute("class", "fa fa-exclamation fa-lg");
                        btnView.setAttribute("style", "color:" + importances[i].color);
                        btnView.setAttribute("title", importances[i].text);
                        return btnView.outerHTML;
                    }
                }
                return "";
            }
        });
    columns.sort((a, b) => (parseInt(a.order) > parseInt(b.order)) ? 1 : -1);
    var customColumn = [];
    for (var i = 0; i < columns.length; i++) {
        var column = columns[i];
        if (!column.isColumnDetail) {
            if (column.isCustom === true) {
                customColumn.push({ columnName: column.name, functionName: column.customFunctionName });
                var customColumnKeyword = column.customFunctionName;
                var escapedVariable = column.customFunctionName.replace(/"/g, '&quot;');

                if (customColumnKeyword.includes("()")) {
                    if (customColumnKeyword == "getCommitteeName()") {
                        customColumnKeyword = "Meeting Type";
                    }

                    if (customColumnKeyword == "getMeetingLocation()") {
                        customColumnKeyword = "Meeting Location";
                    }
                }

                gridcolumns.push({
                    title: GetCustomAttributeTranslationByLangauge(customColumnKeyword), "orderable": false, 'defaultContent': '<div data-function="' + escapedVariable + '"></div>',
                    "createdCell": function (td, cellData, rowData, row, col) {
                        var htmlCell = "";
                        var div = td.children[0];
                        var customFunctionName = div !== null && typeof div !== "undefined" ? $(div).attr("data-function") : "";
                        //if (customFunctionName && typeof window[customFunctionName.replace("(", "").replace(")", "")] === 'function')
                        //{
                        //    var functionName = customFunctionName.split("(")[0];
                        //    htmlCell = eval(functionName + "(" + JSON.stringify(rowData) + ")");
                        //}

                        var customColumns = JSON.parse(rowData.documentForm.form);
                        var columnName = customColumn.find(f => f.functionName == customFunctionName).columnName;

                        var customData = customColumns[columnName]

                        if (customFunctionName.includes("()")) {
                            if (customFunctionName == "getCommitteeName()") {
                                customData = getCommitteeName(customData);
                            }

                            if (customFunctionName == "getMeetingLocation()") {
                                customData = getMeetingLocation(customData);
                            }
                        }

                        htmlCell = htmlCell == "" && customColumns != null ? (customData == undefined ? "" : customData) : htmlCell;

                        $(td).html(htmlCell);
                    }
                });
            } else {
                switch (column.name) {
                    case "Note":
                        gridcolumns.push(
                            {
                                title: Resources.Notes,
                                data: "notes",
                                render: function (data, type, full, meta) {
                                    if (Array.isArray(full.note) && full.note.length > 0) {
                                        return full.note.map(n => n.notes).join(',');
                                    }
                                    return "";
                                }
                            }
                        )
                        break;
                    case "Instruction":
                        gridcolumns.push(
                            {
                                title: Resources.Instructions,
                                data: "instruction",
                                render: function (data, type, full, meta) {
                                    gridcolumns.push({
                                        title: Resources.Instructions, data: "instruction",
                                        render: $.fn.dataTable.render.text(), "orderable": !fromChildTable, orderSequence:
                                            fromChildTable == true ? [] : ["asc", "desc"], "className": "min-max-width-150-250"
                                    });
                                    return "";
                                }
                            }
                        )
                        break;
                    case "Category":
                        gridcolumns.push({
                            title: Resources.Category, data: "categoryId", "orderable": !fromChildTable, orderSequence: fromChildTable == true ? [] : ["asc", "desc"], "className": "min-max-width-50-150",
                            render: function (data, type, full, meta) {
                                var categories = new Categories().get(window.language);
                                for (var i = 0; i < categories.length; i++) {
                                    if (categories[i].id === data) {
                                        return categories[i].text;
                                    }
                                }
                                return "";
                            }
                        });
                        break;
                    case "ReferenceNumber":
                        gridcolumns.push({
                            title: Resources.ReferenceNumber, data: "referenceNumber",
                            render: function (data, type, full, meta) {
                                if (full.referenceNumber != undefined && full.referenceNumber != "" && full.referenceNumber != null) {
                                    var linkedCorresRefNumList = full.referenceNumber.split(',');
                                    var html = '';
                                    $(linkedCorresRefNumList).each(function () {
                                        html += "<div>" + this + "</div>";
                                    });
                                    return html;
                                }
                                return "";
                            },
                            "orderable": false, "className": "min-max-width-50-150 copyToClipboard"
                        });
                        break;
                    case "Subject":
                        gridcolumns.push({ title: Resources.Subject, data: "subject", render: function(data, type, full, meta)
                            {
                                return `<span class="subjectSpan">${full.subject ?? ""}</span>`;
                            }, "orderable": !fromChildTable, orderSequence: fromChildTable == true ? [] : ["asc", "desc"], "className": "min-max-width-50-150" });
                        break;
                    case "To":
                        gridcolumns.push({
                            visible: fromChildTable | !IsInboxModeWithGrouping,
                            title: Resources.To, "orderable": false,
                            "render": function (data, type, full, meta) {
                                var retValue = "";
                                if (full.toStructure) {
                                    retValue += full.toStructure;
                                }
                                if (full.toUser) {
                                    var user = full.toUser;
                                    retValue += retValue !== "" ? "/" + user : user;
                                }
                                return retValue;
                            }
                        });
                        break;
                    case "From":
                        gridcolumns.push({
                            visible: fromChildTable | !IsInboxModeWithGrouping,

                            title: Resources.From, "orderable": false,
                            "render": function (data, type, full, meta) {
                                var retValue = "";
                                if (full.fromStructure) {
                                    retValue += full.fromStructure;
                                }
                                if (full.fromUser) {
                                    var user = full.fromUser;
                                    retValue += retValue !== "" ? "/" + user : user;
                                }
                                return retValue;
                            }
                        });
                        break;
                    case "TransferDate":
                        gridcolumns.push({
                            visible: fromChildTable | !IsInboxModeWithGrouping,
                            title: Resources.TransferDate, data: "transferDate", "orderable": !fromChildTable, orderSequence: fromChildTable == true ? [] : ["asc", "desc"], width: "100px",
                            render: function (data, type, full, meta) {
                                return DateConverter.toHijriFormated(full.transferDate, null, window.CalendarType);
                            }
                        });
                        break;
                    case "Owner":
                        gridcolumns.push({ visible: fromChildTable | !IsInboxModeWithGrouping, title: Resources.Owner, data: "lockedBy", "orderable": false });
                        break;
                    case "SendingEntity":
                        gridcolumns.push({ title: Resources.SendingEntity, data: "sendingEntity", "orderable": false, width: "100px" });
                        break;
                    case "ReceivingEntity":
                        gridcolumns.push({ title: Resources.ReceivingEntity, data: "receivingEntity", "orderable": false, width: "100px" });
                        break;
                    case "Purpose":
                        gridcolumns.push({
                            title: Resources.Purpose, data: "purpose", "orderable": false, width: "100px",
                            render: function (data, type, full, meta) {
                                var purposes = new CoreComponents.Lookup.Purposes().get(window.language);
                                for (let i = 0; i < purposes.length; i++) {
                                    if (purposes[i].id === full.purposeId) {
                                        return purposes[i].text;
                                    }
                                }
                                return "";
                            }
                        });
                        break;
                    case "Priority":
                        gridcolumns.push({
                            title: Resources.Priority, data: "priority", "orderable": false, width: "100px",
                            render: function (data, type, full, meta) {
                                var priorities = new CoreComponents.Lookup.Priorities().get(window.language);
                                for (let i = 0; i < priorities.length; i++) {
                                    if (priorities[i].id === full.priorityId) {
                                        return priorities[i].text;
                                    }
                                }
                                return "";
                            }
                        });
                        break;
                    case "Privacy":
                        gridcolumns.push({
                            title: Resources.Privacy, data: "privacy", "orderable": false, width: "100px",
                            render: function (data, type, full, meta) {
                                var privacies = new CoreComponents.Lookup.Privacies().get(window.language);
                                for (let i = 0; i < privacies.length; i++) {
                                    if (privacies[i].id === full.privacyId) {
                                        return privacies[i].text;
                                    }
                                }
                                return "";
                            }
                        });
                        break;
                    case "CreatedBy":
                        gridcolumns.push({ title: Resources.CreatedBy, data: "createdByUser", "orderable": false, width: "100px" });
                        break;
                    case "OpenedDate":
                        gridcolumns.push({
                            title: Resources.OpenedDate, data: "openedDate", "orderable": false, width: "100px",
                            render: function (data, type, full, meta) {
                                return DateConverter.toHijriFormated(full.openedDate, ConversionDateFormat.DateTimeFormat24_Slash, window.CalendarType);
                            }
                        });
                        break;
                    case "ClosedDate":
                        gridcolumns.push({
                            title: Resources.ClosedDate, data: "closedDate", "orderable": false, width: "100px",
                            render: function (data, type, full, meta) {
                                return DateConverter.toHijriFormated(full.closedDate, ConversionDateFormat.DateTimeFormat24_Slash, window.CalendarType);
                            }
                        });
                        break;
                    case "From":
                        gridcolumns.push({
                            title: Resources.From, "orderable": false,
                            "render": function (data, type, full, meta) {
                                var retValue = "";
                                if (full.fromStructure) {
                                    retValue += full.fromStructure;
                                }
                                if (full.fromUser) {
                                    var user = full.fromUser;
                                    retValue += retValue !== "" ? "/" + user : user;
                                }
                                return retValue;
                            }
                        });
                        break;
                    case "TransferDate":
                        gridcolumns.push({
                            title: Resources.TransferDate, data: "transferDate", render: $.fn.dataTable.render.text(), "orderable": true, orderSequence: ["asc", "desc"], width: "100px",
                            render: function (data, type, full, meta) {
                                return DateConverter.toHijriFormated(full.transferDate, null, window.CalendarType);
                            }
                        });
                        break;
                }
            }
        }
    }
}
function buildColumnsDetails(row, nodeId) {
    var nodes = new CoreComponents.Lookup.Nodes().getTreeNodes(window.language);
    var node = $.grep(nodes, function (element, index) {
        return element.id.toString() === nodeId;
    })[0];
    var columns = node.columns !== "" && node.columns !== null ? JSON.parse(node.columns) : [];
    columns.sort((a, b) => (parseInt(a.order) > parseInt(b.order)) ? 1 : -1);
    var html = "";
    for (var i = 0; i < columns.length; i++) {
        var column = columns[i];
        if (column.isColumnDetail) {
            if (column.isCustom === true) {
                var customColumn = column.name;
                var customColumnKeyword = column.customFunctionName;

                var htmlCell = "";
                var customFunctionName = column.customFunctionName;
                //if (customFunctionName && typeof window[customFunctionName.replace("(", "").replace(")", "")] === 'function')
                //{
                //    var functionName = customFunctionName.split("(")[0];
                //    htmlCell = eval(functionName + "(" + JSON.stringify(row.data()) + ")");
                //}

                if (customColumnKeyword.includes("()")) {
                    if (customColumnKeyword == "getCommitteeName()") {
                        customColumnKeyword = "Meeting Type";
                    }

                    if (customColumnKeyword == "getMeetingLocation()") {
                        customColumnKeyword = "Meeting Location";
                    }
                }

                var customColumns = JSON.parse(row.data().documentForm.form);

                customColumns = customColumns[customColumn];

                if (customFunctionName.includes("()")) {
                    if (customFunctionName == "getCommitteeName()") {
                        customColumns = getCommitteeName(customColumns);
                    }

                    if (customFunctionName == "getMeetingLocation()") {
                        customColumns = getMeetingLocation(customColumns);
                    }
                }

                htmlCell = htmlCell == "" && customColumns != null ? (customColumns == undefined ? "" : customColumns) : htmlCell;

                html += '<tr><th style="width: 10%;padding:5px">' + (GetCustomAttributeTranslationByLangauge(customColumnKeyword)) + ':</th><td style="width: 90%;padding:5px;word-break: break-all;"">' + htmlCell + '</td></tr>';

            } else {
                switch (column.name) {
                    case "Category":
                        var category = "";
                        var categories = new Categories().get(window.language);
                        if (categories && Array.isArray(categories)) {
                            var matchedCategory = categories.find(c => c.id === row.data().categoryId);
                            category = matchedCategory ? matchedCategory.text : "";
                        }
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.Category + ':</th>' +
                            '<td style="width: 85%;padding:5px;word-break: break-all;">' + category + '</td></tr>';
                        break;
                    case "ReferenceNumber":
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.ReferenceNumber + ':</th><td style="width: 90%;padding:5px;word-break: break-all;"">' + (row.data().referenceNumber || '') + '</td></tr>';
                        break;
                    case "Subject":
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.Subject + ':</th><td style="width: 90%;padding:5px;word-break: break-all;"">' + (row.data().subject || '') + '</td></tr>';
                        break;
                    case "To":
                        var to = "";
                        if (row.data().toStructure) {
                            to += row.data().toStructure;
                        }
                        if (row.data().toUser) {
                            var user = row.data().toUser;
                            to += to !== "" ? "/" + user : user;
                        }
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.To + ':</th><td style="width: 90%;padding:5px;word-break: break-all;"">' + to + '</td></tr>';
                        break;
                    case "TransferDate":
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.TransferDate + ':</th><td style="width: 90%;padding:5px;word-break: break-all;"">' + (DateConverter.toHijriFormated(row.data().transferDate, null, window.CalendarType) || '') + '</td></tr>';
                        break;
                    case "Owner":
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.Owner + ':</th><td style="width: 90%;padding:5px;word-break: break-all;"">' + (row.data().lockedBy || '') + '</td></tr>';
                        break;
                    case "SendingEntity":
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.SendingEntity + ':</th><td style="width: 90%;padding:5px;word-break: break-all;"">' + (row.data().sendingEntity || '') + '</td></tr>';
                        break;
                    case "ReceivingEntity":
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.ReceivingEntity + ':</th><td style="width: 90%;padding:5px;word-break: break-all;"">' + (row.data().receivingEntity || '') + '</td></tr>';
                        break;
                    case "Purpose":
                        var purpose = "";
                        var purposes = new CoreComponents.Lookup.Purposes().get(window.language);
                        for (let i = 0; i < purposes.length; i++) {
                            if (purposes[i].id === row.data().purposeId) {
                                purpose = purposes[i].text;
                            }
                        }
                        html += '<tr><th style="width: 15%;padding:5px">' + Resources.Purpose + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + purpose + '</td></tr>';
                        break;
                    case "Priority":
                        var priority = "";
                        var priorities = new CoreComponents.Lookup.Priorities().get(window.language);
                        for (let i = 0; i < priorities.length; i++) {
                            if (priorities[i].id === row.data().priorityId) {
                                priority = priorities[i].text;
                            }
                        }
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.Priority + ':</th><td style="width: 90%;padding:5px;word-break: break-all;"">' + priority + '</td></tr>';
                        break;
                    case "Privacy":
                        var privacy = "";
                        var privacies = new CoreComponents.Lookup.Privacies().get(window.language);
                        for (let i = 0; i < privacies.length; i++) {
                            if (privacies[i].id === row.data().privacyId) {
                                privacy = privacies[i].text;
                            }
                        }
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.Privacy + ':</th><td style="width: 90%;padding:5px;word-break: break-all;"">' + privacy + '</td></tr>';
                        break;
                    case "CreatedBy":
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.CreatedBy + ':</th><td style="width: 90%;padding:5px;word-break: break-all;"">' + (row.data().createdByUser || '') + '</td></tr>';
                        break;
                    case "OpenedDate":
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.OpenedDate + ':</th><td style="width: 90%;padding:5px;word-break: break-all;"">' + (DateConverter.toHijriFormated(row.data().openedDate, ConversionDateFormat.DateTimeFormat24_Slash, window.CalendarType) || '') + '</td></tr>';
                        break;
                    case "ClosedDate":
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.ClosedDate + ':</th><td style="width: 90%;padding:5px;word-break: break-all;"">' + (DateConverter.toHijriFormated(row.data().closedDate, ConversionDateFormat.DateTimeFormat24_Slash, window.CalendarType) || '') + '</td></tr>';
                        break;
                }
            }
        }
    }
    return html;
}

var gTableName = "grdSentItems";
var gLocked = false;
var dataTableParents = [];
var datatableParentsAndChildren = [];
var IsInboxModeWithGrouping = window.InboxMode === "InboxDefaultWithGrouping";
var _isFollowUpNode = false;
var wrapperParent;
class DocumentSentView extends Intalio.View {
    constructor(element, model) {
        wrapperParent = model;
        super(element, "sent", model);
        this.statuses = model.statuses;
    }
    render() {
        var self = this;
        var model = this.model;
        fromStructureSent = model.fromStructureSent;
        gfirstTime = true;
        gDataChanged = true;
        gAction = "";
        gFilterChanged = false;
        gFilterStructure = "";
        gFilterFromDate = "";
        gFilterToDate = "";
        gFilterUser = "";
        gFilterOverdue = "";
        gOverdue = false;
        clear = false;
        gReportName = "Sent Report";
        let isDepartmentFollowUpsNode = (self.model.nodeId == window.DepartmentFollowUpsNode);
        if (isDepartmentFollowUpsNode == true)
            $('.content-heading').text(Resources.DepartmentFollowUps);
        else
            $('.content-heading').text(Resources.DocumentSent);
        $.fn.select2.defaults.set("theme", "bootstrap");
        var followUpCategoryIndex = self.model.categories.findIndex(item => item.id == window.FollowUpCategory);
        self.model.categories.splice(followUpCategoryIndex, 1);

        buildFilters(self.model.nodeId, self.model.categories);
        Common.gridCommon();
        var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));
        //var buttons = SecurityMatrix.getToolbarActions(securityMatrix, self.model.nodeId, gTableName);
        var buttons = SecurityMatrix.getToolbarActions(securityMatrix, self.model.nodeId, gTableName).map(button => {
            button.className = (button.className || '') + ' hidden conditional-buttons';
            return button;
        });

        var reportTitle;

        var nodes = new CoreComponents.Lookup.Nodes().get(window.language);
        var currentNode = $.grep(nodes, function (element, index) {
            return element.id.toString() === self.model.nodeId;
        })[0];
        if (currentNode)
            reportTitle = currentNode.text + (currentNode.parentName ? '_' + currentNode.parentName : '');



        var exportButton = [{
            className: 'btn-sm btn-primary',

            extend: 'print',
            text: Resources.Print,
            title: function () {
                return Resources.SentReport;
            },
            filename: function () {
                var currentDate = new Date();

                var formattedDate = currentDate.toISOString().split('T')[0];

                var formattedTime = currentDate.toTimeString().split(' ')[0].replace(/:/g, '-');

                return Resources.SentReport + '_' + formattedDate + '_' + formattedTime;
            },

            exportOptions: {
                columns: [':visible', 2]

            },
            customize: CTSReportDatatblesCustomComponents.CustomDatatableReport.reportsent.print
        },
        {
            className: 'btn-sm btn-primary',

            extend: 'excelHtml5',
            title: function () {
                return Resources.SentReport;
            },
            filename: function () {
                var currentDate = new Date();

                var formattedDate = currentDate.toISOString().split('T')[0];

                var formattedTime = currentDate.toTimeString().split(' ')[0].replace(/:/g, '-');

                //var reportTitle;

                //if (self.model.nodeId == '48')
                //    reportTitle = Resources.SentInternal;
                //else if (self.model.nodeId == '35')
                //    reportTitle = Resources.SentIncoming;
                //else if (self.model.nodeId == '42')
                //    reportTitle = Resources.SentOutgoing;
                //else
                //    reportTitle = Resources.SentReport;
                return reportTitle + '_' + formattedDate + '_' + formattedTime;
            },


            exportOptions: {
                columns: [':visible', 2, 3]

            },
            customize: CTSReportDatatblesCustomComponents.CustomDatatableReport.reportsent.excelHTML5
        },
        {
            className: 'btn-sm btn-primary',

            extend: 'pdfHtml5',
            title: function () {
                //var reportTitle;

                //if (self.model.nodeId == '48')
                //    reportTitle = Resources.SentInternal;
                //else if (self.model.nodeId == '35')
                //    reportTitle = Resources.SentIncoming;
                //else if (self.model.nodeId == '42')
                //    reportTitle = Resources.SentOutgoing;

                return reportTitle;
            },
            filename: function () {
                var currentDate = new Date();

                var formattedDate = currentDate.toISOString().split('T')[0];

                var formattedTime = currentDate.toTimeString().split(' ')[0].replace(/:/g, '-');
                //var reportTitle;

                //if (self.model.nodeId == '48')
                //    reportTitle = Resources.SentInternal;
                //else if (self.model.nodeId == '35')
                //    reportTitle = Resources.SentIncoming;
                //else if (self.model.nodeId == '42')
                //    reportTitle = Resources.SentOutgoing;

                return reportTitle + '_' + formattedDate + '_' + formattedTime;
            },

            exportOptions: {
                columns: [':visible', 2, 3]

            },


            customize: CTSReportDatatblesCustomComponents.CustomDatatableReport.reportsent.pdfHTML5
        }, {
            className: 'btn-sm btn-primary',
            text: Resources.CustomizeColumns,
            action: function (e, dt, node, config) {

                var wrapper = $(".modal-window");
                var customeModel = new CustomizeNodeColomns.CustomizeNodeColomns();
                customeModel.nodeId = model.nodeId;
                customeModel.text = "Sent";
                var CustomizeNodeColomnsViews = new CustomizeNodeColomns.CustomizeNodeColomnsView(wrapper, customeModel);
                CustomizeNodeColomnsViews.render();

                $("#nodeColomnsModal").parsley().reset();

                $('#nodeColomnsModal').modal('show');

                $("#nodeColomnsModal").off("hidden.bs.modal");
                $("#nodeColomnsModal").off("shown.bs.modal");

                $('#nodeColomnsModal').on('shown.bs.modal', function () {
                });

                $('#nodeColomnsModal').on('hidden.bs.modal', function () {
                    $('#formPostNode').parsley().reset();
                    $('#nodeColomnsModal').remove();
                });
            }
        }
        ];

        var allButtons = [exportButton, ...buttons];

        _isFollowUpNode = isFollowUpNode(self.model.nodeId);
        var columns = [
            //{
            //    visible: IsInboxModeWithGrouping,
            //    title: '<a id="expandAll" class="expand-control expand"></a>',
            //    "autoWidth": false,
            //    "bAutoWidth": false,
            //    "width": "16px",
            //    "orderable": false,
            //    "className": "min-max-width-50-50 ",
            //    "render": function (data, type, row) {
            //        return null;
            //    }
            //},
            {

                title: '<input id="chkAll" type="checkbox"/>', // "checkAll" checkbox
                data: null,
                orderable: false,
                width: '16px',
                className: 'parentCheckAll',
                render: function (data, type, row) {
                    return "<input type='checkbox' class='rowCheckbox'  data-id='" + row.id + "'/>";
                }
            },
            //{

            //    visible:buttons.length > 0, title: '<input id="chkAll" type="checkbox" />',
            //    width: '16px',
            //    "orderable": false,
            //    "className": "parentCheckAll",
            //    "render": function (data, type, row) {
            //        if (!IsInboxModeWithGrouping) {
            //            return /*!_isFollowUpNode ?*/ "<input type='checkbox' onclick='event.stopPropagation();' data-id=" + row.id + " /> ";
            //        }
            //        else {
            //            return null;
            //        }
            //    }
            //},
            { title: "Id", data: "id", visible: false, "orderable": false }];
        buildColumns(columns, self.model.nodeId, false);
        columns.push({
            'visible': _isFollowUpNode,
            "className": "text-right",
            "autoWidth": false,
            "bAutoWidth": false,
            width: "16px",
            'orderable': false,
            'sortable': false,
            'render': function (data, type, full, meta) {

                var html = "";
                var color = "";
                var priorities = new CoreComponents.Lookup.Priorities().get(window.language);
                var privacies = new CoreComponents.Lookup.Privacies().get(window.language);

                    for (var i = 0; i < privacies.length; i++) {
                        if (privacies[i].id === full.privacyId) {
                            color = privacies[i].color;
                            html += "<div class='mr-sm' title='" + Resources.Privacy + "' style='height: 24px'>" +
                                "<i class='fa fa-shield fa-lg' style='color: " + color + "'></i>&nbsp;" +
                                "</div>";
                            break;
                        }
                    }
             
                if (full.isOverDue) {
                    html += "<div class='mr-sm' title='" + Resources.OverDue + "'><i class='fa fa-clock-o fa-lg text-danger'></i></div>&nbsp;";
                }
                if (full.hasAttachment) {
                    html += "<div class='mr-sm' title='" + (full.attachmentCount != null ? full.attachmentCount.toString() : "0") + " " + Resources.Attachments + "'><i class='fa fa-paperclip text-info'></i></div>&nbsp;";
                }

                var completedStatus = new CoreComponents.Lookup.Statuses().findById(full.transferStatusId, 'en');
                if (completedStatus) {
                    //if (completedStatus.text.replace(/\s/g, '').toLowerCase() === "completed") {
                    if (completedStatus.id === 3) {
                        html += "<div class='mr-sm' title='" + Resources.Completed + "'><i class='fa fa-check-square-o mr-sm text-success'></i></div>&nbsp;";
                    }
                    if (completedStatus.id === 4) {
                        html += "<div class='mr-sm' title='" + Resources.RequestToComplete + "'><i class='fa fa-fw fa-hand-paper-o mr-sm text-danger'></i></div>&nbsp;";
                    }
                }
                return "<div id='divLock" + full.id + "' style='display: inline-flex;'>" + html + "</div>";
            }
        });
        columns.push({
            'visible': !IsInboxModeWithGrouping,
            "className": "text-right",
            "autoWidth": false,
            "bAutoWidth": false,
            width: "16px",
            'orderable': false,
            'sortable': false,
            'render': function (data, type, full, meta) {
                var html = '';
                if (!_isFollowUpNode) {
                    var color = "";
                    var text = "";
                    var privacies = new CoreComponents.Lookup.Privacies().get(window.language);
                        for (var i = 0; i < privacies.length; i++) {
                            if (privacies[i].id === full.privacyId) {
                                color = privacies[i].color;
                                text = Resources.Privacy + ": " + privacies[i].text;
                                html += "<div class='mr-sm' title='" + text + "' style='height: 24px'>" +
                                    "<i class='fa fa-shield fa-lg' style='color: " + color + "'></i>&nbsp;" +
                                    "</div>";
                                break;
                            }
                        }
                   
                    var categories = new Categories().get(window.language);
                    var matchedCategory, categoryName;
                    matchedCategory = categories.find(c => c.id === full.categoryId);
                    categoryName = matchedCategory ? matchedCategory.text : "";
                    if (matchedCategory.text == "Incoming") {


                        html += "<div class='mr-sm' title='" + categoryName + "' style='height: 24px'>" +
                            "<i class=' fa fa-arrow-down '></i>&nbsp;" +
                            "</div>";
                    }
                    else if (matchedCategory.text == "Outgoing") {


                        html += "<div class='mr-sm' title='" + categoryName + "' style='height: 24px'>" +
                            "<i class=' fa fa-arrow-up '></i>&nbsp;" +
                            "</div>";
                    } else if (matchedCategory.text == "Internal") {


                        html += "<div class='mr-sm' title='" + categoryName + "' style='height: 24px'>" +
                            "<i class=' fa fa-arrows-h '></i>&nbsp;" +
                            "</div>";
                    }
            
                    if (full.openedDate === "") {
                       

                        let btnRecall = document.createElement("button");
                        btnRecall.setAttribute("class", "btn btn-xs btn-success mr-sm recall");
                        btnRecall.setAttribute("title", Resources.Recall);
                        btnRecall.setAttribute("clickattr", "recall(" + full.id + ", " + self.model.delegationId + ")");
                        btnRecall.innerHTML = "<i class='fa fa-repeat fa-white'/>";
                        html += btnRecall.outerHTML;
                    }
                }

                let btnView = document.createElement("button");
                btnView.setAttribute("class", "btn btn-xs btn-warning mr-sm view");
                btnView.setAttribute("title", Resources.View);
                btnView.setAttribute("clickattr", "openDocument(" + full.id + ", " + self.model.delegationId + ", " + self.model.nodeId + ")");
                btnView.innerHTML = "<i class='fa fa-eye fa-white'/>";
                html += btnView.outerHTML

                if (!_isFollowUpNode) {
                    const visualtrackingBtn = document.createElement("button");
                    visualtrackingBtn.setAttribute("class", "btn btn-xs btn-success visualtracking");
                    visualtrackingBtn.setAttribute("title", Resources.ViewVisualTracking);
                    visualtrackingBtn.setAttribute("clickattr", "VisualTracking.openVisualTracking(" + full.documentId + "," + model.delegationId + ")");
                    visualtrackingBtn.innerHTML = "<i class='fa fa-sitemap fa-white'/>";
                    html += visualtrackingBtn.outerHTML;



                }
                return "<div style='display: inline-flex;'>" + html + "</div>";

            }
        });
        if (!IsInboxModeWithGrouping) {
            SecurityMatrix.getRowActions(securityMatrix, columns, self.model.nodeId);
        }
        var table = $("#" + gTableName).DataTable({
            "createdRow": function (row, data, dataIndex) {
                var color = "";
                var priorities = new CoreComponents.Lookup.Priorities().get(window.language);
                for (var i = 0; i < priorities.length; i++) {
                    if (priorities[i].id === data.priorityId) {
                        color = priorities[i].color;
                    }
                }
                if (color !== "") {
                   $(row).attr('style', "color:" + color);
                }
                var recallButton = $(row).find(".recall");
                if (data.transferStatusId === 3) { 
                    recallButton.hide();
                } else {
                    recallButton.show();
                }
            
            },
            processing: true,
            ordering: true,
            serverSide: true,
            pageLength: 10,
            "ajax": {
                "url": "/Transfer/ListSent",
                "type": "POST",
                "datatype": "json",
                data: function (d) {
                    d.__RequestVerificationToken = $('input[name="__RequestVerificationToken"]').val();
                    d.DelegationId = self.model.delegationId;
                    d.NodeId = self.model.nodeId;
                    d.PriorityId = $("#cmbFilterSentPriority").val() !== null && typeof $("#cmbFilterSentPriority").val() !== "undefined" ? $("#cmbFilterSentPriority").val() : "0";
                    d.PrivacyId = $("#cmbFilterSentPrivacy").val() !== null && typeof $("#cmbFilterSentPrivacy").val() !== "undefined" ? $("#cmbFilterSentPrivacy").val() : "0";
                    d.PurposeId = $("#cmbFilterSentPurpose").val() !== null && typeof $("#cmbFilterSentPurpose").val() !== "undefined" ? $("#cmbFilterSentPurpose").val() : "0";
                    d.CategoryId = $("#cmbFilterSentCategory").val() !== null && typeof $("#cmbFilterSentCategory").val() !== "undefined" ? $("#cmbFilterSentCategory").val() : "0";
                    d.StatusId = $("#cmbFilterSentStatus").val() !== null && typeof $("#cmbFilterSentStatus").val() !== "undefined" ? $("#cmbFilterSentStatus").val() : "0";
                    d.ReferenceNumber = $("#txtFilterSentReferenceNumber").val() !== "" && typeof $("#txtFilterSentReferenceNumber").val() !== "undefined" ? $("#txtFilterSentReferenceNumber").val() : "";
                    d.Subject = $("#txtFilterSentSubject").val() !== "" && typeof $("#txtFilterSentSubject").val() !== "undefined" ? $("#txtFilterSentSubject").val() : "";
                    d.FromDate = $("#filterSentFromDate").val() !== "" && typeof $("#filterSentFromDate").val() !== "undefined" ? $("#filterSentFromDate").val() : "";
                    d.ToDate = $("#filterSentToDate").val() !== "" && typeof $("#filterSentToDate").val() !== "undefined" ? $("#filterSentToDate").val() : "";
                    d.StructureIds = $("#cmbFilterSentStructure").val() !== null && typeof $("#cmbFilterSentStructure").val() !== "undefined" ? $("#cmbFilterSentStructure").val() : [];
                    d.UserIds = $("#cmbFilterSentUser").val() !== null && typeof $("#cmbFilterSentUser").val() !== "undefined" ? $("#cmbFilterSentUser").val() : [];
                    d.structureId = window.EnablePerStructure ? $('#hdLoggedInStructureId').val() : $("#hdStructureId").val();
                    d.FromStructure = fromStructureSent;
                    return d;
                }, "dataSrc": function (response) {
                    if (response.data && response.data != "") {
                        var result = [];
                        response.data.map(function (obj) {
                            var value = {
                                key: obj.documentId.toString(),
                                arr: response.data.filter((item) => item.documentId == obj.documentId)
                            }
                            if (result.length == 0 || (result.length > 0 && !result.filter((element) => element.key == obj.documentId).length > 0))
                                result.push(value);
                        }, {});
                        datatableParentsAndChildren = result.map(function (item) {
                            return {
                                parent: item.arr[0],
                                children: item.arr
                            };
                        });
                        dataTableParents = datatableParentsAndChildren.map(function (item) {
                            return item.parent;
                        });
                    }
                    var returnValue = IsInboxModeWithGrouping ? dataTableParents : response.data;
                    return returnValue;
                }
            },
            "order": [],
            "columns": columns,
            "fnInitComplete": function (settings, json) {
                $('[data-toggle="tooltip"]').tooltip();
            },
            dom: '<"html5buttons "B>ltrpi',
            buttons: allButtons
        });

        table.on('draw.dt', function (d, f, c, a) {
            $('#chkAll').on('click', function () {
                if ($(this).is(":checked")) {
                    if (!_isFollowUpNode) {
                        if ($("#recallBtn").length === 0) {
                            $(".html5buttons .btn-group").append(`
                                <button class="btn btn-primary btn-success btn-sm  btn-default recall" title="${Resources.Recall}" aria-controls="grdSentItems" id="recallBtn">
                                    ${Resources.Recall}
                                </button>
                            `);
                        }
                    }
                }
                else {
                    $("#recallBtn").remove();
                }

            });
            $(document).off("click", "#recallBtn").on("click", "#recallBtn", function () {
                
                
                if (window.EnableConfirmationMessage === "True") {
                    Common.showConfirmMsg(Resources.RecallConfirmation, function () {
                        singleOrMultiRecallAction(table);
                    })
                }
                else {
                    singleOrMultiRecallAction(table);
                }
            });
            $('.rowCheckbox').on('click', function () {
                let hasRecallableRows = false;
                $('.rowCheckbox:checked').each(function () {
                    let $selectedRow = $(this).closest('tr');
                    if ($selectedRow.find(".recall").length > 0) {
                        hasRecallableRows = true;
                        return false; 
                    }
                });

                if (!_isFollowUpNode) {
                    if (hasRecallableRows) {
                        if ($("#recallBtn").length === 0) {
                            $(".html5buttons .btn-group").append(`
                                    <button class="btn btn-primary btn-success btn-sm  btn-default recall" title="${Resources.Recall}" id="recallBtn">
                                        ${Resources.Recall}
                                    </button>
                            `);
                        }
                        $(".conditional-buttons").removeClass("hidden");
                    } else {
                        $(".conditional-buttons").addClass("hidden");
                        $("#recallBtn").remove();
                    }
                }
            });
            $('#' + gTableName + ' tbody tr td').each(function () {
                this.setAttribute('title', $(this).text());
            });
            GridCommon.CheckSelectedRows(gTableName);

            if ($('#expandAll')[0] != undefined) {
                $('#expandAll')[0].classList.add("expand");
                $('#expandAll')[0].classList.remove("colllapse");
            }

        })

        GridCommon.AddCheckBoxEvents(gTableName);
        if (window.AllowRowSelection == "True") {
            $('#' + gTableName + ' tbody').on('click', 'tr', function () {
                // Check if any selected rows have recall buttons
                let hasRecallableRows = false;
                $('.rowCheckbox:checked').each(function () {
                    let $selectedRow = $(this).closest('tr');
                    if ($selectedRow.find(".recall").length > 0) {
                        hasRecallableRows = true;
                        return false; // Break out of each loop
                    }
                });

                if (!_isFollowUpNode) {
                    if (hasRecallableRows) {
                        // Show recall button if any selected row can be recalled
                        if ($("#recallBtn").length === 0) {
                            $(".html5buttons .btn-group").append(`
                                <button class="btn btn-primary btn-success btn-sm  btn-default recall" title="${Resources.Recall}" id="recallBtn">
                                    ${Resources.Recall}
                                </button>
                            `);
                        }
                    } else {
                        // Hide recall button if no selected rows can be recalled
                        $("#recallBtn").remove();
                    }
                }

            });
        }

        table.$('td').tooltip({
            "delay": 0,
            "track": true,
            "fade": 250
        });
        $('#' + gTableName + ' tbody').on('click', 'td.details-control', function () {
            let tr = $(this).closest('tr');
            let row = table.row(tr);
            if (row.child.isShown()) {
                row.child.hide();
                tr.removeClass('shown');
            }
            else {
                if (IsInboxModeWithGrouping) {

                    row.child(formatChild(self, row)).show();
                } else {

                    row.child(format(row, self.model.nodeId)).show();
                }

                tr.addClass('shown');
            }
            //if (_isFollowUpNode) {
            //    $('[data-documentId=' + row.data().documentId + ']').prop('checked', !row.child.isShown());
            //    $('[data-documentId=' + row.data().documentId + ']').trigger("change");
            //}
        });

        $('#' + gTableName + ' tbody').on('click', 'td.parentCheckAll', function (event) {
            event.stopPropagation();
            let tr = $(this).closest('tr');
            var data = table.row(tr).data();
            $('[parent=' + data.id + ']').prop('checked', $('[data-id=' + data.id + ']').prop('checked'));
            $('[parent=' + data.id + ']').trigger("change");
        });
        if (!IsInboxModeWithGrouping) {
            $('#' + gTableName + ' tbody').on('click', ".view,.visualtracking", function () {
                var onclick = $(this).attr("clickattr");
                eval(onclick);
            });
            $('#' + gTableName + ' tbody').on('click', ".recall", function () {

                var onclick = $(this).attr("clickattr");
                eval(onclick);
            });
            $('#' + gTableName + ' tbody').on('dblclick', 'tr', function () {

                if (!gLocked) {
                    gLocked = true;
                    try {
                        var onclick = $(this).find(".view").attr("clickattr");
                        eval(onclick);
                    } catch (e) {
                        gLocked = false;
                    }
                }
            });
            $('#' + gTableName + ' tbody').on('click', '.subjectSpan', function () {
                if (!gLocked) {
                    gLocked = true;
                    try {
                        var onclick = $(this).closest('tr').find(".view").attr("clickattr");
                        eval(onclick);
                    } catch (e) {
                        gLocked = false;
                    }
                }
            });
        }
        else {

            $('#' + gTableName + ' tbody').on('dblclick', 'tr', function () {

                $(this).find('td.details-control').trigger('click');

            });
        }

        $('#expandAll').on('click', function () {
            var expandAllbtn = this;

            table.rows().eq(0).each(function (index) {
                var row = table.row(index);
                var tr = row.node();
                if (expandAllbtn.classList.contains("expand")) {
                    if (row.child.isShown()) {
                        return;
                    }

                    if (IsInboxModeWithGrouping) {

                        row.child(formatChild(self, row)).show();
                    } else {

                        row.child(format(row, self.model.nodeId)).show();
                    }
                    tr.classList.add("shown");
                }
                else if (expandAllbtn.classList.contains("colllapse")) {
                    row.child.hide();
                    tr.classList.remove("shown")
                }
            });
            // use word colllapse not collapse to avoid hide the expandAll btn 
            if (expandAllbtn.classList.contains("expand")) {
                expandAllbtn.classList.add("colllapse");
                expandAllbtn.classList.remove("expand");

            } else if (expandAllbtn.classList.contains("colllapse")) {
                expandAllbtn.classList.add("expand");
                expandAllbtn.classList.remove("colllapse");
            }
        });


        SecurityMatrix.InitToolbarColor();
        SecurityMatrix.InitContextMenu(securityMatrix, self.model.nodeId);
        $('#filtersContainer input').off('keydown', function (e) { });
        $('#filtersContainer input').on('keydown', function (e) {
            var code = e.keyCode || e.which;
            if (code === 13) {
                $("#btnFilterSentSearch").trigger('click');
            }
        });
        $('#' + gTableName).on('click', '#chkAll', function () {
            let isChecked = $(this).is(":checked");

            if (isChecked) {
                $(".html5buttons .btn-danger").removeClass("hidden");
                $(".conditional-buttons").removeClass("hidden");
            } else {
                $(".html5buttons .btn-danger").addClass("hidden");
                $(".conditional-buttons").addClass("hidden");
            }

            let table = $('#' + gTableName).DataTable();
            let pageNodes = table.rows({ page: 'current' }).nodes();
            $('input[type="checkbox"]', pageNodes).prop('checked', isChecked);
        });

        $('#' + gTableName).on('change', 'input[type="checkbox"]:not(#chkAll)', function () {
            let table = $('#' + gTableName).DataTable();
            let pageNodes = table.rows({ page: 'current' }).nodes();

            let total = $('input[type="checkbox"]:not(#chkAll)', pageNodes).length;
            let checked = $('input[type="checkbox"]:not(#chkAll):checked', pageNodes).length;

          
            $('#chkAll').prop('checked', total === checked);

            if (checked > 0) {
                $(".html5buttons .btn-danger").removeClass("hidden");
                $(".conditional-buttons").removeClass("hidden");
            } else {
                $(".html5buttons .btn-danger").addClass("hidden");
                $(".conditional-buttons").addClass("hidden");
            }
        });
        table.on('draw', function () {
            $('#chkAll').prop('checked', false);

            let pageNodes = table.rows({ page: 'current' }).nodes();
            $('input[type="checkbox"]', pageNodes).prop('checked', false);

            $(".html5buttons .btn-danger").addClass("hidden");
            $(".conditional-buttons").addClass("hidden");
        });

        $('.toggleVIP').on('click', function () {
            if (window.InboxMode === "InboxDefault") {
                window.InboxMode = "LocalVIPView";
            } else if (window.InboxMode === "LocalInboxDefaultView") {
                window.InboxMode = "InboxVIPView";
            }

            let wrapper = $(".content-wrapper");
            let VIPmodel = new VipDocumentSent.VipDocumentSent();
            VIPmodel.nodeId = wrapperParent.nodeId;
            VIPmodel.delegationId = wrapperParent.delegationId;
            VIPmodel.categories = new Categories().get(window.language, wrapperParent.delegationId);
            VIPmodel.privacies = new CoreComponents.Lookup.Privacies().get(window.language);
            VIPmodel.title = $('.content-heading').text();
            let documentView = new VipDocumentSent.VipDocumentSentView(wrapper, VIPmodel);
            documentView.render();

        })
    }
}
export default { DocumentSent, DocumentSentView };