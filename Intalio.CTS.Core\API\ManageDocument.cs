﻿using Intalio.Core;
using Intalio.Core.API;
using Intalio.Core.Model;
using Intalio.CTS.Core.DAL;
using Intalio.CTS.Core.Model;
using Intalio.CTS.Core.Utility;
using Intalio.CTS;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Text.RegularExpressions;
using Aspose.Pdf.Devices;
using Microsoft.AspNetCore.Authorization;
using System.Net.Http;
using LinqKit;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.SignalR;
using Serilog;
using System.IO;
using System.Text;
using Aspose.Pdf ;
using Document = Intalio.CTS.Core.DAL.Document;
using Note = Intalio.CTS.Core.DAL.Note;
using Operator = Intalio.Core.Operator;
using NPOI.Util;
using Intalio.Core.Utility;

//using Intalio.Core.DAL;


namespace Intalio.CTS.Core.API
{
    public static class ManageDocument
    {
        #region Internal Methods

        /// <summary>
        /// Update document status
        /// </summary>
        /// <param name="item"></param>
        /// <param name="status"></param>
        internal static void UpdateDocumentStatusById(long id, DocumentStatus status)
        {
            var item = new Document().Find(id);
            if (item != null && item.StatusId != (short)status)
            {
                item.StatusId = (short)status;
                item.UpdateStatus();
            }
        }

        /// <summary>
        /// Update document status
        /// </summary>
        /// <param name="item"></param>
        /// <param name="status"></param>
        internal static void UpdateDocumentStatus(Document item, DocumentStatus status)
        {
            if (item != null && item.StatusId != (short)status)
            {
                item.StatusId = (short)status;
                item.UpdateStatus();
            }
        }

        /// <summary>
        /// Update documents statuses by ids
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="status"></param>
        internal static void UpdateDocumentStatusByIds(List<long> ids, DocumentStatus status)
        {
            if (ids.Count > 0)
            {
                List<Document> toBeUpdated = new List<Document>();
                List<Document> documents = new Document().ListByIds(ids);
                if (documents.Count > 0)
                {
                    for (int i = 0; i < documents.Count; i++)
                    {
                        if (documents[i].StatusId != (short)status && documents[i].StatusId == (short)DocumentStatus.Draft)
                        {
                            documents[i].StatusId = (short)status;
                            toBeUpdated.Add(documents[i]);
                        }
                    }
                    new Document().UpdateStatusByList(toBeUpdated);
                }
            }
        }

        /// <summary>
        /// Check if document has an external document linked to it by external reference number
        /// </summary>
        /// <param name="item"></param>
        /// <param name="userId"></param>
        /// <param name="isEnableEdit"></param>
        /// <returns></returns>
        internal static bool HasExternalReferencedDocument(Document item, long userId, bool isEnableEdit = false)
        {
            bool retValue = true;
            if (string.IsNullOrEmpty(item.ExternalReferenceNumber))
            {
                retValue = false;
            }
            else if (item.CreatedByUserId == userId || isEnableEdit)
            {
                retValue = new Document().HasExternalReferencedDocument(item.Id, item.CategoryId, item.SendingEntityId, item.ExternalReferenceNumber);
            }
            return retValue;
        }

        /// <summary>
        /// Copy document metadata to a new document
        /// </summary>
        /// <param name="model"></param>
        /// <param name="userId"></param>
        /// <param name="structureId"></param>
        /// <param name="prevDocId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="AttachmentIds"></param>
        /// <returns></returns>
        internal static async Task<Document> CopyDocumentMetadataByCategory(DocumentViewModel model, long userId, long structureId, long prevDocId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, List<string> AttachmentIds, Language language)
        {
            var enabledFields = new List<string>();
            var prevSendingType = string.Empty;
            var prevRecievingType = string.Empty;
            var prevCarbonCopyType = string.Empty;
            var newSendingType = string.Empty;
            var newRecievingType = string.Empty;
            var newCarbonCopyType = string.Empty;
            var newMultipleReceivingEntity = true;
            var copyToReceivingEntity = false;
            var previousDocument = new Document().FindIncludeDocumentForm(prevDocId);
            var previousCategory = new Category().FindWithInclude(previousDocument.CategoryId);
            var newCategory = new Category().Find(model.CategoryId);
            dynamic dataConfiguration = JsonConvert.DeserializeObject(previousCategory.Configuration);
            dynamic previousBasicAttribute = JsonConvert.DeserializeObject(previousCategory.BasicAttribute);
            dynamic newBasicAttribute = JsonConvert.DeserializeObject(newCategory.BasicAttribute);
            dynamic oldCustomAttributes = null;
            dynamic newCustomAttributes = null;

            foreach (var attr in previousBasicAttribute.Root)
            {
                if (attr.Name.Value == "SendingEntity")
                {
                    prevSendingType = attr.Type.Value;
                }
                if (attr.Name.Value == "ReceivingEntity")
                {
                    prevRecievingType = attr.Type.Value;
                }
                if (attr.Name.Value == "CarbonCopy")
                {
                    prevCarbonCopyType = attr.Type.Value;
                }

                if (attr.Enabled != null && attr.Enabled.Value)
                {
                    enabledFields.Add(attr.Name.Value);
                }
            }
            foreach (var attr in newBasicAttribute.Root)
            {
                if (attr.Name.Value == "SendingEntity")
                {
                    newSendingType = attr.Type.Value;
                }
                if (attr.Name.Value == "ReceivingEntity")
                {
                    newRecievingType = attr.Type.Value;
                }
                if (attr.Name.Value == "CarbonCopy")
                {
                    newCarbonCopyType = attr.Type.Value;
                }
                if (attr.Name.Value == "MultipleReceivingEntity")
                {
                    newMultipleReceivingEntity = attr.DefaultValue.Value;
                }
            }
            Document item = new Document
            {
                CategoryId = model.CategoryId,
                StatusId = (short)DocumentStatus.Draft,
                CreatedByUserId = userId,
                CreatedByStructureId = model.CreatedByStructureId,
                DocumentForm = new DocumentForm { Form = model.FormData }
            };
            item.Insert();


            foreach (var replyItems in dataConfiguration.Reply)
            {
                if (Convert.ToInt64(replyItems.Name) == model.CategoryId)
                {
                    if (previousDocument.DocumentForm.Form != null && previousDocument.DocumentForm.Form != "[]")
                    {
                        oldCustomAttributes = JsonConvert.DeserializeObject<Dictionary<string, dynamic>>(previousDocument.DocumentForm.Form);
                        newCustomAttributes = new Dictionary<string, dynamic>();
                    }
                    foreach (var field in replyItems.Value)
                    {
                        if (field.Key != null && field.Copy.Value == true)
                        {
                            newCustomAttributes.Add(field.Key.Value, oldCustomAttributes[field.Key.Value]);
                        }


                        switch (field.Name.Value)
                        {
                            case ("Subject"):
                                if (field.Copy.Value)
                                {
                                    item.Subject = previousDocument.Subject;

                                }
                                break;
                            case ("SendingEntity"):
                                if (field.CopyToReceivingEntity != null && field.CopyToReceivingEntity.Value)
                                {
                                    copyToReceivingEntity = true;
                                    if (prevSendingType == newRecievingType)
                                    {
                                        //recheck
                                        var receiversList = new DocumentReceiverEntity().List(prevDocId);
                                        if (newRecievingType.ToLower() == StructureType.Internal.ToString().ToLower() && Configuration.EnableSendingRules)
                                        {
                                            var structureIdsSendingRules = ManageSendingRule.ListStructureIdsBySendingRules(userId, structureId);
                                            if (structureIdsSendingRules.Contains(previousDocument.SendingEntityId))
                                            {
                                                item.DocumentReceiverEntity.Add(new DocumentReceiverEntity
                                                {
                                                    StructureId = previousDocument.SendingEntityId.Value
                                                });
                                            }
                                        }
                                        else
                                        {
                                            item.DocumentReceiverEntity.Add(new DocumentReceiverEntity
                                            {
                                                StructureId = previousDocument.SendingEntityId.Value
                                            });
                                        }
                                    }
                                }
                                if (field.Copy.Value)
                                {
                                    if (prevSendingType == newSendingType)
                                    {
                                        if (newSendingType.ToLower() == StructureType.Internal.ToString().ToLower() && Configuration.EnableSendingRules)
                                        {
                                            var structureIdsSendingRules = ManageSendingRule.ListStructureIdsBySendingRules(userId, structureId);
                                            if (structureIdsSendingRules.Contains(previousDocument.SendingEntityId))
                                            {
                                                item.SendingEntityId = previousDocument.SendingEntityId;
                                            }
                                        }
                                        else
                                        {
                                            item.SendingEntityId = previousDocument.SendingEntityId;
                                        }
                                    }
                                }
                                break;
                            case ("ReceivingEntity"):
                                var receiverList = new DocumentReceiverEntity().List(prevDocId);
                                if (field.CopyToSendingEntity != null && field.CopyToSendingEntity.Value)
                                {
                                    if (prevRecievingType == newSendingType)
                                    {
                                        if (newSendingType.ToLower() == StructureType.Internal.ToString().ToLower() && Configuration.EnableSendingRules)
                                        {
                                            //recheck
                                            var structureIdsSendingRules = ManageSendingRule.ListStructureIdsBySendingRules(userId, structureId);
                                            var exist = receiverList.Where(x => x.StructureId.HasValue).Select(x => x.StructureId.Value).Intersect(structureIdsSendingRules.Select(x => x.Value));
                                            if (exist.Count() > 0)
                                            {
                                                item.SendingEntityId = exist.First();
                                            }
                                        }
                                        else
                                        {
                                            item.SendingEntityId = receiverList.Where(x => x.StructureId.HasValue).Select(x => x.StructureId).First();
                                        }
                                    }
                                }
                                if (field.Copy.Value)
                                {
                                    if (prevRecievingType == newRecievingType)
                                    {
                                        if (copyToReceivingEntity && !newMultipleReceivingEntity)
                                        {
                                            item.DocumentReceiverEntity.Clear();
                                        }
                                        if (newRecievingType.ToLower() == StructureType.Internal.ToString().ToLower() && Configuration.EnableSendingRules)
                                        {
                                            var structureIdsSendingRules = ManageSendingRule.ListStructureIdsBySendingRules(userId, structureId);
                                            var exist = receiverList.Where(x => x.StructureId.HasValue).Select(x => x.StructureId.Value).Intersect(structureIdsSendingRules.Select(x => x.Value));
                                            if (exist.Count() > 0)
                                            {
                                                exist.Where(t => !item.DocumentReceiverEntity.Any(n => n.StructureId == t)).Select(t => new DocumentReceiverEntity
                                                {
                                                    StructureId = t
                                                }).ToList().ForEach(t => item.DocumentReceiverEntity.Add(t));
                                            }
                                            exist = receiverList.Where(x => x.EntityGroupId.HasValue).Select(x => x.EntityGroupId.Value).ToList();
                                            if (exist.Count() > 0)
                                            {
                                                exist.Where(t => !item.DocumentReceiverEntity.Any(n => n.EntityGroupId == t)).Select(t => new DocumentReceiverEntity
                                                {
                                                    EntityGroupId = t
                                                }).ToList().ForEach(t => item.DocumentReceiverEntity.Add(t));
                                            }
                                        }
                                        else
                                        {
                                            receiverList.Where(t => t.StructureId.HasValue && !item.DocumentReceiverEntity.Any(n => n.StructureId == t.StructureId)).Select(t => new DocumentReceiverEntity
                                            {
                                                StructureId = t.StructureId
                                            }).ToList().ForEach(t => item.DocumentReceiverEntity.Add(t));
                                            receiverList.Where(t => t.EntityGroupId.HasValue && !item.DocumentReceiverEntity.Any(n => n.EntityGroupId == t.EntityGroupId)).Select(t => new DocumentReceiverEntity
                                            {
                                                EntityGroupId = t.EntityGroupId
                                            }).ToList().ForEach(t => item.DocumentReceiverEntity.Add(t));
                                        }
                                    }
                                }
                                break;
                            case ("CarbonCopy"):
                                if (enabledFields.Contains("CarbonCopy") && field.Copy.Value)
                                {
                                    if (prevCarbonCopyType == newCarbonCopyType)
                                    {
                                        var carbonCopyList = new DocumentCarbonCopy().List(prevDocId);
                                        if (newCarbonCopyType.ToLower() == StructureType.Internal.ToString().ToLower() && Configuration.EnableSendingRules)
                                        {
                                            var structureIdsSendingRules = ManageSendingRule.ListStructureIdsBySendingRules(userId, structureId);
                                            var exist = carbonCopyList.Select(x => x.StructureId).Intersect(structureIdsSendingRules.Select(x => x.Value));
                                            if (exist.Count() > 0)
                                            {
                                                exist.Where(t => !item.DocumentCarbonCopy.Any(n => n.StructureId == t)).Select(t => new DocumentCarbonCopy
                                                {
                                                    StructureId = t
                                                }).ToList().ForEach(t => item.DocumentCarbonCopy.Add(t));
                                            }
                                        }
                                        else
                                        {
                                            carbonCopyList.Where(t => !item.DocumentCarbonCopy.Any(n => n.StructureId == t.StructureId)).Select(t => new DocumentCarbonCopy
                                            {
                                                StructureId = t.StructureId
                                            }).ToList().ForEach(t => item.DocumentCarbonCopy.Add(t));
                                        }
                                    }
                                }
                                break;
                            case ("Attachment"):
                                if (field.Copy.Value)
                                {
                                    var copiedFolder = new Folder
                                    {
                                        Name = TranslationUtility.Translate("CopiedAttachments", language),
                                        PhysicalName = "Copied Attachments",
                                        ParentId = null, // Root level folder
                                        DocumentId = item.Id,
                                    };
                                    copiedFolder.Insert(); // Insert into DB

                                    var finalAttachmentIds = new List<string>();

                                    foreach (var id in AttachmentIds)
                                    {
                                        if (id.StartsWith("folder_"))
                                        {
                                            string folderSpecifier = id.Substring("folder_".Length);

                                            if (folderSpecifier.Equals("originalMail", StringComparison.OrdinalIgnoreCase))
                                            {
                                                // Get attachments in the root folder (FolderId == null)
                                                var rootAttachments = new Attachment()
                                                    .ListTreeRootAttachments(prevDocId, userId, structureIds);

                                                finalAttachmentIds.AddRange(rootAttachments.Select(a => a.Id.ToString()));
                                            }
                                            else if (long.TryParse(folderSpecifier, out long folderId))
                                            {
                                                // Get attachments from a specific folder
                                                var folderAttachments = new Attachment()
                                                    .ListTreeFolderAttachments(prevDocId, userId, structureIds, folderId);

                                                finalAttachmentIds.AddRange(folderAttachments.Select(a => a.Id.ToString()));
                                            }
                                            else
                                            {
                                                Console.WriteLine($"Unrecognized folder identifier: {folderSpecifier}");
                                            }
                                        }
                                        else
                                        {
                                            // Add direct attachment ID
                                            finalAttachmentIds.Add(id);
                                        }
                                    }
                                    finalAttachmentIds = finalAttachmentIds.Distinct().ToList();
                                    var attachmentList = new Attachment()
                                        .ListAttachments(prevDocId, userId, structureIds)
                                        .Where(a => finalAttachmentIds.Contains(a.Id.ToString()))
                                        .ToList();
                                    //var attachmentList = new Attachment().ListTreeRootAttachments(prevDocId, userId, structureIds).Where(a => AttachmentIds.Contains(a.Id.ToString())).ToList();
                                    //var attachmentList = new Attachment().ListAttachments(prevDocId, userId, structureIds).Where(a => AttachmentIds.Contains(a.Id.ToString())).ToList();
                                    for (int i = 0; i < attachmentList.Count; i++)
                                    {
                                        //if (attachmentList[i].Id != previousDocument.AttachmentId)
                                        //{
                                        Attachment attachment = attachmentList[i];
                                        var storageObj = new Intalio.Core.Model.StorageAttachmentModel();
                                        storageObj = await ManageAttachment.GetStorageAttachmentModel(attachment.StorageAttachmentId);
                                        var fileViewModel = new FileViewModel()
                                        {
                                            Name = storageObj.Name,
                                            FileSize = (long)storageObj.FileSize,
                                            Extension = storageObj.Extension,
                                            ContentType = storageObj.ContentType,
                                            Data = storageObj.Data,
                                            MD5Checksum = storageObj.MD5Checksum,

                                        };
                                        await ManageAttachment.UploadAttachment(item.Id, null, copiedFolder.Id, item.CategoryId, fileViewModel, userId, structureIds, isStructureReceiver, privacyLevel, null);
                                        //}

                                    }
                                }
                                break;
                            case ("Priority"):
                                if (field.Copy.Value)
                                {
                                    item.PriorityId = previousDocument.PriorityId;
                                }
                                break;
                            case ("Privacy"):
                                if (field.Copy.Value)
                                {
                                    item.PrivacyId = previousDocument.PrivacyId;
                                }
                                break;
                            case ("Importance"):
                                if (field.Copy.Value)
                                {
                                    item.ImportanceId = previousDocument.ImportanceId;
                                }
                                break;
                            case ("Classification"):
                                if (enabledFields.Contains("Classification") && field.Copy.Value)
                                {
                                    item.ClassificationId = previousDocument.ClassificationId;
                                }
                                break;
                            case ("DocumentType"):
                                if (enabledFields.Contains("DocumentType") && field.Copy.Value)
                                {
                                    item.DocumentTypeId = previousDocument.DocumentTypeId;
                                }
                                break;
                            case ("Body"):
                                if (enabledFields.Contains("Body") && field.Copy.Value)
                                {
                                    item.DocumentForm.Body = previousDocument.DocumentForm != null ? previousDocument.DocumentForm.Body : string.Empty;
                                }
                                break;
                            case ("ExternalReferenceNumber"):
                                if (enabledFields.Contains("ExternalReferenceNumber") && field.Copy.Value)
                                {
                                    item.ExternalReferenceNumber = previousDocument.ExternalReferenceNumber;
                                }
                                break;
                            case ("Keyword"):
                                if (enabledFields.Contains("Keyword") && field.Copy.Value)
                                {
                                    item.DocumentForm.Keyword = previousDocument.DocumentForm != null ? previousDocument.DocumentForm.Keyword : string.Empty;
                                }
                                break;
                        }
                    }
                }
            }

            item.DocumentForm.Form = JsonConvert.SerializeObject(newCustomAttributes);
            return item;
        }

        internal static bool HasDocumentEdit(Document document, bool allowEditSigned)
        {
            if (!Configuration.EnableAttributeEdit)
                return false;

            //if (Configuration.EnableAttributeEditSign)
            //    return true;
            if (allowEditSigned)
                return true;

            return document != null && (!document.IsSigned.HasValue || document.IsSigned.HasValue && !document.IsSigned.Value);
        }


        #endregion

        #region Public Methods


        public static async Task<List<G2GRecipient>> MapRecipients(List<G2GRequestModel> govIds)
        {
            string url = Intalio.CTS.Core.Configuration.G2GService.TrimEnd('/') + "/G2GActions.svc/json/MapRecipients";
            var json = JsonConvert.SerializeObject(govIds);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            using var client = new HttpClient(new HttpClientHandler() { ServerCertificateCustomValidationCallback = HttpClientHandler.DangerousAcceptAnyServerCertificateValidator });

            var response = await client.PostAsync(url, content);
            response.EnsureSuccessStatusCode();

            var responseBody = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<List<G2GRecipient>>(responseBody);
        }

        public static async Task<G2GResponseModel> ExportToG2G(int documentId, List<G2GRequestModel> govIds, long userId, long structureId)
        {
            //Get the document info, then call the export api
            string url = Intalio.CTS.Core.Configuration.G2GService.TrimEnd('/') + "/G2GActions.svc/json/ExportUsingG2G";

            //This line will be replaced later on by a call from the front end
            var attachments = new Attachment().ListIdsByDocumentIdNoOriginalMail(documentId);
            var recipients = await MapRecipients(govIds);

            var requestBody = new
            {
                docId = documentId,
                recipients,
                attachments,
                exportedBy = userId,
                exportedByStructure = structureId,
                notes = "",
                token = Intalio.CTS.Core.Configuration.IdentityAccessToken
            };

            var json = JsonConvert.SerializeObject(requestBody);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var client = new HttpClient(new HttpClientHandler() { ServerCertificateCustomValidationCallback = HttpClientHandler.DangerousAcceptAnyServerCertificateValidator });
            client.DefaultRequestHeaders.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));

            var response = await client.PostAsync(url, content);
            if (!response.IsSuccessStatusCode)
            {
                return null;
            }
            string responseBody = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<G2GResponseModel>(responseBody);
        }
        /// <summary>
        /// Add document
        /// </summary>
        /// <param name="model"></param>
        /// <param name="file"></param>
        /// /// <param name="userId"></param>
        /// <param name="roleId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <returns></returns>
        public static async Task<bool> Create(DocumentViewModel model, FileViewModel file, long userId, int roleId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, Language language = Language.EN)
        {
            bool retValue = false;
            if (!ManageCategory.CheckCategoryPermission(model.CategoryId, roleId))
            {
                return retValue;
            }
            Document item = new Document
            {
                CategoryId = model.CategoryId,
                StatusId = (short)DocumentStatus.Draft,
                DraftStatus = (int)DraftStatus.Created,
                CreatedByUserId = userId,
                CreatedByStructureId = model.CreatedByStructureId,
                SenderPerson = model.Sender == null ? 0 : model.Sender,
                ReceiverPerson = string.IsNullOrEmpty(model.Receiver) ? string.Empty : model.Receiver,
                DocumentForm = new DocumentForm { Form = model.FormData },
                Attachment = model.Attachment,
                Note = model.Note,
                NonArchivedAttachments = model.NonArchivedAttachments,
                LinkedDocumentDocument = model.LinkedDocumentDocument,
                LinkedDocumentLinkedDocumentNavigation = model.LinkedDocumentLinkedDocumentNavigation
            };

            if (model.CategoryId == Configuration.FollowUpCategory)
            {
                item.StatusId = (short)DocumentStatus.InProgress;
            }
            bool hasSignature;
            if (file.Data != null && (file.Extension == "doc" || file.Extension == "docx"))
            {
                hasSignature = ManageTemplate.CheckFileHasSignatureImage(file.Data, "signature");
                item.TemplateHasSignature = hasSignature;
            }

            if (model.TemplateId.HasValue)
            {
                var template = ManageTemplate.FindById(model.TemplateId.Value);

                item.ByTemplate = true;
                item.TemplateHasSignature = template.HasSignature;
                file = ManageTemplate.FindAttachmentDataByTemplateId(model.TemplateId.Value);
            }
            var structureIdsForProvision = new List<long>();
            structureIdsForProvision.Add(model.CreatedByStructureId);
            ManageStructure.Provision(structureIdsForProvision);
            item.Insert();
            var newDocumentValue = JsonConvert.SerializeObject(new DocumentModel
            {
                Subject = item.Subject,
                FormData = item.DocumentForm != null ? item.DocumentForm.Form : string.Empty,
                Classification = item.Classification != default(Classification) ? language == Language.AR ? item.Classification.NameAr : language == Language.FR ? item.Classification.NameFr : item.Classification.Name : String.Empty,
                DocumentType = item.DocumentType != default(DocumentType) ? language == Language.AR ? item.DocumentType.NameAr : language == Language.FR ? item.DocumentType.NameFr : item.DocumentType.Name : String.Empty,
                DueDate = item.DueDate.HasValue ? item.DueDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty,
                DocumentDate = item.DocumentDate.HasValue ? item.DocumentDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty,
                Importance = item.Importance != default(Importance) ? language == Language.AR ? item.Importance.NameAr : language == Language.FR ? item.Importance.NameFr : item.Importance.Name : String.Empty,
                Priority = item.Priority != default(Priority) ? language == Language.AR ? item.Priority.NameAr : language == Language.FR ? item.Priority.NameFr : item.Priority.Name : String.Empty,
                Privacy = item.Privacy != default(Privacy) ? language == Language.AR ? item.Privacy.NameAr : language == Language.FR ? item.Privacy.NameFr : item.Privacy.Name : String.Empty,
                SendingEntity = item.SendingEntity != default(Structure) ? language == Language.AR ? !string.IsNullOrEmpty(item.SendingEntity.NameAr) ? item.SendingEntity.NameAr : item.SendingEntity.Name
                                                   : language == Language.FR ? !string.IsNullOrEmpty(item.SendingEntity.NameFr) ? item.SendingEntity.NameFr : item.SendingEntity.Name
                                                   : item.SendingEntity.Name : String.Empty,
                Receivers = new List<string>(),
                ExternalReferenceNumber = item.ExternalReferenceNumber,
                Keyword = item.DocumentForm != null ? item.DocumentForm.Keyword : string.Empty,
                Body = item.DocumentForm != null ? item.DocumentForm.Body : string.Empty,
                CarbonCopies = item.DocumentCarbonCopy.Any() ? item.DocumentCarbonCopy.Where(x => x.Structure != default(Structure))
                                               .Select(x => language == Language.AR ? !String.IsNullOrEmpty(x.Structure.NameAr) ? x.Structure.NameAr : x.Structure.Name
                                                   : language == Language.FR ? !String.IsNullOrEmpty(x.Structure.NameFr) ? x.Structure.NameFr : x.Structure.Name
                                                   : x.Structure.Name).ToList() : new List<string>()
            });
            ManageActivityLog.AddActivityLog(item.Id, null, (int)Core.ActivityLogs.Save, userId, "", newDocumentValue);

            var attachmentFolders = ManageAttachmentFolder.ListByCategoryId(model.CategoryId);
            foreach (var attachmentFolder in attachmentFolders)
            {
                var folder = new Folder
                {
                    Name = attachmentFolder.Name,
                    PhysicalName = attachmentFolder.Name,
                    ParentId = attachmentFolder.ParentId,
                    DocumentId = item.Id,
                };
                folder.Insert();
                var childrenFolders = attachmentFolders.Where(x => x.ParentId == attachmentFolder.Id);
                if (childrenFolders.Any())
                {
                    foreach (var childrenFolder in childrenFolders)
                    {
                        childrenFolder.ParentId = (int)folder.Id;
                    }
                }
            }

            if (!file.IsNull() && !file.Data.IsNullOrEmpty())
            {
                var attachmentId = await ManageAttachment.Upload(item.Id, null, null, model.CategoryId, file, userId, structureIds, isStructureReceiver, privacyLevel);
                if (attachmentId.IsNull())
                {
                    item.Delete();
                    return retValue;
                }
                item.AttachmentId = attachmentId;
                item.UpdateAttachmentIdWithoutModifiedDate();
            }
            model.Id = item.Id;
            retValue = true;
            new EventReceivers().OnDocumentCreated(model, file);
            return retValue;
        }


        /// <summary>
        /// Add document
        /// </summary>
        /// <param name="model"></param>
        /// <param name="file"></param>
        /// <param name="userId"></param>
        /// <param name="roleId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="delegatedUserId"></param>
        /// <param name="isStructureSender"></param>
        /// <param name="language"></param>
        /// <param name="structureId"></param>
        /// <returns></returns>
        public static async Task<(bool success,string message)> CreateCopy(DocumentViewModel model, FileViewModel file, long userId, int roleId, List<long> structureIds, bool isStructureReceiver, bool isStructureSender, short privacyLevel, long structureId, string activityLogNote, ActivityLogs? activityLogAction = null, Language language = Language.EN, long? delegatedUserId = null, bool fromExport = false, string usertoken = null)
        {

            (bool success, string message) retValue = (false, null);
            if (!ManageCategory.CheckCategoryPermission(model.CategoryId, roleId))
            {
                retValue.message = "dosenothaveaccessonthiscategory";
                return retValue;
            }
            Document item = new Document().FindIncludeForCopy(model.DocumentCopyId ?? 0);
            item.StatusId = (short)DocumentStatus.Draft;
            item.CreatedByUserId = userId;
            item.CreatedByStructureId = model.CreatedByStructureId;
            string CopiedTitle = item.ReferenceNumber ?? item.Subject;
            item.ReferenceNumber = null;
            item.ReferenceSequence = null;
            item.ExternalReferenceNumber = null;
            item.ModifiedDate = null;
            item.CreatedDate = DateTime.Now;
            item.ClosedDate = null;
            item.InitialCreatedDate = null;
            item.IsLocked = null;
            item.IsSigned = null;
            item.DocumentDate = null;

            if (item.DocumentForm != null)
            {
                item.DocumentForm.Id = 0;
            }
            if (item.DocumentCarbonCopy != null && item.DocumentCarbonCopy.Count() > 0)
            {
                item.DocumentCarbonCopy.ForEach(x => x.Id = 0);
            }
            if (model.CategoryId == Configuration.FollowUpCategory)
            {
                item.StatusId = (short)DocumentStatus.InProgress;
                item.CategoryId = model.CategoryId;

            }

            var entities = ManageUser.GetUsersStructuresFromCTS(null, model.DelegationId, Configuration.EnableSendingRules, Configuration.EnableTransferToUsers, isStructureSender, structureIds, delegatedUserId ?? userId, structureId, 0, int.MaxValue, StructureType.Both, null, null, true, language);
            var sendingEntities = entities != null ? entities.Select(x => x.Id) : new List<long>();
            if (!(model.CopyOptionsModal.WithEntities && sendingEntities.Contains(item.SendingEntityId ?? 0)))
            {
                item.SendingEntityId = null;
                item.SenderPerson = null;
            }
            if (!fromExport)
            {
                if (model.CopyOptionsModal.WithEntities && item.DocumentReceiverEntity != null && item.DocumentReceiverEntity.Count() > 0)
                {
                    foreach (var entity in item.DocumentReceiverEntity)
                    {
                        if (sendingEntities.Contains(entity.StructureId ?? 0))
                        {
                            entity.Id = 0;
                            continue;
                        }
                        item.DocumentReceiverEntity.Remove(entity);
                    }
                }
                else
                {
                    item.DocumentReceiverEntity = null;
                    item.ReceiverPerson = null;
                }
            }
            else
            {
                item.DocumentReceiverEntity.Clear();
                item.DocumentReceiverEntity.Add(model.DocumentReceiverEntity);
            }



            if (model.CopyOptionsModal.WithNotes && item.Note != null && item.Note.Count() > 0)
            {
                List<Note> deletednotes = new List<Note>();
                item.Note.ForEach(x =>
                {
                    if (x.IsPrivate)
                    {
                        item.Note.Remove(x);
                    }
                    x.Id = 0;
                    x.CreatedDate = DateTime.Now;
                    x.ModifiedDate = null;
                    x.CreatedByUserId = fromExport? x.CreatedByUserId:userId;
                    x.CreatedByDelegatedUserId = fromExport?x.CreatedByDelegatedUserId:delegatedUserId;
                    x.TransferId = null;
                    x.DocumentLockId = null;
                });
            }
            else
            {
                item.Note = new List<Note>();
            }
            ICollection<NonArchivedAttachments> nonArchivedAttachments = new List<NonArchivedAttachments>();

            if (model.CopyOptionsModal.WithNonArchivedAttachments == true && item.NonArchivedAttachments != null && item.NonArchivedAttachments.Count() > 0)
            {

                item.NonArchivedAttachments.ForEach(x =>
                {
                    x.Id = 0;
                    x.CreatedDate = DateTime.Now;
                    x.ModifiedDate = null;
                    x.CreatedByUserId = userId;
                    x.CreatedByDelegatedUserId = delegatedUserId;
                    x.TransferId = null;
                    x.DocumentLockId = null;

                    nonArchivedAttachments.Add(x);
                });
                item.NonArchivedAttachments = null;
            }
            else
            {
                item.NonArchivedAttachments = new List<NonArchivedAttachments>();
            }


            ICollection<LinkedDocument> linkeddocuments = new List<LinkedDocument>();
            if (model.CopyOptionsModal.WithLinkedCorrespondences && item.LinkedDocumentDocument != null && item.LinkedDocumentDocument.Count() > 0 )
            {
                
                    item.LinkedDocumentDocument.ForEach(x =>
                    {
                        var haveaccess = ManageUserAccess.HaveAccess(x.LinkedDocumentId, delegatedUserId ?? userId, structureIds, isStructureReceiver, privacyLevel, model.DelegationId);
                        if (haveaccess)
                        {
                            x.Id = 0;
                            x.TransferId = null;
                            x.CreatedDate = DateTime.Now;
                            x.CreatedByUserId = userId;
                            linkeddocuments.Add(x);
                        }
                    });
           
                item.LinkedDocumentDocument = null;
            }
            else
            {
                item.LinkedDocumentDocument = new List<LinkedDocument>();
            }
            item.LinkedDocumentLinkedDocumentNavigation = null;

            if (item.AttachmentId != null)
            {
                if (model.CopyOptionsModal.SpecificVersionId == null)
                {
                    model.CopyOptionsModal.SpecificVersionId = (await ManageAttachment.ListVersionHistory(item.AttachmentId ?? 0, 0, 1, model.DocumentCopyId ?? 0, model.TransferId, delegatedUserId ?? userId, structureIds, isStructureReceiver, privacyLevel, model.DelegationId, (model.ExportedDocumentId == null ? true : false))).Item2?.FirstOrDefault()?.Version;
                }
                //var Storagefile = await ManageAttachment.GetStorageAttachmentModel(new Attachment().Find(item.AttachmentId ?? 0)?.StorageAttachmentId ?? 0);
                ////var viewerfile = await ManageAttachment.DownloadFromViewer(item.AttachmentId ?? 0, model.TransferId, delegatedUserId ?? userId, structureIds, isStructureReceiver, privacyLevel, model.DelegationId, false, true, model.CopyOptionsModal.SpecificVersionId);
                //if (Storagefile.Data?.Length > 0/*&& viewerfile != null*/)
                //{
                //    file.Name = Intalio.Core.Helper.GetFileName(Storagefile.Name);
                //    file.FileSize = Convert.ToInt64(Storagefile.FileSize);
                //    file.ContentType = /*viewerfile != null ? "application/pdf" :*/ Storagefile.ContentType;
                //    file.Extension = /*viewerfile != null ? "pdf" :*/ Storagefile.Extension;
                //    file.Data = /*viewerfile != null ? (byte[])viewerfile.GetType().GetProperty("Data").GetValue(viewerfile) :*/ Storagefile.Data;/*(byte[])viewerfile.GetType().GetProperty("Data").GetValue(viewerfile);*/
                //}

                file = await ManageAttachment.GetAttachmentInfo(item.AttachmentId ?? 0, model.CopyOptionsModal.SpecificVersionId, item.Id, model.TransferId, delegatedUserId ?? userId, structureIds, isStructureReceiver, privacyLevel, roleId, structureId, model.DelegationId);
                var viewerFileData = await ManageAttachment.DownloadFromViewerWithoutWaterMark(item.AttachmentId ?? 0, model.TransferId, delegatedUserId ?? userId, structureIds, isStructureReceiver, privacyLevel, model.DelegationId, false, true,usertoken:usertoken);
                if (file != null)
                {
                    file.Extension = Intalio.Core.Helper.GetFileExtension(file.Name);
                    file.Name = file.Name.Replace("." + file.Extension, "");
                    ExceptionLogger.LogException("exportedDocumentId : " + model?.ExportedDocumentId?.ToString(), level: LoggingLevel.Info);
                    ExceptionLogger.LogException("item.attachmentId : " + item?.AttachmentId?.ToString(), level: LoggingLevel.Info);
                    ExceptionLogger.LogException("viewerFileData Length : " + (viewerFileData != null ? (((byte[])viewerFileData?.GetType().GetProperty("Data").GetValue(viewerFileData)).Length) : "Null"), level: LoggingLevel.Info);
                    file.Data = model.ExportedDocumentId == null ? await ManageAttachment.GetAttachmentData(item.AttachmentId ?? 0, model.CopyOptionsModal.SpecificVersionId, item.Id, model.TransferId, delegatedUserId ?? userId, structureIds, isStructureReceiver, privacyLevel, model.DelegationId)
                        : (byte[])viewerFileData.GetType().GetProperty("Data").GetValue(viewerFileData);
                }
                item.AttachmentId = null;
             
            }
            var structureIdsForProvision = new List<long>();
            structureIdsForProvision.Add(model.CreatedByStructureId);
            ManageStructure.Provision(structureIdsForProvision);

            try
            {
                item.Id = 0;
                item.Insert();

                if (linkeddocuments.Count() > 0)
                {
                    if (model.ExportedDocumentId == null)
                    {
                        ManageLinkedDocument.Create(delegatedUserId ?? userId, linkeddocuments.Select(x => x.LinkedDocumentId).ToList(), item.Id, null, model.DelegationId, structureIds, isStructureReceiver, privacyLevel);
                    }
                    else
                    {
                        var linkedDocsFolder = new Folder();
                        if (ManageFolder.CheckUnique(null, null, TranslationUtility.Translate("ExportLinkedDocuments", language), item.Id))
                        {
                            linkedDocsFolder = ManageFolder.Find(TranslationUtility.Translate("ExportLinkedDocuments", language), null, item.Id);
                        }
                        else
                        {
                            linkedDocsFolder.Name = TranslationUtility.Translate("ExportLinkedDocuments", language);
                            linkedDocsFolder.PhysicalName = "Linked Documents";
                            linkedDocsFolder.DocumentId = item.Id;

                            linkedDocsFolder.Insert();
                        }
                        var subjectCounter = new Dictionary<string, int>();
                        List<Model.TreeNode> linkedAttachments = linkeddocuments.Where(x => x.LinkedDocumentNavigation.AttachmentId != null).Select(x =>
                        {
                            var originalSubject = CleanDocumentSubject(x.LinkedDocumentNavigation.Subject, 150);
                            var subject = originalSubject;

                            if (subjectCounter.ContainsKey(originalSubject))
                            {
                                subjectCounter[originalSubject]++;
                                subject = $"{originalSubject} ({subjectCounter[originalSubject]})";
                            }
                            else
                            {
                                subjectCounter[originalSubject] = 1;
                            }
                            return new Model.TreeNode
                            {
                                Id = "file_" + x.LinkedDocumentNavigation.AttachmentId,
                                Text = subject,
                                Type = ((int)NodeType.File).ToString(),
                            };
                        }).ToList();
                        await UploadCopyAttachments(linkedAttachments, item.Id, model.TransferId, language, delegatedUserId ?? userId, model.DelegationId, model.CategoryId, structureIds, isStructureReceiver, privacyLevel, linkedDocsFolder.Id, fromExport: true, fromExportLinked:true) ;
                    }
                }
                if (nonArchivedAttachments.Count() > 0)
                {
                    foreach (var archived in nonArchivedAttachments)
                    {
                        var archivedModel = new NonArchivedAttachmentsViewModel()
                        {
                            Description = archived.Description,
                            DocumentId = item.Id,
                            Quantity = archived.Quantity,
                            TransferId = archived.TransferId,
                            Id = archived.Id,
                            TypeId = archived.TypeId,
                        };
                        ManageNonArchivedAttachments.Create(archivedModel, userId, structureIds, isStructureReceiver, privacyLevel, delegatedUserId, language);

                    }
                }
                if (!file.IsNull() && !file.Data.IsNullOrEmpty())
                {
                    var attachmentId = await ManageAttachment.Upload(item.Id, null, null, model.CategoryId, file, userId, structureIds, isStructureReceiver, privacyLevel, fromExport: true);
                    if (attachmentId.IsNull())
                    {
                        item.Delete();
                        return retValue;
                    }
                    item.AttachmentId = attachmentId;
                    item.UpdateAttachmentIdWithoutModifiedDate();
                }
                //else
                //{
                //    throw new Exception("Cannot downlad original Mail");
                //}
                //if (model.CopyOptionsModal.WithPublicAttachmentsOnly)
                //{
                //    var documentAttachments = (await ManageAttachment.List(model.DocumentCopyId ?? 0, model.TransferId, delegatedUserId ?? userId, roleId, structureId, structureIds, isStructureReceiver, privacyLevel, model.DelegationId, null, language)).Where(x => x.Id != "folder_originalMail").ToList();
                //    var nonPublicAttachments = documentAttachments.Where(x => x.SecurityCount > 0).ToList();
                //    if (nonPublicAttachments.Count > 0)
                //    {
                //        foreach (var attachment in nonPublicAttachments)
                //        {
                //            documentAttachments.Remove(attachment);
                //        }
                //    }
                //    UploadCopyAttachments(documentAttachments, item.Id, model.TransferId, language, delegatedUserId ?? userId, model.DelegationId, model.CategoryId, structureIds, isStructureReceiver, privacyLevel);
                //}
                if (model.CopyOptionsModal.WithAttachment)
                {
                    List<Model.TreeNode> documentAttachments = new List<Model.TreeNode>();
                    if (fromExport)
                    {
                        documentAttachments = await ManageAttachment.ListForExport(model.DocumentCopyId ?? 0, model.TransferId, delegatedUserId ?? userId, roleId, structureId, structureIds, isStructureReceiver, privacyLevel, model.DelegationId, null, language);
                        var attachmentsFolder = new Folder();
                        if (ManageFolder.CheckUnique(null, null, TranslationUtility.Translate("Attachments", language), item.Id))
                        {
                            attachmentsFolder = ManageFolder.Find(TranslationUtility.Translate("Attachments", language), null, item.Id);
                        }
                        else
                        {
                            attachmentsFolder.Name = TranslationUtility.Translate("Attachments", language);
                            attachmentsFolder.PhysicalName = "Attachments";
                            attachmentsFolder.DocumentId = item.Id;

                            attachmentsFolder.Insert();
                        }
                        await UploadCopyAttachments(documentAttachments, item.Id, model.TransferId, language, delegatedUserId ?? userId, model.DelegationId, model.CategoryId, structureIds, isStructureReceiver, privacyLevel, attachmentsFolder.Id, fromExport: model.ExportedDocumentId != null);
                    }
                    else
                    {
                        documentAttachments = (await ManageAttachment.List(model.DocumentCopyId ?? 0, model.TransferId, delegatedUserId ?? userId, roleId, structureId, structureIds, isStructureReceiver, privacyLevel, model.DelegationId, null, language)).Where(x => x.Id != "folder_originalMail").ToList();
                        await UploadCopyAttachments(documentAttachments, item.Id, model.TransferId, language, delegatedUserId ?? userId, model.DelegationId, model.CategoryId, structureIds, isStructureReceiver, privacyLevel, fromExport: model.ExportedDocumentId != null);
                    }
                }
                else
                {
                    var attachmentFolders = ManageAttachmentFolder.ListByCategoryId(model.CategoryId);
                    foreach (var attachmentFolder in attachmentFolders)
                    {
                        var folder = new Folder
                        {
                            Name = attachmentFolder.Name,
                            PhysicalName = attachmentFolder.Name,
                            ParentId = attachmentFolder.ParentId,
                            DocumentId = item.Id,
                        };
                        folder.Insert();
                        var childrenFolders = attachmentFolders.Where(x => x.ParentId == attachmentFolder.Id);
                        if (childrenFolders.Any())
                        {
                            foreach (var childrenFolder in childrenFolders)
                            {
                                childrenFolder.ParentId = (int)folder.Id;
                            }
                        }
                    }

                }
                if (activityLogAction != null)
                {


                    var documentLogsIds = new ActivityLog().GetByDocumentId(item.Id).Select(l => l.Id).ToList();
                    new ActivityLog().Delete(documentLogsIds);
                    var newDocumentValue = JsonConvert.SerializeObject(new DocumentModel
                    {
                        Subject = item.Subject,
                        FormData = item.DocumentForm != null ? item.DocumentForm.Form : string.Empty,
                        Classification = item.Classification != default(Classification) ? language == Language.AR ? item.Classification.NameAr : language == Language.FR ? item.Classification.NameFr : item.Classification.Name : String.Empty,
                        DocumentType = item.DocumentType != default(DocumentType) ? language == Language.AR ? item.DocumentType.NameAr : language == Language.FR ? item.DocumentType.NameFr : item.DocumentType.Name : String.Empty,
                        DueDate = item.DueDate.HasValue ? item.DueDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty,
                        DocumentDate = item.DocumentDate.HasValue ? item.DocumentDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty,
                        Importance = item.Importance != default(Importance) ? language == Language.AR ? item.Importance.NameAr : language == Language.FR ? item.Importance.NameFr : item.Importance.Name : String.Empty,
                        Priority = item.Priority != default(Priority) ? language == Language.AR ? item.Priority.NameAr : language == Language.FR ? item.Priority.NameFr : item.Priority.Name : String.Empty,
                        Privacy = item.Privacy != default(Privacy) ? language == Language.AR ? item.Privacy.NameAr : language == Language.FR ? item.Privacy.NameFr : item.Privacy.Name : String.Empty,
                        SendingEntity = item.SendingEntity != default(Structure) ? language == Language.AR ? !string.IsNullOrEmpty(item.SendingEntity.NameAr) ? item.SendingEntity.NameAr : item.SendingEntity.Name
                                           : language == Language.FR ? !string.IsNullOrEmpty(item.SendingEntity.NameFr) ? item.SendingEntity.NameFr : item.SendingEntity.Name
                                           : item.SendingEntity.Name : String.Empty,
                        Receivers = new List<string>(),
                        ExternalReferenceNumber = item.ExternalReferenceNumber,
                        Keyword = item.DocumentForm != null ? item.DocumentForm.Keyword : string.Empty,
                        Body = item.DocumentForm != null ? item.DocumentForm.Body : string.Empty,
                        CarbonCopies = item.DocumentCarbonCopy.Any() ? item.DocumentCarbonCopy.Where(x => x.Structure != default(Structure))
                                       .Select(x => language == Language.AR ? !String.IsNullOrEmpty(x.Structure.NameAr) ? x.Structure.NameAr : x.Structure.Name
                                           : language == Language.FR ? !String.IsNullOrEmpty(x.Structure.NameFr) ? x.Structure.NameFr : x.Structure.Name
                                           : x.Structure.Name).ToList() : new List<string>()
                    });
                    if (delegatedUserId != null)
                    {
                        ManageActivityLog.AddActivityLog(item.Id, null, (int)activityLogAction, userId, "", newDocumentValue, activityLogNote + " " + CopiedTitle);
                    }
                    else
                    {
                        ManageActivityLog.AddActivityLog(item.Id, null, (int)activityLogAction, userId, "", newDocumentValue, activityLogNote + " " + CopiedTitle);
                    }
                }
                model.Id = item.Id;
                retValue = (true,"");
                new EventReceivers().OnDocumentCreated(model, file);
                return retValue;

            }
            catch (Exception ex)
            {
                await Delete(userId, structureIds[0], new List<long> { item.Id });
                throw new Exception("Cannot Copy the correspondence", ex);
            }
        }

        public static byte[] ConvertPdfBytesToFormat(object viewerfile, string targetExtension)
        {
            //  Get byte[] from the object
            byte[] fileBytes = (byte[])viewerfile.GetType().GetProperty("Data").GetValue(viewerfile);
            if (fileBytes == null || fileBytes.Length == 0)
                throw new ArgumentException("viewerfile.Data is null or empty");

            AsposeLicense asposeLicense = new AsposeLicense();
            Stream licenseStream = asposeLicense.Get();
            if (licenseStream != null)
            {
                License license = new License();
                license.SetLicense(licenseStream);
            }
            else
            {
                throw new ArgumentException("Aspose license is not available.");
            }
            // Load PDF from memory

            using (MemoryStream inputStream = new MemoryStream(fileBytes))
            {
                Aspose.Pdf.Document pdfDocument = new Aspose.Pdf.Document(inputStream);
                if (pdfDocument == null || pdfDocument.Pages.Count == 0)
                    throw new InvalidDataException("Invalid or empty PDF document");

                switch (targetExtension?.Trim().ToLower())
                {
                    case "doc":
                    case "docx":
                        return SavePdfToMemory(pdfDocument, SaveFormat.DocX);
                        

                    case "xls":
                    case "xlsx":
                        return SavePdfToMemory(pdfDocument, SaveFormat.Excel);

                    case "pptx":
                        return SavePdfToMemory(pdfDocument, SaveFormat.Pptx);

                    case "html":
                        return SavePdfToMemory(pdfDocument, SaveFormat.Html);

                    case "jpeg":
                    case "jpg":
                    case "png":

                        Page page = pdfDocument.Pages[1];
                        double pdfWidth = page.PageInfo.Width;
                        double pdfHeight = page.PageInfo.Height;

                        // Convert to pixels at 96 DPI (common screen resolution)
                        int pixelWidth = (int)(pdfWidth * 96 / 72);
                        int pixelHeight = (int)(pdfHeight * 96 / 72);
                        // For image formats, convert first page only
                        return ConvertPdfPageToImageBytes(pdfDocument, 1, targetExtension.ToLower(), pixelWidth, pixelHeight);

                    default:
                        throw new NotSupportedException($"Extension '{targetExtension}' is not supported.");
                }
            }

        }
        // Helper to save to formats like DOCX, XLSX, PPTX, HTML
        private static byte[] SavePdfToMemory(Aspose.Pdf.Document pdfDocument, SaveFormat format)
        {
            using (MemoryStream outputStream = new MemoryStream())
            {
                pdfDocument.Save(outputStream, format);
                return outputStream.ToArray();
            }
        }
        private static byte[] ConvertPdfPageToImageBytes(Aspose.Pdf.Document pdfDocument, int pageNumber, string imageFormat ,int width, int height)
        {
            using (MemoryStream imageStream = new MemoryStream())
            {
                // Declare device outside the switch
                ImageDevice device;

                switch (imageFormat.ToLower())
                {
                    case "jpg":
                    case "jpeg":
                        device = new JpegDevice(width, height);
                        break;

                    case "png":
                        device = new PngDevice(width, height);
                        break;

                    default:
                        throw new NotSupportedException("Only jpg and png formats are supported.");
                }

                // Process the page and write to memory stream
                device.Process(pdfDocument.Pages[pageNumber], imageStream);

                return imageStream.ToArray();
            }
        }
        public static string CleanFileName(string name)
        {
            
            var cleaned = Regex.Replace(name, @"^[\.\-_]+", ""); 

            return cleaned;
        }

        public static string CleanDocumentSubject(string originalName, int maxLength = 200)
        {

            string safeName = Regex.Replace(originalName, @"[\r\n\t\\\""',;:/?<>|*]", " ").Trim();

            safeName = Regex.Replace(safeName, @"\s+", " ");

            safeName = Regex.Replace(safeName, @"^[\.\-_]+", "");

            if (safeName.Length > maxLength)
                safeName = safeName.Substring(0, maxLength);

            return safeName;

        }

        private static async Task<bool> UploadCopyAttachments(List<Model.TreeNode> documentAttachments, long DocumentId, long? transferId, Language lang, long userId, long? delegationId, short CategoryId, List<long> StructureIds, bool isStructureReceiver, short privacyLevel, long? folderId = null, bool fromExport = false, bool fromExportLinked = false)
        {
            var retValue = false;
            foreach (var attachment in documentAttachments)
            {
                if (attachment.Type == ((int)NodeType.Folder).ToString())
                {
                    await UploadCopyAttachments(((IEnumerable<Model.TreeNode>)attachment.Children).ToList(), DocumentId, transferId, lang, userId, delegationId, CategoryId, StructureIds, isStructureReceiver, privacyLevel, AddFolder(attachment.Text, folderId, lang, userId, StructureIds, isStructureReceiver, privacyLevel, DocumentId), fromExport: fromExport);
                }
                else if (attachment.Type == ((int)NodeType.File).ToString())
                {
                    if (!fromExport || (fromExport && !(attachment.SecurityCount > 0)))
                    {
                        var attachmentId = Convert.ToInt64(attachment.Id.Split('_')[1]);
                        var StorageAttachmentId = new Attachment().Find(attachmentId).StorageAttachmentId;
                        var viewerfile = await ManageAttachment.DownloadFromViewerWithoutWaterMark(attachmentId, transferId, userId, StructureIds, isStructureReceiver, privacyLevel, delegationId, false, true);
                        var Storagefile = await ManageAttachment.GetStorageAttachmentModel(StorageAttachmentId);
                        FileViewModel file = new FileViewModel();
                        if (Storagefile?.Data?.Length > 0/*&& viewerfile != null*/ )
                        {
                            var originalName = Path.GetFileNameWithoutExtension(Intalio.Core.Helper.GetFileName(Storagefile.Name));
                            var cleanBaseName = Path.GetFileNameWithoutExtension(CleanFileName(attachment.Text));
                            cleanBaseName = cleanBaseName == "" ?  originalName : cleanBaseName;
                            file.Name = fromExportLinked ?  cleanBaseName: originalName;
                            file.FileSize = Convert.ToInt64(Storagefile.FileSize);
                            file.ContentType = Storagefile.ContentType;
                            file.Extension = Storagefile.Extension;
                            //file.Data = viewerfile != null ? (byte[])viewerfile.GetType().GetProperty("Data").GetValue(viewerfile) : Storagefile.Data;/*(byte[])viewerfile.GetType().GetProperty("Data").GetValue(viewerfile);*/
                            file.Data = Storagefile.Extension == "pdf" ? (byte[])viewerfile.GetType().GetProperty("Data").GetValue(viewerfile) : ConvertPdfBytesToFormat(viewerfile, Storagefile.Extension);
                        }
                        var newAttchmentId = await ManageAttachment.UploadAttachment(DocumentId, null, folderId, CategoryId, file, userId, StructureIds, isStructureReceiver, privacyLevel, delegationId);
                        if (newAttchmentId == null)
                        {
                            throw new Exception("Error in Uploading An Attachment " + Storagefile.Name);
                        }
                    }
                }
            }
            retValue = true;
            return retValue;
        }
        
        private static long? AddFolder(string Name, long? ParentId, Language lang, long userId, List<long> StructureIds, bool isStructureReceiver, short privacyLevel, long DocumentId)
        {
            var Foldermodel = new AttachmentNodeModel
            {
                Name = Name,
                ParentId = ParentId,
                DocumentId = DocumentId,
            };
            ManageFolder.Create(Foldermodel, lang, userId, StructureIds, isStructureReceiver, privacyLevel);
            return Foldermodel.Id;
        }

        /// <summary>
        /// Edit document
        /// </summary>
        /// <param name="model"></param>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="transferId"></param>
        /// <param name="StructureId"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        /// 

        public static async Task<(bool Edited, string ReferenceNumber, string Message)> Edit(DocumentViewModel model, long userId, long structureId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, bool allowEditSigned = false, long? transferId = null, Language language = Language.EN, bool fromExport = false)
        {
            bool retValue = false;
            var reference = string.Empty;
            string originalDocumentValue = string.Empty;
            string followUpModel = string.Empty;
            var structureIdsForProvision = new List<long>();
            if (model.Id != null)
            {

                if (model.CategoryId == Configuration.FollowUpCategory && !model.FollowUpId.HasValue)
                {
                    return (true, string.Empty, "InvalidFollowUp");
                }

                Document item = !transferId.HasValue ? await new Document().FindIncludeDocumentFormAsync(model.Id.Value) : await new Document().FindIncludeDocumentMetadataAsync(model.Id.Value);
                if (item.AttachmentId.HasValue)
                {
                    var attachment = new Attachment().Find(item.AttachmentId.Value);
                    var storageAttachmentModel = await ManageAttachment.GetStorageAttachmentModel(attachment.StorageAttachmentId);
                    bool hasSignature;
                    if (storageAttachmentModel.Data != null && (storageAttachmentModel.Extension == "doc" || storageAttachmentModel.Extension == "docx"))
                    {
                        hasSignature = ManageTemplate.CheckFileHasSignatureImage(storageAttachmentModel.Data, "signature");
                        item.TemplateHasSignature = hasSignature;
                    }
                }

                FollowUp followUpItem = model.FollowUpId.HasValue ? await new FollowUp().FindWithIncludeAsync(model.FollowUpId.Value) : null;
                if (item == null)
                {
                    return (retValue, reference, string.Empty);
                }
                if ((item.CreatedByUserId == userId && item.StatusId == (short)DocumentStatus.Draft) || transferId.HasValue ||
                    (item.CreatedByStructureId == structureId && item.StatusId == (short)DocumentStatus.Draft)  ||
                    (model.CategoryId == Configuration.FollowUpCategory && (followUpItem.FollowUpStatusId == (short)FollowUpStatus.InProgress || followUpItem.FollowUpStatusId == (short)FollowUpStatus.Postponed)))
                {
                    if (transferId.HasValue && transferId > 0)
                    {
                        var transfer = await new Transfer().FindByDocumentIdAsync(transferId.Value, model.Id.Value);
                        if (transfer != null && transfer.ClosedDate.IsNull() &&
                            (item.CreatedByUserId == userId || transfer.ToUserId == userId || transfer.OwnerUserId == userId || transfer.OwnerDelegatedUserId == userId) || (model.FromRejectedDocument == true && transfer.FromStructureId == structureId))
                        {
                            if (!HasDocumentEdit(item, allowEditSigned))
                            {
                                //check is enable attribute edit and not signed
                                return (true, string.Empty, "MustBeAllowEditAttributeAndNotSigned");
                            }
                            List<string> receivingEntities = new List<string>();
                            foreach (var receiver in item.DocumentReceiverEntity)
                            {
                                string text = string.Empty;
                                if (receiver.EntityGroupId.HasValue)
                                {
                                    text = language == Language.AR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameAr) ? receiver.EntityGroup.NameAr : receiver.EntityGroup.Name
                                    : language == Language.FR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameFr) ? receiver.EntityGroup.NameFr : receiver.EntityGroup.Name
                                    : receiver.EntityGroup.Name;
                                }
                                else
                                {
                                    text = language == Language.AR ? !string.IsNullOrEmpty(receiver.Structure.NameAr) ? receiver.Structure.NameAr : receiver.Structure.Name
                                   : language == Language.FR ? !string.IsNullOrEmpty(receiver.Structure.NameFr) ? receiver.Structure.NameFr : receiver.Structure.Name
                                   : receiver.Structure.Name;
                                }
                                receivingEntities.Add(text);
                            }
                            originalDocumentValue = JsonConvert.SerializeObject(new DocumentModel
                            {
                                Subject = item.Subject,
                                FormData = item.DocumentForm != null ? item.DocumentForm.Form : string.Empty,
                                Classification = item.Classification != default(Classification) ? language == Language.AR ? item.Classification.NameAr : language == Language.FR ? item.Classification.NameFr : item.Classification.Name : String.Empty,
                                DocumentType = item.DocumentType != default(DocumentType) ? language == Language.AR ? item.DocumentType.NameAr : language == Language.FR ? item.DocumentType.NameFr : item.DocumentType.Name : String.Empty,
                                DueDate = item.DueDate.HasValue ? item.DueDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty,
                                Importance = item.Importance != default(Importance) ? language == Language.AR ? item.Importance.NameAr : language == Language.FR ? item.Importance.NameFr : item.Importance.Name : String.Empty,
                                Priority = item.Priority != default(Priority) ? language == Language.AR ? item.Priority.NameAr : language == Language.FR ? item.Priority.NameFr : item.Priority.Name : String.Empty,
                                Privacy = item.Privacy != default(Privacy) ? language == Language.AR ? item.Privacy.NameAr : language == Language.FR ? item.Privacy.NameFr : item.Privacy.Name : String.Empty,
                                SendingEntity = item.SendingEntity != default(Structure) ? language == Language.AR ? !string.IsNullOrEmpty(item.SendingEntity.NameAr) ? item.SendingEntity.NameAr : item.SendingEntity.Name
                                                    : language == Language.FR ? !string.IsNullOrEmpty(item.SendingEntity.NameFr) ? item.SendingEntity.NameFr : item.SendingEntity.Name
                                                    : item.SendingEntity.Name : String.Empty,
                                Receivers = receivingEntities,
                                ExternalReferenceNumber = item.ExternalReferenceNumber,
                                Keyword = item.DocumentForm != null ? item.DocumentForm.Keyword : string.Empty,
                                Body = item.DocumentForm != null ? item.DocumentForm.Body : string.Empty,
                                CarbonCopies = item.DocumentCarbonCopy.Any() ? item.DocumentCarbonCopy.Where(x => x.Structure != default(Structure))
                                                .Select(x => language == Language.AR ? !String.IsNullOrEmpty(x.Structure.NameAr) ? x.Structure.NameAr : x.Structure.Name
                                                    : language == Language.FR ? !String.IsNullOrEmpty(x.Structure.NameFr) ? x.Structure.NameFr : x.Structure.Name
                                                    : x.Structure.Name).ToList() : new List<string>()
                            });
                        }
                        else
                        {
                            return (true, string.Empty, "MustBeAllowEditAttributeAndNotSigned");
                        }
                    }
                    item.ExternalReferenceNumber = string.IsNullOrEmpty(model.ExternalReferenceNumber) ? string.Empty : model.ExternalReferenceNumber;
                    item.SendingEntityId = model.SendingEntityId;
                    item.SenderPerson = model.Sender == null ? 0 : model.Sender;
                    item.ReceiverPerson = string.IsNullOrEmpty(model.Receiver) ? string.Empty : model.Receiver;
                    item.IsExternalReceiver = model.IsExternalReceiver;
                    item.IsExternalSender = model.IsExternalSender;
                    
                    if (CheckOriginalDocumentLocked(model.Id.Value))
                    {
                        return (true, string.Empty, "OriginalFileInUse");
                    }
                    DateTime? dueDate = null;
                    if (!string.IsNullOrEmpty(model.DueDate))
                    {
                        dueDate = Convert.ToDateTime(model.DueDate);
                    }

                    item.PriorityId = model.PriorityId;
                    item.PrivacyId = model.PrivacyId;
                    item.Subject = string.IsNullOrEmpty(model.Subject) ? string.Empty : model.Subject;
                    item.ClassificationId = model.ClassificationId;
                    item.ImportanceId = model.ImportanceId;
                    item.DocumentTypeId = model.DocumentTypeId;
                    item.G2GDocumentId = model.G2GInternalId;
                    item.DueDate = dueDate;
                    item.CategoryId = model.CategoryId;
                    if (!transferId.HasValue && !(model.CategoryId == Configuration.FollowUpCategory))
                    {
                        item.CreatedByStructureId = model.CreatedByStructureId;
                    }
                    if (DateTime.TryParse(model.DocumentDate, out DateTime parsedDate))
                    {
                        item.DocumentDate = parsedDate;
                    }
                    else
                    {
                        item.DocumentDate = null;
                    }

                    if (!model.Receivers.IsNullOrEmpty())
                    {
                        structureIdsForProvision.AddRange(model.Receivers.Where(t => !t.IsEntityGroup).Select(t => t.Id).ToList());
                        var tobeDeleted = item.DocumentReceiverEntity.Where(t => !model.Receivers.Any(n => (n.Id == t.StructureId && !n.IsEntityGroup) || (n.Id == t.EntityGroupId && n.IsEntityGroup))).Select(t => t.Id).ToList();
                        if (!tobeDeleted.IsNullOrEmpty())
                        {
                            new DocumentReceiverEntity().Delete(tobeDeleted);
                            foreach (var removedItem in tobeDeleted)
                            {
                                var property = item.DocumentReceiverEntity.FirstOrDefault(t => t.Id == removedItem);
                                if (property != null)
                                {
                                    item.DocumentReceiverEntity.Remove(property);
                                }
                            }
                        }
                        model.Receivers.Where(t => !t.IsEntityGroup && !item.DocumentReceiverEntity.Any(n => n.StructureId == t.Id))
                            .Select(t => new DocumentReceiverEntity
                            {
                                StructureId = t.Id,
                            }).ToList().ForEach(t => item.DocumentReceiverEntity.Add(t));
                        model.Receivers.Where(t => t.IsEntityGroup && !item.DocumentReceiverEntity.Any(n => n.EntityGroupId == t.Id))
                           .Select(t => new DocumentReceiverEntity
                           {
                               EntityGroupId = t.Id,
                           }).ToList().ForEach(t => item.DocumentReceiverEntity.Add(t));
                    }
                    else
                    {
                        var tobeDeleted = item.DocumentReceiverEntity.Select(t => t.Id).ToList();
                        if (!tobeDeleted.IsNullOrEmpty())
                        {
                            new DocumentReceiverEntity().Delete(tobeDeleted);
                        }
                        foreach (var removedItem in tobeDeleted)
                        {
                            var property = item.DocumentReceiverEntity.FirstOrDefault(t => t.Id == removedItem);
                            if (property != null)
                            {
                                item.DocumentReceiverEntity.Remove(property);
                            }
                        }
                    }
                    if (!model.CarbonCopy.IsNullOrEmpty())
                    {
                        structureIdsForProvision.AddRange(model.CarbonCopy);
                        var tobeDeleted = item.DocumentCarbonCopy.Where(t => !model.CarbonCopy.Any(n => n == t.StructureId)).Select(t => t.Id).ToList();
                        if (!tobeDeleted.IsNullOrEmpty())
                        {
                            new DocumentCarbonCopy().Delete(tobeDeleted);
                            foreach (var removedItem in tobeDeleted)
                            {
                                var property = item.DocumentCarbonCopy.FirstOrDefault(t => t.Id == removedItem);
                                if (property != null)
                                {
                                    item.DocumentCarbonCopy.Remove(property);
                                }
                            }
                        }
                        model.CarbonCopy.Where(t => !item.DocumentCarbonCopy.Any(n => n.StructureId == t))
                            .Select(t => new DocumentCarbonCopy
                            {
                                StructureId = t
                            }).ToList().ForEach(t => item.DocumentCarbonCopy.Add(t));
                    }
                    else
                    {
                        var tobeDeleted = item.DocumentCarbonCopy.Select(t => t.Id).ToList();
                        if (!tobeDeleted.IsNullOrEmpty())
                        {
                            new DocumentCarbonCopy().Delete(tobeDeleted);
                        }
                        foreach (var removedItem in tobeDeleted)
                        {
                            var property = item.DocumentCarbonCopy.FirstOrDefault(t => t.Id == removedItem);
                            if (property != null)
                            {
                                item.DocumentCarbonCopy.Remove(property);
                            }
                        }
                    }
                    if (model.SendingEntityId.HasValue)
                    {
                        structureIdsForProvision.Add(model.SendingEntityId.Value);
                    }
                    structureIdsForProvision.Add(model.CreatedByStructureId);
                    if (structureIdsForProvision.Count > 0)
                    {
                        structureIdsForProvision = structureIdsForProvision.Distinct().ToList();
                        ManageStructure.Provision(structureIdsForProvision);
                    }
                    item.DocumentForm = new DocumentForm
                    {
                        Id = item.Id,
                        Form = model.FormData,
                        Body = string.IsNullOrEmpty(model.Body) ? string.Empty : model.Body,
                        Keyword = string.IsNullOrEmpty(model.Keyword) ? string.Empty : model.Keyword
                    };
                    if (followUpItem != null)
                    {
                        var allFollowUpUsers = ManageFollowUp.GetFollowUpUsers(followUpItem.Id);

                        var customAttributes = DocumentCrawlingModel.DeserializeToDictionary(model.FormData);
                        long? teamId = customAttributes.Any(a => a.Key.Equals("team")) && customAttributes.FirstOrDefault(a => a.Key.Equals("team")).Value.ToString() != string.Empty ? (long)customAttributes.FirstOrDefault(a => a.Key.Equals("team")).Value : null;
                        bool? isPrivate = customAttributes.Any(a => a.Key.Equals("isPrivate")) && customAttributes.FirstOrDefault(a => a.Key.Equals("isPrivate")).Value.ToString() != string.Empty ? bool.Parse(customAttributes.FirstOrDefault(a => a.Key.Equals("isPrivate")).Value.ToString()) : false;

                        teamId = teamId == 0 ? null : teamId;
                        if ((teamId == null || teamId == 0) && (isPrivate == null || !isPrivate.Value))
                        {
                            return (true, string.Empty, "FollowUpTeamEmptyAndNotPrivate");
                        }
                        if (string.IsNullOrEmpty(model.Subject))
                        {
                            return  (true, string.Empty, "InvalidSubject");
                        }

                        followUpItem.FollowUpToDate = dueDate.HasValue ? (DateTime)dueDate : followUpItem.FollowUpToDate;
                        followUpItem.IsPrivate = isPrivate;
                        if (teamId.HasValue)
                        {

                            if (followUpItem.TeamId == null || followUpItem.TeamId != teamId)
                            {

                                if (followUpItem.TeamId != default)
                                {
                                    var existingTeamUsers = ManageTeamUsers.GetTeamUsersByTeam(followUpItem.TeamId.Value);
                                    var deletedFollowUpUsers = allFollowUpUsers
                                        .Where(s => existingTeamUsers.Any(t => t.UserId == s.UserId && t.StructureId == s.StructureId && t.UserId != userId))
                                        .ToList();

                                    if (deletedFollowUpUsers.Any())
                                    {
                                        new FollowUpUsers().Delete(deletedFollowUpUsers.Select(i => i.Id).ToList());
                                        allFollowUpUsers = allFollowUpUsers.Where(s => !deletedFollowUpUsers.Contains(s)).ToList();
                                    }

                                }

                                var newTeamUsers = ManageTeamUsers.GetTeamUsersByTeam(teamId.Value);

                                if (newTeamUsers.Any())
                                {
                                    var newFollowUpUsers = newTeamUsers
                                        .Where(x => !allFollowUpUsers.Any(s => s.UserId == x.UserId && s.StructureId == x.StructureId))
                                        .Select(user => new FollowUpUsers
                                        {
                                            UserId = (long)user.UserId,
                                            StructureId = user.StructureId,
                                            FollowUpSecurityId = user.UserId != userId ? (short)FollowUpRoles.Editor : (short)FollowUpRoles.Owner,
                                            FollowUpId = followUpItem.Id,
                                            CreatedByUserId = userId,
                                            CreatedDate = DateTime.Now
                                        })
                                        .ToList();

                                    if (newFollowUpUsers.Any())
                                    {
                                        followUpItem.FollowUpUsers = newFollowUpUsers;
                                    }
                                }
                            }
                        }
                        followUpItem.TeamId = teamId;
                        followUpItem.Update(userId);

                        followUpModel = JsonConvert.SerializeObject(new {
                            IsPrivate = followUpItem.IsPrivate,
                            Subject = item.Subject,
                            PriorityId = item.PriorityId,

                        });

                    }

                    item.DraftStatus = (short)DraftStatus.Pending;
                    item.ModifiedDate = DateTime.Now;
                    item.UpdateIncludeDocumentForm();
                    new EventReceivers().OnDocumentSaved(model);

                    item = await new Document().FindIncludeDocumentMetadataAsync(model.Id.Value);
                    List<string> receivingEntitieslog = new List<string>();
                    foreach (var receiver in item.DocumentReceiverEntity)
                    {
                        string text = string.Empty;
                        if (receiver.EntityGroupId.HasValue)
                        {
                            text = language == Language.AR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameAr) ? receiver.EntityGroup.NameAr : receiver.EntityGroup.Name
                            : language == Language.FR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameFr) ? receiver.EntityGroup.NameFr : receiver.EntityGroup.Name
                            : receiver.EntityGroup.Name;
                        }
                        else
                        {
                            text = language == Language.AR ? !string.IsNullOrEmpty(receiver.Structure.NameAr) ? receiver.Structure.NameAr : receiver.Structure.Name
                           : language == Language.FR ? !string.IsNullOrEmpty(receiver.Structure.NameFr) ? receiver.Structure.NameFr : receiver.Structure.Name
                           : receiver.Structure.Name;
                        }
                        receivingEntitieslog.Add(text);
                    }

                    var newDocumentValue = JsonConvert.SerializeObject(new DocumentModel
                    {
                        Subject = item.Subject,
                        FormData = item.DocumentForm != null ? item.DocumentForm.Form : string.Empty,
                        Classification = item.Classification != default(Classification) ? language == Language.AR ? item.Classification.NameAr : language == Language.FR ? item.Classification.NameFr : item.Classification.Name : String.Empty,
                        DocumentType = item.DocumentType != default(DocumentType) ? language == Language.AR ? item.DocumentType.NameAr : language == Language.FR ? item.DocumentType.NameFr : item.DocumentType.Name : String.Empty,
                        DueDate = item.DueDate.HasValue ? item.DueDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty,
                        Importance = item.Importance != default(Importance) ? language == Language.AR ? item.Importance.NameAr : language == Language.FR ? item.Importance.NameFr : item.Importance.Name : String.Empty,
                        Priority = item.Priority != default(Priority) ? language == Language.AR ? item.Priority.NameAr : language == Language.FR ? item.Priority.NameFr : item.Priority.Name : String.Empty,
                        Privacy = item.Privacy != default(Privacy) ? language == Language.AR ? item.Privacy.NameAr : language == Language.FR ? item.Privacy.NameFr : item.Privacy.Name : String.Empty,
                        SendingEntity = item.SendingEntity != default(Structure) ? language == Language.AR ? !string.IsNullOrEmpty(item.SendingEntity.NameAr) ? item.SendingEntity.NameAr : item.SendingEntity.Name
                                                : language == Language.FR ? !string.IsNullOrEmpty(item.SendingEntity.NameFr) ? item.SendingEntity.NameFr : item.SendingEntity.Name
                                                : item.SendingEntity.Name : String.Empty,
                        Receivers = receivingEntitieslog,
                        ExternalReferenceNumber = item.ExternalReferenceNumber,
                        Keyword = item.DocumentForm != null ? item.DocumentForm.Keyword : string.Empty,
                        Body = item.DocumentForm != null ? item.DocumentForm.Body : string.Empty,
                        CarbonCopies = item.DocumentCarbonCopy.Any() ? item.DocumentCarbonCopy.Where(x => x.Structure != default(Structure))
                                            .Select(x => language == Language.AR ? !String.IsNullOrEmpty(x.Structure.NameAr) ? x.Structure.NameAr : x.Structure.Name
                                                : language == Language.FR ? !String.IsNullOrEmpty(x.Structure.NameFr) ? x.Structure.NameFr : x.Structure.Name
                                                : x.Structure.Name).ToList() : new List<string>()
                    });




                    if (transferId.HasValue && transferId > 0)
                    {
                        ManageActivityLog.AddActivityLog(item.Id, transferId.HasValue ? transferId.Value : null, (int)Core.ActivityLogs.Save, userId,
                        originalDocumentValue, newDocumentValue);
                    }
                    else if (model.FollowUpId.HasValue)
                    {
                        ManageActivityLog.AddActivityLog(item.Id, null, (int)Core.ActivityLogs.EditFollowup, userId,
                           originalDocumentValue, newDocumentValue);
                    }
                    else if (item.StatusId == (short)DocumentStatus.Draft && !fromExport)
                    {
                        ManageActivityLog.AddActivityLog(item.Id, transferId.HasValue ? transferId.Value : null, (int)Core.ActivityLogs.Save, userId,
                        originalDocumentValue, newDocumentValue);
                    }



                    retValue = true;
                    if (model.Register && string.IsNullOrEmpty(item.ReferenceNumber))
                    {
                        var returnedResult = new CategoryReferenceCounter().Generate(item.CategoryId, userId, model.CreatedByStructureId, item.Id, transferId, language);
                        reference = returnedResult.Reference;
                        item.ReferenceNumber = returnedResult.Reference;
                        item.ReferenceSequence = returnedResult.Sequence;

                        bool barcodeGenerated = false;
                        if (!string.IsNullOrEmpty(item.ReferenceNumber))
                        {
                            try
                            {
                                var tryGenerateBarcode = ManageDocument.GenerateBarcode(item.Id, item.ReferenceNumber);
                                barcodeGenerated = true;
                            }
                            catch (Exception exp)
                            {
                                throw exp;
                            }

                        }
                        if (barcodeGenerated)
                        {
                        //to maintain reference sequence
                            item.UpdateReferenceNumber();
                            new EventReceivers().OnDocumentReferenceNumberGenerated(item.Id);
                        }
                    }
                    if (item.AttachmentId.HasValue && ManageCategory.CheckCategoryByFileOrTemplate(item.CategoryId))
                    {
                        Log.Information("ReplaceBookmark: Attachment is a word document and category is file or template category");
                        var attachment = new Attachment().Find(item.AttachmentId.Value);
                        if (attachment != default(Attachment) && Intalio.Core.Helper.IsWord($"{attachment.Name}.{attachment.Extension}"))
                        {
                            Log.Information("ReplaceBookmark: Attachment is a word document and category is file or template category, attachment id: {0}, storage attachment id: {1}, document id: {2}, user id: {3}, transfer id: {4}", attachment.Id, attachment.StorageAttachmentId, item.Id, userId, transferId);
                            await ManageBookmark.FindReplaceBookmarkAndAttachment(attachment.Id, attachment.StorageAttachmentId, item.Id, userId, transferId);
                        }
                        else
                        {
                            Log.Information("ReplaceBookmark: Attachment is not a word document or category is not a file or template category, attachment id: {0}, storage attachment id: {1}, document id: {2}, user id: {3}, transfer id: {4}", attachment.Id, attachment.StorageAttachmentId, item.Id, userId, transferId);
                        }
                    }
                    else
                    {
                        Log.Information("ReplaceBookmark: Attachment is not a word document or category is not a file or template category");
                    }
                }
            }
            return (retValue, reference, string.IsNullOrEmpty(followUpModel) ? string.Empty : followUpModel);
        }



        public static async Task<(bool Edited, string ReferenceNumber, string Message)> EditComplete(DocumentViewModel model, long userId, long RoleId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, bool allowEditSigned = false, long? transferId = null, Language language = Language.EN)
        {
            bool retValue = false;
            var reference = string.Empty;
            string originalDocumentValue = string.Empty;
            var structureIdsForProvision = new List<long>();
            if (model.Id != null)
            {
                Document item = !transferId.HasValue ? await new Document().FindIncludeDocumentFormAsync(model.Id.Value) : await new Document().FindIncludeDocumentMetadataAsync(model.Id.Value);
                Intalio.Core.DAL.Action Action = new Intalio.Core.DAL.Action();
                List<Intalio.Core.DAL.Action> actions = Action.List();
                bool haveaccess = false;

                var editaction = actions.Where(action => action.JsFunction == "CTSCoreComponents.CustomActions.openSearchDocumentEdit").FirstOrDefault();
                if (editaction == default)
                {
                    return (false, string.Empty, "actiondoesntexist");
                }

                if (Configuration.EnablePerStructure ? ManageUser.CheckBreakInheritanceInStructure(userId, structureIds[0]) : Intalio.Core.API.ManageUser.CheckBreakInheritance(userId))
                {
                    //haveaccess = true;
                    //check if edit action assigned to user
                    haveaccess = editaction.ActionSecurity.Where(asecurity => asecurity.UserId == userId).Count() > 0;
                }
                else
                {
                    haveaccess = editaction.ActionSecurity.Where(asecurity => asecurity.RoleId == RoleId).Count() > 0;
                }


                if (haveaccess)//if (item != null && ((item.CreatedByUserId == userId  || transferId.HasValue)))
                {
                    if (transferId.HasValue)
                    {

                        var transfer = await new Transfer().FindByDocumentIdAsync(transferId.Value, model.Id.Value);
                        //remove this condition and add to check role
                        //break inher button 
                        if (transfer != null &&
                            (item.CreatedByUserId == userId || transfer.ToUserId == userId || transfer.OwnerUserId == userId || transfer.OwnerDelegatedUserId == userId))
                        {
                            if (!HasDocumentEdit(item, allowEditSigned))
                            {
                                //check is enable attribute edit and not signed
                                return (true, string.Empty, "MustBeAllowEditAttributeAndNotSigned");
                            }
                            List<string> receivingEntities = new List<string>();
                            foreach (var receiver in item.DocumentReceiverEntity)
                            {
                                string text = string.Empty;
                                if (receiver.EntityGroupId.HasValue)
                                {
                                    text = language == Language.AR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameAr) ? receiver.EntityGroup.NameAr : receiver.EntityGroup.Name
                                    : language == Language.FR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameFr) ? receiver.EntityGroup.NameFr : receiver.EntityGroup.Name
                                    : receiver.EntityGroup.Name;
                                }
                                else
                                {
                                    text = language == Language.AR ? !string.IsNullOrEmpty(receiver.Structure.NameAr) ? receiver.Structure.NameAr : receiver.Structure.Name
                                   : language == Language.FR ? !string.IsNullOrEmpty(receiver.Structure.NameFr) ? receiver.Structure.NameFr : receiver.Structure.Name
                                   : receiver.Structure.Name;
                                }
                                receivingEntities.Add(text);
                            }
                            originalDocumentValue = JsonConvert.SerializeObject(new DocumentModel
                            {
                                Subject = item.Subject,
                                FormData = item.DocumentForm != null ? item.DocumentForm.Form : string.Empty,
                                Classification = item.Classification != default(Classification) ? language == Language.AR ? item.Classification.NameAr : language == Language.FR ? item.Classification.NameFr : item.Classification.Name : String.Empty,
                                DocumentType = item.DocumentType != default(DocumentType) ? language == Language.AR ? item.DocumentType.NameAr : language == Language.FR ? item.DocumentType.NameFr : item.DocumentType.Name : String.Empty,
                                DueDate = item.DueDate.HasValue ? item.DueDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty,
                                Importance = item.Importance != default(Importance) ? language == Language.AR ? item.Importance.NameAr : language == Language.FR ? item.Importance.NameFr : item.Importance.Name : String.Empty,
                                Priority = item.Priority != default(Priority) ? language == Language.AR ? item.Priority.NameAr : language == Language.FR ? item.Priority.NameFr : item.Priority.Name : String.Empty,
                                Privacy = item.Privacy != default(Privacy) ? language == Language.AR ? item.Privacy.NameAr : language == Language.FR ? item.Privacy.NameFr : item.Privacy.Name : String.Empty,
                                SendingEntity = item.SendingEntity != default(Structure) ? language == Language.AR ? !string.IsNullOrEmpty(item.SendingEntity.NameAr) ? item.SendingEntity.NameAr : item.SendingEntity.Name
                                                    : language == Language.FR ? !string.IsNullOrEmpty(item.SendingEntity.NameFr) ? item.SendingEntity.NameFr : item.SendingEntity.Name
                                                    : item.SendingEntity.Name : String.Empty,
                                Receivers = receivingEntities,

                                ExternalReferenceNumber = item.ExternalReferenceNumber,
                                Keyword = item.DocumentForm != null ? item.DocumentForm.Keyword : string.Empty,
                                Body = item.DocumentForm != null ? item.DocumentForm.Body : string.Empty,
                                CarbonCopies = item.DocumentCarbonCopy.Any() ? item.DocumentCarbonCopy.Where(x => x.Structure != default(Structure))
                                                .Select(x => language == Language.AR ? !String.IsNullOrEmpty(x.Structure.NameAr) ? x.Structure.NameAr : x.Structure.Name
                                                    : language == Language.FR ? !String.IsNullOrEmpty(x.Structure.NameFr) ? x.Structure.NameFr : x.Structure.Name
                                                    : x.Structure.Name).ToList() : new List<string>()
                            });
                        }
                        else
                        {
                            return (true, string.Empty, "MustBeAllowEditAttributeAndNotSigned");
                        }
                    }
                    item.ExternalReferenceNumber = string.IsNullOrEmpty(model.ExternalReferenceNumber) ? string.Empty : model.ExternalReferenceNumber;
                    item.SendingEntityId = model.SendingEntityId;
                    
                    if (CheckOriginalDocumentLocked(model.Id.Value))
                    {
                        return (true, string.Empty, "OriginalFileInUse");
                    }
                    DateTime? dueDate = null;
                    if (!string.IsNullOrEmpty(model.DueDate))
                    {
                        dueDate = Convert.ToDateTime(model.DueDate);
                    }
                    //zwdt hena
                    item.SenderPerson = model.Sender;
                    item.ReceiverPerson = model.Receiver;
                    //h7d hena
                    item.PriorityId = model.PriorityId;
                    item.PrivacyId = model.PrivacyId;
                    item.Subject = string.IsNullOrEmpty(model.Subject) ? string.Empty : model.Subject;
                    item.ClassificationId = model.ClassificationId;
                    item.ImportanceId = model.ImportanceId;
                    item.DocumentTypeId = model.DocumentTypeId;
                    item.DueDate = dueDate;
                    if (!transferId.HasValue)
                    {
                        item.CreatedByStructureId = model.CreatedByStructureId;
                    }
                    if (!model.Receivers.IsNullOrEmpty())
                    {
                        structureIdsForProvision.AddRange(model.Receivers.Where(t => !t.IsEntityGroup).Select(t => t.Id).ToList());
                        var tobeDeleted = item.DocumentReceiverEntity.Where(t => !model.Receivers.Any(n => (n.Id == t.StructureId && !n.IsEntityGroup) || (n.Id == t.EntityGroupId && n.IsEntityGroup))).Select(t => t.Id).ToList();
                        if (!tobeDeleted.IsNullOrEmpty())
                        {
                            new DocumentReceiverEntity().Delete(tobeDeleted);
                            foreach (var removedItem in tobeDeleted)
                            {
                                var property = item.DocumentReceiverEntity.FirstOrDefault(t => t.Id == removedItem);
                                if (property != null)
                                {
                                    item.DocumentReceiverEntity.Remove(property);
                                }
                            }
                        }
                        model.Receivers.Where(t => !t.IsEntityGroup && !item.DocumentReceiverEntity.Any(n => n.StructureId == t.Id))
                            .Select(t => new DocumentReceiverEntity
                            {
                                StructureId = t.Id,
                            }).ToList().ForEach(t => item.DocumentReceiverEntity.Add(t));
                        model.Receivers.Where(t => t.IsEntityGroup && !item.DocumentReceiverEntity.Any(n => n.EntityGroupId == t.Id))
                           .Select(t => new DocumentReceiverEntity
                           {
                               EntityGroupId = t.Id,
                           }).ToList().ForEach(t => item.DocumentReceiverEntity.Add(t));
                    }
                    else
                    {
                        var tobeDeleted = item.DocumentReceiverEntity.Select(t => t.Id).ToList();
                        if (!tobeDeleted.IsNullOrEmpty())
                        {
                            new DocumentReceiverEntity().Delete(tobeDeleted);
                        }
                        foreach (var removedItem in tobeDeleted)
                        {
                            var property = item.DocumentReceiverEntity.FirstOrDefault(t => t.Id == removedItem);
                            if (property != null)
                            {
                                item.DocumentReceiverEntity.Remove(property);
                            }
                        }
                    }
                    if (!model.CarbonCopy.IsNullOrEmpty())
                    {
                        structureIdsForProvision.AddRange(model.CarbonCopy);
                        var tobeDeleted = item.DocumentCarbonCopy.Where(t => !model.CarbonCopy.Any(n => n == t.StructureId)).Select(t => t.Id).ToList();
                        if (!tobeDeleted.IsNullOrEmpty())
                        {
                            new DocumentCarbonCopy().Delete(tobeDeleted);
                            foreach (var removedItem in tobeDeleted)
                            {
                                var property = item.DocumentCarbonCopy.FirstOrDefault(t => t.Id == removedItem);
                                if (property != null)
                                {
                                    item.DocumentCarbonCopy.Remove(property);
                                }
                            }
                        }
                        model.CarbonCopy.Where(t => !item.DocumentCarbonCopy.Any(n => n.StructureId == t))
                            .Select(t => new DocumentCarbonCopy
                            {
                                StructureId = t
                            }).ToList().ForEach(t => item.DocumentCarbonCopy.Add(t));
                    }
                    else
                    {
                        var tobeDeleted = item.DocumentCarbonCopy.Select(t => t.Id).ToList();
                        if (!tobeDeleted.IsNullOrEmpty())
                        {
                            new DocumentCarbonCopy().Delete(tobeDeleted);
                        }
                        foreach (var removedItem in tobeDeleted)
                        {
                            var property = item.DocumentCarbonCopy.FirstOrDefault(t => t.Id == removedItem);
                            if (property != null)
                            {
                                item.DocumentCarbonCopy.Remove(property);
                            }
                        }
                    }
                    if (model.SendingEntityId.HasValue)
                    {
                        structureIdsForProvision.Add(model.SendingEntityId.Value);
                    }
                    structureIdsForProvision.Add(model.CreatedByStructureId);
                    if (structureIdsForProvision.Count > 0)
                    {
                        structureIdsForProvision = structureIdsForProvision.Distinct().ToList();
                        ManageStructure.Provision(structureIdsForProvision);
                    }
                    item.DocumentForm = new DocumentForm
                    {
                        Id = item.Id,
                        Form = model.FormData,
                        Body = string.IsNullOrEmpty(model.Body) ? string.Empty : model.Body,
                        Keyword = string.IsNullOrEmpty(model.Keyword) ? string.Empty : model.Keyword
                    };
                    item.UpdateIncludeDocumentForm();
                    new EventReceivers().OnDocumentSaved(model);
                    if (transferId.HasValue)
                    {
                        item = await new Document().FindIncludeDocumentMetadataAsync(model.Id.Value);
                        List<string> receivingEntities = new List<string>();
                        foreach (var receiver in item.DocumentReceiverEntity)
                        {
                            string text = string.Empty;
                            if (receiver.EntityGroupId.HasValue)
                            {
                                text = language == Language.AR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameAr) ? receiver.EntityGroup.NameAr : receiver.EntityGroup.Name
                                : language == Language.FR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameFr) ? receiver.EntityGroup.NameFr : receiver.EntityGroup.Name
                                : receiver.EntityGroup.Name;
                            }
                            else
                            {
                                text = language == Language.AR ? !string.IsNullOrEmpty(receiver.Structure.NameAr) ? receiver.Structure.NameAr : receiver.Structure.Name
                               : language == Language.FR ? !string.IsNullOrEmpty(receiver.Structure.NameFr) ? receiver.Structure.NameFr : receiver.Structure.Name
                               : receiver.Structure.Name;
                            }
                            receivingEntities.Add(text);
                        }

                        var newDocumentValue = JsonConvert.SerializeObject(new DocumentModel
                        {
                            Subject = item.Subject,
                            FormData = item.DocumentForm != null ? item.DocumentForm.Form : string.Empty,
                            Classification = item.Classification != default(Classification) ? language == Language.AR ? item.Classification.NameAr : language == Language.FR ? item.Classification.NameFr : item.Classification.Name : String.Empty,
                            DocumentType = item.DocumentType != default(DocumentType) ? language == Language.AR ? item.DocumentType.NameAr : language == Language.FR ? item.DocumentType.NameFr : item.DocumentType.Name : String.Empty,
                            DueDate = item.DueDate.HasValue ? item.DueDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty,
                            Importance = item.Importance != default(Importance) ? language == Language.AR ? item.Importance.NameAr : language == Language.FR ? item.Importance.NameFr : item.Importance.Name : String.Empty,
                            Priority = item.Priority != default(Priority) ? language == Language.AR ? item.Priority.NameAr : language == Language.FR ? item.Priority.NameFr : item.Priority.Name : String.Empty,
                            Privacy = item.Privacy != default(Privacy) ? language == Language.AR ? item.Privacy.NameAr : language == Language.FR ? item.Privacy.NameFr : item.Privacy.Name : String.Empty,
                            SendingEntity = item.SendingEntity != default(Structure) ? language == Language.AR ? !string.IsNullOrEmpty(item.SendingEntity.NameAr) ? item.SendingEntity.NameAr : item.SendingEntity.Name
                                                    : language == Language.FR ? !string.IsNullOrEmpty(item.SendingEntity.NameFr) ? item.SendingEntity.NameFr : item.SendingEntity.Name
                                                    : item.SendingEntity.Name : String.Empty,
                            Receivers = receivingEntities,
                            ExternalReferenceNumber = item.ExternalReferenceNumber,
                            Keyword = item.DocumentForm != null ? item.DocumentForm.Keyword : string.Empty,
                            Body = item.DocumentForm != null ? item.DocumentForm.Body : string.Empty,
                            CarbonCopies = item.DocumentCarbonCopy.Any() ? item.DocumentCarbonCopy.Where(x => x.Structure != default(Structure))
                                                .Select(x => language == Language.AR ? !String.IsNullOrEmpty(x.Structure.NameAr) ? x.Structure.NameAr : x.Structure.Name
                                                    : language == Language.FR ? !String.IsNullOrEmpty(x.Structure.NameFr) ? x.Structure.NameFr : x.Structure.Name
                                                    : x.Structure.Name).ToList() : new List<string>()
                        });
                        ManageActivityLog.AddActivityLog(item.Id, transferId.HasValue ? transferId.Value : null, (int)Core.ActivityLogs.SaveEdit, userId,
                           originalDocumentValue, newDocumentValue);
                    }
                    ManageActivityLog.AddActivityLog(item.Id, transferId, (int)Core.ActivityLogs.SaveEdit, userId, originalDocumentValue);
                    retValue = true;
                    if (model.Register && string.IsNullOrEmpty(item.ReferenceNumber))
                    {
                        try
                        {
                            var returnedResult = new CategoryReferenceCounter().Generate(item.CategoryId, userId, model.CreatedByStructureId, item.Id, transferId, language);
                            reference = returnedResult.Reference;
                            item.ReferenceNumber = returnedResult.Reference;
                            item.ReferenceSequence = returnedResult.Sequence;
                            //to maintain reference sequence
                            item.UpdateReferenceNumber();
                            new EventReceivers().OnDocumentReferenceNumberGenerated(item.Id);
                        }
                        catch (Exception)
                        {
                            return (true, string.Empty, "CantGenerateReferenceNumber");
                        }
                    }

                    if (item.AttachmentId.HasValue && ManageCategory.CheckCategoryByFileOrTemplate(item.CategoryId))
                    {
                        var attachment = new Attachment().Find(item.AttachmentId.Value);
                        if (attachment != default(Attachment) && Intalio.Core.Helper.IsWord($"{attachment.Name}.{attachment.Extension}"))
                        {
                            await ManageBookmark.FindReplaceBookmarkAndAttachment(attachment.Id, attachment.StorageAttachmentId, item.Id, userId, transferId);
                        }
                    }
                }
            }
            return (retValue, reference, string.Empty);
        }

        /// <summary>
        /// Delete document
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="ids"></param>
        public static async Task<List<string>> Delete(long userId, long structureId, List<long> ids, bool deleteTransfers = false)
        {
            var registeredDocuments = new List<string>();
            foreach (var id in ids)
            {
                var item = new Document().Find(id);
                if (item != null && (item.CreatedByUserId == userId || item.CreatedByStructureId == structureId) && ((item.StatusId == (int)DocumentStatus.Draft
                    && string.IsNullOrEmpty(item.ReferenceNumber)) || item.CategoryId == Configuration.FollowUpCategory) || deleteTransfers)
                {
                    var attachmentsIds = new Attachment().ListIdsByDocumentId(id);
                    var storageAttachmentsIds = new Attachment().ListStorageIdsByDocumentId(id);
                    foreach (var storageAttachmentId in storageAttachmentsIds)
                    {
                        await Intalio.Core.Helper.HttpPostAsync($"{Configuration.StorageServerUrl}/storage/DeleteFile?fileId={storageAttachmentId}", Configuration.IdentityAccessToken);
                    }

                   
                    var documentLogsIds = new ActivityLog().GetByDocumentId(id).Select(l => l.Id).ToList();
                    new ActivityLog().Delete(documentLogsIds);

                    var deletedNotes = new Note().List(0, int.MaxValue, id, userId).Select(x => x.Id).ToList();
                    new Note().Delete(deletedNotes);

                    ManageLinkedDocument.DeleteByDocumentId(id);

                    if (deleteTransfers)
                    {
                        foreach (var attachmentId in attachmentsIds)
                        {
                            var attachment = new Attachment().Find(attachmentId);
                            attachment.TransferId = null;
                            attachment.Update();
                        }
                        var documentTransfersIds = new Transfer().ListByDocumentId(id).Select(l => l.Id).ToList();
                        new Transfer().Delete(documentTransfersIds);

                    }

                    item.Delete();
                    //new Attachment().Delete(attachmentsIds);
                }
                else
                {
                    if (item != null)
                    {
                        registeredDocuments.Add(item.ReferenceNumber);
                    }
                }
            }
            return registeredDocuments;
        }

        /// <summary>
        /// Send document
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureSender"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="id"></param>
        /// <param name="transferId"></param>
        /// <param name="purposeId"></param>
        /// <param name="dueDate"></param>
        /// <param name="instruction"></param>
        /// <param name="VoiceNote"></param>
        /// <param name="structureId"></param>
        /// <returns></returns>
        /// 
        public static async Task<(bool Sent, string Message)> Export(long userId, List<long> structureIds, bool isStructureSender, bool isStructureReceiver, short privacyLevel, long id, long? transferId, short purposeId, DateTime? dueDate, string instruction, long structureId, byte[] VoiceNote, bool VoiceNotePrivacy, Language language = Language.EN)
        {
            bool retValue = false;
            if (ManageUserAccess.HaveAccess(id, transferId, userId, structureIds, isStructureReceiver, privacyLevel))
            {
                if (Configuration.AttachmentEditable)
                {
                    if (ManageAttachment.DocumentHasLockedAttachments(id))
                    {
                        return (false, "FileInUse");
                    }
                }
                bool referenceNumperUpdated = false;
                long ReferenceNumberGeneratorType = 0;
                Document item = new Document().FindIncludeEntities(id);
                if (item != null)
                {
                    if (string.IsNullOrEmpty(item.ReferenceNumber))
                    {
                        try
                        {
                            ReferenceNumberGeneratorType = ManageCategory.GetReferenceNumberGeneratorTypeByCategoryId(item.CategoryId);
                            if (ReferenceNumberGeneratorType == 3)
                            {
                                var returnedResult = new CategoryReferenceCounter().Generate(item.CategoryId, userId, item.CreatedByStructureId, id, transferId, Language.EN);
                                item.ReferenceNumber = returnedResult.Reference;
                                item.ReferenceSequence = returnedResult.Sequence;
                                referenceNumperUpdated = true;
                            }
                        }
                        catch (Exception ex)
                        {
                            return (false, "CantGenerateReferenceNumber");
                        }
                    }
                    if (await ManageTransfer.ExportToReceiverEntity(userId, structureId, structureIds, isStructureSender, transferId, item, purposeId, dueDate, instruction, language))
                    {

                        //to maintain reference sequence
                        if (ReferenceNumberGeneratorType == 3 && referenceNumperUpdated)
                        {
                            item.UpdateReferenceNumber();
                            new EventReceivers().OnDocumentReferenceNumberGenerated(item.Id);
                        }

                        if (transferId.HasValue)
                        {
                            ManageTransfer.Close(transferId.Value);
                        }
                        retValue = true;
                    }
                }
            }
            return (retValue, string.Empty);
        }
        public static async Task<(bool Sent, string Message)> Send(long userId, List<long> structureIds, bool isStructureSender, bool isStructureReceiver, short privacyLevel, long id, long? transferId, short purposeId, DateTime? dueDate, string instruction, long structureId, byte[] VoiceNote, bool VoiceNotePrivacy, Language language = Language.EN, long? requestStatus = null, long? exportedDocumentId = null)
        {
            bool retValue = false;
            if (ManageUserAccess.HaveAccess(id, transferId, userId, structureIds, isStructureReceiver, privacyLevel))
            {
                if (Configuration.AttachmentEditable)
                {
                    if (ManageAttachment.DocumentHasLockedAttachments(id))
                    {
                        return (false, "FileInUse");
                    }
                }
                bool referenceNumperUpdated = false;
                long ReferenceNumberGeneratorType = 0;
                Document item = new Document().FindIncludeEntities(id);
                if (item != null)
                {
                    if (string.IsNullOrEmpty(item.ReferenceNumber))
                    {
                        try
                        {
                            ReferenceNumberGeneratorType = ManageCategory.GetReferenceNumberGeneratorTypeByCategoryId(item.CategoryId);
                            if (ReferenceNumberGeneratorType == 3)
                            {
                                var returnedResult = new CategoryReferenceCounter().Generate(item.CategoryId, userId, item.CreatedByStructureId, id, transferId, Language.EN);
                                item.ReferenceNumber = returnedResult.Reference;
                                item.ReferenceSequence = returnedResult.Sequence;
                                referenceNumperUpdated = true;
                            }
                        }
                        catch (Exception ex)
                        {
                            return (false, "CantGenerateReferenceNumber");
                        }
                    }
                    if (await ManageTransfer.SendToReceiverEntity(userId, structureId, structureIds, isStructureSender, transferId, item, purposeId, dueDate, instruction, language, null, false, false, requestStatus, exportedDocumentId))
                    {

                        //to maintain reference sequence
                        if (ReferenceNumberGeneratorType == 3 && referenceNumperUpdated)
                        {
                            item.UpdateReferenceNumber();
                            new EventReceivers().OnDocumentReferenceNumberGenerated(item.Id);
                        }

                        if (transferId.HasValue)
                        {
                            ManageTransfer.Close(transferId.Value);
                        }
                        retValue = true;
                    }
                }
            }
            return (retValue, string.Empty);
        }
        public static (bool result, Document document) GenerateReferenceNumberFirstTime(long userId, long DocumentId, long? transferId, Intalio.Core.Language language)
        {
            Document document = new Document();
            document = ManageCategoryReferenceNumber.GetReferenceNumberGeneratorTypeByDocumentId(DocumentId);
            if ((document.Category.CategoryReferenceNumberTypeId == 2) && (document.ReferenceNumber == "" || document.ReferenceNumber == null))
            {
                var returnedResult = new CategoryReferenceCounter().Generate(document.CategoryId, userId, document.CreatedByStructureId, DocumentId, transferId, language);
                document.ReferenceNumber = returnedResult.Reference;
                document.ReferenceSequence = returnedResult.Sequence;


                bool barcodeGenerated = false;
                if (!string.IsNullOrEmpty(document.ReferenceNumber))
                {
                    try
                    {
                        var tryGenerateBarcode = ManageDocument.GenerateBarcode(document.Id, document.ReferenceNumber);
                        barcodeGenerated = true;
                    }
                    catch (Exception exp)
                    {
                        throw exp;
                    }

                }
                if (barcodeGenerated)
                {
                    UpdateReferenceNumberFirstTime(document);
                }

                return (true, document);
            }
            else if (document.Category.CategoryReferenceNumberTypeId != 2)
            {
                return (true, document);
            }
            return (false, document);
        }
        public static void UpdateReferenceNumberFirstTime(Document document)
        {
            document.UpdateReferenceNumber();
            new EventReceivers().OnDocumentReferenceNumberGenerated(document.Id);
        }

        /// <summary>
        /// For Draft
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="id"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static async Task<DocumentDetailsModel> FindCreatedByUser(long userId, long id, int roleId, long? structureId, Language language = Language.EN, bool fromRejectedDocument = false, DelegationModel delegation = null)
        {
            DocumentDetailsModel retValue = null;
            Document item = await new Document().FindIncludeAllAsync(id);
            DAL.Note noteitem = new DAL.Note();
            var nodeCount = noteitem.GetCountForTabs(item.Id, userId);
            List<LinkedDocument> linkedDocuments = new LinkedDocument().ListByDocumentId(item.Id, null);

            List<Attachment> rootAttachments = await new Attachment().ListAttachmentsAsyncForTabsByStructureForDraft(item.Id, userId, structureId);

            var ifHaveManageCorrespondenceAccess = Configuration.EnablePerStructure ? ManageUserStructure.ListMenuesByRoleIdUserIdStructureId(roleId, userId, structureId).Any(x => x.Name == "ManageCorrespondence") : ManageMenu.ListMenuesByRoleIdUserId(roleId, userId).Any(x => x.Name == "ManageCorrespondence");
            var ifHaveFollowUpOnEmployeesAccess = Configuration.EnablePerStructure ? ManageUserStructure.ListMenuesByRoleIdUserIdStructureId(roleId, userId, structureId).Any(x => x.Name == "ManageStructureUsersCorrespondences") : ManageMenu.ListMenuesByRoleIdUserId(roleId, userId).Any(x => x.Name == "ManageStructureUsersCorrespondences");
            var ifHaveFollowUpAccess = await ManageFollowUp.CheckIfDocumentHasFollowUpAccess(userId, structureId, id);

            if (item != null && ((item.CreatedByUserId == userId || item.CreatedByStructureId == structureId.Value) && (item.StatusId == (short)DocumentStatus.Draft || item.Transfer.Count == 0)) || ifHaveManageCorrespondenceAccess || ifHaveFollowUpOnEmployeesAccess || ifHaveFollowUpAccess || (fromRejectedDocument && (item.CreatedByStructureId == structureId || item.Transfer.Any(i => i.ToStructureId == structureId))))
            {
                var categoryName = item.Category.Name;
                if (language == Language.AR)
                {
                    categoryName = item.Category.NameAr;
                }
                else if (language == Language.FR)
                {
                    categoryName = item.Category.NameFr;
                }
                ValueText sendingEntity = null;
                if (item.SendingEntity != default(Structure))
                {
                    sendingEntity = new ValueText
                    {
                        Id = item.SendingEntity.Id,
                        Text = language == Language.AR ? !string.IsNullOrEmpty(item.SendingEntity.NameAr)  ? item.SendingEntity.NameAr : item.SendingEntity.Name
                        : language == Language.FR ? !string.IsNullOrEmpty(item.SendingEntity.NameFr) ? item.SendingEntity.NameFr : item.SendingEntity.Name
                        : item.SendingEntity.Name
                    };
                }
                ValueText classification = null;
                if (item.Classification != default(Classification))
                {
                    classification = new ValueText
                    {
                        Id = item.Classification.Id,
                        Text = language == Language.AR ? item.Classification.NameAr : language == Language.FR ? item.Classification.NameFr : item.Classification.Name
                    };
                }
                ValueText documentType = null;
                if (item.DocumentType != default(DocumentType))
                {
                    documentType = new ValueText
                    {
                        Id = item.DocumentType.Id,
                        Text = language == Language.AR ? item.DocumentType.NameAr : language == Language.FR ? item.DocumentType.NameFr : item.DocumentType.Name
                    };
                }

                List<ReceivingEntityModel> receivers = new List<ReceivingEntityModel>();
                List<ReceivingEntityModel> receivingEntities = new List<ReceivingEntityModel>();

                foreach (var receiver in item.DocumentReceiverEntity)
                {
                    long targetId = receiver.StructureId.HasValue ? receiver.StructureId.Value : receiver.EntityGroupId.Value;
                    bool isEntityGroup = receiver.EntityGroupId.HasValue;
                    string text = string.Empty;
                    string parent = string.Empty;
                    if (isEntityGroup)
                    {
                        text = language == Language.AR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameAr) ? receiver.EntityGroup.NameAr : receiver.EntityGroup.Name
                        : language == Language.FR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameFr) ? receiver.EntityGroup.NameFr : receiver.EntityGroup.Name
                        : receiver.EntityGroup.Name;
                    }
                    else
                    {
                        text = language == Language.AR ? !string.IsNullOrEmpty(receiver.Structure.NameAr) ? receiver.Structure.NameAr : receiver.Structure.Name
                       : language == Language.FR ? !string.IsNullOrEmpty(receiver.Structure.NameFr) ? receiver.Structure.NameFr : receiver.Structure.Name
                       : receiver.Structure.Name;
                        
                        if (receiver.Structure.Parent != null)
                        {
                            parent = language == Language.AR ? !string.IsNullOrEmpty(receiver.Structure.Parent.NameAr) ? receiver.Structure.Parent.NameAr : receiver.Structure.Parent.Name
                            : language == Language.FR ? !string.IsNullOrEmpty(receiver.Structure.Parent.NameFr) ? receiver.Structure.Parent.NameFr : receiver.Structure.Parent.Name
                            : receiver.Structure.Parent.Name;
                        }
                    }

                    receivers.Add(new ReceivingEntityModel
                    {
                        Id = targetId,
                        IsEntityGroup = isEntityGroup
                    });

                    receivingEntities.Add(new ReceivingEntityModel
                    {
                        Id = targetId,
                        Text = text,
                        IsEntityGroup = isEntityGroup,
                        IsExternal = receiver.Structure.IsExternal,
                        ParentName = parent
                    });
                }

                bool _fullControl = false;

                if (item.CategoryId == Configuration.FollowUpCategory)
                {
                    Assignee _assignee = ManageAssignee.FindByDocumentIdUserId(item.Id, userId);
                    if (_assignee != null)
                    {
                        _fullControl = _assignee.FollowUpSecurity.Name.ToLower().Contains("full") ? true : false;
                    }
                }
                var yy = item;
                var latestAttachmentVersion = (item.AttachmentId != null && item.AttachmentNavigation != null) ? await ManageAttachment.GetCurrentVersionNumber(item.AttachmentNavigation.StorageAttachmentId) : "";
                retValue = new DocumentDetailsModel
                {
                    Id = item.Id,
                    CategoryId = item.CategoryId,
                    SenderPerson = item.SenderPerson,
                    ReceiverPerson = item.ReceiverPerson,
                    IsExternalReceiver = item.IsExternalReceiver,
                    IsExternalSender = item.IsExternalSender,
                    CategoryName = categoryName,
                    ReferenceNumber = item.ReferenceNumber,
                    Subject = item.Subject,
                    Status = item.StatusId,
                    CustomAttributes = item.Category.CustomAttribute,
                    CustomAttributesTranslation = item.Category.CustomAttributeTranslation,
                    BasicAttributes = item.Category.BasicAttribute,
                    FormData = item.DocumentForm != null ? item.DocumentForm.Form : string.Empty,
                    ClassificationId = item.ClassificationId,
                    Classification = classification,
                    DocumentTypeId = item.DocumentTypeId,
                    DocumentType = documentType,
                    DueDate = item.DueDate.HasValue ? item.DueDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty,
                    ImportanceId = item.ImportanceId,
                    PriorityId = item.PriorityId,
                    PrivacyId = item.PrivacyId,
                    SendingEntityId = item.SendingEntityId,
                    SendingEntity = sendingEntity,
                    Receivers = receivers,
                    ReceivingEntities = receivingEntities,
                    CarbonCopy = item.DocumentCarbonCopy.Count > 0 ? item.DocumentCarbonCopy.Select(x => x.StructureId).ToList() : new List<long>(),
                    CarbonCopies = item.DocumentCarbonCopy.Count > 0 ? item.DocumentCarbonCopy.Where(x => x.Structure != default(Structure)).Select(x => new ValueText
                    {
                        Id = x.Structure.Id,
                        Text = language == Language.AR ? !String.IsNullOrEmpty(x.Structure.NameAr) ? x.Structure.NameAr : x.Structure.Name
                               : language == Language.FR ? !String.IsNullOrEmpty(x.Structure.NameFr) ? x.Structure.NameFr : x.Structure.Name
                               : x.Structure.Name,
                        ParentName = (language == Language.AR ? !String.IsNullOrEmpty(x.Structure.Parent?.NameAr) ? x.Structure.Parent.NameAr : x.Structure.Parent?.Name
                               : language == Language.FR ? !String.IsNullOrEmpty(x.Structure.Parent?.NameFr) ? x.Structure.Parent.NameFr : x.Structure.Parent?.Name
                               : x.Structure.Parent?.Name) ?? ""
                    }).ToList() : new List<ValueText>(),
                    CreatedByUser = item.CreatedByUser == null ? String.Empty :
                        (language == Language.EN ?
                        $"{item.CreatedByUser?.Firstname} {item.CreatedByUser?.Lastname}" :
                        $"{IdentityHelperExtension.GetFullName(item.CreatedByUserId, language)}"),
                    CreatedByStructureId = item.CreatedByStructureId,
                    CreatedDate= item.CreatedDate.ToString("yyyy-MM-dd"),
                    AttachmentId = item.AttachmentId,
                    AttachmentVersion = latestAttachmentVersion,
                    Body = item.DocumentForm != null ? item.DocumentForm.Body : string.Empty,
                    ExternalReferenceNumber = item.ExternalReferenceNumber,
                    Keyword = item.DocumentForm != null ? item.DocumentForm.Keyword : string.Empty,
                    CreatedByUserId = item.CreatedByUserId,
                    FullControl = _fullControl,
                    DocumentDate = item.DocumentDate.HasValue ? item.DocumentDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty,
                    AttachmentCount = rootAttachments.Count,
                    NotesCount = nodeCount,
                    LinkedCorrespondanceCount = linkedDocuments.Count,
                    ByTemplate = item.ByTemplate,
                    DocumentCarbonCopy = item.DocumentCarbonCopy.Select(x => new ReceivingEntityModel
                    {
                        Id = x.StructureId,
                        Text = language == Language.AR ? (string.IsNullOrEmpty(x.Structure.NameAr) ? x.Structure.Name : x.Structure.NameAr) : language == Language.FR ? (string.IsNullOrEmpty(x.Structure.NameFr) ? x.Structure.Name : x.Structure.NameFr) : x.Structure.Name,
                        IsExternal = x.Structure.IsExternal,
                        IsCC = true
                    }).ToList(),
                    HasUserCofigureSignature = item.Attachment?.Where(att => att.AttachmentSignUser.Where(asu => asu.UserId == userId && asu.SignatureRegion.Any()).Any()).Any(),
                    TemplateHasSignature = item.TemplateHasSignature,
                    AttachmentExtention = item.AttachmentNavigation != null ? item.AttachmentNavigation.Extension : "",
                    AllowSign = delegation != null ? delegation.AllowSign : true,
                    IsSigned = item.IsSigned ?? false,
                    //AttachmentExtention = item.AttachmentNavigation != null ? item.AttachmentNavigation.Extension : string.Empty,
                    //TemplateHasSignature = item.TemplateHasSignature,
                    //IsSigned = item.IsSigned,
                    //TemplateHasSignature = item.TemplateHasSignature,
                    //IsSigned = item.IsSigned == null ? false : true,
                    //attachmentExtention = rootAttachments.Where(a => a.Id == item.AttachmentId).FirstOrDefault().Extension
                };
            }
            return retValue;
        }

        /// <summary>
        /// Check statuses in use
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public static List<short> CheckStatusInUse(List<short> ids)
        {
            List<short> retValue = new List<short>();
            foreach (var id in ids)
            {
                if (new Document().CheckStatusInUse(id))
                {
                    retValue.Add(id);
                }
            }
            return retValue;
        }

        /// <summary>
        /// Check priorities in use
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public static List<short> ListPrioritiesInUse(List<short> ids)
        {
            List<short> retValue = new List<short>();
            foreach (var id in ids)
            {
                if (new Document().CheckPriorityInUse(id))
                {
                    retValue.Add(id);
                }
            }
            return retValue;
        }

        /// <summary>
        /// Filter categories
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public static (List<short> UsedCategory, List<short> UnUsedCategory) FilterCategories(List<short> ids)
        {
            List<short> usedCategory = new List<short>();
            foreach (var id in ids)
            {
                var used = new Document().CheckCategoryInUse(id);
                if (used)
                {
                    usedCategory.Add(id);
                }
            }
            ids = ids.Except(usedCategory).ToList();
            return (usedCategory, ids);
        }

        /// <summary>
        /// Check classifications in use
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public static List<short> CheckClassificationInUse(List<short> ids)
        {
            List<short> retValue = new List<short>();
            foreach (var id in ids)
            {
                if (new Document().CheckClassificationInUse(id))
                {
                    retValue.Add(id);
                }
            }
            return retValue;
        }

        /// <summary>
        /// Check importances in use
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public static List<short> CheckImportanceInUse(List<short> ids)
        {
            List<short> retValue = new List<short>();
            foreach (var id in ids)
            {
                if (new Document().CheckImportanceInUse(id))
                {
                    retValue.Add(id);
                }
            }
            return retValue;
        }

        /// <summary>
        /// Check privacies in use
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public static List<short> CheckPrivacyInUse(List<short> ids)
        {
            List<short> retValue = new List<short>();
            foreach (var id in ids)
            {
                if (new Document().CheckPrivacyInUse(id))
                {
                    retValue.Add(id);
                }
            }
            return retValue;
        }

        /// <summary>
        /// Check documentTypes in use
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public static List<short> CheckDocumentTypeInUse(List<short> ids)
        {
            List<short> retValue = new List<short>();
            foreach (var id in ids)
            {
                if (new Document().CheckDocumentTypeInUse(id))
                {
                    retValue.Add(id);
                }
            }
            return retValue;
        }

        /// <summary>
        /// List documents with draft status.
        /// Must be created by the user
        /// </summary>
        /// <param name="startIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="userId"></param>
        /// <param name="filter"></param>
        /// <param name="sortExpression"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static async Task<(int, List<DocumentDraftListViewModel>)> ListDraft(int startIndex, int pageSize, long userId, ExpressionBuilderFilters filter = null,
              List<SortExpression> sortExpression = null, long? delegationId = null, Language language = Language.EN, bool isStructureDraft = false, short PrivacyLevel = 0)
        {
            using (Document item = new Document())
            {
                var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value) : null;
                var categoryIds = new List<short>();
                short? privacyLevel = isStructureDraft ? PrivacyLevel : null;
                if (delegation != null && delegation.DraftInbox == true)
                {
                    userId = delegation.FromUserId;
                    categoryIds = delegation.CategoryIds?.Select(t => (short)t).ToList();
                    privacyLevel = delegation.PrivacyLevel;
                    if (filter == null)
                        filter = new ExpressionBuilderFilters();

                    if (delegation.ShowOldCorespondence && delegation.StartDate != null && delegation.StartDate != DateTime.MinValue)
                        filter.Add("CreatedDate", delegation.StartDate, Operator.GreaterThanOrEqual);
                    else if (!delegation.ShowOldCorespondence)
                        filter.Add("CreatedDate", delegation.FromDate, Operator.GreaterThanOrEqual);

                    filter.Add("CreatedDate", delegation.ToDate, Operator.LessThanOrEqualTo);
                    //var privacies = new Intalio.Core.DAL.Privacy().List();
                    //var privacyId = privacies.Where(p => p.Name == privacyLevel)
                    //filter.Add("PrivacyId", privacyLevel, Operator.LessThanOrEqualTo);
                }

                var filterExp = filter != null ? ExpressionBuilder.GetExpression<Document>(filter, ExpressionBuilderOperator.And) : null;
                var countResult = item.GetDraftCount(userId, categoryIds, filterExp , privacyLevel, isStructureDraft);
                var itemList = await item.ListDraftAsync(startIndex, pageSize, userId, categoryIds, filterExp, sortExpression?.OrderByExpression<Document>(), privacyLevel, isStructureDraft);
                return (await countResult, itemList.Select(t =>
                {
                    var sendingEntity = string.Empty;
                    if (t.SendingEntity != null)
                    {
                        sendingEntity = t.SendingEntity.Name;
                        if (language == Language.AR && !string.IsNullOrEmpty(t.SendingEntity.NameAr) )
                        {
                            sendingEntity = t.SendingEntity.NameAr;
                        }
                        else if (language == Language.FR && t.SendingEntity.NameFr != null)
                        {
                            sendingEntity = t.SendingEntity.NameFr;
                        }
                    }
                    string receivingEntity = string.Empty;
                    string structureText = string.Empty;
                    string entityGroupText = string.Empty;
                    foreach (var receiver in t.DocumentReceiverEntity)
                    {
                        long targetId = receiver.StructureId.HasValue ? receiver.StructureId.Value : receiver.EntityGroupId.Value;
                        bool isEntityGroup = receiver.EntityGroupId.HasValue;
                        string text = string.Empty;
                        if (isEntityGroup)
                        {
                            text = language == Language.AR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameAr) ? receiver.EntityGroup.NameAr : receiver.EntityGroup.Name
                            : language == Language.FR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameFr) ? receiver.EntityGroup.NameFr : receiver.EntityGroup.Name
                            : receiver.EntityGroup.Name;
                            entityGroupText += text + Constants.SEPARATOR;
                        }
                        else
                        {
                            text = language == Language.AR ? !string.IsNullOrEmpty(receiver.Structure.NameAr) ? receiver.Structure.NameAr : receiver.Structure.Name
                           : language == Language.FR ? !string.IsNullOrEmpty(receiver.Structure.NameFr) ? receiver.Structure.NameFr : receiver.Structure.Name
                           : receiver.Structure.Name;
                            structureText += text + Constants.SEPARATOR;
                        }
                    }
                    receivingEntity = $"{structureText}{entityGroupText}";
                    if (!string.IsNullOrEmpty(receivingEntity) && receivingEntity.Substring(receivingEntity.Length - 1) == Constants.SEPARATOR)
                    {
                        receivingEntity = receivingEntity.Remove(receivingEntity.Length - 1);
                    }

                    var isEnLang = language == Language.EN;
                    return new DocumentDraftListViewModel
                    {
                        Id = t.Id,
                        Subject = t.Subject,
                        SendingEntity = sendingEntity,
                        ReceivingEntity = receivingEntity,
                        CategoryId = t.CategoryId,
                        ImportanceId = t.ImportanceId,
                        ReferenceNumber = t.ReferenceNumber,
                        Status = t.StatusId,
                        PriorityId = t.PriorityId,
                        CreatedDate = t.CreatedDate.ToString(Constants.DATE_FORMAT),
                        ModifiedDate = t.ModifiedDate != default ? t.ModifiedDate?.ToString(Constants.DATE_FORMAT) : string.Empty,
                        DocumentForm = t.DocumentForm,
                        Note = t.Note,
                        Body = t.DocumentForm.Body != null ? t.DocumentForm.Body : string.Empty,
                        CreatedByUser = (isEnLang ? $"{t.CreatedByUser.Firstname} {t.CreatedByUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.CreatedByUserId, language)}"),
                        CreatedByUserId = t.CreatedByUserId,
                        DraftStatus = t.DraftStatus
                    };
                }).ToList());
            }
        }

        /// <summary>
        /// List non draft documents.
        /// Must be created by the user
        /// </summary>
        /// <param name="startIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="userId"></param>
        /// <param name="filter"></param>
        /// <param name="sortExpression"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static async Task<(int, List<DocumentDraftListViewModel>)> ListMyRequests(int startIndex, int pageSize, long userId, ExpressionBuilderFilters filter = null,
              List<SortExpression> sortExpression = null, Language language = Language.EN)
        {
            using (Document item = new Document())
            {
                var filterExp = filter != null ? ExpressionBuilder.GetExpression<Document>(filter, ExpressionBuilderOperator.And) : null;
                var countResult = item.GetMyRequestsCount(userId, filterExp);
                var itemList = await item.ListMyRequestsAsync(startIndex, pageSize, userId, filterExp, sortExpression?.OrderByExpression<Document>());
                return (await countResult, itemList.Select(t =>
                {
                    var sendingEntity = string.Empty;
                    if (t.SendingEntity != null)
                    {
                        sendingEntity = t.SendingEntity.Name;
                        if (language == Language.AR && !string.IsNullOrEmpty(t.SendingEntity.NameAr))
                        {
                            sendingEntity = t.SendingEntity.NameAr;
                        }
                        else if (language == Language.FR && !string.IsNullOrEmpty(t.SendingEntity.NameFr))
                        {
                            sendingEntity = t.SendingEntity.NameFr;
                        }
                    }
                    var isOverdue = false;
                    if (t.DueDate.HasValue)
                    {
                        if (t.ClosedDate.HasValue)
                        {
                            isOverdue = t.ClosedDate.Value > t.DueDate.Value;
                        }
                        else
                        {
                            isOverdue = DateTime.Now.Date > t.DueDate.Value;
                        }
                    }
                    string receivingEntity = string.Empty;
                    string structureText = string.Empty;
                    string entityGroupText = string.Empty;
                    var isEnLang = language == Language.EN;

                    foreach (var receiver in t.DocumentReceiverEntity)
                    {
                        long targetId = receiver.StructureId.HasValue ? receiver.StructureId.Value : receiver.EntityGroupId.Value;
                        bool isEntityGroup = receiver.EntityGroupId.HasValue;
                        string text = string.Empty;
                        if (isEntityGroup)
                        {
                            text = language == Language.AR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameAr) ? receiver.EntityGroup.NameAr : receiver.EntityGroup.Name
                            : language == Language.FR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameFr) ? receiver.EntityGroup.NameFr : receiver.EntityGroup.Name
                            : receiver.EntityGroup.Name;
                            entityGroupText += text + Constants.SEPARATOR;
                        }
                        else
                        {
                            text = language == Language.AR ? !string.IsNullOrEmpty(receiver.Structure.NameAr) ? receiver.Structure.NameAr : receiver.Structure.Name
                           : language == Language.FR ? !string.IsNullOrEmpty(receiver.Structure.NameFr) ? receiver.Structure.NameFr : receiver.Structure.Name
                           : receiver.Structure.Name;
                            structureText += text + Constants.SEPARATOR;
                        }
                    }
                    receivingEntity = $"{structureText}{entityGroupText}";
                    if (!string.IsNullOrEmpty(receivingEntity) && receivingEntity.Substring(receivingEntity.Length - 1) == Constants.SEPARATOR)
                    {
                        receivingEntity = receivingEntity.Remove(receivingEntity.Length - 1);
                    }
                    string linkedCorrespondencesReferenceNo = string.Empty;

                    foreach (var linkedCorrespondence in t.LinkedDocumentDocument)
                    {
                        if (!string.IsNullOrEmpty(linkedCorrespondence.LinkedDocumentNavigation?.ReferenceNumber))
                            linkedCorrespondencesReferenceNo += (linkedCorrespondencesReferenceNo.Length > 0 ? ", " : "") + linkedCorrespondence.LinkedDocumentNavigation.ReferenceNumber;
                    }
                    return new DocumentDraftListViewModel
                    {
                        Id = t.Id,
                        Subject = t.Subject,
                        SendingEntity = sendingEntity,
                        ReceivingEntity = receivingEntity,
                        CategoryId = t.CategoryId,
                        ImportanceId = t.ImportanceId,
                        ReferenceNumber = t.CategoryId != Configuration.FollowUpCategory ? t.ReferenceNumber : linkedCorrespondencesReferenceNo,
                        Status = t.StatusId,
                        IsOverDue = isOverdue,
                        PriorityId = t.PriorityId,
                        PrivacyId = t.PrivacyId,
                        DueDate = t.DueDate != null ? t.DueDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty,
                        CreatedDate = t.CreatedDate.ToString(Constants.DATE_FORMAT),
                        ModifiedDate = t.ModifiedDate != default ? t.ModifiedDate?.ToString(Constants.DATE_FORMAT) : string.Empty,
                        DocumentForm = t.DocumentForm,
                        //CreatedByUser = (isEnLang ? $"{t.CreatedByUser.Firstname} {t.CreatedByUser.Lastname}" :
                        //$"{IdentityHelperExtension.GetFullName(t.CreatedByUserId, language)}"),
                        CreatedByUser = isEnLang
                       ? (t.CreatedByUser != null
                      ? $"{t.CreatedByUser.Firstname} {t.CreatedByUser.Lastname}"
                        : " ")
                  : $"{IdentityHelperExtension.GetFullName(t.CreatedByUserId, language)}",

                        CreatedByUserId = t.CreatedByUserId,


                        Note = t.Note
                    };
                }).ToList());
            }
        }


        /// <summary>
        /// List Custom Basket documents.
        /// </summary>
        /// <param name="startIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="DocumentIds"></param>
        /// <param name="filter"></param>
        /// <param name="sortExpression"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static async Task<(int, List<DocumentDraftListViewModel>)> ListCustomBasketDocuments(int startIndex, int pageSize, List<long> DocumentIds, ExpressionBuilderFilters filter = null,
              List<SortExpression> sortExpression = null, Language language = Language.EN)
        {
            using (Document item = new Document())
            {
                var filterExp = filter != null ? ExpressionBuilder.GetExpression<Document>(filter, ExpressionBuilderOperator.And) : null;
                var countResult = item.GetCustomBasketCount(DocumentIds, filterExp);
                var itemList = await item.ListCustomBasketDocumentsAsync(startIndex, pageSize, DocumentIds, filterExp, sortExpression?.OrderByExpression<Document>());
                return (await countResult, itemList.Select(t =>
                {
                    var sendingEntity = string.Empty;
                    if (t.SendingEntity != null)
                    {
                        sendingEntity = t.SendingEntity.Name;
                        if (language == Language.AR && !string.IsNullOrEmpty(t.SendingEntity.NameAr))
                        {
                            sendingEntity = t.SendingEntity.NameAr;
                        }
                        else if (language == Language.FR && !string.IsNullOrEmpty(t.SendingEntity.NameFr))
                        {
                            sendingEntity = t.SendingEntity.NameFr;
                        }
                    }
                    var isOverdue = false;
                    if (t.DueDate.HasValue)
                    {
                        if (t.ClosedDate.HasValue)
                        {
                            isOverdue = t.ClosedDate.Value > t.DueDate.Value;
                        }
                        else
                        {
                            isOverdue = DateTime.Now.Date > t.DueDate.Value;
                        }
                    }
                    string receivingEntity = string.Empty;
                    string structureText = string.Empty;
                    string entityGroupText = string.Empty;
                    foreach (var receiver in t.DocumentReceiverEntity)
                    {
                        long targetId = receiver.StructureId.HasValue ? receiver.StructureId.Value : receiver.EntityGroupId.Value;
                        bool isEntityGroup = receiver.EntityGroupId.HasValue;
                        string text = string.Empty;
                        if (isEntityGroup)
                        {
                            text = language == Language.AR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameAr) ? receiver.EntityGroup.NameAr : receiver.EntityGroup.Name
                            : language == Language.FR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameFr) ? receiver.EntityGroup.NameFr : receiver.EntityGroup.Name
                            : receiver.EntityGroup.Name;
                            entityGroupText += text + Constants.SEPARATOR;
                        }
                        else
                        {
                            text = language == Language.AR ? !string.IsNullOrEmpty(receiver.Structure.NameAr) ? receiver.Structure.NameAr : receiver.Structure.Name
                           : language == Language.FR ? !string.IsNullOrEmpty(receiver.Structure.NameFr) ? receiver.Structure.NameFr : receiver.Structure.Name
                           : receiver.Structure.Name;
                            structureText += text + Constants.SEPARATOR;
                        }
                    }
                    receivingEntity = $"{structureText}{entityGroupText}";
                    if (!string.IsNullOrEmpty(receivingEntity) && receivingEntity.Substring(receivingEntity.Length - 1) == Constants.SEPARATOR)
                    {
                        receivingEntity = receivingEntity.Remove(receivingEntity.Length - 1);
                    }
                    return new DocumentDraftListViewModel
                    {
                        Id = t.Id,
                        Subject = t.Subject,
                        SendingEntity = sendingEntity,
                        ReceivingEntity = receivingEntity,
                        CategoryId = t.CategoryId,
                        ImportanceId = t.ImportanceId,
                        ReferenceNumber = t.ReferenceNumber,
                        Status = t.StatusId,
                        IsOverDue = isOverdue,
                        PriorityId = t.PriorityId,
                        DueDate = t.DueDate != null ? t.DueDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty,
                        CreatedDate = t.CreatedDate.ToString(Constants.DATE_FORMAT),
                        ModifiedDate = t.ModifiedDate != default ? t.ModifiedDate?.ToString(Constants.DATE_FORMAT) : string.Empty
                    };
                }).ToList());
            }
        }
        /// <summary>
        /// List closed documents.
        /// Must be created by user or document transfer sent to the user or the user structures
        /// </summary>
        /// <param name="startIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="filter"></param>
        /// <param name="sortExpression"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static async Task<(int, List<DocumentDraftListViewModel>)> ListClosed(int startIndex, int pageSize, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, ExpressionBuilderFilters filter = null,
              List<SortExpression> sortExpression = null, Language language = Language.EN)
        {
            using (Document item = new Document())
            {
                var filterExp = filter != null ? ExpressionBuilder.GetExpression<Document>(filter, ExpressionBuilderOperator.And) : null;
                var countResult = item.GetClosedCount(userId, structureIds, isStructureReceiver, privacyLevel, filterExp);
                var itemList = await item.ListClosedAsync(startIndex, pageSize, userId, structureIds, isStructureReceiver, privacyLevel, filterExp, sortExpression?.OrderByExpression<Document>());
                return (await countResult, itemList.Select(t =>
                {
                    var sendingEntity = string.Empty;
                    if (t.SendingEntity != null)
                    {
                        sendingEntity = t.SendingEntity.Name;
                        if (language == Language.AR && !string.IsNullOrEmpty(t.SendingEntity.NameAr))
                        {
                            sendingEntity = t.SendingEntity.NameAr;
                        }
                        else if (language == Language.FR && !string.IsNullOrEmpty(t.SendingEntity.NameFr))
                        {
                            sendingEntity = t.SendingEntity.NameFr;
                        }
                    }
                    string receivingEntity = string.Empty;
                    string structureText = string.Empty;
                    string entityGroupText = string.Empty;
                    foreach (var receiver in t.DocumentReceiverEntity)
                    {
                        long targetId = receiver.StructureId.HasValue ? receiver.StructureId.Value : receiver.EntityGroupId.Value;
                        bool isEntityGroup = receiver.EntityGroupId.HasValue;
                        string text = string.Empty;
                        if (isEntityGroup)
                        {
                            text = language == Language.AR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameAr) ? receiver.EntityGroup.NameAr : receiver.EntityGroup.Name
                            : language == Language.FR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameFr) ? receiver.EntityGroup.NameFr : receiver.EntityGroup.Name
                            : receiver.EntityGroup.Name;
                            entityGroupText += text + Constants.SEPARATOR;
                        }
                        else
                        {
                            text = language == Language.AR ? !string.IsNullOrEmpty(receiver.Structure.NameAr) ? receiver.Structure.NameAr : receiver.Structure.Name
                           : language == Language.FR ? !string.IsNullOrEmpty(receiver.Structure.NameFr) ? receiver.Structure.NameFr : receiver.Structure.Name
                           : receiver.Structure.Name;
                            structureText += text + Constants.SEPARATOR;
                        }
                    }
                    receivingEntity = $"{structureText}{entityGroupText}";
                    if (!string.IsNullOrEmpty(receivingEntity) && receivingEntity.Substring(receivingEntity.Length - 1) == Constants.SEPARATOR)
                    {
                        receivingEntity = receivingEntity.Remove(receivingEntity.Length - 1);
                    }
                    return new DocumentDraftListViewModel
                    {
                        Id = t.Id,
                        Subject = t.Subject,
                        SendingEntity = sendingEntity,
                        ReceivingEntity = receivingEntity,
                        CategoryId = t.CategoryId,
                        ImportanceId = t.ImportanceId,
                        ReferenceNumber = t.ReferenceNumber,
                        Status = t.StatusId,
                        PriorityId = t.PriorityId,
                        PrivacyId = t.PrivacyId,
                        CreatedDate = t.CreatedDate.ToString(Constants.DATE_FORMAT),
                        ModifiedDate = t.ModifiedDate.HasValue ? t.ModifiedDate?.ToString(Constants.DATE_FORMAT) : string.Empty,
                        ClosedDate = t.ClosedDate.HasValue ? t.ClosedDate?.ToString(Constants.DATE_FORMAT) : string.Empty,
                        DocumentForm = t.DocumentForm,
                        Note = t.Note,
                        Body = t.DocumentForm != null ? t.DocumentForm.Body : string.Empty,

                    };
                }).ToList());
            }
        }

        /// <summary>
        /// Get documents with draft status count.
        /// Must be created by the user
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="filter"></param>
        /// <returns></returns>
        public static async Task<(int Total, int Today)> GetDraftCounts(long userId, long? loggedInStructureId, ExpressionBuilderFilters filter = null, long? delegationId = null, short PrivacyLevel = 0)
        {
            using (Document item = new Document())
            {
                var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value) : null;
                var categoryIds = new List<short>();
                //short? privacyLevel = PrivacyLevel;
                bool isStructureDraft = filter.Any(f => f.PropertyName == "CreatedByStructure");
                filter.RemoveAll(f => f.PropertyName == "CreatedByStructure");
                short? privacyLevel = isStructureDraft ? PrivacyLevel : null;
                if (delegation != null && delegation.DraftInbox == true)
                {
                    userId = delegation.FromUserId;
                    categoryIds = delegation.CategoryIds?.Select(t => (short)t).ToList();
                    privacyLevel = delegation.PrivacyLevel;
                    if (filter == null)
                        filter = new ExpressionBuilderFilters();

                    if (delegation.ShowOldCorespondence && delegation.StartDate != null && delegation.StartDate != DateTime.MinValue)
                        filter.Add("CreatedDate", delegation.StartDate, Operator.GreaterThanOrEqual);
                    else if (!delegation.ShowOldCorespondence)
                        filter.Add("CreatedDate", delegation.FromDate, Operator.GreaterThanOrEqual);

                    filter.Add("CreatedDate", delegation.ToDate, Operator.LessThanOrEqualTo);
                    //var privacies = new Intalio.Core.DAL.Privacy().List();
                    //var privacyId = privacies.Where(p => p.Name == privacyLevel)
                    //filter.Add("PrivacyId", privacyLevel, Operator.LessThanOrEqualTo);
                }
                if (Configuration.EnablePerStructure && loggedInStructureId != null)
                    filter.Add("CreatedByStructureId", loggedInStructureId, Operator.Equals);


                var filterExp = filter != null ? ExpressionBuilder.GetExpression<Document>(filter, ExpressionBuilderOperator.And) : null;
                var draftTotal = await item.GetDraftCount(userId, categoryIds, filterExp, privacyLevel, isStructureDraft);
                var draftToday = await item.GetDraftTodayCount(userId, categoryIds, filterExp, privacyLevel, isStructureDraft);
                return (draftTotal, draftToday);
            }
        }

        /// <summary>
        /// Get non documents count.
        /// Must be created by the user
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="filter"></param>
        /// <returns></returns>
        public static async Task<(int Total, int Today)> GetMyRequestsCounts(long userId, long? loggedInStructureId, ExpressionBuilderFilters filter = null)
        {
            using (var item = new Document())
            {
                if (Configuration.EnablePerStructure && loggedInStructureId != null)
                    filter.Add("CreatedByStructureId", loggedInStructureId, Operator.Equals);

                var filterExp = filter != null ? ExpressionBuilder.GetExpression<Document>(filter, ExpressionBuilderOperator.And) : null;
                var myRequestTotal = await item.GetMyRequestsCount(userId, filterExp);
                var myRequestToday = await item.GetMyRequestsTodayCount(userId, filterExp);
                return (myRequestTotal, myRequestToday);
            }
        }

        /// <summary>
        /// Get closed documents count.
        /// Must be created by user or document transfer sent to the user or the user structures
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="filter"></param>
        /// <returns></returns>
        public static async Task<(int Total, int Today)> GetClosedCounts(long userId, List<long> structureIds, long? loggedInStructureId, bool isStructureReceiver, short privacyLevel, ExpressionBuilderFilters filter = null)
        {
            using (var item = new Document())
            {
                if (Configuration.EnablePerStructure && loggedInStructureId != null)
                    filter.Add("CreatedByStructureId", loggedInStructureId, Operator.Equals);

                var filterExp = filter != null ? ExpressionBuilder.GetExpression<Document>(filter, ExpressionBuilderOperator.And) : null;
                var closedTotal = await item.GetClosedCount(userId, structureIds, isStructureReceiver, privacyLevel, filterExp);
                var closedToday = await item.GetClosedTodayCount(userId, structureIds, isStructureReceiver, privacyLevel, filterExp);
                return (closedTotal, closedToday);
            }
        }

        /// <summary>
        /// Must be created by user
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="id"></param>
        /// <param name="language"></param>
        /// <param name="basketId"></param>
        /// <returns></returns>
        public async static Task<DocumentDetailsModel> GetDocument(long userId, long id, int? basketId, List<long> StructureIds, Language language = Language.EN, int? roleId = null)
        {
            DocumentDetailsModel retValue = null;
            bool haveBasketDocumentAccess = false;
            if (basketId.HasValue)
            {
                haveBasketDocumentAccess = ManageBasket.HaveDocumentAccess(basketId.Value, userId);
            }
            Document item = new Document().FindIncludeAll(id);
            DAL.Note noteitem = new DAL.Note();
            var nodeCount = noteitem.GetCountForTabs(item.Id, userId);

            var folowUpParameter = ManageParameter.FindByKeyWord("FollowUpCategory");
            int linkedDocumentsCount = new LinkedDocument().ListCountByDocumentId(item.Id, null, Convert.ToInt64(folowUpParameter.Content));

            List<Attachment> rootAttachments = new Attachment().ListAttachmentsForTabs(item.Id, userId, StructureIds);

            if (item != null && ((item.CreatedByUserId == userId || haveBasketDocumentAccess) || (item.StatusId == (short)DocumentStatus.Draft || item.Transfer.Count == 0)))
            {
                bool _fullControl = false;
                if (item.CategoryId == Configuration.FollowUpCategory)
                {
                    Assignee _assignee = ManageAssignee.FindByDocumentIdUserId(item.Id, userId);
                    if (_assignee != null)
                    {
                        _fullControl = _assignee.FollowUpSecurity.Name.ToLower().Contains("full") ? true : false;
                    }
                }

                var categoryName = item.Category.Name;
                if (language == Language.AR)
                {
                    categoryName = item.Category.NameAr;
                }
                else if (language == Language.FR)
                {
                    categoryName = item.Category.NameFr;
                }
                var sendingEntity = new ValueText();
                if (item.SendingEntity != default(Structure))
                {
                    sendingEntity = new ValueText
                    {
                        Id = item.SendingEntity.Id,
                        Text = language == Language.AR ? !string.IsNullOrEmpty(item.SendingEntity.NameAr) ? item.SendingEntity.NameAr : item.SendingEntity.Name
                        : language == Language.FR ? !string.IsNullOrEmpty(item.SendingEntity.NameFr) ? item.SendingEntity.NameFr : item.SendingEntity.Name
                        : item.SendingEntity.Name
                    };
                }
                var classification = new ValueText();
                if (item.Classification != default(Classification))
                {
                    classification = new ValueText
                    {
                        Id = item.Classification.Id,
                        Text = language == Language.AR ? item.Classification.NameAr : language == Language.FR ? item.Classification.NameFr : item.Classification.Name
                    };
                }
                var documentType = new ValueText();
                if (item.DocumentType != default(DocumentType))
                {
                    documentType = new ValueText
                    {
                        Id = item.DocumentType.Id,
                        Text = language == Language.AR ? item.DocumentType.NameAr : language == Language.FR ? item.DocumentType.NameFr : item.DocumentType.Name
                    };
                }


                List<ReceivingEntityModel> receivers = new List<ReceivingEntityModel>();
                List<ReceivingEntityModel> receivingEntities = new List<ReceivingEntityModel>();

                foreach (var receiver in item.DocumentReceiverEntity)
                {
                    long targetId = receiver.StructureId.HasValue ? receiver.StructureId.Value : receiver.EntityGroupId.Value;
                    bool isEntityGroup = receiver.EntityGroupId.HasValue;
                    string text = string.Empty;
                    if (isEntityGroup)
                    {
                        text = language == Language.AR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameAr) ? receiver.EntityGroup.NameAr : receiver.EntityGroup.Name
                        : language == Language.FR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameFr) ? receiver.EntityGroup.NameFr : receiver.EntityGroup.Name
                        : receiver.EntityGroup.Name;
                    }
                    else
                    {
                        text = language == Language.AR ? !string.IsNullOrEmpty(receiver.Structure.NameAr) ? receiver.Structure.NameAr : receiver.Structure.Name
                       : language == Language.FR ? !string.IsNullOrEmpty(receiver.Structure.NameFr) ? receiver.Structure.NameFr : receiver.Structure.Name
                       : receiver.Structure.Name;
                    }

                    receivers.Add(new ReceivingEntityModel
                    {
                        Id = targetId,
                        IsEntityGroup = isEntityGroup
                    });

                    receivingEntities.Add(new ReceivingEntityModel
                    {
                        Id = targetId,
                        Text = text,
                        IsEntityGroup = isEntityGroup
                    });
                }
                var latestAttachmentVersion = (item.AttachmentId != null && item.AttachmentNavigation != null) ? await ManageAttachment.GetCurrentVersionNumber(item.AttachmentNavigation.StorageAttachmentId) : "";

                retValue = new DocumentDetailsModel
                {
                    Id = item.Id,
                    CategoryId = item.CategoryId,
                    CategoryName = categoryName,
                    ReferenceNumber = item.ReferenceNumber,
                    SenderPerson = item.SenderPerson,
                    ReceiverPerson = item.ReceiverPerson,
                    IsExternalSender = item.IsExternalSender,
                    IsExternalReceiver = item.IsExternalReceiver,
                    Subject = item.Subject,
                    Status = item.StatusId,
                    CustomAttributes = item.Category.CustomAttribute,
                    CustomAttributesTranslation = item.Category.CustomAttributeTranslation,
                    BasicAttributes = item.Category.BasicAttribute,
                    FormData = item.DocumentForm != null ? item.DocumentForm.Form : string.Empty,
                    ClassificationId = item.ClassificationId,
                    Classification = classification,
                    DocumentTypeId = item.DocumentTypeId,
                    DocumentType = documentType,
                    DueDate = item.DueDate.HasValue ? item.DueDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty,
                    ImportanceId = item.ImportanceId,
                    PriorityId = item.PriorityId,
                    PrivacyId = item.PrivacyId,
                    SendingEntityId = item.SendingEntityId,
                    SendingEntity = sendingEntity,
                    Receivers = receivers,
                    ReceivingEntities = receivingEntities,
                    CarbonCopy = item.DocumentCarbonCopy.Count > 0 ? item.DocumentCarbonCopy.Select(x => x.StructureId).ToList() : new List<long>(),
                    CarbonCopies = item.DocumentCarbonCopy.Count > 0 ? item.DocumentCarbonCopy.Where(x => x.Structure != default(Structure)).Select(x => new ValueText
                    {
                        Id = x.Structure.Id,
                        Text = language == Language.AR ? !String.IsNullOrEmpty(x.Structure.NameAr) ? x.Structure.NameAr : x.Structure.Name
                               : language == Language.FR ? !String.IsNullOrEmpty(x.Structure.NameFr) ? x.Structure.NameFr : x.Structure.Name
                               : x.Structure.Name
                    }).ToList() : new List<ValueText>(),
                    CreatedByUser = item.CreatedByUser == null ? String.Empty :
                        (language == Language.EN ?
                        $"{item.CreatedByUser?.Firstname} {item.CreatedByUser?.Lastname}" :
                        $"{IdentityHelperExtension.GetFullName(item.CreatedByUserId, language)}"),
                    CreatedByStructureId = item.CreatedByStructureId,
                    AttachmentId = item.AttachmentId,
                    Body = item.DocumentForm != null ? item.DocumentForm.Body : string.Empty,
                    ExternalReferenceNumber = item.ExternalReferenceNumber,
                    Keyword = item.DocumentForm != null ? item.DocumentForm.Keyword : string.Empty,
                    CreatedByUserId = item.CreatedByUserId,
                    FullControl = _fullControl,
                    IsTaskCreator = item.CreatedByUserId == userId,
                    AttachmentCount = rootAttachments.Count,
                    NotesCount = nodeCount,
                    LinkedCorrespondanceCount = linkedDocumentsCount,
                    AttachmentVersion = latestAttachmentVersion,
                };
            }
            return retValue;
        }

        /// <summary>
        /// Get document
        /// Must be created by user or have access on any document transfer
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="id"></param>
        /// <param name="delegationId"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public async static Task<DocumentDetailsModel> GetSearchDocument(long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long id, long? delegationId = null, long? parentDocumentId = null, Language language = Language.EN, bool overrideAccess = false)
        {
            DocumentDetailsModel retValue = null;
            Document item = new Document().FindIncludeAll(id);
            DAL.Note noteitem = new DAL.Note();
            var nodeCount = noteitem.GetCountForTabs(item.Id, userId);
            //var linkedDocuments = await ManageLinkedDocument.List(item.Id, transfer.Id, userId, 1, transfer.FromStructureId, structureIds, isStructureReceiver, privacyLevel, delegationId);
            // List<LinkedDocument> linkedDocuments = new LinkedDocument().ListByDocumentId(item.Id, null);
            //var linkedDocuments =  ManageLinkedDocument.LinkedCount(item.Id, transfer.Id, userId, roleId.Value, transfer.FromStructureId, structureIds, isStructureReceiver, privacyLevel, delegationId);

            var folowUpParameter = ManageParameter.FindByKeyWord("FollowUpCategory");
            int linkedDocumentsCount = new LinkedDocument().ListCountByDocumentId(item.Id, null, Convert.ToInt64(folowUpParameter.Content));


            List<Attachment> rootAttachments = new Attachment().ListAttachmentsForTabs(item.Id, userId, structureIds);

            var accessCondition = item != null && (ManageUserAccess.HaveAccess(id, userId, structureIds, isStructureReceiver, privacyLevel, delegationId)
                    || (item.LinkedDocumentDocument.Count > 0 && parentDocumentId != null && ManageUserAccess.HaveAccess(parentDocumentId.Value, userId, structureIds, isStructureReceiver, privacyLevel, delegationId))
                    || (item.LinkedDocumentLinkedDocumentNavigation.Count > 0 && parentDocumentId != null && ManageUserAccess.HaveAccess(parentDocumentId.Value, userId, structureIds, isStructureReceiver, privacyLevel, delegationId)));

            if (accessCondition || overrideAccess)
            {
                var categoryName = item.Category.Name;
                if (language == Language.AR)
                {
                    categoryName = item.Category.NameAr;
                }
                else if (language == Language.FR)
                {
                    categoryName = item.Category.NameFr;
                }
                var sendingEntity = new ValueText();
                if (item.SendingEntity != default(Structure))
                {
                    sendingEntity = new ValueText
                    {
                        Id = item.SendingEntity.Id,
                        Text = (language == Language.AR ? item.SendingEntity.NameAr : language == Language.FR ? item.SendingEntity.NameFr : item.SendingEntity.Name)?? item.SendingEntity.Name
                    };
                }
                var classification = new ValueText();
                if (item.Classification != default(Classification))
                {
                    classification = new ValueText
                    {
                        Id = item.Classification.Id,
                        Text = language == Language.AR ? item.Classification.NameAr : language == Language.FR ? item.Classification.NameFr : item.Classification.Name
                    };
                }
                var documentType = new ValueText();
                if (item.DocumentType != default(DocumentType))
                {
                    documentType = new ValueText
                    {
                        Id = item.DocumentType.Id,
                        Text = language == Language.AR ? item.DocumentType.NameAr : language == Language.FR ? item.DocumentType.NameFr : item.DocumentType.Name
                    };
                }

                List<ReceivingEntityModel> receivers = new List<ReceivingEntityModel>();
                List<ReceivingEntityModel> receivingEntities = new List<ReceivingEntityModel>();

                foreach (var receiver in item.DocumentReceiverEntity)
                {
                    long targetId = receiver.StructureId.HasValue ? receiver.StructureId.Value : receiver.EntityGroupId.Value;
                    bool isEntityGroup = receiver.EntityGroupId.HasValue;
                    string text = string.Empty;
                    if (isEntityGroup)
                    {
                        text = language == Language.AR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameAr) ? receiver.EntityGroup.NameAr : receiver.EntityGroup.Name
                        : language == Language.FR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameFr) ? receiver.EntityGroup.NameFr : receiver.EntityGroup.Name
                        : receiver.EntityGroup.Name;
                    }
                    else
                    {
                        text = language == Language.AR ? !string.IsNullOrEmpty(receiver.Structure.NameAr) ? receiver.Structure.NameAr : receiver.Structure.Name
                       : language == Language.FR ? !string.IsNullOrEmpty(receiver.Structure.NameFr) ? receiver.Structure.NameFr : receiver.Structure.Name
                       : receiver.Structure.Name;
                    }

                    receivers.Add(new ReceivingEntityModel
                    {
                        Id = targetId,
                        IsEntityGroup = isEntityGroup
                    });

                    receivingEntities.Add(new ReceivingEntityModel
                    {
                        Id = targetId,
                        Text = text,
                        IsEntityGroup = isEntityGroup
                    });
                }
                var latestAttachmentVersion = (item.AttachmentId != null && item.AttachmentNavigation!=null) ? await ManageAttachment.GetCurrentVersionNumber(item.AttachmentNavigation.StorageAttachmentId) : "";

                retValue = new DocumentDetailsModel
                {
                    Id = item.Id,
                    CategoryId = item.CategoryId,
                    SenderPerson = item.SenderPerson,
                    ReceiverPerson = item.ReceiverPerson,
                    IsExternalReceiver = item.IsExternalReceiver,
                    IsExternalSender = item.IsExternalSender,
                    CategoryName = categoryName,
                    ReferenceNumber = item.ReferenceNumber,
                    Subject = item.Subject,
                    Status = item.StatusId,
                    CustomAttributes = item.Category.CustomAttribute,
                    CustomAttributesTranslation = item.Category.CustomAttributeTranslation,
                    BasicAttributes = item.Category.BasicAttribute,
                    FormData = item.DocumentForm != null ? item.DocumentForm.Form : string.Empty,
                    ClassificationId = item.ClassificationId,
                    Classification = classification,
                    DocumentTypeId = item.DocumentTypeId,
                    DocumentType = documentType,
                    DueDate = item.DueDate.HasValue ? item.DueDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty,
                    //DocumentDate = item.DocumentDate.HasValue ? item.DocumentDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty,
                    ImportanceId = item.ImportanceId,
                    PriorityId = item.PriorityId,
                    PrivacyId = item.PrivacyId,
                    SendingEntityId = item.SendingEntityId,
                    SendingEntity = sendingEntity,
                    Receivers = receivers,
                    ReceivingEntities = receivingEntities,
                    CarbonCopy = item.DocumentCarbonCopy.Count > 0 ? item.DocumentCarbonCopy.Select(x => x.StructureId).ToList() : new List<long>(),
                    CarbonCopies = item.DocumentCarbonCopy.Count > 0 ? item.DocumentCarbonCopy.Where(x => x.Structure != default(Structure)).Select(x => new ValueText
                    {
                        Id = x.Structure.Id,
                        Text = language == Language.AR ? !String.IsNullOrEmpty(x.Structure.NameAr) ? x.Structure.NameAr : x.Structure.Name
                               : language == Language.FR ? !String.IsNullOrEmpty(x.Structure.NameFr) ? x.Structure.NameFr : x.Structure.Name
                               : x.Structure.Name
                    }).ToList() : new List<ValueText>(),
                    CreatedByUser = item.CreatedByUser == null ? String.Empty :
                        (language == Language.EN ?
                        $"{item.CreatedByUser?.Firstname} {item.CreatedByUser?.Lastname}" :
                        $"{IdentityHelperExtension.GetFullName(item.CreatedByUserId, language)}"),
                    CreatedByStructureId = item.CreatedByStructureId,
                    AttachmentId = item.AttachmentId,
                    Body = item.DocumentForm != null ? item.DocumentForm.Body : string.Empty,
                    ExternalReferenceNumber = item.ExternalReferenceNumber,
                    Keyword = item.DocumentForm != null ? item.DocumentForm.Keyword : string.Empty,
                    AttachmentCount = rootAttachments.Count,
                    NotesCount = nodeCount,
                    LinkedCorrespondanceCount = linkedDocumentsCount,
                    G2GInternalId = item.G2GDocumentId,
                    AttachmentVersion = latestAttachmentVersion,

                };
            }
            return retValue;
        }

        /// <summary>
        /// Get document metadata by transferId
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="id"></param>
        /// <param name="delegationId"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static async Task<DocumentDetailsModel> FindDocumentBasicInfoByTransferId(long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long id, long? delegationId = null, Language language = Language.EN, bool overrideAccess = false, short? nodeId = null)
        {
            var retValue = new DocumentDetailsModel();

            var transfer = await new Transfer().FindIncludeDocumentCategoryUserAsync(id);
            bool hasNodeAccess = false;
            if(nodeId.HasValue)
            {
                hasNodeAccess = new Intalio.Core.DAL.Node().HasAccess(nodeId.Value, userId);
            }
            //add role condition
            if (transfer != null && (await ManageUserAccess.HaveTransferAccessAsync(id, userId, structureIds, isStructureReceiver, privacyLevel, delegationId) || overrideAccess == true || hasNodeAccess))
            {
                var item = transfer.Document;
                //var attachmentNodes = await ManageAttachment.List(item.Id,transfer.Id, userId, 1, transfer.FromStructureId, structureIds, isStructureReceiver, privacyLevel, delegationId);
                DAL.Note noteitem = new DAL.Note();
                var nodeCount = await noteitem.GetCount(item.Id, userId);


                var folowUpParameter = ManageParameter.FindByKeyWord("FollowUpCategory");
                int linkedDocumentsCount = new LinkedDocument().ListCountByDocumentId(item.Id, null, Convert.ToInt64(folowUpParameter.Content));

                List<Attachment> rootAttachments = await new Attachment().ListAttachmentsAsyncForTabs(item.Id, userId, structureIds);


                var categoryName = item.Category.Name;
                if (language == Language.AR)
                {
                    categoryName = item.Category.NameAr;
                }
                else if (language == Language.FR)
                {
                    categoryName = item.Category.NameFr;
                }

                bool _fullControl = false;
                if (item.CategoryId == Configuration.FollowUpCategory)
                {
                    Assignee _assignee = ManageAssignee.FindByDocumentIdUserId(item.Id, userId);
                    if (_assignee != null)
                    {
                        _fullControl = _assignee.FollowUpSecurity.Name.ToLower().Contains("full") ? true : false;
                    }
                }

                var latestAttachmentVersion = (item.AttachmentId != null && item.AttachmentNavigation != null) ? await ManageAttachment.GetCurrentVersionNumber(item.AttachmentNavigation.StorageAttachmentId) : "";
                retValue = new DocumentDetailsModel
                {
                    Id = item.Id,
                    CategoryName = categoryName,
                    CategoryId = item.CategoryId,
                    ReferenceNumber = item.ReferenceNumber,
                    Status = item.StatusId,
                    CreatedByUser =
                    item.CreatedByUser == null ? String.Empty :
                        (language == Language.EN ?
                        $"{item.CreatedByUser?.Firstname} {item.CreatedByUser?.Lastname}" :
                        $"{IdentityHelperExtension.GetFullName(item.CreatedByUserId, language)}"),
                    CreatedByStructureId = item.CreatedByStructureId,
                    AttachmentId = item.AttachmentId,
                    AttachmentVersion = latestAttachmentVersion,
                    AssigneeOperationPermission = item.CreatedByUserId == userId ? true : false,
                    FullControl = _fullControl,
                    CreatedByUserId = item.CreatedByUserId,
                    AttachmentCount = rootAttachments.Count,
                    NotesCount = nodeCount,
                    LinkedCorrespondanceCount = linkedDocumentsCount,
                    RequestStatus = transfer.RequestStatus,
                    Subject = item.Subject,
                    AttachmentIslocked = item.AttachmentNavigation?.DocumentLockId.HasValue ?? false
                };
            }
            return retValue;
        }

        /// <summary>
        /// Get document metadata
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="id"></param>
        /// <param name="delegationId"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static DocumentDetailsModel FindDocumentBasicInfo(long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long id, long? delegationId = null, Language language = Language.EN)
        {
            var retValue = new DocumentDetailsModel();
            var document = new Document().FindIncludeCategoryUser(id);
            if (document != null && ManageUserAccess.HaveAccess(id, userId, structureIds, isStructureReceiver, privacyLevel, delegationId))
            {
                var item = document;
                var categoryName = item.Category.Name;
                if (language == Language.AR)
                {
                    categoryName = item.Category.NameAr;
                }
                else if (language == Language.FR)
                {
                    categoryName = item.Category.NameFr;
                }
                retValue = new DocumentDetailsModel
                {
                    Id = item.Id,
                    CategoryName = categoryName,
                    CategoryId = item.CategoryId,
                    ReferenceNumber = item.ReferenceNumber,
                    Status = item.StatusId,
                    CreatedByUser = item.CreatedByUser == null ? String.Empty :
                    (language == Language.EN ?
                    $"{item.CreatedByUser?.Firstname} {item.CreatedByUser?.Lastname}" :
                    $"{IdentityHelperExtension.GetFullName(item.CreatedByUserId, language)}"),
                    CreatedByStructureId = item.CreatedByStructureId
                };
            }
            return retValue;
        }

        /// <summary>
        /// Get document tracking data.
        /// Including document transfers
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="documentId"></param>
        /// <param name="delegationId"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static async Task<List<TrackingModel>> GetTrackingData(
           long userId,
           List<long> structureIds,
           bool isStructureReceiver,
           short privacyLevel,
           long documentId,
           int roleId,
           long? delegationId = null,
           Language language = Language.EN)
        {
            var retValue = new List<TrackingModel>();
            var document = await new Document().FindIncludeTransfersPrioritiesAndPrioritiesAsync(documentId);
            if (document == null) return retValue;

            var ignoredTransfers = new List<IgnoredTransfers>();
            var PrivateStructures = new Structure().ListPrivateStructures();
            var transfers = document.Transfer.ToList();

            if (!await ManageUserAccess.HaveAccessAsync(documentId, userId, structureIds, isStructureReceiver, privacyLevel, delegationId))
                return retValue;

            var categoryName = language switch
            {
                Language.AR => document.Category.NameAr,
                Language.FR => document.Category.NameFr,
                _ => document.Category.Name
            };

            var sendingEntity = document.SendingEntity != null ? language switch
            {
                Language.AR when !string.IsNullOrEmpty(document.SendingEntity.NameAr) => document.SendingEntity.NameAr,
                Language.FR when !string.IsNullOrEmpty(document.SendingEntity.NameFr) => document.SendingEntity.NameFr,
                _ => document.SendingEntity.Name
            } : string.Empty;

            var priority = document.Priority != null ? language switch
            {
                Language.AR when !string.IsNullOrEmpty(document.Priority.NameAr) => document.Priority.NameAr,
                Language.FR when!string.IsNullOrEmpty(document.Priority.NameFr) => document.Priority.NameFr,
                _ => document.Priority.Name
            } : string.Empty;

            var privacy = document.Privacy != null ? language switch
            {
                Language.AR when !string.IsNullOrEmpty(document.Privacy.NameAr) => document.Privacy.NameAr,
                Language.FR when !string.IsNullOrEmpty(document.Privacy.NameFr) => document.Privacy.NameFr,
                _ => document.Privacy.Name
            } : string.Empty;

            var receivingEntity = string.Join(Constants.SEPARATOR, document.DocumentReceiverEntity.Select(receiver =>
            {
                var text = receiver.EntityGroupId.HasValue
                    ? language switch
                    {
                        Language.AR => receiver.EntityGroup.NameAr ?? receiver.EntityGroup.Name,
                        Language.FR => receiver.EntityGroup.NameFr ?? receiver.EntityGroup.Name,
                        _ => receiver.EntityGroup.Name
                    }
                    : language switch
                    {
                        Language.AR => receiver.Structure.NameAr ?? receiver.Structure.Name,
                        Language.FR => receiver.Structure.NameFr ?? receiver.Structure.Name,
                        _ => receiver.Structure.Name
                    };
                return text;
            }));
            var trackingModel = new TrackingModel
            {
                Id = $"document_{document.Id}",
                ParentId = null,
                Category = categoryName,
                ReferenceNumber = document.ReferenceNumber,
                CreatedDate = document.CreatedDate.ToString(Constants.DATE_FORMAT),
                CreatedBy = language == Language.EN ?
                $"{document.CreatedByUser.Firstname} {document.CreatedByUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(document.CreatedByUserId, language)}",
                IsRoot = true,
                ReceivingEntity = receivingEntity,
                SendingEntity = sendingEntity,
                Subject = document.Subject,
                Priority = priority,
                Privacy = privacy,
            };
            retValue.Add(trackingModel);

            foreach (var transfer in transfers)
            {
                var isTransferBetweenDifferentStructures = transfer.FromStructureId != transfer.ToStructureId;
                var isUserInStructure = structureIds[0] == transfer.ToStructureId.Value || structureIds[0] == transfer.FromStructureId.Value;

                var transferPrivateStructure = ManageStructure.GetClosestPrivateStructure(transfer.ToStructureId.Value);
                var isStructurePublic = transferPrivateStructure == null;
                isUserInStructure = !isStructurePublic && ManageStructure.IsParentToStructure(transferPrivateStructure.Id, structureIds[0]);

                if (isStructurePublic || isTransferBetweenDifferentStructures ||
                    (!isTransferBetweenDifferentStructures && isUserInStructure))
                {
                    var isOverdue = transfer.DueDate.HasValue && (transfer.ClosedDate.HasValue
                        ? transfer.ClosedDate.Value > transfer.DueDate.Value
                        : DateTime.Now > transfer.DueDate.Value);

                    var trackingItem = new TrackingModel
                    {
                        Id = $"transfer_{transfer.Id}",
                        ParentId = transfer.ParentTransferId.HasValue
                            ? $"transfer_{transfer.ParentTransferId}"
                            : $"document_{document.Id}",
                        Category = document.Subject,
                        TransferDate = transfer.CreatedDate.ToString(Constants.DATE_FORMAT),
                        UserId = transfer.ToUserId,
                        StructureId = transfer.ToStructureId,
                        IsRead = transfer.OpenedDate.HasValue,
                        OwnerDelegatedUserId = transfer.OwnerDelegatedUserId,
                        OwnerUserId = transfer.OwnerUserId,
                        IsLocked = transfer.OwnerUserId.HasValue,
                        IsOverDue = isOverdue,
                        IsClosed = transfer.ClosedDate.HasValue,
                        OpenedDate = transfer.OpenedDate?.ToString(Constants.DATETIME_FORMAT24),
                        DueDate = transfer.DueDate?.ToString(Constants.DATETIME_FORMAT24),
                        CreatedBy = language == Intalio.Core.Language.EN
                            ? $"{document.CreatedByUser.Firstname} {document.CreatedByUser.Lastname}"
                            : IdentityHelperExtension.GetFullName(document.CreatedByUserId, language),
                        Instruction = (userId != transfer.FromUserId && (transfer.ToUserId != null && userId != transfer.ToUserId) && transfer.PrivateInstruction == true) || (!structureIds.Contains((long)transfer.ToStructureId) && transfer.PrivateInstruction == true) ? null : transfer.Instruction,
                        IsCced = transfer.Cced
                    };

                    UpdateParentIdFromIgnoredTransfers(trackingItem, ignoredTransfers);

                    retValue.Add(trackingItem);
                }
                else
                {
                    UpdateIgnoredTransfers(retValue, transfer, ignoredTransfers, PrivateStructures, document);
                }
            }

            ManageActivityLog.AddFullActivityLog(documentId, null, (int)ActivityLogs.ViewVisualTracking, userId, "", "");

            return retValue;
        }

        private static void UpdateParentIdFromIgnoredTransfers(TrackingModel trackingItem, List<IgnoredTransfers> ignoredTransfers)
        {
            if (ignoredTransfers.Count <= 0) return;

            string currentId = trackingItem.ParentId;
            string lastParentId = currentId;
            while (true)
            {
                var parentItem = ignoredTransfers.FirstOrDefault(x => x.Id == currentId);
                if (parentItem == null) break;
                lastParentId = parentItem.ParentId;
                currentId = lastParentId;

                if (!ignoredTransfers.Any(x => x.Id == lastParentId)) break;
            }

            trackingItem.ParentId = lastParentId;
        }

        private static void UpdateIgnoredTransfers(List<TrackingModel> retValue, Transfer transfer, List<IgnoredTransfers> ignoredTransfers, List<Structure> PrivateStructures, Document document)
        {
            var lastItem = retValue.LastOrDefault();
            if (lastItem != null && lastItem.StructureId != transfer.ToStructureId)
            {
                var structureName = PrivateStructures.FirstOrDefault(s => s.Id == transfer.ToStructureId)?.Name ?? string.Empty;
                var trackingItem = new TrackingModel
                {
                    Id = $"transfer_{transfer.Id}",
                    ParentId = transfer.ParentTransferId.HasValue
                        ? $"transfer_{transfer.ParentTransferId}"
                        : $"document_{document.Id}",
                    Category = structureName,
                    TransferDate = transfer.CreatedDate.ToString(Constants.DATE_FORMAT),
                    CreatedBy = "----",
                    StructureId = transfer.ToStructureId,
                };

                UpdateParentIdFromIgnoredTransfers(trackingItem, ignoredTransfers);

                retValue.Add(trackingItem);
            }
            else
            {
                var igTransfers = new IgnoredTransfers
                {
                    Id = $"transfer_{transfer.Id}",
                    ParentId = transfer.ParentTransferId.HasValue
                        ? $"transfer_{transfer.ParentTransferId}"
                        : $"document_{document.Id}"
                };

                ignoredTransfers.Add(igTransfers);
            }
        }

        /// <summary>
        /// For Non Draft
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="id"></param>
        /// <param name="delegationId"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static async Task<DocumentViewModel> FindByTransferId(long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long id, bool allowEditSign = false, long? delegationId = null, Language language = Language.EN, int roleId = 0)
        {
            var currentUserId = userId;
            var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value) : null;
            if (delegation != null)
            {
                userId = delegation.FromUserId;
                structureIds = delegation.StructureIds;
                isStructureReceiver = delegation.IsStructureReceiver;
                privacyLevel = delegation.PrivacyLevel;
            }
            var retValue = new DocumentViewModel();
            var transfer = await new Transfer().FindIncludeDocumentDataAsync(id);
            bool isFollowUp = (transfer.Document.CategoryId == Configuration.FollowUpCategory);
            if ((transfer != null && await ManageUserAccess.HaveTransferAccessAsync(id, userId, structureIds, isStructureReceiver, privacyLevel)) || (isFollowUp && (int)Role.Administrator == roleId))
            {
                var item = transfer.Document;
                var categoryName = item.Category.Name;
                if (language == Language.AR)
                {
                    categoryName = item.Category.NameAr;
                }
                else if (language == Language.FR)
                {
                    categoryName = item.Category.NameFr;
                }

                bool _fullControl = false;
                if (item.CategoryId == Configuration.FollowUpCategory)
                {
                    Assignee _assignee = ManageAssignee.FindByDocumentIdUserId(item.Id, userId);
                    if (_assignee != null)
                    {
                        _fullControl = _assignee.FollowUpSecurity.Name.ToLower().Contains("full") ? true : false;
                    }
                }

                var sendingEntity = new ValueText();
                if (item.SendingEntity != default(Structure))
                {
                    sendingEntity = new ValueText
                    {
                        Id = item.SendingEntity.Id,
                        Text = language == Language.AR ? !string.IsNullOrEmpty(item.SendingEntity.NameAr) ? item.SendingEntity.NameAr : item.SendingEntity.Name
                        : language == Language.FR ? !string.IsNullOrEmpty(item.SendingEntity.NameFr) ? item.SendingEntity.NameFr : item.SendingEntity.Name
                        : item.SendingEntity.Name
                    };
                }
                var classification = new ValueText();
                if (item.Classification != default(Classification))
                {
                    classification = new ValueText
                    {
                        Id = item.Classification.Id,
                        Text = language == Language.AR ? item.Classification.NameAr : language == Language.FR ? item.Classification.NameFr : item.Classification.Name
                    };
                }
                var documentType = new ValueText();
                if (item.DocumentType != default(DocumentType))
                {
                    documentType = new ValueText
                    {
                        Id = item.DocumentType.Id,
                        Text = language == Language.AR ? item.DocumentType.NameAr : language == Language.FR ? item.DocumentType.NameFr : item.DocumentType.Name
                    };
                }

                List<ReceivingEntityModel> receivers = new List<ReceivingEntityModel>();
                List<ReceivingEntityModel> receivingEntities = new List<ReceivingEntityModel>();

                foreach (var receiver in item.DocumentReceiverEntity)
                {
                    long targetId = receiver.StructureId.HasValue ? receiver.StructureId.Value : receiver.EntityGroupId.Value;
                    bool isEntityGroup = receiver.EntityGroupId.HasValue;
                    string text = string.Empty;
                    string parent = string.Empty;
                    if (isEntityGroup)
                    {
                        text = language == Language.AR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameAr) ? receiver.EntityGroup.NameAr : receiver.EntityGroup.Name
                        : language == Language.FR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameFr) ? receiver.EntityGroup.NameFr : receiver.EntityGroup.Name
                        : receiver.EntityGroup.Name;
                    }
                    else
                    {
                        text = language == Language.AR ? !string.IsNullOrEmpty(receiver.Structure.NameAr) ? receiver.Structure.NameAr : receiver.Structure.Name
                       : language == Language.FR ? !string.IsNullOrEmpty(receiver.Structure.NameFr) ? receiver.Structure.NameFr : receiver.Structure.Name
                       : receiver.Structure.Name;
                        if (receiver.Structure.Parent != null)
                        {
                            parent = language == Language.AR ? receiver.Structure.Parent.NameAr != null ? receiver.Structure.Parent.NameAr : receiver.Structure.Parent.Name
                            : language == Language.FR ? receiver.Structure.Parent.NameFr != null ? receiver.Structure.Parent.NameFr : receiver.Structure.Parent.Name
                            : receiver.Structure.Parent.Name;
                        }
                    }

                    receivers.Add(new ReceivingEntityModel
                    {
                        Id = targetId,
                        IsEntityGroup = isEntityGroup
                    });

                    receivingEntities.Add(new ReceivingEntityModel
                    {
                        Id = targetId,
                        Text = text,
                        IsEntityGroup = isEntityGroup,
                        IsExternal = receiver.Structure.IsExternal,
                        ParentName = parent
                    });
                }

                long structureId = UserContextAccessor.UserContext.StructureId;

                var haveAccessStructureInbox = transfer.CheckHaveAccessStructureInboxAsync(null, item.Id, userId, structureIds, isStructureReceiver, privacyLevel, roleId, structureId);

                var documentTransfer = item.FindIncludeTransfers(item.Id);

                var latestAttachmentVersion = (item.AttachmentId != null && item.AttachmentNavigation != null) ? await ManageAttachment.GetCurrentVersionNumber(item.AttachmentNavigation.StorageAttachmentId) : "";

                retValue = new DocumentDetailsModel
                {
                    Id = item.Id,
                    instruction = transfer.Instruction,
                    CategoryId = item.CategoryId,
                    SenderPerson = item.SenderPerson,
                    ReceiverPerson = item.ReceiverPerson,
                    IsExternalReceiver = item.IsExternalReceiver,
                    IsExternalSender = item.IsExternalSender,
                    CategoryName = categoryName,
                    ReferenceNumber = item.ReferenceNumber,
                    Subject = item.Subject,
                    Status = item.StatusId,
                    CustomAttributes = item.Category.CustomAttribute,
                    CustomAttributesTranslation = item.Category.CustomAttributeTranslation,
                    BasicAttributes = item.Category.BasicAttribute,
                    FormData = item.DocumentForm != null ? item.DocumentForm.Form : string.Empty,
                    ClassificationId = item.ClassificationId,
                    Classification = classification,
                    DocumentTypeId = item.DocumentTypeId,
                    DocumentType = documentType,
                    DueDate = item.DueDate.HasValue ? item.DueDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty,
                    CreatedDate = item.CreatedDate.ToString(Constants.DATE_FORMAT),
                    DocumentDate = item.DocumentDate?.ToString(Constants.DATE_FORMAT),
                    ImportanceId = item.ImportanceId,
                    PriorityId = item.PriorityId,
                    PrivacyId = item.PrivacyId,
                    SendingEntityId = item.SendingEntityId,
                    SendingEntity = sendingEntity,
                    Receivers = receivers,
                    ReceivingEntities = receivingEntities,
                    CarbonCopy = item.DocumentCarbonCopy.Count > 0 ? item.DocumentCarbonCopy.Select(x => x.StructureId).ToList() : new List<long>(),
                    CarbonCopies = item.DocumentCarbonCopy.Count > 0 ? item.DocumentCarbonCopy.Where(x => x.Structure != default(Structure)).Select(x => new ValueText
                    {
                        Id = x.Structure.Id,
                        Text = language == Language.AR ? !String.IsNullOrEmpty(x.Structure.NameAr) ? x.Structure.NameAr : x.Structure.Name
                               : language == Language.FR ? !String.IsNullOrEmpty(x.Structure.NameFr) ? x.Structure.NameFr : x.Structure.Name
                               : x.Structure.Name,
                        ParentName = (language == Language.AR ? !String.IsNullOrEmpty(x.Structure.Parent?.NameAr) ? x.Structure.Parent.NameAr : x.Structure.Parent?.Name
                               : language == Language.FR ? !String.IsNullOrEmpty(x.Structure.Parent?.NameFr) ? x.Structure.Parent.NameFr : x.Structure.Parent?.Name
                               : x.Structure.Parent?.Name) ?? ""
                    }).ToList() : new List<ValueText>(),
                    CreatedByUser =
                    item.CreatedByUser == null ? String.Empty :
                    (language == Language.EN ?
                    $"{item.CreatedByUser?.Firstname} {item.CreatedByUser?.Lastname}" :
                    $"{IdentityHelperExtension.GetFullName(item.CreatedByUserId, language)}"),
                    CreatedByStructureId = item.CreatedByStructureId,
                    Body = item.DocumentForm != null ? item.DocumentForm.Body : string.Empty,
                    ExternalReferenceNumber = item.ExternalReferenceNumber,
                    Keyword = item.DocumentForm != null ? item.DocumentForm.Keyword : string.Empty,
                    EnableEdit = Configuration.EnableAttributeEdit && !item.ClosedDate.HasValue && !transfer.Cced
                                    && (transfer.ToUserId == currentUserId || transfer.OwnerUserId == currentUserId || transfer.OwnerDelegatedUserId == currentUserId || (haveAccessStructureInbox && (!transfer.OwnerUserId.HasValue || (transfer.OwnerUserId.HasValue && transfer.OwnerUserId == currentUserId))))
                                    && (allowEditSign ? true : !(item.IsSigned ?? false)),
                    FullControl = _fullControl,
                    CreatedByUserId = item.CreatedByUserId,
                    RequestStatus = transfer.RequestStatus,
                    FromAccept = documentTransfer.Transfer.Any(x => x.RequestStatus != 0),
                    AttachmentVersion = latestAttachmentVersion
                };
                ManageActivityLog.AddFullActivityLog(transfer.DocumentId, transfer.Id, (int)ActivityLogs.ViewDocument, userId, "", "");
                if (transfer.StatusId == 3)
                    retValue.EnableEdit = false;
            }
            return retValue;
        }

        /// <summary>
        /// Generate barcode
        /// </summary>
        /// <param name="documentId"></param>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="delegationId"></param>
        /// <returns></returns>
        public static byte[] GenerateBarcode(int documentId, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long? delegationId = null)
        {
            byte[] retValue = null;
            if (ManageUserAccess.HaveAccess(documentId, userId, structureIds, isStructureReceiver, privacyLevel, delegationId))
            {
                retValue = GenerateBarcode(documentId);
            }
            return retValue;
        }

        public static byte[] GenerateBarcode(long documentId, string referenceNumber = null)
        {
            byte[] retValue = null;

            Document document = new Document().FindIncludeCategoryCreatedByStructure(documentId);
            if (ManageCustomizationFile.FindByTypeId((byte)CustomizationFileType.Barcode) != null)
            {
                CategoryModel category = new CategoryModel
                {
                    Id = document.Category.Id,
                    Name = document.Category.Name,
                    NameAr = document.Category.NameAr,
                    NameFr = document.Category.NameFr
                };
                StructureViewModel structure = new StructureViewModel
                {
                    Id = document.CreatedByStructure.Id,
                    Code = document.CreatedByStructure.Code,
                    Name = document.CreatedByStructure.Name,
                    NameAr = document.CreatedByStructure.NameAr,
                    NameFr = document.CreatedByStructure.NameFr
                };
                Model.BarcodeModel barcodeModel = new Model.BarcodeModel
                {
                    DocumentId = documentId,
                    ReferenceNumber = document.ReferenceNumber,
                    CodeText = ManageCategoryReferenceCounter.GetReferenceNumberSequence(document.Category.Id),
                    CreatedByStructure = structure,
                    Category = category
                };
                retValue = ManageBarcode.GenerateCustomBarcode(barcodeModel);
            }
            if (retValue == null)
            {
                BarcodeConfigurationModel barcodeConfiguration = ManageBarcode.FindByCategoryId(document.Category.Id);
                if (barcodeConfiguration == null)
                {
                    barcodeConfiguration = ManageBarcode.GetDefaultConfiguration();
                }
                if (barcodeConfiguration != null)
                {
                    string captionAboveCreatedDate = document.CreatedDate.ToString(Constants.DATE_FORMAT);
                    if (Configuration.CalendarType != CalendarType.None)
                    {
                        captionAboveCreatedDate = Intalio.Core.Helper.ConvertGregorianToHijri(document.CreatedDate, Constants.DATE_FORMAT);
                    }
                    barcodeConfiguration.CaptionAboveStructureId = document.CreatedByStructure.Id;
                    barcodeConfiguration.CaptionBelowStructureId = document.CreatedByStructure.Id;
                    barcodeConfiguration.CaptionAboveCreatedDate = captionAboveCreatedDate;
                    barcodeConfiguration.CaptionBelowCreatedDate = captionAboveCreatedDate;
                    if (string.IsNullOrEmpty(referenceNumber))
                    {
                        retValue = ManageBarcode.GetBarcode(document.ReferenceNumber, barcodeConfiguration);
                    }
                    else
                    {
                        retValue = ManageBarcode.GetBarcode(referenceNumber, barcodeConfiguration);
                    }
                    
                }
            }

            return retValue;
        }

        /// <summary>
        /// Find document id by file id
        /// </summary>
        /// <param name="fileId"></param>
        /// <returns></returns>
        public static long FindDocumentIdByFileId(long fileId)
        {
            return (long)new Attachment().FindDocumentId(fileId);
        }

        /// <summary>
        /// Check document draft
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public static bool CheckDocumentDraft(long id)
        {
            return new Document().CheckDraft(id);
        }

        /// <summary>
        /// Check if original document is locked
        /// </summary>
        /// <param name="documentId"></param>
        /// <returns></returns>
        public static bool CheckOriginalDocumentLocked(long documentId)
        {
            return new Document().CheckOriginalDocumentLocked(documentId);
        }

        /// <summary>
        /// Check if original document is locked
        /// </summary>
        /// <param name="documentId"></param>
        /// <returns></returns>
        public static bool CheckHasLockedAttachmentsWithOriginal(long documentId)
        {
            return new Document().CheckHasLockedAttachmentsWithOriginal(documentId);
        }

        /// <summary>
        /// Update document status
        /// </summary>
        /// <param name="documentId"></param>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="status"></param>
        /// <param name="delegationId"></param>
        /// <returns></returns>
        public static bool UpdateDocumentStatus(long documentId, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, DocumentStatus status, long? delegationId = null)
        {
            bool retValue = false;
            if (ManageUserAccess.HaveAccess(documentId, userId, structureIds, isStructureReceiver, privacyLevel, delegationId))
            {
                Document document = new Document().Find(documentId);
                if (document != null && document.StatusId != (short)status)
                {
                    document.StatusId = (short)status;
                    document.UpdateStatus();
                    retValue = true;
                }
            }
            return retValue;
        }

        /// <summary>
        /// Check if document has an external document linked to it by external reference number
        /// </summary>
        /// <param name="id"></param>
        /// /// <param name="userId"></param>
        /// <returns></returns>
        public static bool HasExternalReferencedDocument(long id, long userId)
        {
            bool retValue = true;
            Document item = new Document().Find(id);
            if (item != null)
            {
                retValue = HasExternalReferencedDocument(item, userId);
            }
            return retValue;
        }

        /// <summary>
        /// Complete draft document
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="id"></param>
        /// <param name="purposeId"></param>
        /// <param name="visibleForStructureUsers"></param>
        /// <returns></returns>
        /// <remarks>
        /// Document will be completed and a closed transfer will be created and sent to document created by structure in case visibleForStructureUsers is <b>true</b>
        /// Document will be completed and a closed transfer will be created and sent to current user in case visibleForStructureUsers is <b>false</b>
        /// </remarks>
        public static (bool Success, string Message) Complete(long userId, long structureId, long id, short purposeId, List<long> toStructureIds, string completeReasonNote, bool visibleForStructureUsers = false)
        {
            bool retValue = false;
            Document item = new Document().FindIncludeCategory(id);
            if (item != null && (item.CreatedByUserId == userId || item.CreatedByStructureId == structureId ) && item.StatusId == (short)DocumentStatus.Draft)
            {
                if (!item.AttachmentId.HasValue)
                {
                    return (false, "LackOfOriginalFile");
                }
                else if (Configuration.AttachmentEditable)
                {
                    if (ManageAttachment.DocumentHasLockedAttachments(id))
                    {
                        return (false, "FileInUse");
                    }
                }
                else if (CheckOriginalDocumentLocked(id))
                {
                    return (false, "OriginalFileInUse");
                }

                if (string.IsNullOrEmpty(item.ReferenceNumber))
                {
                    Language language = Language.EN;
                    var returnedResult = new CategoryReferenceCounter().Generate(item.CategoryId, userId, item.CreatedByStructureId, item.Id, null, language);
                    item.ReferenceNumber = returnedResult.Reference;
                    item.ReferenceSequence = returnedResult.Sequence;
                    //to maintain reference sequence
                    item.UpdateReferenceNumber();
                    new EventReceivers().OnDocumentReferenceNumberGenerated(item.Id);
                }
                List<dynamic> categoryBasicAttribute = JsonConvert.DeserializeObject<List<dynamic>>(item.Category.BasicAttribute);
                dynamic receivingEntity = categoryBasicAttribute.Find(e => e.Name == "ReceivingEntity");
                string receivingEntityType = receivingEntity != null ? receivingEntity.Type : "";
                bool? isCategoryReceivingEntityExternal = receivingEntityType == "" ? null : receivingEntityType == "external" ? true : false;

                item.StatusId = (short)DocumentStatus.Completed;
                item.ClosedDate = DateTime.Now;
                if (isCategoryReceivingEntityExternal == true)
                {
                    var transfer = new Transfer
                    {
                        DocumentId = id,
                        StatusId = (short)DocumentStatus.Completed,
                        PurposeId = purposeId,
                        FromStructureId = item.CreatedByStructureId,
                        FromUserId = userId,
                        ToStructureId = item.CreatedByStructureId,
                        CreatedByUserId = userId,
                        Cced = false,
                        CreatedDate = DateTime.Now,
                        ClosedDate = DateTime.Now
                    };
                    if (!visibleForStructureUsers)
                    {
                        transfer.ToUserId = userId;
                    }
                    item.Transfer.Add(transfer);

                }
                else
                {
                    foreach (var toStructureId in toStructureIds)
                    {
                        var transfer = new Transfer
                        {
                            DocumentId = id,
                            StatusId = (short)DocumentStatus.Completed,
                            PurposeId = purposeId,
                            FromStructureId = item.CreatedByStructureId,
                            FromUserId = userId,
                            ToStructureId = toStructureId,
                            CreatedByUserId = userId,
                            Cced = false,
                            CreatedDate = DateTime.Now,
                            ClosedDate = DateTime.Now
                        };
                        item.Transfer.Add(transfer);
                    }

                    if (visibleForStructureUsers)
                    {
                        item.Transfer.Add(new Transfer
                        {
                            DocumentId = id,
                            StatusId = (short)DocumentStatus.Completed,
                            PurposeId = purposeId,
                            FromStructureId = item.CreatedByStructureId,
                            FromUserId = userId,
                            ToStructureId = item.CreatedByStructureId,
                            CreatedByUserId = userId,
                            Cced = false,
                            CreatedDate = DateTime.Now,
                            ClosedDate = DateTime.Now,

                        });

                    }

                }

                item.UpdateStatusCloseDateAndTransfer();
                foreach (var transfer in item.Transfer)
                {
                    ManageActivityLog.AddActivityLog(transfer.DocumentId.Value, transfer.Id, (int)ActivityLogs.Complete, userId, note: completeReasonNote);
                }
                retValue = true;

            }
            return (retValue, string.Empty);
        }

        /// <summary>
        /// Add document for reply by category
        /// </summary>
        /// <param name="model"></param>
        /// <param name="file"></param>
        /// <param name="prevDocId"></param>
        /// <param name="userId"></param>
        /// <param name="structureId"></param>
        /// <param name="roleId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="isStructureSender"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="AttachmentIds"></param>
        /// <returns></returns>
        public static async Task<bool> CreateReplyByCategory(DocumentViewModel model, FileViewModel file, long prevDocId, long userId, long structureId, int roleId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, bool isStructureSender, List<string> AttachmentIds, Language language)
        {

            bool retValue = false;
            if (!ManageCategory.CheckCategoryPermission(model.CategoryId, roleId))
            {
                return retValue;
            }
            var item = await CopyDocumentMetadataByCategory(model, userId, structureId, prevDocId, structureIds, isStructureReceiver, privacyLevel, AttachmentIds, language);
            if (model.TemplateId.HasValue)
            {
                item.ByTemplate = true;
                var template = ManageTemplate.FindById(model.TemplateId.Value);
                file = ManageTemplate.FindAttachmentDataByTemplateId(model.TemplateId.Value);
                item.TemplateHasSignature = template.HasSignature;
            }
            var structureIdsForProvision = new List<long>();
            structureIdsForProvision.Add(model.CreatedByStructureId);
            ManageStructure.Provision(structureIdsForProvision);
            item.UpdateIncludeDocumentForm();
            var attachmentFolders = ManageAttachmentFolder.ListByCategoryId(model.CategoryId);
            foreach (var attachmentFolder in attachmentFolders)
            {
                var folder = new Folder
                {
                    Name = attachmentFolder.Name,
                    PhysicalName = attachmentFolder.Name,
                    ParentId = attachmentFolder.ParentId,
                    DocumentId = item.Id,
                };
                folder.Insert();
                var childrenFolders = attachmentFolders.Where(x => x.ParentId == attachmentFolder.Id);
                if (childrenFolders.Any())
                {
                    foreach (var childrenFolder in childrenFolders)
                    {
                        childrenFolder.ParentId = (int)folder.Id;
                    }
                }
            }
            if (!file.IsNull() && !file.Data.IsNullOrEmpty())
            {
                var attachmentId = await ManageAttachment.Upload(item.Id, null, null, model.CategoryId, file, userId, structureIds, isStructureReceiver, privacyLevel);
                if (attachmentId.IsNull())
                {
                    item.Delete();
                    return retValue;
                }
                item.AttachmentId = attachmentId;
                item.UpdateAttachmentIdWithoutModifiedDate();
            }
            model.Id = item.Id;
            List<long> linkedDocumentsId = new List<long>();
            linkedDocumentsId.Add(prevDocId);
            ManageLinkedDocument.Create(userId, linkedDocumentsId, item.Id, null, null, structureIds, isStructureReceiver, privacyLevel);
            retValue = true;
            new EventReceivers().OnDocumentCreated(model, file);
            return retValue;
        }

        /// <summary>
        /// List non draft documents for vip mode.
        /// Must be created by the user
        /// </summary>
        /// <param name="startIndex"></param>
        /// <param name="userId"></param>
        /// <param name="filter"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static List<DocumentDraftListViewVipModel> ListMyRequestsVip(int startIndex, long userId, ExpressionBuilderFilters filter = null, Language language = Language.EN)
        {
            var filterExp = filter != null ? ExpressionBuilder.GetExpression<Document>(filter, ExpressionBuilderOperator.And) : null;
            var itemList = new Document().ListMyRequestsVip(startIndex, userId, filterExp);
            return itemList.Select(t =>
            {
                var sendingEntity = string.Empty;
                if (t.SendingEntity != null)
                {
                    sendingEntity = t.SendingEntity.Name;
                    if (language == Language.AR && !string.IsNullOrEmpty(t.SendingEntity.NameAr))
                    {
                        sendingEntity = t.SendingEntity.NameAr;
                    }
                    else if (language == Language.FR && t.SendingEntity.NameFr != null)
                    {
                        sendingEntity = t.SendingEntity.NameFr;
                    }
                }
                return new DocumentDraftListViewVipModel
                {
                    Id = t.Id,
                    Subject = t.Subject == null ? "" : t.Subject,
                    SendingEntity = sendingEntity,
                    CategoryId = t.CategoryId,
                    ImportanceId = t.ImportanceId,
                    ReferenceNumber = t.ReferenceNumber,
                    CreatedDate = t.CreatedDate.ToString(Constants.DATETIME_FORMAT24),
                    PriorityId = t.PriorityId,
                    PrivacyId = t.PrivacyId


                };
            }).ToList();
        }

        /// <summary>
        /// List closed documents for vip mode.
        /// Must be created by user or document transfer sent to the user or the user structures
        /// </summary>
        /// <param name="startIndex"></param>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="filter"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static List<DocumentDraftListViewVipModel> ListClosedVip(int startIndex, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel,
            ExpressionBuilderFilters filter = null, Language language = Language.EN)
        {
            var filterExp = filter != null ? ExpressionBuilder.GetExpression<Document>(filter, ExpressionBuilderOperator.And) : null;
            var itemList = new Document().ListClosedVip(startIndex, userId, structureIds, isStructureReceiver, privacyLevel, filterExp);
            return itemList.Select(t =>
            {
                var sendingEntity = string.Empty;
                if (t.SendingEntity != null)
                {
                    sendingEntity = t.SendingEntity.Name;
                    if (language == Language.AR && !string.IsNullOrEmpty(t.SendingEntity.NameAr))
                    {
                        sendingEntity = t.SendingEntity.NameAr;
                    }
                    else if (language == Language.FR && t.SendingEntity.NameFr != null)
                    {
                        sendingEntity = t.SendingEntity.NameFr;
                    }
                }
                return new DocumentDraftListViewVipModel
                {
                    Id = t.Id,
                    Subject = t.Subject == null ? "" : t.Subject,
                    SendingEntity = sendingEntity,
                    CategoryId = t.CategoryId,
                    ImportanceId = t.ImportanceId,
                    ReferenceNumber = t.ReferenceNumber,
                    CreatedDate = t.CreatedDate.ToString(Constants.DATETIME_FORMAT24),
                    PrivacyId = t.PrivacyId,
                    PriorityId = t.PriorityId
                };
            }).ToList();
        }

        /// <summary>
        /// List documents with draft status for vip mode.
        /// Must be created by the user
        /// </summary>
        /// <param name="startIndex"></param>
        /// <param name="userId"></param>
        /// <param name="filter"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static List<DocumentDraftListViewVipModel> ListDraftVip(int startIndex, long userId, ExpressionBuilderFilters filter = null, Language language = Language.EN, bool isStructureDraft = false, short PrivacyLevel = 0)
        {
            var filterExp = filter != null ? ExpressionBuilder.GetExpression<Document>(filter, ExpressionBuilderOperator.And) : null;
            var itemList = new Document().ListDraftVip(startIndex, userId, filterExp, isStructureDraft, PrivacyLevel);
            return itemList.Select(t =>
            {
                var sendingEntity = string.Empty;
                if (t.SendingEntity != null)
                {
                    sendingEntity = t.SendingEntity.Name;
                    if (language == Language.AR && !string.IsNullOrEmpty(t.SendingEntity.NameAr))
                    {
                        sendingEntity = t.SendingEntity.NameAr;
                    }
                    else if (language == Language.FR && t.SendingEntity.NameFr != null)
                    {
                        sendingEntity = t.SendingEntity.NameFr;
                    }
                }
                return new DocumentDraftListViewVipModel
                {
                    Id = t.Id,
                    Subject = t.Subject == null ? "" : t.Subject,
                    SendingEntity = sendingEntity,
                    CategoryId = t.CategoryId,
                    ImportanceId = t.ImportanceId,
                    ReferenceNumber = t.ReferenceNumber,
                    CreatedDate = t.CreatedDate.ToString(Constants.DATETIME_FORMAT24),
                    PriorityId = t.PriorityId,
                    PrivacyId = t.PrivacyId,
                    DraftStatusId = t.DraftStatus
                };
            }).ToList();
        }

        /// <summary>
        /// Must be created by user
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="id"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static DocumentDraftListViewVipModel GetDraftDocument(long userId, long id, Language language = Language.EN)
        {
            DocumentDraftListViewVipModel retValue = null;
            Document item = new Document().FindIncludeSendingEntity(id);
            if (item != null && item.CreatedByUserId == userId)
            {
                var sendingEntity = string.Empty;
                if (item.SendingEntity != null)
                {
                    sendingEntity = item.SendingEntity.Name;
                    if (language == Language.AR && !string.IsNullOrEmpty(item.SendingEntity.NameAr))
                    {
                        sendingEntity = item.SendingEntity.NameAr;
                    }
                    else if (language == Language.FR && !string.IsNullOrEmpty(item.SendingEntity.NameFr))
                    {
                        sendingEntity = item.SendingEntity.NameFr;
                    }
                }
                retValue = new DocumentDraftListViewVipModel
                {
                    Id = item.Id,
                    Subject = item.Subject,
                    SendingEntity = sendingEntity,
                    CategoryId = item.CategoryId,
                    ImportanceId = item.ImportanceId,
                    ReferenceNumber = item.ReferenceNumber,
                    CreatedDate = item.CreatedDate.ToString(Constants.DATETIME_FORMAT24)
                };
            }
            return retValue;
        }

        /// <summary>
        /// Update IsSigned property of target document by id
        /// </summary>
        /// <param name="id"></param>
        /// <param name="isSigned"></param>
        public static void UpdateDocumentIsSigned(long id, bool isSigned, string signedVersion = null)
        {
            new Document().UpdateDocumentIsSigned(id, isSigned, signedVersion);
        }

        /// <summary>
        /// Check if document is registered
        /// </summary>
        /// <param name="id"></param>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <returns></returns>
        public static async Task<bool> IsDocumentRegistered(long id, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel)
        {
            var retValue = false;
            if (ManageUserAccess.HaveAccess(id, userId, structureIds, isStructureReceiver, privacyLevel))
            {
                retValue = await new Document().IsRegistered(id);
            }
            return retValue;
        }

        /// <summary>
        /// Check if document is Completed
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public static bool IsDocumentCompleted(long id)
        {
            return new Document().IsCompleted(id);
        }
        public static async Task<(bool, bool)> IsOriginalDocumentSigned(long attachmentId, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel)
        {
            var retValue = false;
            var attachment = new Attachment().Find(attachmentId);
            var isPDF = attachment.Extension?.ToLower()?.EndsWith("pdf") ?? false;
            if (!isPDF)
                return (isPDF: false, isSigned: false);
            var id = attachment.DocumentId.Value;
            if (ManageUserAccess.HaveAccess(id, userId, structureIds, isStructureReceiver, privacyLevel))
            {
                retValue = await new Document().IsOriginalDocumentSigned(id, attachmentId);
            }
            return (isPDF: true, isSigned: retValue);
        }
        public async static Task<bool> IsDocumentHasSignatures(long fileId, string version)
        {
            string viewerUrl = $"{Configuration.ViewerUrl}/api/document/{fileId}/version/{version}/signature";
            var result = await Intalio.Core.Helper.HttpGetAsync<object>(viewerUrl, Configuration.IdentityAccessToken);
            if (result != null && ((Newtonsoft.Json.Linq.JContainer)result)?.Count > 0)
                return true;
            return false;

        }
        public static APIResponseViewModel ReturnToDraft(long id, long userId, int roleId, Language lang = Language.EN)
        {
            var response = new APIResponseViewModel { Message = "", Success = false };

            try
            {
                if (roleId != (int)Role.Administrator)
                {
                    response.Message = TranslationUtility.Translate("NoPermission", lang);
                    return response;
                }

                var document = new Document().FindIncludeTransfers(id);

                if (document == null)
                {
                    response.Message = TranslationUtility.Translate("RecordNotFound", lang);
                    return response;
                }

                if (document.Transfer == null)
                {
                    response.Message = TranslationUtility.Translate("ThisCorrespondenceDontHaveAnyTransfer", lang);
                    return response;
                }

                if (document.Transfer.Any(s => s.OpenedDate != null))
                {
                    response.Message = TranslationUtility.Translate("TransferOpenedByUser", lang);
                    return response;
                }

                foreach (var transfer in document.Transfer)
                {
                    new Attachment().ResetTransferToDelete(transfer.Id);
                    ManageActivityLog.AddActivityLog(id, transfer.Id, (int)ActivityLogs.ReturnToDraft, userId);
                    transfer.Delete();
                }

                UpdateDocumentStatusById(id, DocumentStatus.Draft);
                response.Success = true;
            }
            catch (Exception ex)
            {
                response.Message = string.IsNullOrWhiteSpace(ex.Message)
                    ? ex.InnerException?.Message
                    : ex.Message;
            }

            return response;
        }

        public async static Task<bool> AddDocumnetAttachments(Document document, Document newDocument, long rootFolderId, DocumentViewModel model, long userId, int roleId, List<long> structureIds, bool isStructureReceiver, short privacyLevel)
        {

            var DocumentFolder = new Folder
            {
                Name = document.ReferenceNumber + " " + document.Subject,
                PhysicalName = document.ReferenceNumber + " " + document.Subject,
                ParentId = rootFolderId,
                DocumentId = newDocument.Id,
            };
            DocumentFolder.Insert();

            // copy all folders with attachments
            var Folders = new Folder().List(document.Id);
            foreach (var attachmentFolder in Folders)
            {
                var folder = new Folder
                {
                    Name = attachmentFolder.Name,
                    PhysicalName = attachmentFolder.Name,
                    ParentId = attachmentFolder.ParentId ?? DocumentFolder.Id,
                    DocumentId = newDocument.Id,
                };
                folder.Insert();
                var childrenFolders = Folders.Where(x => x.ParentId == attachmentFolder.Id);
                if (childrenFolders.Any())
                {
                    foreach (var childrenFolder in childrenFolders)
                    {
                        childrenFolder.ParentId = (int)folder.Id;
                    }
                }
                var attAttachmentsInFolder = document.Attachment.Where(x => x.FolderId == attachmentFolder.Id);

                foreach (var attachment in attAttachmentsInFolder)
                {

                    var storageAttachmentModel = await ManageAttachment.GetStorageAttachmentModel(attachment.StorageAttachmentId);
                    FileViewModel fileAttachment = new FileViewModel();
                    if (storageAttachmentModel.Data?.Length > 0)
                    {
                        fileAttachment.Name = Intalio.Core.Helper.GetFileName(storageAttachmentModel.Name);
                        fileAttachment.FileSize = Convert.ToInt64(storageAttachmentModel.FileSize);
                        fileAttachment.ContentType = storageAttachmentModel.ContentType;
                        fileAttachment.Extension = storageAttachmentModel.Extension;
                        fileAttachment.Data = storageAttachmentModel.Data;
                    }
                    var attachmentId = await ManageAttachment.Upload(newDocument.Id, null, folder.Id, model.CategoryId, fileAttachment, userId, structureIds, isStructureReceiver, privacyLevel);
                    if (attachmentId.IsNull())
                    {
                        newDocument.Delete();
                        return false;
                    }
                }
            }
            //////////////////////////////////

            //copy root attachments
            var rootFiles = document.Attachment.Where(x => !x.FolderId.HasValue);
            foreach (var rootFile in rootFiles)
            {
                long subFolderId = DocumentFolder.Id;

                if (rootFile.Id == document.AttachmentId)
                {

                    var OrginalFolder = new Folder
                    {
                        Name = "Orginal document",
                        PhysicalName = "Orginal document",
                        ParentId = DocumentFolder.Id,
                        DocumentId = newDocument.Id,
                    };
                    OrginalFolder.Insert();

                    subFolderId = OrginalFolder.Id;
                }
                var storageAttachmentModel = await ManageAttachment.GetStorageAttachmentModel(rootFile.StorageAttachmentId);
                FileViewModel fileAttachment = new FileViewModel();
                if (storageAttachmentModel.Data?.Length > 0)
                {
                    fileAttachment.Name = Intalio.Core.Helper.GetFileName(storageAttachmentModel.Name);
                    fileAttachment.FileSize = Convert.ToInt64(storageAttachmentModel.FileSize);
                    fileAttachment.ContentType = storageAttachmentModel.ContentType;
                    fileAttachment.Extension = storageAttachmentModel.Extension;
                    fileAttachment.Data = storageAttachmentModel.Data;
                }
                var attachmentId = await ManageAttachment.Upload(newDocument.Id, null, subFolderId, model.CategoryId, fileAttachment, userId, structureIds, isStructureReceiver, privacyLevel);
                if (attachmentId.IsNull())
                {
                    newDocument.Delete();
                    return false;
                }
            }

            return true;
        }

        public static List<AgendaTopicsList> FindDocumentCustomInfo(long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long id, long? delegationId = null, Language language = Language.EN)
        {
            var retValue = new List<AgendaTopicsList>();
            var document = new Document().FindIncludeDocumentForm(id);
            if (document != null && ManageUserAccess.HaveAccess(id, userId, structureIds, isStructureReceiver, privacyLevel, delegationId))
            {
                var customAttribute = JsonConvert.DeserializeObject<dynamic>(document.DocumentForm.Form);

                foreach (var item in customAttribute.agendaList)
                {
                    var attr = new AgendaTopicsList()
                    {
                        Id = item.id,
                        ReferenceNumber = item.referenceNumber,
                        Subject = item.subject,
                        Resolution = item.resolution,
                    };

                    retValue.Add(attr);
                }


            }
            return retValue;
        }


        public static bool DeleteDocumentCustomInfo(long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long id, List<long> documentIds, long? delegationId = null, Language language = Language.EN)
        {
            var retValue = false;
            var document = new Document().FindIncludeDocumentForm(id);
            if (document != null && ManageUserAccess.HaveAccess(id, userId, structureIds, isStructureReceiver, privacyLevel, delegationId))
            {
                var customAttribute = JsonConvert.DeserializeObject<dynamic>(document.DocumentForm.Form);
                //var x= customAttribute.agendaList.itemList.RemoveAll( (dynamic item) => documentIds.Contains(item.Id));

                for (int i = customAttribute.agendaList.Count - 1; i >= 0; i--)
                {
                    if (documentIds.Contains((long)customAttribute.agendaList[i].id))
                    {
                        customAttribute.agendaList.RemoveAt(i);
                    }
                }

                document.DocumentForm.Form = JsonConvert.SerializeObject(customAttribute);
                document.UpdateIncludeDocumentForm();
                retValue = true;
            }
            return retValue;
        }

        public static bool EditDocumentCustomInfo(long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long id, long documentId, string rsolution, long? delegationId = null, Language language = Language.EN)
        {
            var retValue = false;
            var document = new Document().FindIncludeDocumentForm(documentId);
            if (document != null && ManageUserAccess.HaveAccess(documentId, userId, structureIds, isStructureReceiver, privacyLevel, delegationId))
            {
                var customAttribute = JsonConvert.DeserializeObject<dynamic>(document.DocumentForm.Form);
                for (int i = customAttribute.agendaList.Count - 1; i >= 0; i--)
                {
                    if (id == (long)customAttribute.agendaList[i].id)
                    {
                        customAttribute.agendaList[i].resolution = rsolution;
                    }
                }

                document.DocumentForm.Form = JsonConvert.SerializeObject(customAttribute);
                document.UpdateIncludeDocumentForm();
                retValue = true;
            }
            return retValue;
        }


        /// <summary>
        /// Add document for reply by Meeting Resolution
        /// </summary>
        /// <param name="model"></param>
        /// <param name="file"></param>
        /// <param name="prevDocId"></param>
        /// <param name="userId"></param>
        /// <param name="structureId"></param>
        /// <param name="roleId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="isStructureSender"></param>
        /// <param name="privacyLevel"></param>
        /// <returns></returns>
        public static async Task<(bool Success, int State)> CreateMeetingResolution(DocumentViewModel model, FileViewModel file, long prevDocId, long userId, long structureId, int roleId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, bool isStructureSender)
        {

            var retValue = (false, 0);
            if (!ManageCategory.CheckCategoryPermission(model.CategoryId, roleId))
            {
                retValue = (true, 1);
                return retValue;
            }

            var previousDocument = new Document().FindIncludeDocumentForm(prevDocId);
            List<long> linkedDocumentsId = new List<long>();
            dynamic oldCustomAttributes = JsonConvert.DeserializeObject<Dictionary<string, dynamic>>(previousDocument.DocumentForm.Form);
            dynamic newCustomAttributes = new Dictionary<string, dynamic>();
            oldCustomAttributes.Add("decision", "");
            if (oldCustomAttributes.ContainsKey("agendaList"))
            {
                //newCustomAttributes.Add("agendaList", oldCustomAttributes["agendaList"]);
                foreach (var document in oldCustomAttributes["agendaList"])
                {
                    linkedDocumentsId.Add((long)document.id);
                }
            }

            // model.FormData = JsonConvert.SerializeObject(newCustomAttributes);
            model.FormData = JsonConvert.SerializeObject(oldCustomAttributes);
            //model.FormData = previousDocument.DocumentForm.Form;

            Document item = new Document
            {
                Subject = previousDocument.Subject,
                CategoryId = model.CategoryId,
                StatusId = (short)DocumentStatus.Draft,
                CreatedByUserId = userId,
                CreatedByStructureId = model.CreatedByStructureId,
                DocumentForm = new DocumentForm { Form = model.FormData }
            };
            item.Insert();

            if (model.TemplateId.HasValue)
            {
                item.ByTemplate = true;
                file = ManageTemplate.FindAttachmentDataByTemplateId(model.TemplateId.Value);
            }
            var structureIdsForProvision = new List<long>();
            structureIdsForProvision.Add(model.CreatedByStructureId);
            ManageStructure.Provision(structureIdsForProvision);

            var CatagoryFolders = ManageAttachmentFolder.ListByCategoryId(model.CategoryId);
            foreach (var attachmentFolder in CatagoryFolders)
            {
                var folder = new Folder
                {
                    Name = attachmentFolder.Name,
                    PhysicalName = attachmentFolder.Name,
                    ParentId = attachmentFolder.ParentId,
                    DocumentId = item.Id,
                };
                folder.Insert();

                var childrenFolders = CatagoryFolders.Where(x => x.ParentId == attachmentFolder.Id);
                if (childrenFolders.Any())
                {
                    foreach (var childrenFolder in childrenFolders)
                    {
                        childrenFolder.ParentId = (int)folder.Id;
                    }
                }
            }

            var Attachmentsfolder = new Folder
            {
                Name = "Attachments",
                PhysicalName = "Attachments",
                ParentId = null,
                DocumentId = item.Id,
            };
            Attachmentsfolder.Insert();

            // add all documnets Attachments to new documnet
            foreach (var documentId in linkedDocumentsId)
            {
                var Document = new Document().FindIncludeDocumentForm(documentId);
                await AddDocumnetAttachments(Document, item, Attachmentsfolder.Id, model, userId, roleId, structureIds, isStructureReceiver, privacyLevel);
            };
            if (!file.IsNull() && !file.Data.IsNullOrEmpty())
            {
                ManageBookmark.AddMeetingResolutionTables(file, linkedDocumentsId);
                var attachmentId = await ManageAttachment.Upload(item.Id, null, null, model.CategoryId, file, userId, structureIds, isStructureReceiver, privacyLevel);
                if (attachmentId.IsNull())
                {
                    item.Delete();
                    return retValue;
                }
                item.AttachmentId = attachmentId;
                item.UpdateAttachmentIdWithoutModifiedDate();
            }
            model.Id = item.Id;
            ManageLinkedDocument.Create(userId, linkedDocumentsId, item.Id, null, null, structureIds, isStructureReceiver, privacyLevel);
            retValue = (true, 0);
            new EventReceivers().OnDocumentCreated(model, file);
            return retValue;
        }

        /// <summary>
        /// Add document for reply by Meeting 
        /// </summary>
        /// <param name="model"></param>
        /// <param name="file"></param>
        /// <param name="SelectedDocumentIds"></param>
        /// <param name="userId"></param>
        /// <param name="structureId"></param>
        /// <param name="roleId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="isStructureSender"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="PreviousDocumentId"></param>
        /// <returns></returns>
        public static async Task<(bool Success, int State)> CreateMeeting(DocumentViewModel model, FileViewModel file, List<long> SelectedDocumentIds, long PreviousDocumentId, long userId, long structureId, int roleId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, bool isStructureSender)
        {

            var retValue = (false, 0);
            long sepResolutionId = long.Parse(ManageParameter.FindByKeyWord("SepResolutionId").Content);

            if (!ManageCategory.CheckCategoryPermission(model.CategoryId, roleId))
            {
                retValue = (true, 1);
                return retValue;
            }
            var selectedDocuments = new Document().ListByIds(SelectedDocumentIds);
            var previousDocument = new Document().FindIncludeDocumentForm(PreviousDocumentId);
            dynamic oldCustomAttributes = JsonConvert.DeserializeObject<Dictionary<string, dynamic>>(previousDocument.DocumentForm.Form);
            var agendaListString = JsonConvert.SerializeObject(oldCustomAttributes["agendaList"]);
            List<dynamic> agendaList = JsonConvert.DeserializeObject<List<dynamic>>(agendaListString);

            if (selectedDocuments.Count > 0)
            {

                var AgendaList = new List<object>();
                foreach (var document in selectedDocuments)
                {
                    var AgendaItem = new
                    {
                        id = document.Id,
                        subject = document.Subject,
                        referenceNumber = document.ReferenceNumber,
                        resolution = agendaList.FirstOrDefault(x => (long)x.id == document.Id).resolution
                    };

                    AgendaList.Add(AgendaItem);
                }
                var FromModel = new
                {
                    agendaList = AgendaList
                };

                model.FormData = JsonConvert.SerializeObject(FromModel);

                //meeting minutes 
                if (model.CategoryId != sepResolutionId)
                {
                    oldCustomAttributes["agendaList"] = AgendaList;
                    model.FormData = JsonConvert.SerializeObject(oldCustomAttributes);
                }


            }

            Document item = new Document
            {
                CategoryId = model.CategoryId,
                StatusId = (short)DocumentStatus.Draft,
                CreatedByUserId = userId,
                CreatedByStructureId = model.CreatedByStructureId,
                Subject = (sepResolutionId == model.CategoryId) ? "Resolution from " + previousDocument.ReferenceNumber : previousDocument.Subject,
                DocumentForm = new DocumentForm { Form = model.FormData }
            };
            item.Insert();

            if (model.TemplateId.HasValue)
            {
                item.ByTemplate = true;
                file = ManageTemplate.FindAttachmentDataByTemplateId(model.TemplateId.Value);
            }
            var structureIdsForProvision = new List<long>();
            structureIdsForProvision.Add(model.CreatedByStructureId);
            ManageStructure.Provision(structureIdsForProvision);

            var CatagoryFolders = ManageAttachmentFolder.ListByCategoryId(model.CategoryId);
            foreach (var attachmentFolder in CatagoryFolders)
            {
                var folder = new Folder
                {
                    Name = attachmentFolder.Name,
                    PhysicalName = attachmentFolder.Name,
                    ParentId = attachmentFolder.ParentId,
                    DocumentId = item.Id,
                };
                folder.Insert();

                var childrenFolders = CatagoryFolders.Where(x => x.ParentId == attachmentFolder.Id);
                if (childrenFolders.Any())
                {
                    foreach (var childrenFolder in childrenFolders)
                    {
                        childrenFolder.ParentId = (int)folder.Id;
                    }
                }
            }

            var Documentfolder = new Folder
            {
                Name = "Attachments",
                PhysicalName = "Attachments",
                ParentId = null,
                DocumentId = item.Id,
            };
            Documentfolder.Insert();
            // add all documnets Attachments to new documnet
            foreach (var documentId in SelectedDocumentIds)
            {
                var Document = new Document().FindIncludeDocumentForm(documentId);
                await AddDocumnetAttachments(Document, item, Documentfolder.Id, model, userId, roleId, structureIds, isStructureReceiver, privacyLevel);
            };
            if (!file.IsNull() && !file.Data.IsNullOrEmpty())
            {
                var attachmentId = await ManageAttachment.Upload(item.Id, null, null, model.CategoryId, file, userId, structureIds, isStructureReceiver, privacyLevel);
                if (attachmentId.IsNull())
                {
                    item.Delete();
                    return retValue;
                }
                item.AttachmentId = attachmentId;
                item.UpdateAttachmentIdWithoutModifiedDate();
            }
            model.Id = item.Id;
            ManageLinkedDocument.Create(userId, SelectedDocumentIds, item.Id, null, null, structureIds, isStructureReceiver, privacyLevel);
            retValue = (true, 0);
            new EventReceivers().OnDocumentCreated(model, file);
            return retValue;
        }


        /// <summary>
        /// Add document
        /// </summary>
        /// <param name="model"></param>
        /// <param name="file"></param>
        /// <param name="userId"></param>
        /// <param name="roleId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="SelectedDocumentIds"></param>
        /// <returns></returns>
        public static async Task<(bool Success, int State)> CreateMeetingAgenda(DocumentViewModel model, FileViewModel file, List<long> SelectedDocumentIds, long userId, int roleId, List<long> structureIds, bool isStructureReceiver, short privacyLevel)
        {

            var retValue = (false, 0);
            if (!ManageCategory.CheckCategoryPermission(model.CategoryId, roleId))
            {
                retValue = (true, 1);
                return retValue;
            }

            var selectedDocuments = new Document().ListByIds(SelectedDocumentIds);

            // fill the hidden attribut with corresondends 
            if (selectedDocuments.Count > 0)
            {

                var AgendaList = new List<object>();
                foreach (var document in selectedDocuments)
                {
                    var AgendaItem = new
                    {
                        id = document.Id,
                        subject = document.Subject,
                        referenceNumber = document.ReferenceNumber
                    };

                    AgendaList.Add(AgendaItem);
                }

                var FromModel = new
                {
                    agendaList = AgendaList
                };

                model.FormData = JsonConvert.SerializeObject(FromModel);
            }

            Document item = new Document
            {
                CategoryId = model.CategoryId,
                StatusId = (short)DocumentStatus.Draft,
                CreatedByUserId = userId,
                CreatedByStructureId = model.CreatedByStructureId,
                DocumentForm = new DocumentForm { Form = model.FormData }
            };
            if (model.TemplateId.HasValue)
            {
                item.ByTemplate = true;
                file = ManageTemplate.FindAttachmentDataByTemplateId(model.TemplateId.Value);
            }
            var structureIdsForProvision = new List<long>();
            structureIdsForProvision.Add(model.CreatedByStructureId);
            ManageStructure.Provision(structureIdsForProvision);
            item.Insert();


            //add linked Documnet
            ManageLinkedDocument.Create(userId, SelectedDocumentIds, item.Id, null, null, structureIds, isStructureReceiver, privacyLevel);


            var catagoryFolders = ManageAttachmentFolder.ListByCategoryId(model.CategoryId);
            foreach (var attachmentFolder in catagoryFolders)
            {
                var folder = new Folder
                {
                    Name = attachmentFolder.Name,
                    PhysicalName = attachmentFolder.Name,
                    ParentId = attachmentFolder.ParentId,
                    DocumentId = item.Id,
                };
                folder.Insert();
                var childrenFolders = catagoryFolders.Where(x => x.ParentId == attachmentFolder.Id);
                if (childrenFolders.Any())
                {
                    foreach (var childrenFolder in childrenFolders)
                    {
                        childrenFolder.ParentId = (int)folder.Id;
                    }
                }
            }

            var Documnetfolder = new Folder
            {
                Name = "Attachments",
                PhysicalName = "Attachments",
                ParentId = null,
                DocumentId = item.Id,
            };
            Documnetfolder.Insert();
            // add all documnets Attachments to new documnet
            foreach (var documentId in SelectedDocumentIds)
            {
                var Document = new Document().FindIncludeDocumentForm(documentId);
                await AddDocumnetAttachments(Document, item, Documnetfolder.Id, model, userId, roleId, structureIds, isStructureReceiver, privacyLevel);
            };

            if (!file.IsNull() && !file.Data.IsNullOrEmpty())
            {
                ManageBookmark.AddAgendaTables(file, SelectedDocumentIds);
                var attachmentId = await ManageAttachment.Upload(item.Id, null, null, model.CategoryId, file, userId, structureIds, isStructureReceiver, privacyLevel);
                if (attachmentId.IsNull())
                {
                    item.Delete();
                    return retValue;
                }
                item.AttachmentId = attachmentId;
                item.UpdateAttachmentIdWithoutModifiedDate();
            }
            model.Id = item.Id;
            retValue = (true, 0);
            new EventReceivers().OnDocumentCreated(model, file);
            return retValue;
        }

        ///// <summary>
        ///// Export
        ///// </summary>
        ///// <param name="documentId"></param>
        ///// <param name="transferId"></param>
        ///// <param name="delegationId"></param>
        ///// <returns></returns>
        //public static async Task<(bool Success, int State)> Export(long documentId, long transferId,long userId ,long roleId ,)
        //{

        //    Document document = new Document().FindIncludeAll(documentId);
        //    DocumentViewModel model = new DocumentViewModel
        //    {

        //        Subject = document.Subject,
        //        CategoryId = new Category().FindByName("Incoming").Id,
        //        StatusId = (short)DocumentStatus.Draft,
        //        CreatedDate = DateTime.Now.ToString(),
        //        PrivacyId = document.PrivacyId,
        //        Register = true,
        //        Body = document.DocumentForm.Body,
        //        SendingEntityId = document.SendingEntityId,
        //        DueDate = document.DueDate == null ? null : document.DueDate.ToString(),
        //    };
        //    //FileViewModel originalMail = new();
        //    //if (document.AttachmentId != null)
        //    //{
        //    //    ManageAttachment.get
        //    //    originalMail.Name = Intalio.Core.Helper.GetFileName(file.FileName);
        //    //    originalMail.FileSize = file.Length;
        //    //    originalMail.ContentType = file.ContentType;
        //    //    originalMail.Data = file.OpenReadStream().StreamToByte();
        //    //    originalMail.Extension = Intalio.Core.Helper.GetFileExtension(file.FileName).ToLower();
        //    //}
        //    foreach (var receivingEntity in document.DocumentReceiverEntity)
        //    {
        //        bool createdDocument;
        //        //if (delegationId != null)
        //        //{
        //        //    var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(UserId, delegationId.Value) : null;
        //        //    if (delegation.DraftInbox == true)
        //        //    {
        //        //        var userData = Core.API.ManageUser.GetUser(delegation.FromUserId);

        //        //        var structureReceiverAttribute = userData.Attributes.Where(a => a.Text == "StructureReceiver").FirstOrDefault();
        //        //        var structureSenderAttribute = userData.Attributes.Where(a => a.Text == "StructureSender").FirstOrDefault();
        //        //        createdDocument = await ManageDocument.Create(model, originalMail, delegation.FromUserId, userData.ApplicationRoleId.Value, userData.StructureIds, Convert.ToBoolean(structureReceiverAttribute.Value), delegation.PrivacyLevel, Language);


        //        //    }
        //        //    else
        //        //        createdDocument = await ManageDocument.Create(model, originalMail, UserId, RoleId, StructureIds, IsStructureReceiver, PrivacyLevel, Language);


        //        //}

        //            createdDocument = await ManageDocument.Create(model, originalMail, UserId, RoleId, StructureIds, IsStructureReceiver, PrivacyLevel, Language);
        //        if (createdDocument /*await ManageDocument.Create(model, originalMail, UserId, RoleId, StructureIds, IsStructureReceiver, PrivacyLevel,Language)*/)
        //        {
        //            retValue = model.Id.ToString();
        //            code = Microsoft.AspNetCore.Http.StatusCodes.Status200OK;
        //        }
        //        else
        //        {
        //            code = Microsoft.AspNetCore.Http.StatusCodes.Status403Forbidden;
        //        }
        //    }

        //}


        #endregion

        public static async Task<bool> CancelTask(long documentId, long userId, bool overrideAccess)
        {
            var retVal = false;
            try
            {
                var item = new Document().Find(documentId);
                if ((item != null && item.CreatedByUserId == userId) || overrideAccess == true)
                {
                    item.StatusId = (short)DocumentStatus.Canceled;
                    var Document = new Document().FindIncludeTransfers(documentId);
                    if (Document != null && Document.Transfer != null)
                    {
                        foreach (var transfer in Document.Transfer)
                        {
                            transfer.StatusId = (short)DocumentStatus.Canceled;
                            transfer.UpdateStatusAndClosedDate();
                        }
                    }
                    item.UpdateStatusAndClosedDate();
                    retVal = true;
                }
                else
                {
                    retVal = false;
                }

                return retVal;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Delete FollowUp document
        /// </summary>
        /// <param name="documentId"></param>
        public static async Task<bool> DeleteFollowUpDocument(long documentId)
        {
            var result = false;
            var item = new Document().Find(documentId);
            if (item != null)
            {
                var attachmentsIds = new Attachment().ListStorageIdsByDocumentId(documentId);
                foreach (var attachmentId in attachmentsIds)
                {
                    await Intalio.Core.Helper.HttpPostAsync($"{Configuration.StorageServerUrl}/storage/DeleteFile?fileId={attachmentId}", Configuration.IdentityAccessToken);
                }
                item.Delete();
                result = true;
            }
            return result;
        }

        public static (string, string) GetDocumentFromToDate(long documentId, long userId)
        {
            var retValue = (string.Empty, string.Empty);
            Document document = new Document().FindIncludeDocumentForm(documentId);
            if (document != null)
            {
                if (document.DocumentForm.Form != null)
                {
                    //var DocumentForm = JObject.Parse(document.DocumentForm.Form);
                    retValue = (document.DocumentDate.HasValue ? document.DocumentDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty, document.DueDate.HasValue ? document.DueDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty);
                }
            }
            return retValue;

        }
        public static long? GetOriginalMailId(long documentId)
        {
            var retValue = (string.Empty, string.Empty);
            Document document = new Document().Find(documentId);
            return document?.AttachmentId;

        }

        /// <summary>
        /// Reopen followUp that closed
        /// </summary>
        /// <param name="documentIds"></param>
        /// <returns></returns>
        public static bool Reopen(List<long> documentIds)
        {
            try
            {
                foreach (var documentId in documentIds)
                {
                    var document = new Document().FindIncludeTransfers(documentId);
                    foreach (var item in document.Transfer)
                    {
                        item.ClosedDate = null;
                        item.StatusId = (short)DocumentStatus.InProgress;
                    }
                    document.ClosedDate = null;
                    document.StatusId = (short)DocumentStatus.InProgress;

                    document.UpdateDocumentIncludeTransfers();
                }
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        public static bool ChangeDraftStatus(long documentId, int draftStatusId, long userId, long structureId, short privacyLevel)
        {
            var item = new Document().FindIncludeAll(documentId);
            if (item.CreatedByStructureId != structureId || item.StatusId != (short)DocumentStatus.Draft || item.Privacy.Level > privacyLevel)
                return false;

            item.DraftStatus = draftStatusId;
            item.Update();

            return true;
        }

        public static (bool result, Document document) GenerateReferenceNumber(long userId, long DocumentId, long? transferId, Intalio.Core.Language language)
        {
            Document document = new Document();
            document = ManageCategoryReferenceNumber.GetReferenceNumberGeneratorTypeByDocumentId(DocumentId);
            if (document.ReferenceNumber == "" || document.ReferenceNumber == null)
            {
                var returnedResult = new CategoryReferenceCounter().Generate(document.CategoryId, userId, document.CreatedByStructureId, DocumentId, transferId, language);
                document.ReferenceNumber = returnedResult.Reference;
                document.ReferenceSequence = returnedResult.Sequence;


                bool barcodeGenerated = false;
                if (!string.IsNullOrEmpty(document.ReferenceNumber))
                {
                    try
                    {
                        var tryGenerateBarcode = ManageDocument.GenerateBarcode(document.Id, document.ReferenceNumber);
                        barcodeGenerated = true;
                    }
                    catch (Exception exp)
                    {
                        throw exp;
                    }

                }
                if (barcodeGenerated)
                {
                    UpdateReferenceNumberFirstTime(document);
                }

                return (true, document);
            }
            return (false, document);
        }

        /// <summary>
        /// Complete draft document
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="id"></param>
        /// <param name="roleId"></param>
        /// <param name="isStructureReciver"></param>
        /// <param name="structureId"></param>
        /// <param name="StructureIds"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="rootFolderId"></param>
        /// <returns></returns>
        /// <remarks>
        /// Document will be completed and a closed transfer will be created and sent to document created by structure in case visibleForStructureUsers is <b>true</b>
        /// Document will be completed and a closed transfer will be created and sent to current user in case visibleForStructureUsers is <b>false</b>
        /// </remarks>
        public static async Task<(bool Success, string Message)> Archive(long userId, long id, int roleId, long? structureId, List<long> StructureIds, bool isStructureReciver, short privacyLevel, long? rootFolderId)
        {
            bool retValue = false;
            Document item = new Document().FindIncludeCategory(id);

            var attachments = await ManageAttachment.List(id, null, userId, roleId, structureId, StructureIds, isStructureReciver, privacyLevel);
            if (attachments != null && attachments.Count() > 0)
            {
                retValue = AddAllAttachments(attachments, rootFolderId ?? 0);
                return (retValue, "Archived Successfully");
            }
            return (retValue, "No Attachments to archive");
        }

        public static async Task<(bool Export, string Message, List<long> NewIncomingDocumentIds)> ExportOutgoing(DocumentViewModel model,long userId, bool isStructureSender,bool isStructureReceiver,short roleId,short privacyLevel,bool allowEditSigned, long StructureId,List<long> structureIds, IFormFile file, Language language = Language.EN)
        {
            (bool Export, string Message, List<long> NewIncomingDocumentIds) result=new (false,"",new List<long>());
            FileViewModel originalMail = new FileViewModel();
            if (file != null)
            {
                originalMail.Name = Intalio.Core.Helper.GetFileName(file.FileName);
                originalMail.FileSize = file.Length;
                originalMail.ContentType = file.ContentType;
                originalMail.Data = file.OpenReadStream().StreamToByte();
                originalMail.Extension = Intalio.Core.Helper.GetFileExtension(file.FileName).ToLower();
            }
            else if (model.ScannedFile != null)
            {
                originalMail = model.ScannedFile;
            }
            if (model.Id.HasValue)
            {
                Document item = new Document().FindIncludeForExport(model.Id.Value);

                if (item.IsSigned.HasValue && !item.IsSigned.Value && !(item.Transfer?.Any(x => x.IsSigned) ?? false))
                {
                    result.Export = false;
                    result.Message = "CannotExportUnsignedDocument";
                    return result;
                }
                ManageActivityLog.AddActivityLog(item.Id, null, (int)Core.ActivityLogs.Exported, userId, "", note: $"{TranslationUtility.Translate("ThisCorrespondanceisExported", language)}");

                DateTime? dueDate = null;
                if (!string.IsNullOrEmpty(model.DueDate))
                {
                    dueDate = Convert.ToDateTime(model.DueDate);
                }
                (bool success, string message) createdDocument;
                item.DocumentReceiverEntity = item.DocumentReceiverEntity.Where(x => model.StructureReceiversWithCC.Where(src => !src.IsCC).Select(s => s.Id).Contains(x.StructureId ?? 0)).ToList();
                foreach (var cced in item.DocumentCarbonCopy.Where(x=>model.StructureReceiversWithCC.Where(src=>src.IsCC).Select(x=>x.Id).Contains(x.StructureId)))
                {
                    DocumentReceiverEntity documentReceiverEntity = new DocumentReceiverEntity()
                    {
                        Document = cced.Document,
                        DocumentId = cced.DocumentId,
                        ExportIsOriginal = false,
                        Structure = cced.Structure,
                        StructureId = cced.StructureId,
                    };
                    item.DocumentReceiverEntity.Add(documentReceiverEntity);
                }
                if (item.DocumentReceiverEntity is null)
                {
                    ExceptionLogger.WriteEntry("DocumentReceiverEntity is empty");
                }
                else
                {
                    ExceptionLogger.WriteEntry("DocumentReceiverEntity is " + item.DocumentReceiverEntity.Count);
                }
                if (model.StructureReceivers is null)
                {
                    ExceptionLogger.WriteEntry("model.StructureReceivers is empty");
                }
                else
                {
                    ExceptionLogger.WriteEntry("model.StructureReceivers is " + model.StructureReceivers.Count);
                }
                var recievers = item.DocumentReceiverEntity; /*model.FromResend==true ? item.DocumentReceiverEntity.Where(s => model.StructureReceivers.Contains((long)s.StructureId)).ToList(): item.DocumentReceiverEntity;*/
                foreach (var reciever in recievers)
                {
                    if (reciever.Structure.IsExternal)
                    {
                        continue;
                    }
                    reciever.Id = 0;
                    reciever.Structure = null;
                    reciever.Document = null;
                    DocumentViewModel receiverModel = new DocumentViewModel
                    {
                        Subject = string.IsNullOrEmpty(item.Subject) ? string.Empty : item.Subject,
                        CategoryId = new Category().FindByName("Incoming").Id,
                        StatusId = (short)DocumentStatus.Draft,
                        CreatedDate = DateTime.Now.ToString(),
                        PrivacyId = item.PrivacyId,
                        Register = false,
                        Body = item.DocumentForm.Body,
                        SendingEntityId = item.SendingEntityId,
                        DueDate = item.DueDate == null ? null : item.DueDate.ToString(),
                        CreatedByStructureId = item.CreatedByStructureId,
                        Receivers = new List<ReceivingEntityModel>()
                                { new ReceivingEntityModel(){Id = reciever.StructureId.Value } },
                        ExternalReferenceNumber = string.IsNullOrEmpty(model.ExternalReferenceNumber) ? string.Empty : model.ExternalReferenceNumber,
                        IsExternalReceiver = item.IsExternalReceiver,
                        IsExternalSender = item.IsExternalSender,
                        PriorityId = item.PriorityId,
                        ClassificationId = item.ClassificationId,
                        ImportanceId = item.ImportanceId,
                        DocumentTypeId = item.DocumentTypeId,
                        Attachment = (HashSet<Attachment>)item.Attachment,
                        Note = (HashSet<Note>)item.Note,
                        NonArchivedAttachments = item.NonArchivedAttachments,
                        LinkedDocumentDocument = (HashSet<LinkedDocument>)item.LinkedDocumentDocument,
                        LinkedDocumentLinkedDocumentNavigation = (HashSet<LinkedDocument>)item.LinkedDocumentLinkedDocumentNavigation,
                        DocumentCopyId = model.Id.Value,
                        CopyOptionsModal = new CopyOptionsModal() { WithEntities = true, WithAttachment = model.CopyOptionsModal.WithAttachment, WithNotes = model.CopyOptionsModal.WithNotes, WithLinkedCorrespondences = model.CopyOptionsModal.WithLinkedCorrespondences, WithNonArchivedAttachments = model.CopyOptionsModal.WithNonArchivedAttachments/*, WithPublicAttachmentsOnly = true*/ },
                        CarbonCopy = model.CarbonCopy,
                        ExportedDocumentId = model.Id,

                        DocumentReceiverEntity = reciever
                    };
                    createdDocument = await ManageDocument.CreateCopy(receiverModel, originalMail, userId, roleId, structureIds, isStructureReceiver, isStructureSender, privacyLevel, StructureId, null, null, language, null, true);
                    if (createdDocument.success)
                    {
                        receiverModel.CategoryId = new Category().FindByName("Incoming").Id;
                        receiverModel.DocumentReceiverEntity = reciever;
                    }

                    (bool Edited, string ReferenceNumber, string Message) editDocument;
                    editDocument = await ManageDocument.Edit(receiverModel, userId,StructureId, structureIds, isStructureReceiver, privacyLevel, allowEditSigned, receiverModel.TransferId, fromExport:true);
                    if (editDocument.Edited)
                    {
                        DocumentViewModel sendModel = new DocumentViewModel()
                        {
                            PurposeId = model.PurposeId,
                            TransferDueDate = model.TransferDueDate,
                            TransferInstruction = model.TransferInstruction,
                            TransferToType = model.TransferToType,
                            StructureReceivers = receiverModel.Receivers.Select(x => x.Id).ToList(),
                            WithSign = model.WithSign,
                            SignatureTemplateId = model.SignatureTemplateId,
                            //TransferId = model.TransferId,
                            RequestStatus = (long)RequestStatuses.Pending,
                            ExportedDocumentId = model.Id.Value,
                        };
                        if (!isStructureSender && sendModel.StructureReceivers.Any(x => !structureIds.Any(t => t == x)))
                        {
                            result.Export = false;
                            result.Message = "NotStructureSender";
                            return result;
                        }
                        if (!structureIds.Contains(sendModel.StructureId))
                        {
                            sendModel.StructureId = StructureId;
                        }
                        if (sendModel.WithSign && sendModel.SignatureTemplateId != null)
                        {
                            var signed = await ManageTransfer.SignDocument(receiverModel.Id.Value, sendModel.TransferId, userId, structureIds, isStructureReceiver, isStructureSender, privacyLevel, sendModel.SignatureTemplateId.Value, sendModel.DelegationId, language);
                            if (!signed.updated)
                            {
                                var res = await Core.API.ManageAttachment.RestoreWordDocument(receiverModel.Id.Value);
                                if (!res)
                                {
                                    throw new InvalidOperationException("Failed to restore Word document.");
                                }
                            }
                            result.Export = signed.updated;
                            result.Message = signed.message;
                            return result;
                        }
                        var sendResult = await ManageDocument.Send(userId, structureIds, isStructureSender, isStructureReceiver, privacyLevel, receiverModel.Id.Value, sendModel.TransferId, sendModel.PurposeId, sendModel.TransferDueDate, sendModel.TransferInstruction, sendModel.StructureId, sendModel.VoiceNote, sendModel.VoiceNotePrivacy, language, sendModel.RequestStatus, sendModel.ExportedDocumentId);
                        if (sendResult.Sent)
                        {
                            result.Export = sendResult.Sent;
                            result.Message = "";
                            result.NewIncomingDocumentIds.Add(receiverModel.Id.Value);
                        }
                        else if (sendResult.Message == "FileInUse")
                        {
                            result.Export = sendResult.Sent;
                            result.Message = "FileInUse";
                        }
                        else if (sendResult.Message == "CantGenerateReferenceNumber")
                        {
                            result.Export = sendResult.Sent;
                            result.Message = "CantGenerateReferenceNumber";
                        }
                        else 
                        {
                            result.Export = sendResult.Sent;
                            result.Message = "";
                        }
                    }
                }

                return result;

            }
            result.Export = false;
            result.Message = "";
            return result;
        }


        /// <summary>
        /// Recall transfer
        /// </summary>
        /// <param name="id"></param>
        /// <param name="delegationId"></param>
        /// <param name="userId"></param>
        /// <param name="structureId"></param>
        /// <param name="lang"></param>
        /// <returns></returns>
        public static bool ResendRejectedDocument(long id, long? delegationId, long userId, long structureId, Language lang = Language.EN)
        {
            bool retValue = false;
            
            
            return retValue;
        }
        private static async Task<long> AddFolder(string name, long parentId)
        {
            string url = Configuration.DMSURL + (parentId == 0 ? ("/Cabinets/Save?Name=" + name) : ("/Folders/Save?Name=" + name + "&ParentId=" + parentId));
            var respone = await Intalio.Core.Helper.HttpPostAsync(url, Configuration.IdentityAccessToken);
            if (!respone.Success)
            {
                return 0;
            }
            var responsemsg = JsonConvert.DeserializeObject<Dictionary<string, string>>(respone.message);
            return Convert.ToInt64(responsemsg["id"]);
        }
        private static bool AddAllAttachments(List<Model.TreeNode> list, long rootFolderId)
        {
            list.ForEach(async x =>
            {
                if (x.Type == ((int)NodeType.Folder).ToString())
                {
                    var parnetFolderId = await AddFolder(x.Text, rootFolderId);
                    var children = x.Children as List<Model.TreeNode>;
                    if (children != null && children.Count() > 0)
                    {
                        AddAllAttachments(children, parnetFolderId);
                    }
                }
                else if (x.Type == ((int)NodeType.File).ToString())
                {
                    var form = new System.Net.Http.MultipartFormDataContent();
                    var attachmentId = System.Convert.ToInt64(x.Id.Split("_")[1]);
                    var filecontent = await ManageAttachment.GetStorageAttachmentModel(attachmentId);
                    form.Add(new ByteArrayContent(filecontent.Data), filecontent.Name + "." + filecontent.Extension);
                    form.Add(new StringContent(rootFolderId.ToString()), "folderId");
                    form.Add(new StringContent(true.ToString()), "overwriteIfCheckOutByMe");
                    form.Add(new StringContent(true.ToString()), "overwrite");
                    var response = await Intalio.Core.Helper.HttpPostAsync<object>(url: Configuration.DMSURL + "/Files/Upload", token: Configuration.IdentityAccessToken, data: form);
                }
            });
            return true;
        }


        /// <summary>
        /// Check if document has an attachments
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public static bool CheckDocumentHasAttachments(long id)
        {
            return new Attachment().CheckDocumentHasAttachments(id);
        }

    }
}
