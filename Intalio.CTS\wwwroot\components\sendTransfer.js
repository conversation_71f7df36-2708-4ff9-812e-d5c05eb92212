﻿var gLocked = false;
function checkAbilityToSend(structureIds, receiverObj, privacyId, privacies, transfers, isMultiple)
{
    var headers = {};
    var url = window.IdentityUrl + '/api/ListUserExistenceAttributeInStructure';
    headers.Authorization = 'Bearer ' + window.IdentityAccessToken;
    var params = {};
    params.ids = structureIds;
    params.attributeName = window.UserStructureReceiver;
    params.attributeValue = true;
    params.returnedAttribute = window.UserPrivacy;
    var message = "";
    Common.ajaxPostWithHeaders(url, params, function (response)
    {
        var html = Resources.NoStructureReceivers;
        var htmlPrivacy = Resources.NoStructureReceiversWithSelectedPrivacy;
        var hasStructureReceiver = true;
        var hasPrivacyLevel = true;
        $.each(response, function (key, value)
        {
            var structureObj = $.grep(receiverObj, function (e)
            {
                return e.id === key || e.dataId === parseInt(key);
            });
            if (structureObj.length > 0)
            {
                if (value === null || value.length==0)
                {
                    hasStructureReceiver = false;
                    html += ' \n ○ ' + structureObj[0].text;
                } else if (!isMultiple)
                {
                    var privacyLevel = $.grep(privacies, function (e)
                    {
                        return e.id.toString() === privacyId;
                    });
                    if (privacyLevel !== null && privacyLevel.length > 0)
                    {
                        var privacyLevels = [];
                        for (var i = 0; i < value.length; i++)
                        {
                            var currentPrivacy = $.grep(privacies, function (e)
                            {
                                return e.id.toString() === value[i];
                            });
                            if (currentPrivacy !== null && currentPrivacy.length > 0)
                            {
                                privacyLevels.push(currentPrivacy[0].level);
                            }
                        }
                        var maxLevel = Math.max.apply(Math, privacyLevels);
                        if (maxLevel < privacyLevel[0].level)
                        {
                            hasPrivacyLevel = false;
                            htmlPrivacy += ' \n ○ ' + structureObj[0].text;
                        }
                    }
                } else
                {
                    for (var i = 0; i < transfers.length; i++)
                    {
                        var privacyLevel = $.grep(privacies, function (e)
                        {
                            return e.id.toString() === transfers[i].documentPrivacyId.toString();
                        });
                        if (privacyLevel !== null && privacyLevel.length > 0)
                        {
                            var privacyLevels = [];
                            for (var j = 0; j < value.length; j++)
                            {
                                var currentPrivacy = $.grep(privacies, function (e)
                                {
                                    return e.id.toString() === value[j];
                                });
                                if (currentPrivacy !== null && currentPrivacy.length > 0)
                                {
                                    privacyLevels.push(currentPrivacy[0].level);
                                }
                            }
                            var maxLevel = Math.max.apply(Math, privacyLevels);
                            if (maxLevel < privacyLevel[0].level)
                            {
                                hasPrivacyLevel = false;
                                htmlPrivacy += ' \n ○ ' + structureObj[0].text;
                                if (transfers[i].referenceNumber)
                                {
                                    htmlPrivacy += ' : ' + transfers[i].referenceNumber;
                                }

                            }
                        }
                    }
                }
            }
        });
        if (!hasStructureReceiver)
        {
            message += html;
        }
        if (!hasPrivacyLevel)
        {
            message += (!hasStructureReceiver ? " \n " : "") + htmlPrivacy;
        }
    }, function ()
    {
        message = "error";
    }, null, null, headers, false);
    return message;
}
function removeListItemByDocumentId(documentId) {
    $("input[type='hidden'][data-id]").each(function () {
        try {
            const data = JSON.parse($(this).val());
            if (data.documentId == documentId) {
                const $li = $(this).closest("li");
                $li.fadeOut(() => $li.remove());
            }
        } catch (e) {
            console.warn("Error parsing JSON from hidden input:", e);
        }
    });
}

function transfer(transferComponent, transferArray, allCced, allCcedClose, multiple, delegationId, fromInbox, vip, callback, withSign, signTemplate, successCallback)
{

    if (!gLocked)
    {
        gLocked = true;
        try
        {
            var maintainTransfer = $('#chkMaintainTransfer').prop('checked');
            
            //if (transferArray.categoryId == 2)
            //    maintainTransfer = true;
            transferComponent.lockButonsOnTransfer();
            Common.ajaxPostJSON('/Transfer/Transfer?delegationId=' + delegationId + '&maintainTransfer=' + maintainTransfer + '&withSign=' + withSign + '&signatureTemplateId=' + signTemplate, JSON.stringify(transferArray), function (data)
            {
                gLocked = false;
                transferComponent.unLockButonsAfterTransfer();
                if (multiple)
                {
                    var unUpdatedData = [];
                    var fileInUseData = [];
                    var transferIsLockedData = [];
                    var fileInUseMsg = Resources.TheBelowTransfersFileInUse;
                    var unUpdatedMsg = Resources.CouldntTransferTheBelow;
                    var transferIsLockedMsg = Resources.TheBelowTransfersLockedByAnotherUser;
                    var alertMessage = "";
                    var error = false;
                    for (var i = 0; i < data.length; i++)
                    {
                        if (data[i].message === "TransferIsLocked")
                        {
                            var transfer = $.grep(transferArray, function (e)
                            {
                                return e.documentId === data[i].documentId;
                            });
                            if (transfer[0])
                            {
                                transferIsLockedData.push(transfer[0].referenceNumber);

                            } else
                            {
                                transferIsLockedData.push(data[i].parentTransferId);
                            }

                        }
                        else if (data[i].message === "CantGenerateReferenceNumber") {
                            setTimeout(function () {
                                Common.alertMsg(Resources.CantGenerateReferenceNumber);
                            }, 500);
                        }
                        else if (data[i].message === "Can't render barcode: not enough space") {
                            setTimeout(function () {
                                Common.alertMsg(data[i].message);
                            }, 500);
                        }
                        else if (data[i].message === "DocumentIsCompleted" && !data[i].updated)
                        {
                            Common.showScreenErrorMsg(Resources.DocumentIsCompleted);

                        }
                        else if (data[i].message === "TransferIsExist" && !data[i].updated) {
                            setTimeout(function () {
                                Common.alertMsg(Resources.TransferIsExist);
                            }, 500);
                        }
                        else if (data[i].message === "FileInUse" || data[i].message === "OriginalFileInUse")
                        {
                            var transfer = $.grep(transferArray, function (e)
                            {
                                return e.documentId === data[i].documentId;
                            });
                            if (transfer[0])
                            {
                                fileInUseData.push(transfer[0].referenceNumber);

                            } else
                            {
                                fileInUseData.push(data[i].parentTransferId);
                            }
                        }
                        else if (!data[i].updated && data[i].documentId)
                        {
                            var transfer = $.grep(transferArray, function (e)
                            {
                                return e.documentId === data[i].documentId;
                            });
                            if (transfer[0])
                            {
                                unUpdatedData.push(transfer[0].referenceNumber);

                            } else
                            {
                                unUpdatedData.push(data[i].documentId);
                            }
                        }
                        else if (!data[i].updated)
                        {
                            error = true;
                            break;
                        }
                    }
                    if (error)
                    {
                        if (vip)
                        {
                            callback(transferArray);
                        } else
                        {
                            GridCommon.Refresh("grdInboxItems");
                        }
                        TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                        TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
                        transferComponent.close();
                        Common.showScreenErrorMsg();
                        $('.modal-documents').empty();
                    }
                    else
                    {
                        if (fileInUseData.length > 0)
                        {
                            for (var i = 0; i < fileInUseData.length; i++)
                            {
                                fileInUseMsg += "\n ○ " + fileInUseData[i];
                            }
                            alertMessage += fileInUseMsg + "\n ";
                        }
                        if (unUpdatedData.length > 0)
                        {
                            for (var i = 0; i < unUpdatedData.length; i++)
                            {
                                unUpdatedMsg += "\n ○ " + unUpdatedData[i];
                            }
                            alertMessage += unUpdatedMsg + "\n ";
                        }
                        if (transferIsLockedData.length > 0)
                        {
                            for (var i = 0; i < transferIsLockedData.length; i++)
                            {
                                transferIsLockedMsg += "\n ○ " + transferIsLockedData[i];
                            }
                            alertMessage += transferIsLockedMsg;
                        }

                        if (alertMessage !== "")
                        {
                            if (allCced)
                            {
                                if (vip)
                                {
                                    callback(data.map(a => a.parentTransferId));
                                } else
                                {
                                    GridCommon.Refresh("grdInboxItems");
                                }
                                TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                                TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
                                TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                                Common.showScreenSuccessMsg(Resources.SentSuccessfully);
                                transferComponent.clear();
                                setTimeout(function ()
                                {
                                    Common.alertMsg(alertMessage);
                                }, 900);
                            } else
                            {
                                if (vip)
                                {
                                    callback(data.map(a => a.parentTransferId));
                                } else
                                {
                                    GridCommon.Refresh("grdInboxItems");
                                }
                                TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                                TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
                                TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                                transferComponent.close();
                                setTimeout(function ()
                                {
                                    Common.alertMsg(alertMessage);
                                }, 900);
                            }
                        } 
                        else
                        {
                            if (allCced)
                            {
                                if (vip)
                                {
                                    callback(data.map(a => a.parentTransferId));
                                } else
                                {
                                    GridCommon.Refresh("grdInboxItems");
                                }
                                TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                                TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
                                TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                                Common.showScreenSuccessMsg(Resources.SentSuccessfully);
                                //$('.modal-documents').empty();
                                transferComponent.clear();
                            } else
                            {
                                if (vip)
                                {
                                    callback(data.map(a => a.parentTransferId));
                                } else
                                {
                                    GridCommon.Refresh("grdInboxItems");
                                }
                                TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                                TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
                                TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                                Common.showScreenSuccessMsg(Resources.SentSuccessfully);
                                //$('.modal-documents').empty();
                                transferComponent.close();
                            }
                        }
                    }
                }
                else
                {
                    if (data[0])
                    {
                        if (data[0].message === "FileInUse")
                        {
                            setTimeout(function ()
                            {
                                Common.alertMsg(Resources.FileInUse);
                            }, 500);
                        }
                        else if (data[0].message === "NoAttachmentToSign") {
                            setTimeout(function () {
                                Common.alertMsg(Resources.NoAttachmentToSign);
                            }, 500);
                        }
                        else if (data[0].message === "NoSignatureConfigured") {
                            setTimeout(function () {
                                Common.alertMsg(Resources.NoSignatureConfigured);
                            }, 500);
                        }
                        else if (data[0].message === "CantGenerateReferenceNumber") {
                            setTimeout(function () {
                                Common.alertMsg(Resources.CantGenerateReferenceNumber);
                            }, 500);
                        }
                        else if (data[0].message === "DocumentIsCompleted" && !data[0].updated) {
                            Common.showScreenErrorMsg(Resources.DocumentIsCompleted);

                        }
                        else if (data[0].message === "TransferIsExist" && !data[0].updated) {
                            setTimeout(function () {
                                Common.alertMsg(Resources.TransferIsExist);
                            }, 500);
                        }
                        else if (data[0].message === "OriginalFileInUse")
                        {
                            setTimeout(function ()
                            {
                                Common.alertMsg(Resources.OriginalFileInUse);
                            }, 500);
                        }
                        else if (data[0].message === "CheckinFailed") {
                            setTimeout(function () {
                                Common.alertMsg(Resources.OriginalFileInUse);
                            }, 500);
                        } else if (data[0].message === "Can't render barcode: not enough space") {
                            setTimeout(function () {
                                Common.alertMsg(data[0].message);
                            }, 500);
                        }  
                        else if (!data[0].updated)
                        {
                            Common.showScreenErrorMsg();
                        }
                        else
                        {
                            if (!allCced && !allCcedClose)
                            {
                                TreeNode.addToNodeCount(TreeNode.MyRequests, 1, 1);
                                TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                                GridCommon.RefreshCurrentPage("grdDraftItems", true);
                                $(".withBorders-o").addClass("waitingBackground");
                                $("#draftDocumentDetailsContainer").empty();
                                if (window.location.href.includes("#Draft")) {
                                    $($("input[data-id='" + transferComponent.model.documentId + "']").parents("li")[0]).fadeOut().remove();
                                } else {
                                    if (!maintainTransfer) {
                                        removeListItemByDocumentId(transferComponent.model.documentId);
                                    }
                                }
                                
                                GridCommon.Refresh("grdMyRequestsItems");
                                 
                                $(".withBorders-o").addClass("waitingBackground");
                                $("#myRequestsDocumentDetailsContainer").empty();

                                
                                 

                            }
                            TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                            TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
                            TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                            GridCommon.Refresh("grdCompletedItems");
                            GridCommon.Refresh("grdSentItems");
                            GridCommon.Refresh("grdInboxItems");
                            
                            $(".withBorders-o").addClass("waitingBackground");
                            $("#inboxDocumentDetailsContainer").empty();
                            
                            $(".withBorders-o").addClass("waitingBackground");
                            $("#completedDocumentDetailsContainer").empty();
                            
                            $(".withBorders-o").addClass("waitingBackground");
                            $("#sentDocumentDetailsContainer").empty();

                            if (!maintainTransfer) {
                                removeListItemByDocumentId(transferComponent.model.documentId);
                            }
                            
                            Common.showScreenSuccessMsg(Resources.SentSuccessfully);
                            //$('.modal-documents').empty();
                            if (allCced)
                            {
                                transferComponent.clear();
                            } else
                            {
                                if (allCcedClose)
                                {
                                    var nodeId = $('[data-inherit="' + TreeNode.Draft + '"]').first().data("id");
                                    if (nodeId !== undefined && $.isNumeric(nodeId))
                                    {
                                        //window.location.href = '#draft/' + nodeId;
                                        $(".close-correspondence").trigger("click");
                                        $("[ref=btnCloseTransfer]").trigger("click");

                                        if ($('li .active').attr("data-id") == undefined) {
                                           // window.location.href = "/";
                                        }
                                        else {
                                            window.location.reload();
                                        }

                                        GridCommon.RefreshCurrentPage("grdDraftItems", true);
                                        $(".withBorders-o").addClass("waitingBackground");
                                        $("#draftDocumentDetailsContainer").empty();
                                        removeListItemByDocumentId(transferComponent.model.documentId);
                                        if (typeof successCallback === 'function') {
                                            successCallback();
                                        }
                                    } else
                                    {
                                        $(".close-correspondence").trigger("click");
                                        $("[ref=btnCloseTransfer]").trigger("click");

                                        if ($('li .active').attr("data-id") == undefined) {
                                            //window.location.href = "/";
                                        }
                                        else {

                                            //window.location.reload();
                                        }

                                        //window.location.href = '/';
                                        if (typeof successCallback === 'function') {
                                            successCallback();
                                        }
                                    }
                                } else
                                {
                                    var nodeId = $('[data-inherit="' + TreeNode.MyRequests + '"]').first().data("id");
                                    var redirectTo = '#myrequests/' + nodeId;
                                    GridCommon.Refresh("grdMyRequestsItems");
                                    $(".withBorders-o").addClass("waitingBackground");
                                    $("#myRequestsDocumentDetailsContainer").empty();
                                    if (!maintainTransfer) {
                                        removeListItemByDocumentId(transferComponent.model.documentId);
                                    }
                                    
                                    if (fromInbox)
                                    {
                                        nodeId = $('[data-inherit="' + TreeNode.Completed + '"]').first().data("id");
                                        redirectTo = '#completed/' + nodeId;
                                        GridCommon.Refresh("grdCompletedItems");
                                        
                                        $(".withBorders-o").addClass("waitingBackground");
                                        $("#completedDocumentDetailsContainer").empty();
                                        if (!maintainTransfer) {
                                            removeListItemByDocumentId(transferComponent.model.documentId);
                                        }
                                    }
                                    if (fromInbox && maintainTransfer) {
                                        nodeId = $('[data-inherit="' + TreeNode.Sent + '"]').first().data("id");
                                        redirectTo = '#sent/' + nodeId;
                                        GridCommon.Refresh("grdSentItems");
                                        
                                        $(".withBorders-o").addClass("waitingBackground");
                                        $("#sentDocumentDetailsContainer").empty();
                                    }
                                    if (nodeId !== undefined && $.isNumeric(nodeId))
                                    {
                                        $(".close-correspondence").trigger("click");
                                        $("[ref=btnCloseTransfer]").trigger("click");

                                        if ($('li .active').attr("data-id") == undefined) {
                                            //window.location.href = "/";
                                        }
                                        else {
                                            //window.location.reload();
}

                                        // window.location.href = redirectTo;
                                        if (typeof successCallback === 'function') {
                                            successCallback();
                                        }
                                    } else
                                    {
                                        $(".close-correspondence").trigger("click");
                                        $("[ref=btnCloseTransfer]").trigger("click");

                                        if ($('li .active').attr("data-id") == undefined) {
                                            //window.location.href = "/";
                                        }
                                        else {

                                            //window.location.reload();
                                        }

                                        // window.location.href = '/';
                                        if (typeof successCallback === 'function') {
                                            successCallback();
                                        }
                                    }
                                }
                            }
                        }
                    } else
                    {
                        Common.showScreenErrorMsg();
                    }
                }
            }, function () { gLocked = false; transferComponent.unLockButonsAfterTransfer(); Common.showScreenErrorMsg(); }, false);
        } catch (e)
        {
            gLocked = false;
        }
    }
}
export default { checkAbilityToSend, transfer };
