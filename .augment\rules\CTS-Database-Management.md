---
type: "always_apply"
description: "Example description"
---
# CTS Database Management

## Table of Contents
- [Overview](#overview)
- [SQL-First Approach](#sql-first-approach)
- [Script Organization](#script-organization)
- [Naming Conventions](#naming-conventions)
- [Script Types and Patterns](#script-types-and-patterns)
- [Execution Guidelines](#execution-guidelines)
- [Version Control](#version-control)
- [Best Practices](#best-practices)

## Overview

The CTS (Correspondence Tracking System) uses a **SQL-first approach** for all database changes. This means:

- ❌ **No Entity Framework Migrations** are used
- ✅ **All database changes** are made via SQL scripts
- ✅ **Manual script execution** is required for deployments
- ✅ **Version control** tracks all database changes

This approach provides precise control over database schema and data changes, ensuring consistency across environments.

## SQL-First Approach

### Why SQL-First?

#### Advantages
1. **Precise Control**: Exact control over SQL execution
2. **Performance**: Optimized queries and indexes
3. **Flexibility**: Complex database operations not limited by ORM
4. **Transparency**: Clear visibility of all database changes
5. **Environment Consistency**: Same scripts across all environments

#### Comparison with EF Migrations
```csharp
// ❌ EF Migrations (NOT USED in CTS)
public partial class AddFollowUpFeature : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.CreateTable(
            name: "FollowUp",
            columns: table => new
            {
                Id = table.Column<long>(nullable: false)
                    .Annotation("SqlServer:Identity", "1, 1"),
                // ...
            });
    }
}

// ✅ SQL Scripts (USED in CTS)
-- File: Ibrahim_AddFollowUpFeature.sql
CREATE TABLE [dbo].[FollowUp] (
    [Id] [bigint] IDENTITY(1,1) NOT NULL,
    [DocumentId] [bigint] NOT NULL,
    [UserId] [bigint] NOT NULL,
    [Status] [int] NOT NULL,
    [CreatedDate] [datetime2](7) NOT NULL,
    CONSTRAINT [PK_FollowUp] PRIMARY KEY CLUSTERED ([Id])
);
```

### Database Change Workflow

```
1. Identify Need for Database Change
           ↓
2. Create SQL Script with Proper Naming
           ↓
3. Test Script in Development Environment
           ↓
4. Code Review and Approval
           ↓
5. Execute in Staging Environment
           ↓
6. Execute in Production Environment
           ↓
7. Verify Changes and Update Documentation
```

## Script Organization

### Directory Structure
```
Database/
├── CTS 4.0.0.sql                     # Base schema script
├── RC 30.sql                         # Release candidate scripts
├── {Developer}_{Feature}.sql         # Individual feature scripts
├── CTS_Sprint1_Queries/              # Sprint-organized scripts
│   ├── Sprint 1 queries.sql
│   ├── TemplateHasSign.sql
│   └── TranslationOfActivityLog.sql
├── CTS_Sprint2_Queries/              # Sprint 2 scripts
├── CTS_Sprint3_Queries/              # Sprint 3 scripts
├── QDB/                              # QDB environment scripts
└── archived/                         # Archived/obsolete scripts
```

### Script Categories

#### 1. Base Schema Scripts
- **Purpose**: Complete database schema creation
- **Examples**: `CTS 4.0.0.sql`
- **Usage**: Initial database setup

#### 2. Release Candidate Scripts
- **Purpose**: Cumulative changes for releases
- **Examples**: `RC 30.sql`, `RC 29.sql`
- **Usage**: Major version deployments

#### 3. Feature Scripts
- **Purpose**: Individual feature implementations
- **Examples**: `Ibrahim_AddFollowUpFeature.sql`
- **Usage**: Incremental feature development

#### 4. Sprint Scripts
- **Purpose**: Sprint-specific changes
- **Location**: `CTS_Sprint{N}_Queries/`
- **Usage**: Sprint deployments

#### 5. Translation Scripts
- **Purpose**: Internationalization updates
- **Examples**: `BashirHatoumTranslationFix.sql`
- **Usage**: Adding/updating translations

## Naming Conventions

### File Naming Standards

#### Individual Developer Scripts
```
{Developer}_{Feature/Description}.sql

Examples:
- Ibrahim_AddFollowUpFeature.sql
- BashirHatoum_TranslationFix.sql
- Maha_AddExportFeature.sql
- Nareman_ReadyForTransferNode.sql
```

#### Sprint/Release Scripts
```
{Type}_{Version/Sprint}.sql

Examples:
- RC 30.sql                    # Release Candidate 30
- CTS_Sprint1_Queries.sql      # Sprint 1 queries
- Sprint 2 queries.sql         # Sprint 2 queries
```

#### Translation Scripts
```
{Developer}_{Feature}_Translation.sql

Examples:
- BashirHatoumTranslationFix.sql
- Ibrahim_FollowUp_Translation.sql
- TranslationIssues.sql
```

#### Environment-Specific Scripts
```
{Environment}_{Description}.sql

Examples:
- QDB_Nodes.sql               # QDB environment
- Production_HotFix.sql       # Production hotfix
```

### Content Naming Standards

#### Table Names
```sql
-- Use PascalCase, descriptive names
CREATE TABLE [dbo].[FollowUp] (...)
CREATE TABLE [dbo].[ActivityLog] (...)
CREATE TABLE [dbo].[TranslatorDictionary] (...)
```

#### Column Names
```sql
-- Use PascalCase, descriptive names
[Id] [bigint] IDENTITY(1,1) NOT NULL,
[DocumentId] [bigint] NOT NULL,
[CreatedDate] [datetime2](7) NOT NULL,
[IsActive] [bit] NOT NULL DEFAULT(1)
```

#### Index Names
```sql
-- Pattern: IX_{TableName}_{ColumnName(s)}
CREATE INDEX [IX_Document_CategoryId] ON [dbo].[Document] ([CategoryId]);
CREATE INDEX [IX_Transfer_DocumentId_Status] ON [dbo].[Transfer] ([DocumentId], [Status]);
```

#### Constraint Names
```sql
-- Primary Key: PK_{TableName}
CONSTRAINT [PK_FollowUp] PRIMARY KEY CLUSTERED ([Id])

-- Foreign Key: FK_{TableName}_{ReferencedTable}_{ColumnName}
CONSTRAINT [FK_Document_Category_CategoryId] 
    FOREIGN KEY ([CategoryId]) REFERENCES [dbo].[Category] ([Id])

-- Check Constraint: CK_{TableName}_{ColumnName}_{Description}
CONSTRAINT [CK_Document_Status_Valid] 
    CHECK ([Status] IN (1, 2, 3, 4))
```

## Script Types and Patterns

### 1. Table Creation Scripts
```sql
-- Pattern for creating new tables
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[TableName]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[TableName] (
        [Id] [bigint] IDENTITY(1,1) NOT NULL,
        [Name] [nvarchar](255) NOT NULL,
        [CreatedDate] [datetime2](7) NOT NULL DEFAULT(GETUTCDATE()),
        [IsActive] [bit] NOT NULL DEFAULT(1),
        CONSTRAINT [PK_TableName] PRIMARY KEY CLUSTERED ([Id])
    );
END
```

### 2. Column Addition Scripts
```sql
-- Pattern for adding columns
IF NOT EXISTS (SELECT * FROM sys.columns 
               WHERE object_id = OBJECT_ID(N'[dbo].[TableName]') 
               AND name = 'NewColumnName')
BEGIN
    ALTER TABLE [dbo].[TableName] 
    ADD [NewColumnName] [nvarchar](100) NULL;
END

-- Add with default value
IF NOT EXISTS (SELECT * FROM sys.columns 
               WHERE object_id = OBJECT_ID(N'[dbo].[Document]') 
               AND name = 'IsExported')
BEGIN
    ALTER TABLE [dbo].[Document] 
    ADD [IsExported] [bit] NOT NULL DEFAULT(0);
END
```

### 3. Index Creation Scripts
```sql
-- Pattern for creating indexes
IF NOT EXISTS (SELECT * FROM sys.indexes 
               WHERE object_id = OBJECT_ID(N'[dbo].[TableName]') 
               AND name = N'IX_TableName_ColumnName')
BEGIN
    CREATE INDEX [IX_TableName_ColumnName] 
    ON [dbo].[TableName] ([ColumnName]);
END

-- Composite index
IF NOT EXISTS (SELECT * FROM sys.indexes 
               WHERE object_id = OBJECT_ID(N'[dbo].[Document]') 
               AND name = N'IX_Document_Status_CreatedDate')
BEGIN
    CREATE INDEX [IX_Document_Status_CreatedDate] 
    ON [dbo].[Document] ([StatusId], [CreatedDate] DESC);
END
```

### 4. Data Update Scripts
```sql
-- Pattern for data updates
-- Always use transactions for data changes
BEGIN TRANSACTION;

BEGIN TRY
    -- Update existing data
    UPDATE [dbo].[Document] 
    SET [ModifiedDate] = GETUTCDATE()
    WHERE [ModifiedDate] IS NULL;
    
    -- Insert new reference data
    IF NOT EXISTS (SELECT 1 FROM [dbo].[Status] WHERE [Id] = 5)
    BEGIN
        INSERT INTO [dbo].[Status] ([Id], [Name], [IsActive])
        VALUES (5, N'Exported', 1);
    END
    
    COMMIT TRANSACTION;
END TRY
BEGIN CATCH
    ROLLBACK TRANSACTION;
    THROW;
END CATCH
```

### 5. Translation Scripts
```sql
-- Pattern for translation entries
IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword = N'NewFeature')
BEGIN
    INSERT [dbo].[TranslatorDictionary] ([Keyword], [EN], [FR], [AR], [IsSystem]) 
    VALUES (N'NewFeature', 
            N'New Feature', 
            N'Nouvelle fonctionnalité', 
            N'ميزة جديدة', 
            1)
END
```

### 6. Stored Procedure Scripts
```sql
-- Pattern for stored procedures
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[usp_ProcedureName]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[usp_ProcedureName];
GO

CREATE PROCEDURE [dbo].[usp_ProcedureName]
    @Parameter1 BIGINT,
    @Parameter2 NVARCHAR(255)
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Procedure logic here
    SELECT * FROM [dbo].[TableName] 
    WHERE [Id] = @Parameter1 AND [Name] = @Parameter2;
END
GO
```

## Execution Guidelines

### Development Environment
```bash
# Execute individual scripts
sqlcmd -S localhost -d CTS_Dev -i "Database/Ibrahim_AddFollowUpFeature.sql"

# Execute with error handling
sqlcmd -S localhost -d CTS_Dev -i "script.sql" -o "output.log" -e
```

### Staging Environment
```bash
# Execute with transaction log
sqlcmd -S staging-server -d CTS_Staging -i "script.sql" -o "staging_execution.log"

# Verify changes
sqlcmd -S staging-server -d CTS_Staging -Q "SELECT COUNT(*) FROM NewTable"
```

### Production Environment
```bash
# Always backup before execution
sqlcmd -S prod-server -d CTS_Prod -Q "BACKUP DATABASE CTS_Prod TO DISK = 'backup_path'"

# Execute with full logging
sqlcmd -S prod-server -d CTS_Prod -i "script.sql" -o "prod_execution.log" -e

# Verify execution
sqlcmd -S prod-server -d CTS_Prod -i "verification_script.sql"
```

### Script Execution Checklist
1. ✅ **Backup database** (especially production)
2. ✅ **Test in development** environment first
3. ✅ **Review script** for syntax and logic errors
4. ✅ **Check dependencies** (other tables, procedures)
5. ✅ **Execute in staging** environment
6. ✅ **Verify results** in staging
7. ✅ **Execute in production** during maintenance window
8. ✅ **Verify results** in production
9. ✅ **Document execution** in deployment log

## Version Control

### Git Integration
```bash
# Add new database script
git add Database/Ibrahim_NewFeature.sql
git commit -m "Add database script for new feature"

# Track script execution
git add Database/executed_scripts.log
git commit -m "Update executed scripts log"
```

### Script Tracking
```sql
-- Create table to track executed scripts
CREATE TABLE [dbo].[DatabaseScriptHistory] (
    [Id] [int] IDENTITY(1,1) NOT NULL,
    [ScriptName] [nvarchar](255) NOT NULL,
    [ExecutedDate] [datetime2](7) NOT NULL DEFAULT(GETUTCDATE()),
    [ExecutedBy] [nvarchar](100) NOT NULL,
    [Environment] [nvarchar](50) NOT NULL,
    [Success] [bit] NOT NULL,
    [Notes] [nvarchar](max) NULL,
    CONSTRAINT [PK_DatabaseScriptHistory] PRIMARY KEY ([Id])
);

-- Log script execution
INSERT INTO [dbo].[DatabaseScriptHistory] 
([ScriptName], [ExecutedBy], [Environment], [Success], [Notes])
VALUES 
('Ibrahim_AddFollowUpFeature.sql', 'ibrahim.mohamed', 'Production', 1, 'Successfully added FollowUp table');
```

## Best Practices

### Script Development
1. **Always use transactions** for data modifications
2. **Check existence** before creating objects
3. **Use parameterized queries** when possible
4. **Include rollback procedures** for complex changes
5. **Test thoroughly** in development environment
6. **🚨 CRITICAL: Use UTF-8 encoding** for all SQL files (see [CTS-SQL-UTF8-Encoding.md](CTS-SQL-UTF8-Encoding.md))

### File Encoding Requirements
- **All SQL scripts** in `Database/` directory must use UTF-8 encoding
- **Translation scripts** require UTF-8 for proper Arabic and French character handling
- **Verify encoding** before committing scripts to version control
- **Configure editors** to default to UTF-8 for `.sql` files

### Error Handling
```sql
-- Comprehensive error handling pattern
BEGIN TRANSACTION;

BEGIN TRY
    -- Your database changes here
    
    -- Verify changes
    IF NOT EXISTS (SELECT 1 FROM [dbo].[NewTable])
    BEGIN
        RAISERROR('Verification failed: NewTable is empty', 16, 1);
    END
    
    COMMIT TRANSACTION;
    PRINT 'Script executed successfully';
END TRY
BEGIN CATCH
    ROLLBACK TRANSACTION;
    
    DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
    DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
    DECLARE @ErrorState INT = ERROR_STATE();
    
    PRINT 'Error occurred: ' + @ErrorMessage;
    RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
END CATCH
```

### Performance Considerations
```sql
-- Create indexes after bulk data operations
-- Disable indexes during large data loads
ALTER INDEX ALL ON [dbo].[LargeTable] DISABLE;

-- Bulk insert operations
INSERT INTO [dbo].[TargetTable] (columns...)
SELECT columns... FROM [dbo].[SourceTable];

-- Re-enable indexes
ALTER INDEX ALL ON [dbo].[LargeTable] REBUILD;
```

### Documentation Standards
```sql
-- Script header template
/*
Script Name: Ibrahim_AddFollowUpFeature.sql
Author: Ibrahim Mohamed
Date: 2024-01-15
Description: Add FollowUp functionality to CTS system
Dependencies: Document table must exist
Rollback: DROP TABLE [dbo].[FollowUp]; (if needed)
*/

-- Inline comments for complex logic
-- Check if user has permission before creating follow-up
IF EXISTS (SELECT 1 FROM [dbo].[UserPermissions] 
           WHERE [UserId] = @UserId AND [Permission] = 'CreateFollowUp')
BEGIN
    -- Create follow-up record
    INSERT INTO [dbo].[FollowUp] (...)
    VALUES (...);
END
```

## Quick Reference

### Common Script Patterns
```sql
-- Check table exists
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[TableName]'))

-- Check column exists  
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Table]') AND name = 'Column')

-- Check index exists
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[Table]') AND name = N'IndexName')

-- Safe column add
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Table]') AND name = 'NewColumn')
    ALTER TABLE [dbo].[Table] ADD [NewColumn] [datatype] NULL;
```

### Execution Commands
```bash
# Local execution
sqlcmd -S localhost -d CTS -i script.sql

# Remote execution with authentication
sqlcmd -S server -d database -U username -P password -i script.sql

# Execute with output logging
sqlcmd -S server -d database -i script.sql -o output.log
```

### File Organization
- **Individual features**: `{Developer}_{Feature}.sql`
- **Translations**: `{Developer}_{Feature}_Translation.sql`
- **Sprint collections**: `CTS_Sprint{N}_Queries/`
- **Release candidates**: `RC {number}.sql`
