﻿using Intalio.Core;
using Intalio.Core.Model;
using Intalio.Core.UI.Filters;
using Intalio.CTS.Core;
using Intalio.CTS.Core.API;
using Intalio.CTS.Core.DAL;
using Intalio.CTS.Core.Model;
using Intalio.CTS.Filters;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.IO.Compression;
using System.IO;
using System.Linq;
using System.Net;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Intalio.CTS.Controllers
{
    [Route("[controller]/[action]")]
    public class ReportController : BaseController
    {
        #region Administration/User

        /*
         Commented for performance
        /// <summary>
        /// Displays for each structure and each user inside a structure the total number of 
        /// created correspondence per category and the total number of sent and received transfers
        /// </summary>
        /// <remarks>Not all data is retrieved (paging is supported). Must be non draft.</remarks>
        /// <param name="draw"></param>
        /// <param name="start">Starting record - The number of records to skip before starting to return records from the query. Default value is 0.</param>
        /// <param name="length">Number of records to be returned</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(GridListViewModel<StatisticalCorrespondenceListViewModel>), 200)]
        //[ValidateAntiForgeryToken]
        public JsonResult StatisticalCorrespondences([FromForm] int draw, [FromForm] int start, [FromForm] int length)
        {
            try
            {
                List<long> structuresIds = Request.Form["StructureIds[]"].Count > 0 ? new List<string>(Request.Form["StructureIds[]"].ToArray()).Select(long.Parse).ToList() : new List<long>();
                List<long> usersIds = Request.Form["UserIds[]"].Count > 0 ? new List<string>(Request.Form["UserIds[]"].ToArray()).Select(long.Parse).ToList() : new List<long>();
                string fromDate = Request.Form["FromDate"].Count > 0 ? Convert.ToString(Request.Form["FromDate"][0]) : string.Empty;
                string toDate = Request.Form["ToDate"].Count > 0 ? Convert.ToString(Request.Form["ToDate"][0]) : string.Empty;
                ExpressionBuilderFilters filter = new ExpressionBuilderFilters();

                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    filter.Add("CreatedDate.Date", from, Operator.GreaterThanOrEqual);
                }
                else
                {
                    DateTime from = new DateTime(DateTime.Now.Year, 1, 1).Date;
                    filter.Add("CreatedDate.Date", from, Operator.GreaterThanOrEqual);
                }

                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    filter.Add("CreatedDate.Date", to, Operator.LessThanOrEqualTo);
                }
                else
                {
                    DateTime to = DateTime.Now.Date.AddDays(1);
                    filter.Add("CreatedDate.Date", to, Operator.LessThanOrEqualTo);
                }

                var retValue = ((long)0, new List<StatisticalCorrespondenceListViewModel>());
                retValue = ManageReport.GetCreatedCountByStructure(UserId, StructureIds, RoleId, start, length, IsStructureReceiver, PrivacyLevel, structuresIds, usersIds, filter, Language);
                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = retValue.Item2
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }

        /// <summary>
        /// Displays for each structure and each user inside a structure the total number of 
        /// created correspondence per category and the total number of sent and received transfers
        /// </summary>
        /// <remarks>All data is retrieved (paging is not supported). Must be non draft.</remarks>
        /// <param name="draw"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(GridListViewModel<StatisticalCorrespondenceListViewModel>), 200)]
        //[ValidateAntiForgeryToken]
        public JsonResult StatisticalCorrespondencesRetrievingAllData([FromForm] int draw)
        {
            try
            {
                List<long> structuresIds = Request.Form["StructureIds[]"].Count > 0 ? new List<string>(Request.Form["StructureIds[]"].ToArray()).Select(long.Parse).ToList() : new List<long>();
                List<long> usersIds = Request.Form["UserIds[]"].Count > 0 ? new List<string>(Request.Form["UserIds[]"].ToArray()).Select(long.Parse).ToList() : new List<long>();
                string fromDate = Request.Form["FromDate"].Count > 0 ? Convert.ToString(Request.Form["FromDate"][0]) : string.Empty;
                string toDate = Request.Form["ToDate"].Count > 0 ? Convert.ToString(Request.Form["ToDate"][0]) : string.Empty;
                ExpressionBuilderFilters filter = new ExpressionBuilderFilters();

                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    filter.Add("CreatedDate.Date", from, Operator.GreaterThanOrEqual);
                }
                else
                {
                    DateTime from = new DateTime(DateTime.Now.Year, 1, 1).Date;
                    filter.Add("CreatedDate.Date", from, Operator.GreaterThanOrEqual);
                }

                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    filter.Add("CreatedDate.Date", to, Operator.LessThanOrEqualTo);
                }
                else
                {
                    DateTime to = DateTime.Now.Date.AddDays(1);
                    filter.Add("CreatedDate.Date", to, Operator.LessThanOrEqualTo);
                }

                var retValue = ((long)0, new List<StatisticalCorrespondenceListViewModel>());
                retValue = ManageReport.GetCreatedCountByStructureRetrievingAllData(UserId, StructureIds, RoleId, IsStructureReceiver, PrivacyLevel, structuresIds, usersIds, filter, Language);
                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = retValue.Item2
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }
         */

        /// <summary>
        /// Displays for each structure the total number of 
        /// created correspondence per category and the total number of sent and received transfers
        /// </summary>
        /// <remarks>Not all data is retrieved (paging is supported). Must be non draft.</remarks>
        /// <param name="draw"></param>
        /// <param name="start">Starting record - The number of records to skip before starting to return records from the query. Default value is 0.</param>
        /// <param name="length">Number of records to be returned</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(GridListViewModel<StatisticalCorrespondenceListViewModel>), 200)]
        [CustomMenusAuthorizationFilter(new string[] { nameof(CustomMenus.StatisticalCorrespondences) })]

        //[ValidateAntiForgeryToken]
        public async Task<JsonResult> StatisticalCorrespondences([FromForm] int draw, [FromForm] int start, [FromForm] int length)
        {
            try
            {
                List<long> structuresIds = Request.Form["StructureIds[]"].Count > 0 ? new List<string>(Request.Form["StructureIds[]"].ToArray()).Select(long.Parse).ToList() : new List<long>();
                List<long> usersIds = Request.Form["UserIds[]"].Count > 0 ? new List<string>(Request.Form["UserIds[]"].ToArray()).Select(long.Parse).ToList() : new List<long>();
                string fromDate = Request.Form["FromDate"].Count > 0 ? Convert.ToString(Request.Form["FromDate"][0]) : string.Empty;
                string toDate = Request.Form["ToDate"].Count > 0 ? Convert.ToString(Request.Form["ToDate"][0]) : string.Empty;
                long structureId = Request.Form["StructureId"].Count>0? Convert.ToInt16(Request.Form["StructureId"][0]) : default;
                ExpressionBuilderFilters filter = new ExpressionBuilderFilters();

                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    filter.Add("CreatedDate.Date", from, Operator.GreaterThanOrEqual);
                }
                else
                {
                    DateTime from = new DateTime(DateTime.Now.Year, 1, 1).Date;
                    filter.Add("CreatedDate.Date", from, Operator.GreaterThanOrEqual);
                }

                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    filter.Add("CreatedDate.Date", to, Operator.LessThanOrEqualTo);
                }
                else
                {
                    DateTime to = DateTime.Now.Date.AddDays(1);
                    filter.Add("CreatedDate.Date", to, Operator.LessThanOrEqualTo);
                }
                filter.Add("CategoryId", Core.Configuration.FollowUpCategory, Operator.NotEqual);

                List<long> LoggedInStructure = new List<long>();
                LoggedInStructure = StructureIds;
                if (structureId != default && Intalio.CTS.Core.Configuration.EnablePerStructure)
                {
                    LoggedInStructure = new List<long>();
                    LoggedInStructure.Add(structureId);
                }
               

                var retValue = ((long)0, new List<StatisticalCorrespondenceListViewModel>());
                retValue = await ManageReport.GetCreatedCountByStructure(UserId, LoggedInStructure, RoleId, start, length, IsStructureReceiver, PrivacyLevel, structuresIds, usersIds, fromDate, toDate, filter, Language);
                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = retValue.Item2
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }

        /// <summary>
        /// Displays for each  each user inside a structure the total number of 
        /// created correspondence per category and the total number of sent and received transfers
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(List<UserStatisticalCorrespondenceListViewModel>), 200)]
        [CustomMenusAuthorizationFilter(new string[] { nameof(CustomMenus.StatisticalCorrespondences) })]

        //[ValidateAntiForgeryToken]
        public async Task<IActionResult> GetStatisticalDataCreatedByUserByStructureId([FromForm] StatisticalReportByUserViewModel model)
        {
            try
            {
                return Ok(await ManageReport.GetStatisticalReportCreatedByUser(model.TargetStructureId, UserId, StructureIds, RoleId, IsStructureReceiver, PrivacyLevel, model.UsersIds, model.StructuresIds, model.FromDate, model.ToDate, Language));
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// Displays for each structure and each user inside a structure the total number of 
        /// created correspondence per category and the total number of sent and received transfers
        /// </summary>
        /// <remarks>All data is retrieved (paging is not supported). Must be non draft.</remarks>
        /// <param name="draw"></param>
        /// <param name="start">Starting record - The number of records to skip before starting to return records from the query. Default value is 0.</param>
        /// <param name="length">Number of records to be returned</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(GridListViewModel<StatisticalCorrespondenceListViewModel>), 200)]
        [CustomMenusAuthorizationFilter(new string[] { nameof(CustomMenus.StatisticalCorrespondences) })]

        //[ValidateAntiForgeryToken]
        public async Task<JsonResult> StatisticalCorrespondencesIncludeUsers([FromForm] int draw, [FromForm] int start, [FromForm] int length)
        {
            try
            {
                List<long> structuresIds = Request.Form["StructureIds[]"].Count > 0 ? new List<string>(Request.Form["StructureIds[]"].ToArray()).Select(long.Parse).ToList() : new List<long>();
                List<long> usersIds = Request.Form["UserIds[]"].Count > 0 ? new List<string>(Request.Form["UserIds[]"].ToArray()).Select(long.Parse).ToList() : new List<long>();
                string fromDate = Request.Form["FromDate"].Count > 0 ? Convert.ToString(Request.Form["FromDate"][0]) : string.Empty;
                string toDate = Request.Form["ToDate"].Count > 0 ? Convert.ToString(Request.Form["ToDate"][0]) : string.Empty;
                ExpressionBuilderFilters filter = new ExpressionBuilderFilters();

                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    filter.Add("CreatedDate.Date", from, Operator.GreaterThanOrEqual);
                }
                else
                {
                    DateTime from = new DateTime(DateTime.Now.Year, 1, 1).Date;
                    filter.Add("CreatedDate.Date", from, Operator.GreaterThanOrEqual);
                }

                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    filter.Add("CreatedDate.Date", to, Operator.LessThanOrEqualTo);
                }
                else
                {
                    DateTime to = DateTime.Now.Date.AddDays(1);
                    filter.Add("CreatedDate.Date", to, Operator.LessThanOrEqualTo);
                }

                var retValue = ((long)0, new List<StatisticalCorrespondenceListViewModel>());
                retValue = await ManageReport.GetCreatedCountByStructure(UserId, StructureIds, RoleId, start, length, IsStructureReceiver, PrivacyLevel, structuresIds, usersIds, fromDate, toDate, filter, Language, true);
                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = retValue.Item2
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }

        #endregion

        #region Administration

        /// <summary>
        /// List Completed Correspondences.
        /// </summary>
        /// <remarks>Not all data is retrieved (paging is supported). Must be non draft.</remarks>
        /// <param name="draw"></param>
        /// <param name="start">Starting record - The number of records to skip before starting to return records from the query. Default value is 0.</param>
        /// <param name="length">Number of records to be returned</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(GridListViewModel<CorrespondenceReportListViewModel>), 200)]
        //[Authorize(Roles = "Administrator")]
        [CustomMenusAuthorizationFilter(new string[] { nameof(CustomMenus.CompletedCorrespondences) })]
        [HideInSwagger]
        //[ValidateAntiForgeryToken]
        public async Task<JsonResult> ListCompletedCorrespondences([FromForm] int draw, [FromForm] int start, [FromForm] int length)
        {
            try
            {
                string structureIds = Request.Form["StructureIds"].Count > 0 ? Convert.ToString(Request.Form["StructureIds"][0]) : string.Empty;
                string userIds = Request.Form["UserIds"].Count > 0 ? Convert.ToString(Request.Form["UserIds"][0]) : string.Empty;
                string fromDate = Request.Form["FromDate"].Count > 0 ? Convert.ToString(Request.Form["FromDate"][0]) : string.Empty;
                string toDate = Request.Form["ToDate"].Count > 0 ? Convert.ToString(Request.Form["ToDate"][0]) : string.Empty;
                bool overdue = Request.Form["Overdue"].Count > 0 ? Convert.ToBoolean(Request.Form["Overdue"][0]) : default;
                int privacyId = Request.Form["PrivacyId"].Count > 0 ? Convert.ToInt32(Request.Form["PrivacyId"][0]) : default;
                int priorityId = Request.Form["PriorityId"].Count > 0 ? Convert.ToInt32(Request.Form["PriorityId"][0]) : default;
                ExpressionBuilderFilters filter = new ExpressionBuilderFilters();
                ExpressionBuilderFilters stcFilter = new ExpressionBuilderFilters();
                ExpressionBuilderFilters userFilter = new ExpressionBuilderFilters();
                if (!string.IsNullOrEmpty(structureIds))
                {
                    string[] array = structureIds.Split(Constants.SPLITTER);
                    foreach (var stcId in array)
                    {
                        stcFilter.Add("CreatedByStructureId", Convert.ToInt64(stcId), Operator.Equals);
                    }
                }
                if (!string.IsNullOrEmpty(userIds))
                {
                    string[] array = userIds.Split(Constants.SPLITTER);
                    foreach (var userId in array)
                    {
                        userFilter.Add("CreatedByUserId", Convert.ToInt64(userId), Operator.Equals);
                    }
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    filter.Add("CreatedDate.Date", from, Operator.GreaterThanOrEqual);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    filter.Add("CreatedDate.Date", to, Operator.LessThanOrEqualTo);
                }
                if (overdue)
                {
                    filter.Add("DueDate", DateTime.Now, Operator.LessThan);
                }
                if (privacyId != default)
                {
                    filter.Add("PrivacyId", privacyId, Operator.Equals);
                }
                if (priorityId != default)
                {
                    filter.Add("PriorityId", priorityId, Operator.Equals);
                }
                filter.Add("CategoryId", Intalio.CTS.Core.Configuration.FollowUpCategory, Operator.NotEqual);
                List<SortExpression> sorting = new List<SortExpression>();
                string column = Request.Form["order[0][column]"];
                string sort = Request.Form["order[0][dir]"];
                if (!string.IsNullOrEmpty(column))
                {
                    SortDirection sortDirection = sort == "asc" ? SortDirection.Ascending : SortDirection.Descending;
                    column = Request.Form[$"columns[{Convert.ToInt32(column)}][data]"];
                    switch (column)
                    {
                        case "categoryId":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "Category.Name" });
                            break;
                        case "sendingEntity":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "SendingEntity.Name" });
                            break;
                        case "subject":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "Subject" });
                            break;
                        case "createdDate":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "CreatedDate" });
                            break;
                    }
                }
                else
                {
                    sorting.Add(new SortExpression { Order = SortDirection.Descending, PropertyName = "CreatedDate" });
                }
                var retValue = ((long)0, new List<CorrespondenceReportListViewModel>());
                retValue = await ManageReport.ListCompletedCorrespondences(start, length, filter, stcFilter, userFilter, sorting, Language);
                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = retValue.Item2
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }

        /// <summary>
        /// List In Progress transfers.
        /// </summary>
        /// <remarks>All data is retrieved (paging is not supported). Must be non draft.</remarks>
        /// <param name="draw"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(GridListViewModel<CorrespondenceReportListViewModel>), 200)]
        //[ValidateAntiForgeryToken]
        //[Authorize(Roles = "Administrator")]
        [CustomMenusAuthorizationFilter(new string[] { nameof(CustomMenus.CompletedCorrespondences) })]
        [HideInSwagger]
        public async Task<JsonResult> ListCompletedCorrespondencesRetrievingAllData([FromForm] int draw)
        {
            try
            {
                string structureIds = Request.Form["StructureIds"].Count > 0 ? Convert.ToString(Request.Form["StructureIds"][0]) : string.Empty;
                string userIds = Request.Form["UserIds"].Count > 0 ? Convert.ToString(Request.Form["UserIds"][0]) : string.Empty;
                string fromDate = Request.Form["FromDate"].Count > 0 ? Convert.ToString(Request.Form["FromDate"][0]) : string.Empty;
                string toDate = Request.Form["ToDate"].Count > 0 ? Convert.ToString(Request.Form["ToDate"][0]) : string.Empty;
                bool overdue = Request.Form["Overdue"].Count > 0 ? Convert.ToBoolean(Request.Form["Overdue"][0]) : default;
                int privacyId = Request.Form["PrivacyId"].Count > 0 ? Convert.ToInt32(Request.Form["PrivacyId"][0]) : default;
                int priorityId = Request.Form["PriorityId"].Count > 0 ? Convert.ToInt32(Request.Form["PriorityId"][0]) : default;
                ExpressionBuilderFilters filter = new ExpressionBuilderFilters();
                ExpressionBuilderFilters stcFilter = new ExpressionBuilderFilters();
                ExpressionBuilderFilters userFilter = new ExpressionBuilderFilters();
                if (!string.IsNullOrEmpty(structureIds))
                {
                    string[] array = structureIds.Split(Constants.SPLITTER);
                    foreach (var stcId in array)
                    {
                        stcFilter.Add("CreatedByStructureId", Convert.ToInt64(stcId), Operator.Equals);
                    }
                }
                if (!string.IsNullOrEmpty(userIds))
                {
                    string[] array = userIds.Split(Constants.SPLITTER);
                    foreach (var userId in array)
                    {
                        userFilter.Add("CreatedByUserId", Convert.ToInt64(userId), Operator.Equals);
                    }
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    filter.Add("CreatedDate.Date", from, Operator.GreaterThanOrEqual);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    filter.Add("CreatedDate.Date", to, Operator.LessThanOrEqualTo);
                }
                if (overdue)
                {
                    filter.Add("DueDate", DateTime.Now, Operator.LessThan);
                }
                if (privacyId != default)
                {
                    filter.Add("PrivacyId", privacyId, Operator.Equals);
                }
                if (priorityId != default)
                {
                    filter.Add("PriorityId", priorityId, Operator.Equals);
                }
                var retValue = ((long)0, new List<CorrespondenceReportListViewModel>());
                retValue = await ManageReport.ListCompletedCorrespondencesRetrievingAllData(filter, stcFilter, userFilter, Language);
                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = retValue.Item2
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }

        /// <summary>
        /// List In Progress correspondences.
        /// </summary>
        /// <remarks>Not all data is retrieved (paging is supported). Must be non draft.</remarks>
        /// <param name="draw"></param>
        /// <param name="start">Starting record - The number of records to skip before starting to return records from the query. Default value is 0.</param>
        /// <param name="length">Number of records to be returned</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(GridListViewModel<CorrespondenceReportListViewModel>), 200)]
        //[ValidateAntiForgeryToken]
        //[Authorize(Roles = "Administrator")]
        [CustomMenusAuthorizationFilter(new string[] { nameof(CustomMenus.InProgressCorrespondences) })]
        [HideInSwagger]
        public async Task<JsonResult> ListInProgressCorrespondences([FromForm] int draw, [FromForm] int start, [FromForm] int length)
        {
            try
            {
                string structureIds = Request.Form["StructureIds"].Count > 0 ? Convert.ToString(Request.Form["StructureIds"][0]) : string.Empty;
                string userIds = Request.Form["UserIds"].Count > 0 ? Convert.ToString(Request.Form["UserIds"][0]) : string.Empty;
                string fromDate = Request.Form["FromDate"].Count > 0 ? Convert.ToString(Request.Form["FromDate"][0]) : string.Empty;
                string toDate = Request.Form["ToDate"].Count > 0 ? Convert.ToString(Request.Form["ToDate"][0]) : string.Empty;
                bool overdue = Request.Form["Overdue"].Count > 0 ? Convert.ToBoolean(Request.Form["Overdue"][0]) : default;
                int privacyId = Request.Form["PrivacyId"].Count > 0 ? Convert.ToInt32(Request.Form["PrivacyId"][0]) : default;
                int priorityId = Request.Form["PriorityId"].Count > 0 ? Convert.ToInt32(Request.Form["PriorityId"][0]) : default;
                ExpressionBuilderFilters filter = new ExpressionBuilderFilters();
                ExpressionBuilderFilters stcFilter = new ExpressionBuilderFilters();
                ExpressionBuilderFilters userFilter = new ExpressionBuilderFilters();
                if (!string.IsNullOrEmpty(structureIds))
                {
                    string[] array = structureIds.Split(Constants.SPLITTER);
                    foreach (var stcId in array)
                    {
                        stcFilter.Add("CreatedByStructureId", Convert.ToInt64(stcId), Operator.Equals);
                    }
                }
                if (!string.IsNullOrEmpty(userIds))
                {
                    string[] array = userIds.Split(Constants.SPLITTER);
                    foreach (var userId in array)
                    {
                        userFilter.Add("CreatedByUserId", Convert.ToInt64(userId), Operator.Equals);
                    }
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    filter.Add("CreatedDate.Date", from, Operator.GreaterThanOrEqual);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    filter.Add("CreatedDate.Date", to, Operator.LessThanOrEqualTo);
                }
                if (overdue)
                {
                    filter.Add("DueDate", DateTime.Now, Operator.LessThan);
                }
                if (privacyId != default)
                {
                    filter.Add("PrivacyId", privacyId, Operator.Equals);
                }
                if (priorityId != default)
                {
                    filter.Add("PriorityId", priorityId, Operator.Equals);
                }
                filter.Add("CategoryId", Intalio.CTS.Core.Configuration.FollowUpCategory, Operator.NotEqual);
                List<SortExpression> sorting = new List<SortExpression>();
                string column = Request.Form["order[0][column]"];
                string sort = Request.Form["order[0][dir]"];
                if (!string.IsNullOrEmpty(column))
                {
                    SortDirection sortDirection = sort == "asc" ? SortDirection.Ascending : SortDirection.Descending;
                    column = Request.Form[$"columns[{Convert.ToInt32(column)}][data]"];
                    switch (column)
                    {
                        case "categoryId":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "Category.Name" });
                            break;
                        case "sendingEntity":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "SendingEntity.Name" });
                            break;
                        case "subject":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "Subject" });
                            break;
                        case "createdDate":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "CreatedDate" });
                            break;
                    }
                }
                else
                {
                    sorting.Add(new SortExpression { Order = SortDirection.Descending, PropertyName = "CreatedDate" });
                }
                var retValue = ((long)0, new List<CorrespondenceReportListViewModel>());
                retValue = await ManageReport.ListInProgressCorrespondences(start, length, filter, stcFilter, userFilter, sorting, Language);
                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = retValue.Item2
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }

        /// <summary>
        /// List In Progress transfers.
        /// </summary>
        /// <remarks>All data is retrieved (paging is not supported). Must be non draft.</remarks>
        /// <param name="draw"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(GridListViewModel<CorrespondenceReportListViewModel>), 200)]
        //[ValidateAntiForgeryToken]
        //[Authorize(Roles = "Administrator")]
        [CustomMenusAuthorizationFilter(new string[] { nameof(CustomMenus.InProgressCorrespondences) })]
        [HideInSwagger]
        public async Task<JsonResult> ListInProgressCorrespondencesRetrievingAllData([FromForm] int draw)
        {
            try
            {
                string structureIds = Request.Form["StructureIds"].Count > 0 ? Convert.ToString(Request.Form["StructureIds"][0]) : string.Empty;
                string userIds = Request.Form["UserIds"].Count > 0 ? Convert.ToString(Request.Form["UserIds"][0]) : string.Empty;
                string fromDate = Request.Form["FromDate"].Count > 0 ? Convert.ToString(Request.Form["FromDate"][0]) : string.Empty;
                string toDate = Request.Form["ToDate"].Count > 0 ? Convert.ToString(Request.Form["ToDate"][0]) : string.Empty;
                bool overdue = Request.Form["Overdue"].Count > 0 ? Convert.ToBoolean(Request.Form["Overdue"][0]) : default;
                int privacyId = Request.Form["PrivacyId"].Count > 0 ? Convert.ToInt32(Request.Form["PrivacyId"][0]) : default;
                int priorityId = Request.Form["PriorityId"].Count > 0 ? Convert.ToInt32(Request.Form["PriorityId"][0]) : default;
                ExpressionBuilderFilters filter = new ExpressionBuilderFilters();
                ExpressionBuilderFilters stcFilter = new ExpressionBuilderFilters();
                ExpressionBuilderFilters userFilter = new ExpressionBuilderFilters();
                if (!string.IsNullOrEmpty(structureIds))
                {
                    string[] array = structureIds.Split(Constants.SPLITTER);
                    foreach (var stcId in array)
                    {
                        stcFilter.Add("CreatedByStructureId", Convert.ToInt64(stcId), Operator.Equals);
                    }
                }
                if (!string.IsNullOrEmpty(userIds))
                {
                    string[] array = userIds.Split(Constants.SPLITTER);
                    foreach (var userId in array)
                    {
                        userFilter.Add("CreatedByUserId", Convert.ToInt64(userId), Operator.Equals);
                    }
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    filter.Add("CreatedDate.Date", from, Operator.GreaterThanOrEqual);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    filter.Add("CreatedDate.Date", to, Operator.LessThanOrEqualTo);
                }
                if (overdue)
                {
                    filter.Add("DueDate", DateTime.Now, Operator.LessThan);
                }
                if (privacyId != default)
                {
                    filter.Add("PrivacyId", privacyId, Operator.Equals);
                }
                if (priorityId != default)
                {
                    filter.Add("PriorityId", priorityId, Operator.Equals);
                }
                var retValue = ((long)0, new List<CorrespondenceReportListViewModel>());
                retValue = await ManageReport.ListInProgressCorrespondencesRetrievingAllData(filter, stcFilter, userFilter, Language);
                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = retValue.Item2
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }

        /// <summary>
        /// List In Progress transfers.
        /// </summary>
        /// <remarks>Not all data is retrieved (paging is supported). Must be non draft.</remarks>
        /// <param name="draw"></param>
        /// <param name="start">Starting record - The number of records to skip before starting to return records from the query. Default value is 0.</param>
        /// <param name="length">Number of records to be returned</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(GridListViewModel<TransferReportListViewModel>), 200)]
        //[Authorize(Roles = "Administrator")]
        [CustomMenusAuthorizationFilter(new string[] { nameof(CustomMenus.InProgressTransfers) })]
        [HideInSwagger]
        //[ValidateAntiForgeryToken]
        public async Task<JsonResult> ListInProgressTransfers([FromForm] int draw, [FromForm] int start, [FromForm] int length)
        {
            try
            {
                string structureIds = Request.Form["StructureIds"].Count > 0 ? Convert.ToString(Request.Form["StructureIds"][0]) : string.Empty;
                string userIds = Request.Form["UserIds"].Count > 0 ? Convert.ToString(Request.Form["UserIds"][0]) : string.Empty;
                string fromDate = Request.Form["FromDate"].Count > 0 ? Convert.ToString(Request.Form["FromDate"][0]) : string.Empty;
                string toDate = Request.Form["ToDate"].Count > 0 ? Convert.ToString(Request.Form["ToDate"][0]) : string.Empty;
                bool overdue = Request.Form["Overdue"].Count > 0 ? Convert.ToBoolean(Request.Form["Overdue"][0]) : default;
                ExpressionBuilderFilters filter = new ExpressionBuilderFilters();
                ExpressionBuilderFilters stcFilter = new ExpressionBuilderFilters();
                ExpressionBuilderFilters userFilter = new ExpressionBuilderFilters();
                if (!string.IsNullOrEmpty(structureIds))
                {
                    string[] array = structureIds.Split(Constants.SPLITTER);
                    foreach (var stcId in array)
                    {
                        stcFilter.Add("FromStructureId", Convert.ToInt64(stcId), Operator.Equals);
                    }
                }
                if (!string.IsNullOrEmpty(userIds))
                {
                    string[] array = userIds.Split(Constants.SPLITTER);
                    foreach (var userId in array)
                    {
                        userFilter.Add("FromUserId", Convert.ToInt64(userId), Operator.Equals);
                    }
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    filter.Add("CreatedDate.Date", from, Operator.GreaterThanOrEqual);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    filter.Add("CreatedDate.Date", to, Operator.LessThanOrEqualTo);
                }
                if (overdue)
                {
                    filter.Add("DueDate", DateTime.Now, Operator.LessThan);
                }
                filter.Add("Document.CategoryId", Intalio.CTS.Core.Configuration.FollowUpCategory, Operator.NotEqual);
                List<SortExpression> sorting = new List<SortExpression>();
                string column = Request.Form["order[0][column]"];
                string sort = Request.Form["order[0][dir]"];
                if (!string.IsNullOrEmpty(column))
                {
                    SortDirection sortDirection = sort == "asc" ? SortDirection.Ascending : SortDirection.Descending;
                    column = Request.Form[$"columns[{Convert.ToInt32(column)}][data]"];
                    switch (column)
                    {
                        case "categoryId":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "Document.Category.Name" });
                            break;
                        case "subject":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "Document.Subject" });
                            break;
                        case "transferDate":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "CreatedDate" });
                            break;
                    }
                }
                else
                {
                    sorting.Add(new SortExpression { Order = SortDirection.Descending, PropertyName = "CreatedDate" });
                }
                var retValue = ((long)0, new List<TransferReportListViewModel>());
                retValue = await ManageReport.ListInProgressTransfers(start, length, filter, stcFilter, userFilter, sorting, Language);
                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = retValue.Item2
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }

        /// <summary>
        /// List In Progress transfers.
        /// </summary>
        /// <remarks>All data is retrieved (paging is not supported). Must be non draft.</remarks>
        /// <param name="draw"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(GridListViewModel<TransferReportListViewModel>), 200)]
        //[Authorize(Roles = "Administrator")]
        [CustomMenusAuthorizationFilter(new string[] { nameof(CustomMenus.InProgressTransfers) })]
        [HideInSwagger]
        //[ValidateAntiForgeryToken]
        public async Task<JsonResult> ListInProgressTransfersRetrievingAllData([FromForm] int draw)
        {
            try
            {
                string structureIds = Request.Form["StructureIds"].Count > 0 ? Convert.ToString(Request.Form["StructureIds"][0]) : string.Empty;
                string userIds = Request.Form["UserIds"].Count > 0 ? Convert.ToString(Request.Form["UserIds"][0]) : string.Empty;
                string fromDate = Request.Form["FromDate"].Count > 0 ? Convert.ToString(Request.Form["FromDate"][0]) : string.Empty;
                string toDate = Request.Form["ToDate"].Count > 0 ? Convert.ToString(Request.Form["ToDate"][0]) : string.Empty;
                bool overdue = Request.Form["Overdue"].Count > 0 ? Convert.ToBoolean(Request.Form["Overdue"][0]) : default;
                ExpressionBuilderFilters filter = new ExpressionBuilderFilters();
                ExpressionBuilderFilters stcFilter = new ExpressionBuilderFilters();
                ExpressionBuilderFilters userFilter = new ExpressionBuilderFilters();
                if (!string.IsNullOrEmpty(structureIds))
                {
                    string[] array = structureIds.Split(Constants.SPLITTER);
                    foreach (var stcId in array)
                    {
                        stcFilter.Add("FromStructureId", Convert.ToInt64(stcId), Operator.Equals);
                    }
                }
                if (!string.IsNullOrEmpty(userIds))
                {
                    string[] array = userIds.Split(Constants.SPLITTER);
                    foreach (var userId in array)
                    {
                        userFilter.Add("FromUserId", Convert.ToInt64(userId), Operator.Equals);
                    }
                }

                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    filter.Add("CreatedDate.Date", from, Operator.GreaterThanOrEqual);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    filter.Add("CreatedDate.Date", to, Operator.LessThanOrEqualTo);
                }
                if (overdue)
                {
                    filter.Add("DueDate", DateTime.Now, Operator.LessThanOrEqualTo);
                }
                var retValue = ((long)0, new List<TransferReportListViewModel>());
                retValue = await ManageReport.ListInProgressTransfersRetrievingAllData(filter, stcFilter, userFilter, Language);
                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = retValue.Item2
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }

        /// <summary>
        /// List Completed transfers.
        /// </summary>
        /// <remarks>Not all data is retrieved (paging is supported). Must be non draft.</remarks>
        /// <param name="draw"></param>
        /// <param name="start">Starting record - The number of records to skip before starting to return records from the query. Default value is 0.</param>
        /// <param name="length">Number of records to be returned</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(GridListViewModel<TransferReportListViewModel>), 200)]
        //[Authorize(Roles = "Administrator")]
        [CustomMenusAuthorizationFilter(new string[] { nameof(CustomMenus.CompletedTransfers) })]
        [HideInSwagger]
        //[ValidateAntiForgeryToken]
        public async Task<JsonResult> ListCompletedTransfers([FromForm] int draw, [FromForm] int start, [FromForm] int length)
        {
            try
            {
                string structureIds = Request.Form["StructureIds"].Count > 0 ? Convert.ToString(Request.Form["StructureIds"][0]) : string.Empty;
                string userIds = Request.Form["UserIds"].Count > 0 ? Convert.ToString(Request.Form["UserIds"][0]) : string.Empty;
                string fromDate = Request.Form["FromDate"].Count > 0 ? Convert.ToString(Request.Form["FromDate"][0]) : string.Empty;
                string toDate = Request.Form["ToDate"].Count > 0 ? Convert.ToString(Request.Form["ToDate"][0]) : string.Empty;
                bool overdue = Request.Form["Overdue"].Count > 0 ? Convert.ToBoolean(Request.Form["Overdue"][0]) : default;
                ExpressionBuilderFilters filter = new ExpressionBuilderFilters();
                ExpressionBuilderFilters stcFilter = new ExpressionBuilderFilters();
                ExpressionBuilderFilters userFilter = new ExpressionBuilderFilters();
                if (structureIds != string.Empty)
                {
                    string[] array = structureIds.Split(Constants.SPLITTER);
                    foreach (var stcId in array)
                    {
                        stcFilter.Add("FromStructureId", Convert.ToInt64(stcId), Operator.Equals);
                    }
                }
                if (userIds != string.Empty)
                {
                    string[] array = userIds.Split(Constants.SPLITTER);
                    foreach (var userId in array)
                    {
                        userFilter.Add("FromUserId", Convert.ToInt64(userId), Operator.Equals);
                    }
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    filter.Add("CreatedDate.Date", from, Operator.GreaterThanOrEqual);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    filter.Add("CreatedDate.Date", to, Operator.LessThanOrEqualTo);
                }
                if (overdue)
                {
                    filter.Add("DueDate", DateTime.Now, Operator.LessThan);
                }
                filter.Add("Document.CategoryId", Intalio.CTS.Core.Configuration.FollowUpCategory, Operator.NotEqual);
                List<SortExpression> sorting = new List<SortExpression>();
                string column = Request.Form["order[0][column]"];
                string sort = Request.Form["order[0][dir]"];
                if (!string.IsNullOrEmpty(column))
                {
                    SortDirection sortDirection = sort == "asc" ? SortDirection.Ascending : SortDirection.Descending;
                    column = Request.Form[$"columns[{Convert.ToInt32(column)}][data]"];
                    switch (column)
                    {
                        case "categoryId":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "Document.Category.Name" });
                            break;
                        case "subject":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "Document.Subject" });
                            break;
                        case "transferDate":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "CreatedDate" });
                            break;
                    }
                }
                else
                {
                    sorting.Add(new SortExpression { Order = SortDirection.Descending, PropertyName = "CreatedDate" });
                }
                var retValue = ((long)0, new List<TransferReportListViewModel>());
                retValue = await ManageReport.ListCompletedTransfers(start, length, filter, stcFilter, userFilter, sorting, Language);
                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = retValue.Item2
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }

        /// <summary>
        /// List Completed transfers.
        /// </summary>
        /// <remarks>All data is retrieved (paging is not supported). Must be non draft.</remarks>
        /// <param name="draw"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(GridListViewModel<TransferReportListViewModel>), 200)]
        //[Authorize(Roles = "Administrator")]
        [CustomMenusAuthorizationFilter(new string[] { nameof(CustomMenus.CompletedTransfers) })]
        [HideInSwagger]
        //[ValidateAntiForgeryToken]
        public async Task<JsonResult> ListCompletedTransfersRetrievingAllData([FromForm] int draw)
        {
            try
            {
                string structureIds = Request.Form["StructureIds"].Count > 0 ? Convert.ToString(Request.Form["StructureIds"][0]) : string.Empty;
                string userIds = Request.Form["UserIds"].Count > 0 ? Convert.ToString(Request.Form["UserIds"][0]) : string.Empty;
                string fromDate = Request.Form["FromDate"].Count > 0 ? Convert.ToString(Request.Form["FromDate"][0]) : string.Empty;
                string toDate = Request.Form["ToDate"].Count > 0 ? Convert.ToString(Request.Form["ToDate"][0]) : string.Empty;
                bool overdue = Request.Form["Overdue"].Count > 0 ? Convert.ToBoolean(Request.Form["Overdue"][0]) : default;
                ExpressionBuilderFilters filter = new ExpressionBuilderFilters();
                ExpressionBuilderFilters stcFilter = new ExpressionBuilderFilters();
                ExpressionBuilderFilters userFilter = new ExpressionBuilderFilters();
                if (!string.IsNullOrEmpty(structureIds))
                {
                    string[] array = structureIds.Split(Constants.SPLITTER);
                    foreach (var stcId in array)
                    {
                        stcFilter.Add("FromStructureId", Convert.ToInt64(stcId), Operator.Equals);
                    }
                }
                if (!string.IsNullOrEmpty(userIds))
                {
                    string[] array = userIds.Split(Constants.SPLITTER);
                    foreach (var userId in array)
                    {
                        userFilter.Add("FromUserId", Convert.ToInt64(userId), Operator.Equals);
                    }
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    filter.Add("CreatedDate.Date", from, Operator.GreaterThanOrEqual);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    filter.Add("CreatedDate.Date", to, Operator.LessThanOrEqualTo);
                }
                if (overdue)
                {
                    filter.Add("DueDate", DateTime.Now, Operator.LessThan);
                }
                var retValue = ((long)0, new List<TransferReportListViewModel>());
                retValue = await ManageReport.ListCompletedTransfersRetrievingAllData(filter, stcFilter, userFilter, Language);
                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = retValue.Item2
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }

        /// <summary>
        /// List Operation by user.
        /// </summary>
        /// <remarks>Not all data is retrieved (paging is supported). Must be non draft.</remarks>
        /// <param name="draw"></param>
        /// <param name="start">Starting record - The number of records to skip before starting to return records from the query. Default value is 0.</param>
        /// <param name="length">Number of records to be returned</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(GridListViewModel<AuditReportListViewModel>), 200)]
        //[Authorize(Roles = "Administrator")]
        [CustomMenusAuthorizationFilter(new string[] { nameof(CustomMenus.OperationByUser) })]
        [HideInSwagger]
        //[ValidateAntiForgeryToken]
        public async Task<JsonResult> ListOperationByUser([FromForm] int draw, [FromForm] int start, [FromForm] int length)
        {
            try
            {
                string userIds = Request.Form["UserIds"].Count > 0 ? Convert.ToString(Request.Form["UserIds"][0]) : string.Empty;
                string fromDate = Request.Form["FromDate"].Count > 0 ? Convert.ToString(Request.Form["FromDate"][0]) : string.Empty;
                string toDate = Request.Form["ToDate"].Count > 0 ? Convert.ToString(Request.Form["ToDate"][0]) : string.Empty;
                int categoryId = Request.Form["CategoryId"].Count > 0 ? Convert.ToInt32(Request.Form["CategoryId"][0]) : default;
                int actionId = Request.Form["ActionId"].Count > 0 ? Convert.ToInt32(Request.Form["ActionId"][0]) : default;
                ExpressionBuilderFilters filter = new ExpressionBuilderFilters();
                ExpressionBuilderFilters userFilter = new ExpressionBuilderFilters();
                if (!string.IsNullOrEmpty(userIds))
                {
                    string[] array = userIds.Split(Constants.SPLITTER);
                    foreach (var userId in array)
                    {
                        userFilter.Add("UserId", Convert.ToInt64(userId), Operator.Equals);
                    }
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    filter.Add("CreatedDate.Date", from, Operator.GreaterThanOrEqual);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    filter.Add("CreatedDate.Date", to, Operator.LessThanOrEqualTo);
                }
                if (categoryId != default)
                {
                    filter.Add("Document.CategoryId", categoryId, Operator.Equals);
                }
                if (actionId != default)
                {
                    filter.Add("ActivityLogActionId", actionId, Operator.Equals);
                }

                List<SortExpression> sorting = new List<SortExpression>();
                string column = Request.Form["order[0][column]"];
                string sort = Request.Form["order[0][dir]"];
                if (!string.IsNullOrEmpty(column))
                {
                    SortDirection sortDirection = sort == "asc" ? SortDirection.Ascending : SortDirection.Descending;
                    column = Request.Form[$"columns[{Convert.ToInt32(column)}][data]"];
                    switch (column)
                    {
                        case "categoryId":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "Document.Category.Name" });
                            break;
                        case "subject":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "Document.Subject" });
                            break;
                        case "createdDate":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "CreatedDate" });
                            break;
                        case "action":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "ActivityLogAction.Name" });
                            break;
                    }
                }
                else
                {
                    sorting.Add(new SortExpression { Order = SortDirection.Descending, PropertyName = "CreatedDate" });
                }
                var retValue = ((long)0, new List<AuditReportListViewModel>());
                retValue = await ManageReport.ListOperationByUser(start, length, filter, userFilter, sorting, Language);
                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = retValue.Item2
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }

        /// <summary>
        ///  List Operation by user.
        /// </summary>
        /// <remarks>All data is retrieved (paging is not supported). Must be non draft.</remarks>
        /// <param name="draw"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(GridListViewModel<AuditReportListViewModel>), 200)]
        //[Authorize(Roles = "Administrator")]
        [CustomMenusAuthorizationFilter(new string[] { nameof(CustomMenus.OperationByUser) })]
        [HideInSwagger]
        //[ValidateAntiForgeryToken]
        public async Task<JsonResult> ListOperationByUserRetrievingAllData([FromForm] int draw)
        {
            try
            {
                string userIds = Request.Form["UserIds"].Count > 0 ? Convert.ToString(Request.Form["UserIds"][0]) : string.Empty;
                string fromDate = Request.Form["FromDate"].Count > 0 ? Convert.ToString(Request.Form["FromDate"][0]) : string.Empty;
                string toDate = Request.Form["ToDate"].Count > 0 ? Convert.ToString(Request.Form["ToDate"][0]) : string.Empty;
                int categoryId = Request.Form["CategoryId"].Count > 0 ? Convert.ToInt32(Request.Form["CategoryId"][0]) : default;
                int actionId = Request.Form["ActionId"].Count > 0 ? Convert.ToInt32(Request.Form["ActionId"][0]) : default;
                ExpressionBuilderFilters filter = new ExpressionBuilderFilters();
                ExpressionBuilderFilters stcFilter = new ExpressionBuilderFilters();
                ExpressionBuilderFilters userFilter = new ExpressionBuilderFilters();
                if (!string.IsNullOrEmpty(userIds))
                {
                    string[] array = userIds.Split(Constants.SPLITTER);
                    foreach (var userId in array)
                    {
                        userFilter.Add("UserId", Convert.ToInt64(userId), Operator.Equals);
                    }
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    filter.Add("CreatedDate.Date", from, Operator.GreaterThanOrEqual);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    filter.Add("CreatedDate.Date", to, Operator.LessThanOrEqualTo);
                }
                if (categoryId != default)
                {
                    filter.Add("Document.CategoryId", categoryId, Operator.Equals);
                }
                if (actionId != default)
                {
                    filter.Add("ActivityLogActionId", actionId, Operator.Equals);
                }
                var retValue = ((long)0, new List<AuditReportListViewModel>());
                retValue = await ManageReport.ListOperationByUserRetrievingAllData(filter, userFilter, Language);
                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = retValue.Item2
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }

        /// <summary>
        /// List Operation by correspondence.
        /// </summary>
        /// <remarks>Not all data is retrieved (paging is supported). Must be non draft.</remarks>
        /// <param name="draw"></param>
        /// <param name="start">Starting record - The number of records to skip before starting to return records from the query. Default value is 0.</param>
        /// <param name="length">Number of records to be returned</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(GridListViewModel<AuditReportListViewModel>), 200)]
        //[Authorize(Roles = "Administrator")]
        [CustomMenusAuthorizationFilter(new string[] { nameof(CustomMenus.OperationByCorrespondence) })]
        [HideInSwagger]
        //[ValidateAntiForgeryToken]
        public async Task<JsonResult> ListOperationByCorrespondence([FromForm] int draw, [FromForm] int start, [FromForm] int length)
        {
            try
            {
                string referenceNumber = Request.Form["ReferenceNumber"].Count > 0 ? Convert.ToString(Request.Form["ReferenceNumber"][0]) : string.Empty;
                int actionId = Request.Form["ActionId"].Count > 0 ? Convert.ToInt32(Request.Form["ActionId"][0]) : default;
                ExpressionBuilderFilters filter = new ExpressionBuilderFilters();
                if (!string.IsNullOrEmpty(referenceNumber))
                {
                    filter.Add("Document.ReferenceNumber", referenceNumber, Operator.Contains);
                }
                if (actionId != default)
                {
                    filter.Add("ActivityLogActionId", actionId, Operator.Equals);
                }
                filter.Add("Document.CategoryId", Intalio.CTS.Core.Configuration.FollowUpCategory, Operator.NotEqual);
                List<SortExpression> sorting = new List<SortExpression>();
                string column = Request.Form["order[0][column]"];
                string sort = Request.Form["order[0][dir]"];
                if (!string.IsNullOrEmpty(column))
                {
                    SortDirection sortDirection = sort == "asc" ? SortDirection.Ascending : SortDirection.Descending;
                    column = Request.Form[$"columns[{Convert.ToInt32(column)}][data]"];
                    switch (column)
                    {
                        case "subject":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "Document.Subject" });
                            break;
                        case "createdDate":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "CreatedDate" });
                            break;
                        case "action":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "ActivityLogAction.Name" });
                            break;
                    }
                }
                else
                {
                    sorting.Add(new SortExpression { Order = SortDirection.Descending, PropertyName = "CreatedDate" });
                }
                var retValue = ((long)0, new List<AuditReportListViewModel>());
                retValue = await ManageReport.ListOperationByCorrespondence(start, length, filter, sorting, Language);
                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = retValue.Item2
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }

        /// <summary>
        /// List Operation by correspondence.
        /// </summary>
        /// <remarks>All data is retrieved (paging is not supported). Must be non draft.</remarks>
        /// <param name="draw"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(GridListViewModel<AuditReportListViewModel>), 200)]
        //[Authorize(Roles = "Administrator")]
        [CustomMenusAuthorizationFilter(new string[] { nameof(CustomMenus.OperationByCorrespondence) })]
        [HideInSwagger]
        //[ValidateAntiForgeryToken]
        public async Task<JsonResult> ListOperationByCorrespondenceRetrievingAllData([FromForm] int draw)
        {
            try
            {
                string referenceNumber = Request.Form["ReferenceNumber"].Count > 0 ? Convert.ToString(Request.Form["ReferenceNumber"][0]) : string.Empty;
                int actionId = Request.Form["ActionId"].Count > 0 ? Convert.ToInt32(Request.Form["ActionId"][0]) : default;
                ExpressionBuilderFilters filter = new ExpressionBuilderFilters();
                if (!string.IsNullOrEmpty(referenceNumber))
                {
                    filter.Add("Document.ReferenceNumber", referenceNumber, Operator.Contains);
                }
                if (actionId != default)
                {
                    filter.Add("ActivityLogActionId", actionId, Operator.Equals);
                }
                var retValue = ((long)0, new List<AuditReportListViewModel>());
                retValue = await ManageReport.ListOperationByCorrespondenceRetrievingAllData(filter, Language);
                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = retValue.Item2
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }

        /// <summary>
        /// List In Progress transfers.
        /// </summary>
        /// <remarks>Not all data is retrieved (paging is supported). Must be non draft.</remarks>
        /// <param name="draw"></param>
        /// <param name="start">Starting record - The number of records to skip before starting to return records from the query. Default value is 0.</param>
        /// <param name="length">Number of records to be returned</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(GridListViewModel<TransferReportListViewModel>), 200)]
        //[Authorize(Roles = "Administrator")]
        [CustomMenusAuthorizationFilter(new string[] { nameof(CustomMenus.TransfersSentToStructure) })]
        [HideInSwagger]
        //[ValidateAntiForgeryToken]
        public async Task<JsonResult> ListTransfersSentToStructure([FromForm] int draw, [FromForm] int start, [FromForm] int length)
        {
            try
            {
                int structureId = Request.Form["structureId"].Count > 0 ? Convert.ToInt32(Request.Form["StructureId"][0]) : 0;
                string userIds = Request.Form["UserIds"].Count > 0 ? Convert.ToString(Request.Form["UserIds"][0]) : string.Empty;
                string fromDate = Request.Form["FromDate"].Count > 0 ? Convert.ToString(Request.Form["FromDate"][0]) : string.Empty;
                string toDate = Request.Form["ToDate"].Count > 0 ? Convert.ToString(Request.Form["ToDate"][0]) : string.Empty;
                bool overdue = Request.Form["Overdue"].Count > 0 ? Convert.ToBoolean(Request.Form["Overdue"][0]) : default;
                ExpressionBuilderFilters filter = new ExpressionBuilderFilters();
                ExpressionBuilderFilters stcFilter = new ExpressionBuilderFilters();
                ExpressionBuilderFilters userFilter = new ExpressionBuilderFilters();
               
                if (!string.IsNullOrEmpty(userIds))
                {
                    string[] array = userIds.Split(Constants.SPLITTER);
                    foreach (var userId in array)
                    {
                        userFilter.Add("FromUserId", Convert.ToInt64(userId), Operator.Equals);
                    }
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    filter.Add("CreatedDate.Date", from, Operator.GreaterThanOrEqual);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    filter.Add("CreatedDate.Date", to, Operator.LessThanOrEqualTo);
                }
                if (overdue)
                {
                    filter.Add("DueDate", DateTime.Now, Operator.LessThan);
                }
                filter.Add("Document.CategoryId", Intalio.CTS.Core.Configuration.FollowUpCategory, Operator.NotEqual);
                List<SortExpression> sorting = new List<SortExpression>();
                string column = Request.Form["order[0][column]"];
                string sort = Request.Form["order[0][dir]"];
                if (!string.IsNullOrEmpty(column))
                {
                    SortDirection sortDirection = sort == "asc" ? SortDirection.Ascending : SortDirection.Descending;
                    column = Request.Form[$"columns[{Convert.ToInt32(column)}][data]"];
                    switch (column)
                    {
                        case "categoryId":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "Document.Category.Name" });
                            break;
                        case "subject":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "Document.Subject" });
                            break;
                        case "transferDate":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "CreatedDate" });
                            break;
                    }
                }
                else
                {
                    sorting.Add(new SortExpression { Order = SortDirection.Descending, PropertyName = "CreatedDate" });
                }
                var retValue = ((long)0, new List<TransferReportListViewModel>());
                retValue = await ManageReport.ListTransfersSentToStructureAsync(start, length,structureId, filter, userFilter, sorting, Language);
                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = retValue.Item2
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }

        /// <summary>
        /// List In Progress transfers.
        /// </summary>
        /// <remarks>All data is retrieved (paging is not supported). Must be non draft.</remarks>
        /// <param name="draw"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(GridListViewModel<TransferReportListViewModel>), 200)]
        //[Authorize(Roles = "Administrator")]
        [CustomMenusAuthorizationFilter(new string[] { nameof(CustomMenus.TransfersSentToStructure) })]
        [HideInSwagger]
        //[ValidateAntiForgeryToken]
        public async Task<JsonResult> TransfersSentToStructureRetrievingAllData([FromForm] int draw)
        {
            try
            {
                int structureId = Request.Form["structureId"].Count > 0 ? Convert.ToInt32(Request.Form["StructureId"][0]) : 0;
                string userIds = Request.Form["UserIds"].Count > 0 ? Convert.ToString(Request.Form["UserIds"][0]) : string.Empty;
                string fromDate = Request.Form["FromDate"].Count > 0 ? Convert.ToString(Request.Form["FromDate"][0]) : string.Empty;
                string toDate = Request.Form["ToDate"].Count > 0 ? Convert.ToString(Request.Form["ToDate"][0]) : string.Empty;
                bool overdue = Request.Form["Overdue"].Count > 0 ? Convert.ToBoolean(Request.Form["Overdue"][0]) : default;
                ExpressionBuilderFilters filter = new ExpressionBuilderFilters();
                ExpressionBuilderFilters userFilter = new ExpressionBuilderFilters();
                
                if (!string.IsNullOrEmpty(userIds))
                {
                    string[] array = userIds.Split(Constants.SPLITTER);
                    foreach (var userId in array)
                    {
                        userFilter.Add("FromUserId", Convert.ToInt64(userId), Operator.Equals);
                    }
                }

                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    filter.Add("CreatedDate.Date", from, Operator.GreaterThanOrEqual);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate).Date;
                    filter.Add("CreatedDate.Date", to, Operator.LessThanOrEqualTo);
                }
                if (overdue)
                {
                    filter.Add("DueDate", DateTime.Now, Operator.LessThanOrEqualTo);
                }
                var retValue = ((long)0, new List<TransferReportListViewModel>());
                retValue = await ManageReport.ListTransfersSentToStructureRetrievingAllData(structureId,filter, userFilter, Language);
                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = retValue.Item2
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }

        /// <summary>
        /// List In Progress transfers.
        /// </summary>
        /// <remarks>Not all data is retrieved (paging is supported). Must be non draft.</remarks>
        /// <param name="draw"></param>
        /// <param name="start">Starting record - The number of records to skip before starting to return records from the query. Default value is 0.</param>
        /// <param name="length">Number of records to be returned</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(GridListViewModel<CorrespondenceReportListViewModel>), 200)]
        //[Authorize(Roles = "Administrator")]
        [CustomMenusAuthorizationFilter(new string[] { nameof(CustomMenus.OutgoingFromDepartment) })]
        [HideInSwagger]
        //[ValidateAntiForgeryToken]
        public async Task<JsonResult> ListOutgoingFromDepartment([FromForm] int draw, [FromForm] int start, [FromForm] int length)
        {
            try
            {
                int departmentId = Request.Form["departmentId"].Count > 0 ? Convert.ToInt32(Request.Form["departmentId"][0]) : 0;
                bool getAll = Request.Form["getAll"].Count > 0 ? Convert.ToBoolean(Request.Form["getAll"][0]) : default;

                List<SortExpression> sorting = new List<SortExpression>();
                string column = Request.Form["order[0][column]"];
                string sort = Request.Form["order[0][dir]"];
                if (!string.IsNullOrEmpty(column))
                {
                    SortDirection sortDirection = sort == "asc" ? SortDirection.Ascending : SortDirection.Descending;
                    column = Request.Form[$"columns[{Convert.ToInt32(column)}][data]"];
                    switch (column)
                    {
                        case "categoryId":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "Category.Name" });
                            break;
                        case "subject":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "Subject" });
                            break;
                        case "transferDate":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "CreatedDate" });
                            break;
                    }
                }
                else
                {
                    sorting.Add(new SortExpression { Order = SortDirection.Descending, PropertyName = "CreatedDate" });
                }
                var retValue = ((long)0, new List<CorrespondenceReportListViewModel>());
                retValue = await ManageReport.ListOutgoingFromDepartmentAsync(start, length, departmentId, getAll, sorting, Language);
                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = retValue.Item2
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }
        #endregion

        #region User

        #region DocumentAttachmentDetail Report


        /// <summary>
        /// download all documents in Attachment Detail Report
        /// </summary>
        /// <param name="documentId">Document id</param>
        /// <param name="attachmentIds">attachment ids</param>
        /// <param name="transferId">transfer id</param>
        /// <param name="delegationId">Delegation id</param>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(FileContentResult), 200)]
        [Produces("application/json")]
        public async Task<IActionResult> MergeAndDownloadPdf(long documentId, [FromQuery] List<string> attachmentIds, long? transferId, long? delegationId)
        {
            try
            {
                var authHeader = HttpContext.Request.Headers["Authorization"].ToString();
                var usertoken = authHeader.StartsWith("Bearer ") ? authHeader.Substring("Bearer ".Length) : authHeader;

                List<byte[]> pdfsToMerge = new List<byte[]>();

                var pdfDocument = await ManageReport.ExportPdfCorrespondenceDetailDocument(documentId, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, delegationId, Language);
                pdfsToMerge.Add(pdfDocument);

                foreach (var id in attachmentIds)
                {
                    if (id == null)
                        continue;

                    var attachmentIdList = id
                        .Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                        .Select(long.Parse)
                        .ToList();

                    foreach (var attachmentId in attachmentIdList)
                    {
                        var item = new Attachment().FindIncludeAttachmentSecurity(attachmentId);
                        if (ManageAttachmentSecurity.ChackAttachmentSecurity(item, UserId, StructureIds))
                        {
                            byte[] attachmentData = null;
                            string attachmentName = string.Empty;

                            if (Configuration.DownloadWithAnnotation)
                            {
                                var file = await ManageAttachment.DownloadFromViewer(attachmentId, transferId, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, delegationId, true,usertoken:usertoken);
                                if (file != null)
                                {
                                    attachmentData = (byte[])file.GetType().GetProperty("Data").GetValue(file);
                                    attachmentName = file.GetType().GetProperty("Name").GetValue(file).ToString();
                                }
                            }
                            else
                            {
                                var attachment = await ManageAttachment.Download(attachmentId, transferId, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, delegationId, true);
                                if (attachment != null)
                                {
                                    attachmentData = attachment.Data;
                                    attachmentName = $"{attachment.Name}.{attachment.Extension}";
                                }
                            }

                            if (attachmentData != null)
                            {
                                if (ManageReport.IsPdf(attachmentData))
                                {
                                    pdfsToMerge.Add(attachmentData);
                                }
                                else if (ManageReport.IsWordFile(attachmentName))
                                {
                                    pdfsToMerge.Add(ManageReport.ConvertWordToPdf(attachmentData, attachmentName));
                                }
                                else
                                {
                                    byte[] convertedPdf = await ManageReport.ConvertToPdf(attachmentData, attachmentName);
                                    pdfsToMerge.Add(convertedPdf);
                                }
                            }
                        }
                    }
                }


                byte[] mergedPdf = await ManageReport.MergePdfDocuments(pdfsToMerge);

                var document = new Core.DAL.Document().Find(documentId);
                string mergedPdfName = $"{document.ReferenceNumber}||{DateTime.Now.ToString("yyyy/MM/dd-HH-mm-ss")}.pdf";
                return File(mergedPdf, "application/pdf", mergedPdfName);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }



        /// <summary>
        /// download all documents in Attachment Detail Report
        /// </summary>
        /// <param name="documentId">Document id</param>
        /// <param name="attachmentIds">attachment ids</param>
        /// <param name="transferId">transfer id</param>
        /// <param name="delegationId">Delegation id</param>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(FileContentResult), 200)]
        [Produces("application/json")]
        public async Task<IActionResult> ExportAllPdfDocument(long documentId, [FromQuery] List<string>? attachmentIds, long? transferId, long? delegationId)
        {
            try
            {
                var authHeader = HttpContext.Request.Headers["Authorization"].ToString();
                var usertoken = authHeader.StartsWith("Bearer ") ? authHeader.Substring("Bearer ".Length) : authHeader;

                var pdfDocument = await ManageReport.ExportPdfCorrespondenceDetailDocument(documentId, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, delegationId, Language);
                string pdfName = TranslationUtility.Translate("CorrespondenceDetail", Language);
                pdfName = Regex.Replace(pdfName, @"\s+", " ").Replace(" ", "_") + ".pdf";

                var attachmentIdList = attachmentIds != null && attachmentIds.Count > 0 ? attachmentIds[0]
                .Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                .Select(id => long.Parse(id))
                .ToList() : new List<long>();


                using (var memoryStream = new MemoryStream())
                {
                    using (var archive = new ZipArchive(memoryStream, ZipArchiveMode.Create, true))
                    {
                        var pdfEntry = archive.CreateEntry(pdfName, CompressionLevel.Fastest);
                        using (var pdfStream = pdfEntry.Open())
                        {
                            await pdfStream.WriteAsync(pdfDocument, 0, pdfDocument.Length);
                        }

                        foreach (var id in attachmentIdList)
                        {
                            var item = new Attachment().FindIncludeAttachmentSecurity(id);
                            if (ManageAttachmentSecurity.ChackAttachmentSecurity(item, UserId, StructureIds))
                            {
                                byte[] attachmentData = null;
                                string attachmentName = string.Empty;
                                string attachmentContentType = string.Empty;

                                if (Configuration.DownloadWithAnnotation)
                                {
                                    var file = await ManageAttachment.DownloadFromViewer(id, transferId, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, delegationId, true, usertoken: usertoken);
                                    if (file != null)
                                    {
                                        attachmentName = file.GetType().GetProperty("ContentDisposition").GetValue(file).ToString();
                                        attachmentContentType = file.GetType().GetProperty("ContentType").GetValue(file).ToString();
                                        attachmentData = (byte[])file.GetType().GetProperty("Data").GetValue(file);
                                    }
                                }
                                else
                                {
                                    var attachment = await ManageAttachment.Download(id, transferId, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, delegationId, true);
                                    if (attachment != null)
                                    {
                                        attachmentName = Regex.Replace(attachment.Name, @"\s+", " ").Replace(" ", "_") + "." + attachment.Extension;
                                        attachmentContentType = attachment.ContentType;
                                        attachmentData = attachment.Data;
                                    }
                                }

                                if (attachmentData != null)
                                {
                                    var attachmentEntry = archive.CreateEntry(attachmentName, CompressionLevel.Fastest);
                                    using (var attachmentStream = attachmentEntry.Open())
                                    {
                                        await attachmentStream.WriteAsync(attachmentData, 0, attachmentData.Length);
                                    }
                                }
                            }
                        }
                    }

                    var zipFileName = "ExportedAllDocuments.zip";
                    Response.Headers.Add("Content-Disposition", "attachment; filename=\"" + WebUtility.UrlEncode(zipFileName) + "\"");
                    return File(memoryStream.ToArray(), "application/zip");
                }
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// Print all documents in Attachment Detail Report
        /// </summary>
        /// <param name="documentId">Document id</param>
        /// <param name="attachmentIds">Attachment ids</param>
        /// <param name="transferId">Transfer id</param>
        /// <param name="delegationId">Delegation id</param>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(byte[]), 200)]
        [Produces("application/pdf")]
        public async Task<IActionResult> PrintAllPdfDocument(long documentId, [FromQuery] List<string> attachmentIds, long? transferId, long? delegationId)
        {
            try
            {
                var authHeader = HttpContext.Request.Headers["Authorization"].ToString();
                var usertoken = authHeader.StartsWith("Bearer ") ? authHeader.Substring("Bearer ".Length) : authHeader;

                var pdfDocument = await ManageReport.ExportPdfCorrespondenceDetailDocument(documentId, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, delegationId, Language);

                var attachmentIdList = attachmentIds[0]
                    .Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                    .Select(id => long.Parse(id))
                    .ToList();

                var outputDocument = new Aspose.Pdf.Document();

                using (var pdfStream = new MemoryStream(pdfDocument))
                {
                    var mainDocument = new Aspose.Pdf.Document(pdfStream);
                    foreach (var page in mainDocument.Pages)
                    {
                        outputDocument.Pages.Add(page);
                    }
                }

                foreach (var id in attachmentIdList)
                {
                    var item = new Attachment().FindIncludeAttachmentSecurity(id);

                    if (ManageAttachmentSecurity.ChackAttachmentSecurity(item, UserId, StructureIds))
                    {
                        byte[] attachmentData = null;

                        if (Configuration.DownloadWithAnnotation)
                        {
                            var file = await ManageAttachment.DownloadFromViewer(id, transferId, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, delegationId, true, usertoken: usertoken);
                            if (file != null)
                            {
                                attachmentData = (byte[])file.GetType().GetProperty("Data").GetValue(file);
                            }
                        }
                        else
                        {
                            var attachment = await ManageAttachment.Download(id, transferId, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, delegationId, true);
                            if (attachment != null)
                            {
                                attachmentData = attachment.Data;
                            }
                        }

                        if (attachmentData != null)
                        {
                            using (var attachmentStream = new MemoryStream(attachmentData))
                            {
                                var attachmentDocument = new Aspose.Pdf.Document(attachmentStream);
                                foreach (var page in attachmentDocument.Pages)
                                {
                                    outputDocument.Pages.Add(page);
                                }
                            }
                        }
                    }
                }

                using (var memoryStream = new MemoryStream())
                {
                    outputDocument.Save(memoryStream);
                    memoryStream.Seek(0, SeekOrigin.Begin);
                    return File(memoryStream.ToArray(), "application/pdf", "CombinedDocument.pdf");
                }
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }
        #endregion
        
        #region DocumentCorrespondenceDetail Report

        /// <summary>
        /// Find documentId by reference number if user has access
        /// </summary>
        /// <param name="referenceNumber"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(long), 200)]
        [CustomMenusAuthorizationFilter(new string[] { nameof(CustomMenus.CorrespondenceDetail) })]

        //[ValidateAntiForgeryToken]
        public async Task<IActionResult> FindDocumentIdByReferenceNumberSecured(string referenceNumber,long loggedInStructure)
        {
            try
            {
                List<long> structures = new List<long>();
                structures = StructureIds;
                if (Core.Configuration.EnablePerStructure && !StructureIds.Any(S => S == loggedInStructure))
                {
                    return BadRequest();
                }
                else if (Core.Configuration.EnablePerStructure)
                {
                    structures = new List<long>();
                    structures.Add(loggedInStructure);
                }

                return Ok(await ManageReport.FindDocumentIdByReferenceNumberSecured(referenceNumber, UserId, structures, IsStructureReceiver, PrivacyLevel));
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// Get Document Correspondence Detail by DocumentId
        /// </summary>
        /// <param name="documentId">Document id</param>
        /// <param name="delegationId">Delegation id</param>
        /// <returns></returns>
        [HttpGet]
        [CustomMenusAuthorizationFilter(new string[] { nameof(CustomMenus.CorrespondenceDetail) })]

        [ProducesResponseType(typeof(GridListViewModel<DocumentCorrespondenceDetailListViewModel>), 200)]
        //[ValidateAntiForgeryToken]
        public async Task<IActionResult> GetDocumentCorrespondenceDetail(long documentId, long? delegationId)
        {
            try
            {
                return Ok(await ManageReport.GetDocumentCorrespondenceDetail(documentId, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, delegationId, Language));
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }

        /// <summary>
        /// List Transfers for Correspondence Detail Report
        /// </summary>
        /// <remarks>Not all data is retrieved (paging is supported). Must be non draft.</remarks>
        /// <param name="draw"></param>
        /// <param name="start">Starting record - The number of records to skip before starting to return records from the query. Default value is 0.</param>
        /// <param name="length">Number of records to be returned</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(GridListViewModel<TransferCorrespondenceDetailListViewModel>), 200)]
        [CustomMenusAuthorizationFilter(new string[] { nameof(CustomMenus.CorrespondenceDetail) })]

        //[ValidateAntiForgeryToken]
        public async Task<JsonResult> ListTransferDocumentCorrespondenceDetail([FromForm] int draw, [FromForm] int start, [FromForm] int length)
        {
            try
            {
                long documentId = Request.Form["DocumentId"].Count > 0 ? Convert.ToInt64(Request.Form["DocumentId"][0]) : 0;
                long? delegationId = Request.Form["DelegationId"].Count > 0 && !string.IsNullOrEmpty(Request.Form["DelegationId"][0]) ? (long?)Convert.ToInt64(Request.Form["DelegationId"][0]) : null;
                List<SortExpression> sorting = new List<SortExpression>();
                string column = Request.Form["order[0][column]"];
                string sort = Request.Form["order[0][dir]"];
                if (!string.IsNullOrEmpty(column))
                {
                    SortDirection sortDirection = sort == "asc" ? SortDirection.Ascending : SortDirection.Descending;
                    column = Request.Form[$"columns[{Convert.ToInt32(column)}][data]"];
                    switch (column)
                    {
                        case "createdDate":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "CreatedDate" });
                            break;
                    }
                }
                else
                {
                    sorting.Add(new SortExpression { Order = SortDirection.Descending, PropertyName = "CreatedDate" });
                }
                var retValue = await ManageReport.ListTransferCorrespondenceDetail(documentId, start, length, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, sorting, delegationId, Language);
                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = retValue.Item2
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }

        /// <summary>
        /// List Transfers for Correspondence Detail Report
        /// </summary>
        /// <remarks>All data is retrieved (paging is not supported). Must be non draft.</remarks>
        /// <param name="draw"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(GridListViewModel<TransferCorrespondenceDetailListViewModel>), 200)]
        [CustomMenusAuthorizationFilter(new string[] { nameof(CustomMenus.CorrespondenceDetail) })]

        //[ValidateAntiForgeryToken]
        public async Task<JsonResult> ListTransferDocumentCorrespondenceDetailRetrievingAllData([FromForm] int draw)
        {
            try
            {
                long documentId = Request.Form["DocumentId"].Count > 0 ? Convert.ToInt64(Request.Form["DocumentId"][0]) : 0;
                long? delegationId = Request.Form["DelegationId"].Count > 0 && !string.IsNullOrEmpty(Request.Form["DelegationId"][0]) ? (long?)Convert.ToInt64(Request.Form["DelegationId"][0]) : null;
                List<SortExpression> sorting = new List<SortExpression>();
                string column = Request.Form["order[0][column]"];
                string sort = Request.Form["order[0][dir]"];
                if (!string.IsNullOrEmpty(column))
                {
                    SortDirection sortDirection = sort == "asc" ? SortDirection.Ascending : SortDirection.Descending;
                    column = Request.Form[$"columns[{Convert.ToInt32(column)}][data]"];
                    switch (column)
                    {
                        case "createdDate":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "CreatedDate" });
                            break;
                    }
                }
                else
                {
                    sorting.Add(new SortExpression { Order = SortDirection.Descending, PropertyName = "CreatedDate" });
                }
                var retValue = await ManageReport.ListTransferCorrespondenceDetailRetrievingAllData(documentId, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, sorting, delegationId, Language);
                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = retValue.Item2
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }

        /// <summary>
        /// List notes for Correspondence Detail Report
        /// </summary>
        /// <remarks>Not all data is retrieved (paging is supported). Must be non draft.</remarks>
        /// <param name="draw"></param>
        /// <param name="start">Starting record - The number of records to skip before starting to return records from the query. Default value is 0.</param>
        /// <param name="length">Number of records to be returned</param>
        /// <param name="documentId">Document id</param>
        /// <param name="delegationId">Delegation id</param>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(GridListViewModel<NoteListViewModel>), 200)]
        [CustomMenusAuthorizationFilter(new string[] { nameof(CustomMenus.CorrespondenceDetail) })]

        public async Task<JsonResult> ListNoteCorrespondenceDetail(int draw, int start, int length, long documentId, long? delegationId)
        {
            try
            {
                var retValue = await ManageReport.ListNoteCorrespondenceDetail(documentId, start, length, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, delegationId);
                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = retValue.Item2
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }

        /// <summary>
        /// List notes for Correspondence Detail Report
        /// </summary>
        /// <remarks>All data is retrieved (paging is not supported). Must be non draft.</remarks>
        /// <param name="draw"></param>
        /// <param name="documentId">Document id</param>
        /// <param name="delegationId">Delegation id</param>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(GridListViewModel<NoteListViewModel>), 200)]
        [CustomMenusAuthorizationFilter(new string[] { nameof(CustomMenus.CorrespondenceDetail) })]

        public async Task<JsonResult> ListNoteCorrespondenceDetailRetrievingAllData(int draw, long documentId, long? delegationId)
        {
            try
            {
                var retValue = await ManageReport.ListNoteCorrespondenceDetailRetrievingAllData(documentId, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, delegationId);
                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = retValue.Item2
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }

        /// <summary>
        ///  List non archived attachments for Correspondence Detail Report
        /// </summary>
        /// <remarks>Not all data is retrieved (paging is supported). Must be non draft.</remarks>
        /// <param name="draw"></param>
        /// <param name="start">Starting record - The number of records to skip before starting to return records from the query. Default value is 0.</param>
        /// <param name="length">Number of records to be returned</param>
        /// <param name="documentId">Document id</param>
        /// <param name="delegationId">Delegation id</param>
        /// <returns></returns>
        [HttpGet]
        [CustomMenusAuthorizationFilter(new string[] { nameof(CustomMenus.CorrespondenceDetail) })]

        public async Task<JsonResult> ListNonArchivedAttachmentsCorrespondenceDetail(int draw, int start, int length, long documentId, long? delegationId)
        {
            try
            {
                var retValue = await ManageReport.ListNonArchivedAttachmentsCorrespondenceDetail(documentId, start, length, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, delegationId, Language);
                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = retValue.Item2
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }

        /// <summary>
        ///  List non archived attachments for Correspondence Detail Report
        /// </summary>
        /// <remarks>All data is retrieved (paging is not supported). Must be non draft.</remarks>
        /// <param name="draw"></param>
        /// <param name="documentId">Document id</param>
        /// <param name="delegationId">Delegation id</param>
        /// <returns></returns>
        [HttpGet]
        [CustomMenusAuthorizationFilter(new string[] { nameof(CustomMenus.CorrespondenceDetail) })]

        public async Task<JsonResult> ListNonArchivedAttachmentsCorrespondenceDetailRetrievingAllData(int draw, long documentId, long? delegationId)
        {
            try
            {
                var retValue = await ManageReport.ListNonArchivedAttachmentsCorrespondenceDetailRetrievingAllData(documentId, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, delegationId, Language);
                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = retValue.Item2
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }

        /// <summary>
        /// List linked documents of a certain document in Correspondence Detail Report
        /// </summary>
        /// <param name="documentId">Document id</param>
        /// <param name="delegationId">Delegation id</param>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(GridListViewModel<LinkedDocumentListViewModel>), 200)]
        [CustomMenusAuthorizationFilter(new string[] { nameof(CustomMenus.CorrespondenceDetail) })]

        public async Task<JsonResult> ListLinkedDocumentCorrespondenceDetail(long documentId, long? delegationId)
        {
            try
            {
                int draw = 0;
                var retValue = await ManageReport.ListLinkedDocumentCorrespondenceDetail(documentId, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, delegationId);
                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Count,
                    recordsFiltered = retValue.Count,
                    data = retValue
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }

        /// <summary>
        /// Export file as pdf
        /// </summary>
        /// <param name="documentId"></param>
        /// <param name="delegationId"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(FileContentResult), 200)]
        [Produces("application/json")]
        public async Task<IActionResult> ExportPdfDocument(long documentId, long? delegationId)
        {
            try
            {
                var retValue = await ManageReport.ExportPdfCorrespondenceDetailDocument(documentId, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, delegationId, Language);
                string name = TranslationUtility.Translate("CorrespondenceDetail", Language);
                name = Regex.Replace(name, @"\s+", " ").Replace(" ", "_");
                Response.Headers.Add("Content-Disposition", "attachment; filename=\"" + WebUtility.UrlEncode(name) + ".pdf\"");
                return File(retValue, "application/pdf");
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// Print file 
        /// </summary>
        /// <param name="documentId"></param>
        /// <param name="delegationId"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(byte[]), 200)]
        public async Task<IActionResult> PrintPdfDocument(long documentId, long? delegationId)
        {
            try
            {
                return Ok(await ManageReport.ExportPdfCorrespondenceDetailDocument(documentId, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, delegationId, Language));
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }
        /// <summary>
        /// Export file as pdf
        /// </summary>
        /// <param name="documentId"></param>
        /// <param name="delegationId"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(FileContentResult), 200)]
        [Produces("application/json")]
        public async Task<IActionResult> ExportExcelDocument(long documentId, long? delegationId)
        {
            try
            {
                var retValue = await ManageReport.ExportExcelCorrespondenceDetailDocument(documentId, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, delegationId, Language);
                string name = TranslationUtility.Translate("CorrespondenceDetail", Language);
                name = Regex.Replace(name, @"\s+", " ").Replace(" ", "_");
                Response.Headers.Add("Content-Disposition", "attachment; filename=\"" + WebUtility.UrlEncode(name) + ".xlsx\"");
                return File(retValue, "application/xlsx");
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        #endregion

        #endregion
    }
}