﻿IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword = N'CannotRecallCorrespondenceWithSubject')
BEGIN
    INSERT [dbo].[TranslatorDictionary] ([Keyword], [EN], [FR], [AR], [IsSystem])
    VALUES (
        N'CannotRecallCorrespondenceWithSubject',
        N'The correspondence with subject: {0} cannot be recalled',
        N'La correspondance avec le sujet : {0} ne peut pas être rappelée',
        N'لا يمكن استرجاع المراسلة ذات الموضوع: {0}',
        1
    )
END

IF NOT EXISTS (SELECT 1 FROM Parameter WHERE Keyword=N'RCVersion')
BEGIN
	INSERT INTO Parameter (Keyword,[Description], Content, IsSystem)
	VALUES (N'RCVersion','RC version of CTS', 'RC36', 0)
END

IF EXISTS (SELECT 1 FROM Parameter WHERE Keyword=N'RCVersion')
BEGIN
update Parameter set Content='RC36' where Keyword='RCVersion'

END