{{#each basicAttributes}}
{{#if Enabled}}
{{#ifEquals Name "CarbonCopy"}}
{{setVar "isCarbonCopy" true}}
{{/ifEquals}}
{{#ifEquals Name "SendingEntity"}}
{{setVar "isSendingEntity" true}}
{{/ifEquals}}
{{#ifEquals Name "ReceivingEntity"}}
{{setVar "isReceivingEntity" true}}
{{/ifEquals}}
{{#ifEquals Name "Subject"}}
{{setVar "isSubject" true}}
{{/ifEquals}}
{{#ifEquals Name "Privacy"}}
{{setVar "isPrivacy" true}}
{{/ifEquals}}
{{#ifEquals Name "DueDate"}}
{{setVar "isDueDate" true}}
{{/ifEquals}}
{{#ifEquals Name "DocumentDate"}}
{{setVar "isDocumentDate" true}}
{{/ifEquals}}
{{#ifEquals Name "Priority"}}
{{setVar "isPriority" true}}
{{/ifEquals}}
{{#ifEquals Name "Sender"}}
{{setVar "isSender" true}}
{{/ifEquals}}
{{#ifEquals Name "Receiver"}}
{{setVar "isReceiver" true}}
{{/ifEquals}}
{{/if}}
{{/each}}
<div ref="{{ComponentId}}" class="form-scroll">
    {{#if readonly}}
    <div class="panel panel-default borderTop-1">
        <div class="panel-body">
            <form ref="formDocumentAttributePost" tabindex="-1" method="post" data-parsley-validate="" novalidate="">
                <div class="row hidden" ref="documentBarcodeContainer">
                    <div class="col-sm-6 col-xs-12 defaultWithViewer hidden">
                        <div class="form-group fs-16">

                            <button ref="lnkDocumentBarcodeView" type="button" class="btn-outline btn-primary">
                            </button>
                        </div>
                    </div>
                </div>
                <div class="row">
                    {{#if isSendingEntity}}
                    <div class="col-sm-6 col-xs-12 defaultWithViewer" ref="customAttributeSenderContainer">
                        <div class="form-group">

                            <label class="control-label">{{Localizer 'SendingEntity'}}</label>
                            <select disabled ref="cmbCustomAttributeSender" required tabindex="2" data-parsley-errors-container="#{{ComponentId}}_cmbCustomAttributeSenderError" class="form-control">
                            </select>
                            <div id="{{ComponentId}}_cmbCustomAttributeSenderError"></div>
                        </div>
                    </div>
                    {{/if}}
                    {{#if isReceivingEntity}}
                    <div class="col-sm-6 col-xs-12 defaultWithViewer" ref="customAttributeReceiverContainer">
                        <div class="form-group">
                            <label class="control-label">{{Localizer 'ReceivingEntity'}}</label>
                            <select disabled ref="cmbCustomAttributeReceiver" required tabindex="3" data-parsley-errors-container="#{{ComponentId}}_cmbCustomAttributeReceiverError" class="form-control cmbCustomAttributeReceiver">
                            </select>
                            <div id="{{ComponentId}}_cmbCustomAttributeReceiverError"></div>
                        </div>
                    </div>
                    {{/if}}
                </div>
                <div class="row">
                    {{#if isSender}}
                    <div class="col-sm-6 col-xs-12 defaultWithViewer" ref="customAttributeSenderPersonContainer">
                        <div class="form-group">
                            <label class="control-label ">{{Localizer 'Sender'}}</label>
                            <select disabled ref="cmbCustomAttributeSenderPerson" tabindex="5" data-parsley-errors-container="#{{ComponentId}}_cmbCustomAttributeSenderPersonError" class="form-control">
                            </select>
                            <div id="{{ComponentId}}_cmbCustomAttributeSenderPersonError"></div>
                        </div>
                    </div>
                    {{/if}}
                    {{#if isReceiver}}
                    <div class="col-sm-6 col-xs-12 defaultWithViewer" ref="customAttributeReceiverPersonContainer">
                        <div class="form-group">
                            <label class="control-label">{{Localizer 'Receiver'}}</label>
                            <select disabled ref="cmbCustomAttributeReceiverPerson" tabindex="5" data-parsley-errors-container="#{{ComponentId}}_cmbCustomAttributeReceiverPersonError" class="form-control">
                            </select>
                            <div id="{{ComponentId}}_cmbCustomAttributeReceiverPersonError"></div>
                        </div>
                    </div>
                    {{/if}}
                </div>
                <div class="row">
                    {{#if isCarbonCopy}}
                    <div class="col-sm-6 col-xs-12 defaultWithViewer" ref="customAttributeCarbonCopyContainer">
                        <div class="form-group">
                            <label class="control-label">{{Localizer 'CarbonCopy'}}</label>
                            <select disabled ref="cmbCustomAttributeCarbonCopy" class="form-control">
                            </select>
                        </div>
                    </div>
                    {{#if isSubject}}
                    <div class="col-sm-6 col-xs-12 defaultWithViewer">
                        <div class="form-group">
                            <label class="control-label">{{Localizer 'Subject'}}</label>
                            <textarea disabled ref="txtCustomAttributeSubject" required maxlength="350" class="form-control soso" autocomplete="off" tabindex="4" aria-hidden="true"></textarea>
                        </div>
                    </div>
                    {{/if}}
                    {{else}}
                    {{#if isSubject}}
                    <div class="col-sm-6 col-xs-12 defaultWithViewer">
                        <div class="form-group">
                            <label class="control-label">{{Localizer 'Subject'}}</label>
                            <textarea disabled ref="txtCustomAttributeSubject" required maxlength="350" class="form-control  hema" autocomplete="off" tabindex="4" aria-hidden="true"></textarea>
                        </div>
                    </div>
                    {{/if}}
                    {{#if isPrivacy}}
                    <div class="col-sm-6 col-xs-12 defaultWithViewer" ref="customAttributePrivacyContainer">
                        <div class="form-group">
                            <label class="control-label">{{Localizer 'Privacy'}}</label>
                            <select disabled ref="cmbCustomAttributePrivacy" required tabindex="5" data-parsley-errors-container="#{{ComponentId}}_cmbCustomAttributePrivacyError" class="form-control">
                                {{#each privacies}}
                                <option value="{{id}}">{{text}}</option>
                                {{/each}}
                            </select>
                            <div id="{{ComponentId}}_cmbCustomAttributePrivacyError"></div>
                        </div>
                    </div>
                    {{/if}}
                    {{/if}}
                </div>

                {{#each basicAttributes}}
                {{#if Enabled}}
                {{#ifEquals Name "ExternalReferenceNumber"}}
                {{addVar "count" 6}}
                <div class="col-sm-6 col-xs-12 defaultWithViewer">
                    <div class="form-group">
                        <label style="margin-left: -15px;" class="control-label ">{{Localizer 'ExternalReferenceNumber'}}</label>
                        <input disabled type="text" ref="txtCustomAttribute{{Name}}" maxlength="80" class="form-control"
                               autocomplete="off" tabindex="5" aria-hidden="true" style="width: 686px; margin-left: -15px;">
                    </div>
                </div>
                {{break}}
                {{/ifEquals}}
                {{/if}}
                {{/each}}
                <div class="row">
                    {{#if isCarbonCopy}}
                    {{#if isPrivacy}}
                    <div class="col-sm-6 col-xs-12 defaultWithViewer" ref="customAttributePrivacyContainer">
                        <div class="form-group">
                            <label class="control-label">{{Localizer 'Privacy'}}</label>
                            <select disabled ref="cmbCustomAttributePrivacy" required tabindex="5" data-parsley-errors-container="#{{ComponentId}}_cmbCustomAttributePrivacyError" class="form-control">
                                {{#each privacies}}
                                <option value="{{id}}">{{text}}</option>
                                {{/each}}
                            </select>
                            <div id="{{ComponentId}}_cmbCustomAttributePrivacyError"></div>
                        </div>
                    </div>
                    {{/if}}
                    {{#if isPriority}}
                    <div class="col-sm-6 col-xs-12 defaultWithViewer" ref="customAttributePriorityContainer">
                        <div class="form-group">
                            <label class="control-label">{{Localizer 'Priority'}}</label>
                            <select disabled ref="cmbCustomAttributePriority" required tabindex="6" data-parsley-errors-container="#{{ComponentId}}_cmbCustomAttributePriorityError" class="form-control">
                            </select>
                            <div id="{{ComponentId}}_cmbCustomAttributePriorityError"></div>
                        </div>
                    </div>
                    {{/if}}
                    {{else}}
                    {{#if isPriority}}
                    <div class="col-sm-6 col-xs-12 defaultWithViewer" ref="customAttributePriorityContainer">
                        <div class="form-group">
                            <label class="control-label">{{Localizer 'Priority'}}</label>
                            <select disabled ref="cmbCustomAttributePriority" required tabindex="6" data-parsley-errors-container="#{{ComponentId}}_cmbCustomAttributePriorityError" class="form-control">
                            </select>
                            <div id="{{ComponentId}}_cmbCustomAttributePriorityError"></div>
                        </div>
                    </div>
                    {{/if}}
                    {{#if isDueDate}}
                    <div class="col-sm-6 col-xs-12 defaultWithViewer dueDateContainer">
                        <div class="form-group">
                            <label class="control-label">{{Localizer 'DueDate'}}</label>
                            <div class="input-group date">
                                <input disabled ref="customAttributeDueDate" type="text" tabindex="7" autocomplete="off" class="form-control" name="Date">
                                <span class="input-group-addon" ref="customAttributeDueDate_img" style="cursor:pointer">
                                    <i class="fa fa-calendar" aria-hidden="true"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                    {{/if}}
                    {{/if}}
                </div>
                <div class="row">
                    {{#if isCarbonCopy}}
                    {{addVar "count" 6}}
                    {{#if isDueDate}}
                    <div class="col-sm-6 col-xs-12 defaultWithViewer dueDateContainer">
                        <div class="form-group">
                            <label class="control-label">{{Localizer 'DueDate'}}</label>
                            <div class="input-group date">
                                <input disabled ref="customAttributeDueDate" type="text" tabindex="7" autocomplete="off" class="form-control" name="Date">
                                <span class="input-group-addon" ref="customAttributeDueDate_img" style="cursor:pointer">
                                    <i class="fa fa-calendar" aria-hidden="true"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                    {{/if}}
                    {{/if}}
                    {{#if isDocumentDate}}
                    <div class="col-sm-6 col-xs-12 defaultWithViewer documentDateContainer">
                        <div class="form-group">
                            <label class="control-label">{{Localizer 'DocumentDate'}}</label>
                            <div class="input-group date">
                                <input disabled ref="customAttributeDocumentDate" type="text" tabindex="7" autocomplete="off" class="form-control" name="Date">
                                <span class="input-group-addon" ref="customAttributeDocumentDate_img" style="cursor:pointer">
                                    <i class="fa fa-calendar" aria-hidden="true"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                    {{/if}}
                    {{#unless isFollowUp}}
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="col-sm-6 col-xs-12 defaultWithViewer createdDateContainer">
                                <div class="form-group">
                                    <label class="control-label">{{Localizer 'CreatedDate'}}</label>
                                    <div class="input-group date">
                                        <input disabled ref="customAttributeCreatedDate" type="text" tabindex="7" autocomplete="off" class="form-control" name="Date">
                                        <span class="input-group-addon" ref="customAttributeCreatedDate_img" style="cursor:pointer">
                                            <i class="fa fa-calendar" aria-hidden="true"></i>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6 col-xs-12 defaultWithViewer" ref="createdByContainer">
                                <div class="form-group">
                                    <label class="control-label">{{Localizer 'CreatedBy'}}</label>
                                    <input disabled id="createdBy" required tabindex="6" class="form-control">

                                </div>
                            </div>
                        </div>
                    </div>
                    {{/unless}}

                    {{#each basicAttributes}}
                    {{#if Enabled}}
                    {{#ifEquals Name "Keyword"}}
                    {{addVar "count" 12}}
                    <div class="col-sm-12" ref="customAttribute{{Name}}Container">
                        <div class="form-group">
                            <label class="control-label">{{Localizer 'Keyword'}}</label>
                            <input type="text" disabled ref="txtCustomAttribute{{Name}}" class="form-control typeahead" data-provide="typeahead" autocomplete="off" />
                        </div>
                    </div>
                    {{else}}
                    {{#ifEquals Name "Body"}}
                    {{addVar "count" 12}}
                    <div class="col-sm-12" ref="customAttribute{{Name}}Container">
                        <div class="form-group">
                            <label class="control-label">{{Localizer Name}}</label>
                            <textarea disabled class="form-control" tabindex="7" maxlength="2000" ref="txtCustomAttribute{{Name}}" rows="5"></textarea>
                        </div>
                    </div>
                    {{else}}
                    {{#ifEquals Name "CarbonCopy"}}
                    {{continue}}
                    {{else}}
                    {{#ifEquals Name "Priority"}}
                    {{continue}}
                    {{else}}
                    {{#ifEquals Name "DueDate"}}
                    {{continue}}
                    {{else}}
                    {{#ifEquals Name "DocumentDate"}}
                    {{continue}}
                    {{else}}
                    {{#ifEquals Name "Privacy"}}
                    {{continue}}
                    {{else}}
                    {{#ifEquals Name "Subject"}}
                    {{continue}}
                    {{else}}
                    {{#ifEquals Name "ReceivingEntity"}}
                    {{continue}}
                    {{else}}
                    {{#ifEquals Name "Receiver"}}
                    {{continue}}
                    {{else}}
                    {{#ifEquals Name "Sender"}}
                    {{continue}}
                    {{else}}
                    {{#ifEquals Name "SendingEntity"}}
                    {{continue}}
                    {{else}}
                    {{#ifEquals Name "ExternalReferenceNumber"}}
                    {{continue}}
                    {{else}}
                    {{addVar "count" 6}}
                    <div class="col-sm-6 col-xs-12 defaultWithViewer" ref="customAttribute{{Name}}Container">
                        <div class="form-group">
                            <label class="control-label">{{Localizer Name}}</label>
                            <select disabled ref="cmbCustomAttribute{{Name}}" class="form-control">
                            </select>
                        </div>
                    </div>
                    {{/ifEquals}}
                    {{/ifEquals}}
                    {{/ifEquals}}
                    {{/ifEquals}}
                    {{/ifEquals}}
                    {{/ifEquals}}
                    {{/ifEquals}}
                    {{/ifEquals}}
                    {{/ifEquals}}
                    {{/ifEquals}}
                    {{/ifEquals}}
                    {{/ifEquals}}
                    {{/ifEquals}}
                    {{#ifEquals ../count 12}}
                    <div class="clearfix"></div>
                    {{setVar "count" 0}}
                    {{/ifEquals}}
                    {{/if}}
                    {{/each}}
                </div>
            </form>
            <div ref="builder"></div>
            <div class="panel panel-default borderTop-1 hidden" hidden id="g2gPanel">
                <input ref="hdG2GInternalId" value="{{g2gInternalId}}" type="hidden" />
            </div>

        </div>
    </div>
    {{else}}
    <div class="panel panel-default borderTop-1">
        <div class="panel-body">

            <form ref="formDocumentAttributePost" tabindex="-1" method="post" data-parsley-validate="" novalidate="">
                <div class="row">
                    {{#unless enableEdit}}
                    <div class="col-sm-6 col-xs-12 defaultWithViewer" ref="customAttributeStructureContainer">
                        <div class="form-group">
                            <label class="control-label">{{Localizer 'UserStructure'}}</label>
                            <select ref="cmbUserStructures" tabindex="1" data-parsley-errors-container="#{{ComponentId}}_cmbUserStructuresError" class="form-control"></select>
                            <div id="{{ComponentId}}_cmbUserStructuresError"></div>
                        </div>
                    </div>
                    {{/unless}}
                    <div ref="documentBarcodeContainer" class="hidden">
                        <div class="col-sm-6 col-xs-12 defaultWithViewer hidden">
                            <div class="barcodeDiv form-group fs-16 {{#unless enableEdit}} mt-33{{/unless}}">
                                <button ref="lnkDocumentBarcodeView" type="button" class="referenceNumber btn-outline btn-primary"></button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    {{#if isSendingEntity}}
                    <div class="col-sm-6 col-xs-12 defaultWithViewer" ref="customAttributeSenderContainer">
                        <div class="form-group">
                            <label class="control-label field-required">{{Localizer 'SendingEntity'}}</label>
                            <div class="input-group">
                                <select {{#if isFollowUp}} disabled {{/if}} ref="cmbCustomAttributeSender" required tabindex="2" data-parsley-errors-container="#{{ComponentId}}_cmbCustomAttributeSenderError" class="form-control">
                                </select>
                                {{#unless isFollowUp}}
                                <span class="input-group-addon" ref="openSenderAddressBook" style="cursor:pointer">
                                    <i class="fa fa-address-book-o" aria-hidden="true"></i>
                                </span>
                                {{/unless}}
                            </div>
                            <div id="{{ComponentId}}_cmbCustomAttributeSenderError"></div>
                        </div>
                    </div>
                    {{else}}
                    <div class="col-sm-6 col-xs-12 defaultWithViewer hidden" ref="customAttributeSenderContainer">
                        <div class="form-group">
                            <label class="control-label field-required">{{Localizer 'SendingEntity'}}</label>
                            <div class="input-group">
                                <select {{#if isFollowUp}} disabled {{/if}} ref="cmbCustomAttributeSender" required tabindex="2" data-parsley-errors-container="#{{ComponentId}}_cmbCustomAttributeSenderError" class="form-control">
                                </select>
                                {{#unless isFollowUp}}
                                <span class="input-group-addon" ref="openSenderAddressBook" style="cursor:pointer">
                                    <i class="fa fa-address-book-o" aria-hidden="true"></i>
                                </span>
                                {{/unless}}

                            </div>
                            <div id="{{ComponentId}}_cmbCustomAttributeSenderError"></div>
                        </div>
                    </div>
                    {{/if}}
                    {{#if isReceivingEntity}}
                    <div class="col-sm-6 col-xs-12 defaultWithViewer addressBookSelect margin-8" ref="customAttributeReceiverContainer">
                        <div class="form-group">
                            <label class="control-label field-required">{{Localizer 'ReceivingEntity'}}</label>
                            <div class="col-md-4 col-sm-2 col-xs-4 pull-right receiver-both-options">
                                <div class="">
                                    <div class="input-group">
                                        <div class="radio c-radio margin-0">
                                            <label>
                                                <input checked="checked" class="form-control" type="radio" value="1" name="{{ComponentId}}_structureSenderSearchType">
                                                <span class="fa fa-circle"></span>{{Localizer 'Internal'}}
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-2 col-xs-4 pull-right receiver-both-options">
                                <div class="">
                                    <div class="input-group">
                                        <div class="radio c-radio margin-0">
                                            <label>
                                                <input class="form-control" type="radio" value="2" name="{{ComponentId}}_structureSenderSearchType">
                                                <span class="fa fa-circle"></span>{{Localizer 'External'}}
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="input-group">
                                <select {{#if isFollowUp}} disabled {{/if}} ref="cmbCustomAttributeReceiver" required tabindex="3" data-parsley-errors-container="#{{ComponentId}}_cmbCustomAttributeReceiverError" class="form-control cmbCustomAttributeReceiver">
                                </select>
                                {{#unless isFollowUp}}
                                <span class="input-group-addon" ref="openReceiverAddressBook" style="cursor:pointer">
                                    <i class="fa fa-address-book-o" aria-hidden="true"></i>
                                </span>
                                <span class="input-group-addon" ref="openBroadcastReceiverAddressBook" style="cursor:pointer;">
                                    <i class="fa fa-users" aria-hidden="true"></i>
                                </span>
                                {{/unless}}
                            </div>
                            <div id="{{ComponentId}}_cmbCustomAttributeReceiverError"></div>
                        </div>
                    </div>
                    {{else}}
                    <div class="col-sm-6 col-xs-12 defaultWithViewer addressBookSelect hidden" ref="customAttributeReceiverContainer">
                        <div class="form-group">
                            <label class="control-label field-required">{{Localizer 'ReceivingEntity'}}</label>
                            <div class="input-group">
                                <select {{#if isFollowUp}} disabled {{/if}} ref="cmbCustomAttributeReceiver" required tabindex="3" data-parsley-errors-container="#{{ComponentId}}_cmbCustomAttributeReceiverError" class="form-control cmbCustomAttributeReceiver">
                                </select>
                                {{#unless isFollowUp}}
                                <span class="input-group-addon" ref="openReceiverAddressBook" style="cursor:pointer">
                                    <i class="fa fa-address-book-o" aria-hidden="true"></i>
                                </span>
                                <span class="input-group-addon" ref="openBroadcastReceiverAddressBook" style="cursor:pointer;">
                                    <i class="fa fa-users" aria-hidden="true"></i>
                                </span>
                                {{/unless}}

                            </div>
                            <div id="{{ComponentId}}_cmbCustomAttributeReceiverError"></div>
                        </div>
                    </div>

                    {{/if}}
                </div>
                <div class="row">
                    {{#if isSender}}
                    <div class="col-sm-6 col-xs-12 defaultWithViewer" ref="customAttributeSenderPersonContainer">
                        <div class="form-group">
                            <label class="control-label ">{{Localizer 'Sender'}}</label>
                            <div class="input-group">
                                <select ref="cmbCustomAttributeSenderPerson" tabindex="5" data-parsley-errors-container="#{{ComponentId}}_cmbCustomAttributeSenderPersonError" class="form-control">
                                </select>
                                <span class="input-group-addon hidden" ref="openSenderDesignatedPerson" style="cursor:pointer">
                                    <i class="fa fa-edit" aria-hidden="true"></i>
                                </span>
                                <div id="{{ComponentId}}_cmbCustomAttributeSenderPersonError"></div>
                            </div>
                        </div>
                    </div>
                    {{else}}
                    <div class="col-sm-6 col-xs-12 defaultWithViewer hidden" ref="customAttributeSenderPersonContainer">
                        <div class="form-group">
                            <label class="control-label">{{Localizer 'Sender'}}</label>
                            <div class="input-group">
                                <select ref="cmbCustomAttributeSenderPerson" tabindex="5" data-parsley-errors-container="#{{ComponentId}}_cmbCustomAttributeSenderPersonError" class="form-control">
                                </select>
                                <span class="input-group-addon hidden" ref="openSenderDesignatedPerson" style="cursor:pointer">
                                    <i class="fa fa-edit" aria-hidden="true"></i>
                                </span>
                                <div id="{{ComponentId}}_cmbCustomAttributeSenderPersonError"></div>
                            </div>
                        </div>
                    </div>
                    {{/if}}
                    {{#if isReceiver}}
                    <div class="col-sm-6 col-xs-12 defaultWithViewer" ref="customAttributeReceiverPersonContainer">
                        <div class="form-group">
                            <label class="control-label">{{Localizer 'Receiver'}}</label>
                            <div class="input-group">
                                <select ref="cmbCustomAttributeReceiverPerson" tabindex="5" data-parsley-errors-container="#{{ComponentId}}_cmbCustomAttributeReceiverPersonError" class="form-control">
                                </select>
                                <span class="input-group-addon hidden" ref="openRecieverDesignatedPerson" style="cursor:pointer">
                                    <i class="fa fa-edit" aria-hidden="true"></i>
                                </span>
                                <div id="{{ComponentId}}_cmbCustomAttributeReceiverPersonError"></div>
                            </div>
                        </div>
                    </div>
                    {{else}}
                    <div class="col-sm-6 col-xs-12 defaultWithViewer hidden" ref="customAttributeReceiverPersonContainer">
                        <div class="form-group">
                            <label class="control-label">{{Localizer 'Receiver'}}</label>
                            <div class="input-group">
                                <select ref="cmbCustomAttributeReceiverPerson" tabindex="5" data-parsley-errors-container="#{{ComponentId}}_cmbCustomAttributeReceiverPersonError" class="form-control">
                                </select>
                                <span class="input-group-addon hidden" ref="openRecieverDesignatedPerson" style="cursor:pointer">
                                    <i class="fa fa-edit" aria-hidden="true"></i>
                                </span>
                                <div id="{{ComponentId}}_cmbCustomAttributeReceiverPersonError"></div>
                            </div>
                        </div>
                    </div>
                    {{/if}}
                </div>

                <div class="row">
                    {{#if isCarbonCopy}}
                    <div class="col-sm-6 col-xs-12 defaultWithViewer margin-8" ref="customAttributeCarbonCopyContainer">
                        <div class="form-group">
                            <label class="control-label">{{Localizer 'CarbonCopy'}}</label>
                            <div class="col-md-4 col-sm-2 col-xs-4 pull-right">
                                <div class="">
                                    <div class="input-group">
                                        <div class="radio c-radio margin-0">
                                            <label>
                                                <input checked="checked" class="form-control" type="radio" value="1" name="{{ComponentId}}_carbonCopySearchType">
                                                <span class="fa fa-circle"></span>{{Localizer 'Internal'}}
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-2 col-xs-4 pull-right">
                                <div class="">
                                    <div class="input-group">
                                        <div class="radio c-radio margin-0">
                                            <label>
                                                <input class="form-control" type="radio" value="2" name="{{ComponentId}}_carbonCopySearchType">
                                                <span class="fa fa-circle"></span>{{Localizer 'External'}}
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="input-group">
                                <select ref="cmbCustomAttributeCarbonCopy" class="form-control" tabindex="4">
                                </select>
                                <span class="input-group-addon" ref="openCarbonCopyAddressBook" style="cursor:pointer">
                                    <i class="fa fa-address-book-o" aria-hidden="true"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                    {{#if isSubject}}
                    <div class="col-sm-6 col-xs-12 defaultWithViewer">
                        <div class="form-group">
                            <label class="control-label field-required">{{Localizer 'Subject'}}</label>
                            <textarea ref="txtCustomAttributeSubject" required maxlength="350" class="form-control   nenen" autocomplete="off" tabindex="5" aria-hidden="true"></textarea>
                        </div>
                    </div>
                    {{else}}
                    <div class="col-sm-6 col-xs-12 hidden defaultWithViewer">
                        <div class="form-group">
                            <label class="control-label">{{Localizer 'Subject'}}</label>
                            <textarea ref="txtCustomAttributeSubject" maxlength="350" class="form-control   kekeee" autocomplete="off" tabindex="5" aria-hidden="true"></textarea>
                        </div>
                    </div>
                    {{/if}}
                    {{else}}
                    {{#if isSubject}}
                    <div class="col-sm-6 col-xs-12 defaultWithViewer">
                        <div class="form-group">
                            <label class="control-label field-required">{{Localizer 'Subject'}}</label>
                            <textarea ref="txtCustomAttributeSubject" required maxlength="350" class="form-control  totoooo" autocomplete="off" tabindex="4" aria-hidden="true"></textarea>
                        </div>
                    </div>
                    {{else}}
                    <div class="col-sm-6 col-xs-12 hidden defaultWithViewer">
                        <div class="form-group">
                            <label class="control-label">{{Localizer 'Subject'}}</label>
                            <textarea ref="txtCustomAttributeSubject" maxlength="350" class="form-control lool" autocomplete="off" tabindex="4" aria-hidden="true"></textarea>
                        </div>
                    </div>
                    {{/if}}

                    {{#if isPrivacy}}
                    <div class="col-sm-6 col-xs-12 defaultWithViewer" ref="customAttributePrivacyContainer">
                        <div class="form-group">
                            <label class="control-label field-required">{{Localizer 'Privacy'}}</label>
                            <select ref="cmbCustomAttributePrivacy" required tabindex="5" data-parsley-errors-container="#{{ComponentId}}_cmbCustomAttributePrivacyError" class="form-control">
                                {{#each privacies}}
                                <option value="{{id}}">{{text}}</option>
                                {{/each}}
                            </select>
                            <div id="{{ComponentId}}_cmbCustomAttributePrivacyError"></div>
                        </div>
                    </div>
                    {{else}}
                    <div class="col-sm-6 col-xs-12 defaultWithViewer hidden" ref="customAttributePrivacyContainer">
                        <div class="form-group">
                            <label class="control-label field-required">{{Localizer 'Privacy'}}</label>
                            <select ref="cmbCustomAttributePrivacy" required tabindex="5" data-parsley-errors-container="#{{ComponentId}}_cmbCustomAttributePrivacyError" class="form-control">
                                {{#each privacies}}
                                <option value="{{id}}">{{text}}</option>
                                {{/each}}
                            </select>
                            <div id="{{ComponentId}}_cmbCustomAttributePrivacyError"></div>
                        </div>
                    </div>
                    {{/if}}
                    {{/if}}
                </div>
                <div class="row">
                    {{#if isCarbonCopy}}
                    {{#if isPrivacy}}
                    <div class="col-sm-6 col-xs-12 defaultWithViewer" ref="customAttributePrivacyContainer">
                        <div class="form-group">
                            <label class="control-label field-required">{{Localizer 'Privacy'}}</label>
                            <select ref="cmbCustomAttributePrivacy" required tabindex="6" data-parsley-errors-container="#{{ComponentId}}_cmbCustomAttributePrivacyError" class="form-control">
                                {{#each privacies}}
                                <option value="{{id}}">{{text}}</option>
                                {{/each}}
                            </select>
                            <div id="{{ComponentId}}_cmbCustomAttributePrivacyError"></div>
                        </div>
                    </div>
                    {{else}}
                    <div class="col-sm-6 col-xs-12 defaultWithViewer hidden" ref="customAttributePrivacyContainer">
                        <div class="form-group">
                            <label class="control-label field-required">{{Localizer 'Privacy'}}</label>
                            <select ref="cmbCustomAttributePrivacy" required tabindex="6" data-parsley-errors-container="#{{ComponentId}}_cmbCustomAttributePrivacyError" class="form-control">
                                {{#each privacies}}
                                <option value="{{id}}">{{text}}</option>
                                {{/each}}
                            </select>
                            <div id="{{ComponentId}}_cmbCustomAttributePrivacyError"></div>
                        </div>
                    </div>
                    {{/if}}
                    {{#each basicAttributes}}
                    {{#if Enabled}}
                    {{#ifEquals Name "ExternalReferenceNumber"}}
                    {{addVar "count" 6}}
                    <div class="col-sm-6 col-xs-12 defaultWithViewer ">
                        <div class="form-group ">
                            <label style="margin-left: -15px;" class="control-label ">{{Localizer 'ExternalReferenceNumber'}}</label>
                            <input type="text" ref="txtCustomAttribute{{Name}}"
                                   maxlength="80" class="form-control" autocomplete="off" tabindex="7" aria-hidden="true" style="width: 500px; margin-left: -15px;">
                        </div>
                    </div>
                    {{break}}
                    {{/ifEquals}}
                    {{/if}}
                    {{/each}}
                    {{#if isPriority}}
                    <div class="col-sm-6 col-xs-12 defaultWithViewer" ref="customAttributePriorityContainer">
                        <div class="form-group">
                            <label class="control-label field-required">{{Localizer 'Priority'}}</label>
                            <select ref="cmbCustomAttributePriority" required tabindex="7" data-parsley-errors-container="#{{ComponentId}}_cmbCustomAttributePriorityError" class="form-control">
                            </select>
                            <div id="{{ComponentId}}_cmbCustomAttributePriorityError"></div>
                        </div>
                    </div>
                    {{else}}
                    <div class="col-sm-6 col-xs-12 defaultWithViewer hidden" ref="customAttributePriorityContainer">
                        <div class="form-group">
                            <label class="control-label field-required">{{Localizer 'Priority'}}</label>
                            <select ref="cmbCustomAttributePriority" required tabindex="7" data-parsley-errors-container="#{{ComponentId}}_cmbCustomAttributePriorityError" class="form-control">
                            </select>
                            <div id="{{ComponentId}}_cmbCustomAttributePriorityError"></div>
                        </div>
                    </div>
                    {{/if}}
                    {{else}}
                    {{#if isPriority}}
                    <div class="col-sm-6 col-xs-12 defaultWithViewer" ref="customAttributePriorityContainer">
                        <div class="form-group">
                            <label class="control-label field-required">{{Localizer 'Priority'}}</label>
                            <select ref="cmbCustomAttributePriority" required tabindex="6" data-parsley-errors-container="#{{ComponentId}}_cmbCustomAttributePriorityError" class="form-control">
                            </select>
                            <div id="{{ComponentId}}_cmbCustomAttributePriorityError"></div>
                        </div>
                    </div>
                    {{else}}
                    <div class="col-sm-6 col-xs-12 defaultWithViewer hidden" ref="customAttributePriorityContainer">
                        <div class="form-group">
                            <label class="control-label field-required">{{Localizer 'Priority'}}</label>
                            <select ref="cmbCustomAttributePriority" required tabindex="6" data-parsley-errors-container="#{{ComponentId}}_cmbCustomAttributePriorityError" class="form-control">
                            </select>
                            <div id="{{ComponentId}}_cmbCustomAttributePriorityError"></div>
                        </div>
                    </div>
                    {{/if}}
                    {{#if isDueDate}}
                    <div class="col-sm-6 col-xs-12 defaultWithViewer dueDateContainer">
                        <div class="form-group">
                            <label class="control-label">{{Localizer 'DueDate'}}</label>
                            <div class="input-group date">
                                <input ref="customAttributeDueDate" type="text" tabindex="7" autocomplete="off" class="form-control" name="Date">
                                <label class="workingDaysLabel" title="{{Localizer 'WorkingDays'}}" />
                                <span class="input-group-addon dum" ref="customAttributeDueDate_img" style="cursor:pointer">
                                    <i class="fa fa-calendar" aria-hidden="true"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                    {{else}}
                    <div class="col-sm-6 col-xs-12 defaultWithViewer hidden dueDateContainer">
                        <div class="form-group">
                            <label class="control-label">{{Localizer 'DueDate'}}</label>
                            <div class="input-group date">
                                <input ref="customAttributeDueDate" type="text" tabindex="7" autocomplete="off" class="form-control" name="Date">
                                <label class="workingDaysLabel" title="{{Localizer 'WorkingDays'}}" />
                                <span class="input-group-addon" ref="customAttributeDueDate_img" style="cursor:pointer">
                                    <i class="fa fa-calendar" aria-hidden="true"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                    {{/if}}

                    {{/if}}
                </div>
                <div class="row">
                    {{#if isDocumentDate}}
                    <div class="col-sm-6 col-xs-12 defaultWithViewer documentDateContainer">
                        <div class="form-group">
                            <label class="control-label">{{Localizer 'DocumentDate'}}</label>
                            <div class="input-group date">
                                <input ref="customAttributeDocumentDate" type="text" tabindex="8" autocomplete="off" class="form-control" name="Date">

                                <span class="input-group-addon" ref="customAttributeDocumentDate_img" style="cursor:pointer">
                                    <i class="fa fa-calendar" aria-hidden="true"></i>
                                </span>
                            </div>
                        </div>
                    </div>

                    {{else}}
                    <div class="col-sm-6 col-xs-12 defaultWithViewer hidden documentDateContainer">
                        <div class="form-group">
                            <label class="control-label">{{Localizer 'DocumentDate'}}</label>
                            <div class="input-group date">
                                <input ref="customAttributeDocumentDate" type="text" tabindex="8" autocomplete="off" class="form-control" name="Date">

                                <span class="input-group-addon" ref="customAttributeDueDate_img" style="cursor:pointer">
                                    <i class="fa fa-calendar" aria-hidden="true"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                    {{/if}}
                </div>

                {{#unless isFollowUp}}
                <div class="row">

                    <div class="col-sm-6 col-xs-12 defaultWithViewer CreatedDateContainer">
                        <div class="form-group">
                            <label class="control-label">{{Localizer 'CreatedDate'}}</label>
                            <div class="input-group" style="width: 100%;">
                                <input ref="customAttributeCreatedDate" type="text" tabindex="8" autocomplete="off" class="form-control" name="Date" disabled style="width: 100%; padding: 10px;">
                            </div>
                        </div>
                    </div>


                    <div class="col-sm-6 col-xs-12 defaultWithViewer" ref="createdByContainer">
                        <div class="form-group">
                            <label class="control-label">{{Localizer 'CreatedBy'}}</label>
                            <div class="input-group" style="width: 100%;">
                                <input id="createdBy" type="text" tabindex="8" autocomplete="off" class="form-control" disabled style="width: 100%; padding: 10px;">
                            </div>
                        </div>
                    </div>
                </div>
                {{/unless}}





                <div class="row">
                    {{#if isCarbonCopy}}
                    {{addVar "count" 6}}
                    {{#if isDueDate}}
                    <div class="col-sm-6 col-xs-12 defaultWithViewer dueDateContainer">
                        <div class="form-group">
                            <label class="control-label">{{Localizer 'DueDate'}}</label>
                            <div class="input-group date">
                                <input ref="customAttributeDueDate" type="text" tabindex="8" autocomplete="off" class="form-control" name="Date">
                                <label class="workingDaysLabel" title="{{Localizer 'WorkingDays'}}">
                                </label>
                                <span class="input-group-addon" ref="customAttributeDueDate_img" style="cursor:pointer">
                                    <i class="fa fa-calendar" aria-hidden="true"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                    {{else}}
                    <div class="col-sm-6 col-xs-12 defaultWithViewer hidden dueDateContainer">
                        <div class="form-group">
                            <label class="control-label">{{Localizer 'DueDate'}}</label>
                            <div class="input-group date">
                                <input ref="customAttributeDueDate" type="text" tabindex="8" autocomplete="off" class="form-control" name="Date">
                                <label class="workingDaysLabel" title="{{Localizer 'WorkingDays'}}">
                                </label>
                                <span class="input-group-addon" ref="customAttributeDueDate_img" style="cursor:pointer">
                                    <i class="fa fa-calendar" aria-hidden="true"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                    {{/if}}
                    {{/if}}

                    {{#each basicAttributes}}
                    {{#if Enabled}}
                    {{#ifEquals Name "Keyword"}}
                    {{addVar "count" 12}}
                    <div class="col-sm-12" ref="customAttribute{{Name}}Container">
                        <div class="form-group">
                            <label class="control-label">{{Localizer 'Keyword'}}</label>
                            <input type="text" ref="txtCustomAttribute{{Name}}" class="form-control typeahead" data-provide="typeahead" autocomplete="off" />
                        </div>
                    </div>
                    {{else}}
                    {{#ifEquals Name "Body"}}
                    {{addVar "count" 12}}
                    <div class="col-sm-12" ref="customAttribute{{Name}}Container">
                        <div class="form-group">
                            <label class="control-label">{{Localizer Name}}</label>
                            <textarea class="form-control" tabindex="8" maxlength="2000" ref="txtCustomAttribute{{Name}}" rows="5"></textarea>
                        </div>
                    </div>
                    {{else}}
                    {{#ifEquals Name "CarbonCopy"}}
                    {{continue}}
                    {{else}}
                    {{#ifEquals Name "Priority"}}
                    {{continue}}
                    {{else}}
                    {{#ifEquals Name "DueDate"}}
                    {{continue}}
                    {{else}}
                    {{#ifEquals Name "DocumentDate"}}
                    {{continue}}
                    {{else}}
                    {{#ifEquals Name "Privacy"}}
                    {{continue}}
                    {{else}}
                    {{#ifEquals Name "Subject"}}
                    {{continue}}
                    {{else}}
                    {{#ifEquals Name "ReceivingEntity"}}
                    {{continue}}
                    {{else}}
                    {{#ifEquals Name "Receiver"}}
                    {{continue}}
                    {{else}}
                    {{#ifEquals Name "Sender"}}
                    {{continue}}
                    {{else}}
                    {{#ifEquals Name "SendingEntity"}}
                    {{continue}}
                    {{else}}
                    {{#ifEquals Name "ExternalReferenceNumber"}}
                    {{continue}}
                    {{else}}
                    {{addVar "count" 6}}
                    <div class="col-sm-6 col-xs-12 defaultWithViewer" ref="customAttribute{{Name}}Container">
                        <div class="form-group">
                            <label class="control-label">{{Localizer Name}}</label>
                            <select ref="cmbCustomAttribute{{Name}}" class="form-control">
                            </select>
                        </div>
                    </div>
                    {{/ifEquals}}
                    {{/ifEquals}}
                    {{/ifEquals}}
                    {{/ifEquals}}
                    {{/ifEquals}}
                    {{/ifEquals}}
                    {{/ifEquals}}
                    {{/ifEquals}}
                    {{/ifEquals}}
                    {{/ifEquals}}
                    {{/ifEquals}}
                    {{/ifEquals}}
                    {{/ifEquals}}
                    {{#ifEquals ../count 12}}
                    <div class="clearfix"></div>
                    {{setVar "count" 0}}
                    {{/ifEquals}}
                    {{/if}}
                    {{/each}}

                </div>
            </form>
            <div ref="builder"></div>
        </div>
        <div class="panel-footer text-right">
            {{#unless isFollowUp}}
            <button ref="btnAttributeRegister" id="{{ComponentId}}_btnAttributeRegister" type="button" class="btn btn-primary" data-loading-text="<i class='fa fa-spinner fa-spin'></i> {{Localizer 'Registering'}}">{{Localizer 'Register'}}</button>
            {{/unless}}
            <button ref="btnAttributeSave" type="button" class="btn btn-primary" data-loading-text="<i class='fa fa-spinner fa-spin'></i> {{Localizer 'Saving'}}">{{Localizer 'Save'}}</button>
        </div>
    </div>
    {{/if}}
</div>
<div data-backdrop="static" id="modalOpenDesignatedPerson" role="dialog" class="modal fade">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div style="max-height: 80vh; overflow-y: auto;" class="modal-body">
            </div>
            <div class="modal-footer">
                <button tabindex="18" ref="btnClosepopup" type="button" class="btn btn-default btn-sm btn-danger" data-dismiss="modal">
                    {{Localizer 'Close'}}
                </button>
            </div>
        </div>
    </div>
</div>