﻿import Intalio from './common.js'
import SendToReceivingEntityIndex from './sendToReceivingEntity.js'
import SendTransferModal from './sendTransfer.js'
import TransferInstructionIndex from './transferInstruction.js'
import { IdentityService, CategoryModel, Categories, Helper } from './lookup.js'
import AttachmentsReplyByCategory from './attachmentsReplyByCategory.js'
import CreateByTemplate from './createByTemplate.js'
import VipDocumentDetails from './vipDocumentDetails.js'
import Transfer from './transfer.js'
import { SignatureTemplates, SignatureTemplatesView } from './signatureTemplate.js'
import common from './common.js'
import Workflow from './Workflow.js'
import ReportCorrespondenceDetailExport from './reportCorrespondenceDetailExport.js'
import ReportAttachmentDetailExport from './reportAttachmentDetailExport.js'
import EmailAttachment from './emailAttachmentModal.js'
import CabinetTree from './CabinetTree.js'
import attachment from './attachment.js'
import { AddCCAndSendModel, AddCCAndSendView } from './addccandsend.js'
import NoteComponentIndex from './noteComponent.js'
import Document from './document.js'
import CopyOptions from '../components/copyOptions.js'
import ExportOptions from '../components/exportOptions.js'



function getFormData(categoryId, register, multipleReceivingEntity, self) {

    var documentViewModel = {};
    var receivers = [];
    var receiverPersons = [];
    if ($(self.refs['cmbCustomAttributeReceiver']).val() !== null && $(self.refs['cmbCustomAttributeReceiver']).val() !== undefined) {
        if (multipleReceivingEntity) {
            var recEntity = $(self.refs['cmbCustomAttributeReceiver']).val();
            for (var i = 0; i < recEntity.length; i++) {
                if (recEntity[i].split('-').length == 2) {
                    receivers.push({ Id: recEntity[i].split('-')[1], IsEntityGroup: true });
                } else {
                    receivers.push({ Id: recEntity[i], IsEntityGroup: false });
                }
            }
            //receivers = $(self.refs['cmbCustomAttributeReceiver']).val();
        } else {
            if ($(self.refs['cmbCustomAttributeReceiver']).val().split('-').length == 2) {
                receivers.push({ Id: $(self.refs['cmbCustomAttributeReceiver']).val().split('-')[1], IsEntityGroup: true });
            } else {
                receivers.push({ Id: $(self.refs['cmbCustomAttributeReceiver']).val(), IsEntityGroup: false });
            }
        }
    }
    if ($(self.refs['cmbCustomAttributeReceiverPerson']).val() !== null && $(self.refs['cmbCustomAttributeReceiverPerson']).val() !== undefined) {
        var recPersonJson = "";
        var recPerson = $(self.refs['cmbCustomAttributeReceiverPerson']).val();
        var receiverPersons = [];

        for (var i = 0; i < recPerson.length; i++) {
            receiverPersons.push(recPerson[i]);
        }
        var joinedIds = receiverPersons.join('-');
        recPersonJson = `{"Id":"${joinedIds}"}`;
    }
    var isExternalSender = false;
    var isExternalReceiver = false;

    if (categoryId == window.MeetingAgendaId
        || categoryId == window.MeetingMinutesId
        || categoryId == window.MeetingResolutionId
        || categoryId == window.SepResolutionId) {
        var documentReceivers = [];

        var selectedOptions = $('[id*=receivers]').parent().children("div").first().children();
        selectedOptions.each(function () {
            documentReceivers.push($(this).attr('data-value'))
        });
        gFormData.receivers = documentReceivers;
    }
    documentViewModel.id = self.model.documentId;
    documentViewModel.categoryId = categoryId;
    documentViewModel.subject = self.model.subject;
    documentViewModel.receivers = receivers;
    documentViewModel.dueDate = self.model.dueDate;
    documentViewModel.sender = $(self.refs['cmbCustomAttributeSenderPerson']).val();
    documentViewModel.isExternalReceiver = isExternalReceiver;
    documentViewModel.isExternalSender = isExternalSender;
    documentViewModel.privacyId = self.model.privacyId;
    documentViewModel.register = register;
    documentViewModel.createdByStructureId = $(self.refs['cmbUserStructures']).val() != null ? $(self.refs['cmbUserStructures']).val() : self.model.createdByStructureId;
    documentViewModel.externalReferenceNumber = $(self.refs['txtCustomAttributeExternalReferenceNumber']).val() ? $(self.refs['txtCustomAttributeExternalReferenceNumber']).val().trim() : $(self.refs['txtCustomAttributeExternalReferenceNumber']).val();
    documentViewModel.transferId = self.model.transferId;
    return documentViewModel;
}

function printDeliveryNote(self, receivingEntityObj) {

    let param = {
        'documentId': self.model.documentId !== null ? self.model.documentId : 0,
        'categoryId': self.model.categoryId,
        'receivingEntity': receivingEntityObj
    }

    Common.ajaxPost('Transfer/PrintDeliveryNote', param,
        function (response) {
            if (navigator.userAgent.indexOf("Firefox") > -1) {
                var htmlpop = '<embed width=100% height=100%'
                    + ' type="application/pdf"'
                    + ' src="data:application/pdf;base64,'
                    + response
                    + '"></embed>';

                var printwindow = window.open("");
                printwindow.document.write(htmlpop);
            } else {
                var byteCharacters = atob(response);
                var byteNumbers = new Array(byteCharacters.length);
                for (var i = 0; i < byteCharacters.length; i++) {
                    byteNumbers[i] = byteCharacters.charCodeAt(i);
                }
                var byteArray = new Uint8Array(byteNumbers);
                var file = new Blob([byteArray], { type: 'application/pdf;base64' });
                var fileURL = URL.createObjectURL(file);
                var prntWindow = window.open(fileURL);
                prntWindow.print();
            }
        },
        function () {
            Common.showScreenErrorMsg();
        });
}
function showToast(message, duration = 3000, container) {

    var toaster = document.createElement('div');
    toaster.textContent = message;
    toaster.style.padding = '3px';
    toaster.style.backgroundColor = '#ff902b';
    toaster.style.color = '#fff';
    toaster.style.borderRadius = '5px';
    toaster.style.marginTop = '10px';
    toaster.style.opacity = '1';
    toaster.style.transition = 'opacity 0.5s ease';
    toaster.style.width = 'fit-content';
    toaster.style.position = 'absolute';
    toaster.style.zIndex = '1000';
    container.appendChild(toaster);

    setTimeout(function () {
        toaster.style.opacity = '0';
        setTimeout(function () {
            toaster.remove();
        }, 500);
    }, duration);
}
function dateFormat(dateText) {
    var dateFull = dateText.split(" ")[0].split("/");
    var today = new Date();
    var dd = today.getDate();
    var mm = today.getMonth() + 1; //January is 0!
    var yy = today.getFullYear();
    var time = DateConverter.toHijriFormated(dateText.split(" ")[0], null, window.CalendarType);
    if (parseInt(dateFull[0]) === dd && parseInt(dateFull[1]) === mm && parseInt(dateFull[2]) === yy) {
        time = "";
        var timeSeparator = ":";
        var hours = parseInt(dateText.split(" ")[1].split(":")[0]);
        var amPm = Resources.AM;
        if (hours > 12) {
            time += (hours - 12) + timeSeparator;
            amPm = Resources.PM;
        } else if (hours === 12) {
            time += "12" + timeSeparator;
            amPm = Resources.PM;
        } else {
            time += (hours < 10 ? '0' : '') + hours + timeSeparator;
            amPm = Resources.AM;
        }
        var minutes = parseInt(dateText.split(" ")[1].split(":")[1]);
        minutes = (minutes < 10 ? '0' : '') + minutes;
        time += minutes + " " + amPm;
    } else if (parseInt(dateFull[0]) === (dd - 1) && parseInt(dateFull[1]) === mm && parseInt(dateFull[2]) === yy) {
        time = Resources.Yesterday;
    }
    return time;
}
function createListData(data) {
    var delegationId = window.location.hash.split("/")[2];
    if (delegationId === undefined)
        delegationId = null;

    var html = '';
    if (data.length === 0 && gPageIndex === 0) {
        html = '<ul class="mdl-ul"><div class="nodata dataTables_empty pt-lg">' + Resources.NoDataToDisplay + '</div></ul>';
        $('#inboxListContainer').html(html);
    } else if (data.length > 0) {
        html = '<ul class="mdl-ul VIPContainerItem">';
        var htmlLi = '';
        for (var i = 0; i < data.length; i++) {
            var transfer = data[i];
            var liClass = "mdl-li";
            var color = "";

            if (!transfer.isRead) {
                liClass += " unread";
            }

            var lockedByMe = false;
            var delegatedUser = delegationId !== null ? new DelegationUsers().getById(Number(delegationId)) : null;
            var delegatedUserId = delegatedUser !== null ? delegatedUser.fromUserId : 0;

            if (transfer.isLocked && (transfer.ownerUserId !== null && transfer.ownerUserId === Number($("#hdUserId").val()) && delegationId === null)
                || (transfer.ownerDelegatedUserId !== null && transfer.ownerDelegatedUserId === Number($("#hdUserId").val())
                    && delegatedUserId === transfer.ownerUserId && delegationId !== null)
                || (transfer.ownerUserId !== null && delegatedUserId === transfer.ownerUserId && delegationId !== null)) {
                lockedByMe = true;
            }

            var priorities = new CoreComponents.Lookup.Priorities().get(window.language);
            var privacies = new CoreComponents.Lookup.Privacies().get(window.language);

            if (window.PriorityPrivacyAction == "2") {
                for (var j = 0; j < privacies.length; j++) {
                    if (privacies[j].id === transfer.privacyId) {
                        color = privacies[j].color;
                        break;
                    }
                }
            } else {
                for (var j = 0; j < priorities.length; j++) {
                    if (priorities[j].id === transfer.priorityId) {
                        color = priorities[j].color;
                        break;
                    }
                }
            }

            var htmlIcons = "";
            if (transfer.importanceId) {
                var importances = new CoreComponents.Lookup.Importances().get(window.language);
                for (var j = 0; j < importances.length; j++) {
                    if (importances[j].id === transfer.importanceId) {
                        htmlIcons += "<i class='fa fa-exclamation fa-lg mr-sm' title='" + importances[j].text + "' style='color:" + importances[j].color + "'></i>";
                    }
                }
            }
            if (transfer.isOverDue) {
                htmlIcons += "<i class='fa fa-clock-o fa-lg text-danger mr-sm' title='" + Resources.OverDue + "'></i>";
            }
            if (transfer.isRead && !transfer.lockedDate) {
                htmlIcons += "<button class='btn btn-xs mr-sm unReadIcon' style='padding: 0px 3px;' " +
                    "data-id='" + transfer.id + "' " +
                    "data-isread='" + transfer.isRead + "' " +
                    "data-delegationid='" + transfer.delegationId + "' " +
                    "data-cced='" + transfer.cced + "' " +
                    "data-lockeddate='" + transfer.lockedDate + "' " +
                    "data-lockedby='" + transfer.lockedBy + "'>" +
                    "<i class='fa fa-envelope text-warning'></i>" +
                    "</button>";
            }
            if (transfer.requestStatus == "Pending") {
                let btn = document.createElement("button");
                btn.setAttribute("class", "btn btn-xs btn-primary mr-sm accept");
                btn.setAttribute("title", Resources.Accept);
                btn.setAttribute("clickattr", "acceptRequest(" + transfer.id + ")");
                btn.innerHTML = "<i class='fa fa-check'/>";
                htmlIcons += btn.outerHTML;

                let rejectBtn = document.createElement("button");
                rejectBtn.setAttribute("class", "btn btn-xs btn-danger mr-sm reject");
                rejectBtn.setAttribute("title", Resources.Reject);
                rejectBtn.setAttribute("clickattr", "rejectRequest(" + transfer.id + ")");
                rejectBtn.innerHTML = "<i class='fa fa-close'/>";
                htmlIcons += rejectBtn.outerHTML;
            }

            let categories = new Categories().get(window.language, null);
            let category = $.grep(categories, function (e) {
                return e.id === transfer.categoryId;
            });
            if (category[0] && category[0].isBroadcast) {
                htmlIcons += "<i class='fa fa-bullhorn text-primary mr-sm'  title='" + Resources.Broadcast + "'></i>";
            } else if (transfer.cced) {
                htmlIcons += "<i class='fa fa-cc text-warning mr-sm'  title='" + Resources.CarbonCopy + "'></i>";
            }
            if (transfer.sentToStructure && !transfer.isLocked && !transfer.cced && transfer.requestStatus != "Pending") {
                htmlIcons += "<button class='edit btn btn-xs btn-primary mr-sm lockIcon' style='padding: 0px 3px;' title='" + Resources.Edit + "'><i class='fa fa-edit'></i></button>";
            }
            var lockedByUser = transfer.ownerUserId === Number($("#hdUserId").val()) ? Resources.You : transfer.lockedBy;
            var lockedBy = transfer.lockedByDelegatedUser !== '' ? transfer.lockedByDelegatedUser + " " + Resources.OnBehalfOf + " " + transfer.lockedBy : lockedByUser;
            var titleLock = Resources.LockedBy + ": " + lockedBy + "\n" + Resources.LockedDate + ": " + DateConverter.toHijriFormated(transfer.lockedDate, null, window.CalendarType);
            if (transfer.sentToStructure && lockedByMe) {
                htmlIcons += "<button class='btn btn-xs btn-success mr-sm unlockIcon' style='padding: 0px 3px;' title='" + titleLock + "'><i class='fa fa-unlock fa-white'></i></button>";
            } else if (transfer.isLocked && !transfer.cced) {
                htmlIcons += "<i class='fa fa-lock fa-lg text-danger mr-sm' title='" + titleLock + "'></i>";
            }
            transfer.referenceNumber = transfer.referenceNumber ?? "";
            var from = transfer.fromStructure !== "" ? transfer.fromStructure + '/' + transfer.fromUser : transfer.fromUser;
            var to = "";
            transfer.receivingEntities.forEach((item) => { to += item.text; });

            htmlLi += '<li class="' + liClass + '" style="color:' + color + ';" data-transfer="' + transfer.id + '">';
            htmlLi += '<div class="mdl-container">';
            htmlLi += '<div id="leftbox" class="pull-left">';
            htmlLi += '<div class="inside_color_line pull_left"></div>';
            htmlLi += '<input data-id=' + transfer.id + ' data-categoryid=' + transfer.categoryId + ' data-cced=' + transfer.cced + ' data-read=' + transfer.isRead +
                ' data-lockedbyme=' + lockedByMe + ' data-senttouser=' + transfer.sentToUser +
                ' type="checkbox" class="pointer m-sm ml-10px" onclick="event.stopPropagation();">';
            htmlLi += "<input type='hidden' data-id=" + transfer.id + " value='" + JSON.stringify(transfer) + "'/>";
            htmlLi += "<span class='mdl-span' style='margin-left:10px; margin-right: 10px;'><i class='fa fa-circle icon-primary'></i></span>"
            htmlLi += "<span class='mdl-span' style='margin-left:10px; margin-right: 10px;'><i class='fa fa-arrow-left'></i></span>"
            htmlLi += "<span class='mdl-span' style='margin-left:10px; margin-right: 10px;'><i class='fa fa-arrow-right'></i></span>"
            htmlLi += '</div>';

            htmlLi += '<div id="middlebox" class="mdl-text-msg pull-left pl-sm card-max-width">';
            htmlLi += '<span class="mdl-span" style="color:' + color + '" title="' + transfer.referenceNumber + '" data-ref>' + transfer.referenceNumber + '</span>';
            htmlLi += '<span class="mdl-span text-primary" style="color:' + color + '" title="' + transfer.subject + '">' + transfer.subject + '</span>';
            htmlLi += '<span class="mdl-span" style="color:' + color + '" title="' + from + '">' + from + '</span>';
            htmlLi += '<span class="mdl-span light_grey_color" style="color:' + color + '" title="' + transfer.toStructure + '">' + transfer.toStructure + '</span>';
            htmlLi += '<span class="mdl-span light_grey_color" style="color:' + color + '" title="' + transfer.purposeName + '">' + transfer.purposeName + '</span>';
            htmlLi += '</div>';

            htmlLi += '<div id="rightbox" class="pull-right text-right">';
            htmlLi += '<div class="mdl-time mr-sm" style="color:' + color + '" title="' + Resources.TransferDate + '">' + dateFormat(transfer.transferDate) + '</div>';
            if (htmlIcons !== "") {
                htmlLi += '<div class="mdl-action">' + htmlIcons + '</div>';
            }
            htmlLi += '</div>';
            htmlLi += '</div>';
            htmlLi += '</li>';
        }
        html += htmlLi;
        html += '</ul>';

        if (gPageIndex === 15) {
            $('#inboxListContainer').html(html);
        } else {
            $('#inboxListContainer ul').append(htmlLi);
        }

        $(".unread").css('font-weight', 'bold');

        if (self.model.transferId && self.model.transferId != null) {
            var transfer = data.find(d => d.id == self.model.transferId);
            var checkbox = $('#leftbox input[data-id="' + self.model.transferId.toString() + '"]');
            if (checkbox.length) {
                var container = checkbox.closest('.mdl-container');
                if (container.length) {
                    if (transfer && transfer.sentToStructure) {
                        container.find('#rightbox .edit').click();
                    } else {
                        container.parent().click();
                    }
                }
            }
        }
    }
}

function addFilters(d) {
    if (gFromSearch) {
        d.PriorityId = $("#cmbFilterInboxPriority").val() !== null && typeof $("#cmbFilterInboxPriority").val() !== "undefined" ? $("#cmbFilterInboxPriority").val() : "0";
        d.PrivacyId = $("#cmbFilterInboxPrivacy").val() !== null && typeof $("#cmbFilterInboxPrivacy").val() !== "undefined" ? $("#cmbFilterInboxPrivacy").val() : "0";
        d.PurposeId = $("#cmbFilterInboxPurpose").val() !== null && typeof $("#cmbFilterInboxPurpose").val() !== "undefined" ? $("#cmbFilterInboxPurpose").val() : "0";
        d.CategoryId = $("#cmbFilterInboxCategory").val() !== null && typeof $("#cmbFilterInboxCategory").val() !== "undefined" ? $("#cmbFilterInboxCategory").val() : "0";
        d.ReferenceNumber = $("#txtFilterInboxReferenceNumber").val() !== "" && typeof $("#txtFilterInboxReferenceNumber").val() !== "undefined" ? $("#txtFilterInboxReferenceNumber").val() : "";
        d.FromDate = $("#filterInboxFromDate").val() !== "" && typeof $("#filterInboxFromDate").val() !== "undefined" ? $("#filterInboxFromDate").val() : "";
        d.ToDate = $("#filterInboxToDate").val() !== "" && typeof $("#filterInboxToDate").val() !== "undefined" ? $("#filterInboxToDate").val() : "";
        d.Read = $("#chkFilterInboxRead").is(':checked');
        d.Locked = $("#chkFilterInboxLocked").is(':checked');
        d.Overdue = $("#chkFilterInboxOverdue").is(':checked');
        d.Subject = $("#txtFilterInboxSubject").val() !== "" && typeof $("#txtFilterInboxSubject").val() !== "undefined" ? $("#txtFilterInboxSubject").val() : "";
        d.StructureIds = $("#cmbFilterInboxStructure").val() !== null && typeof $("#cmbFilterInboxStructure").val() !== "undefined" ? $("#cmbFilterInboxStructure").val() : [];
        d.UserIds = $("#cmbFilterInboxUser").val() !== null && typeof $("#cmbFilterInboxUser").val() !== "undefined" ? $("#cmbFilterInboxUser").val() : [];
    }
}

var gNoMoreData = false;
var gFromSearch = false;
var gPageIndex = 0;
var gSelectedRowId, gSelf;
var gLocked;
function loadInboxList() {
    if (!gNoMoreData) {
        var delegationId = window.location.hash.split("/")[2];
        if (delegationId == undefined)
            delegationId = null;
        var nodeId = window.location.hash.split("/")[1];
        if (nodeId == undefined)
            nodeId = null;
        Common.mask(document.getElementById('inboxListContainer'), "inboxListContainer-mask");
        var params = {};
        addFilters(params);
        params.NodeId = nodeId;
        params.DelegationId = delegationId;
        params.start = gPageIndex;
        params.structureId = window.EnablePerStructure ? $('#hdLoggedInStructureId').val() : $("#hdStructureId").val();
        Common.ajaxPost('/Transfer/ListInboxVip', params, function (response) {
            if (response.length > 0) {
                gPageIndex += window.Paging;
                if (response.length < window.Paging) {
                    gNoMoreData = true;
                }
            } else {
                gNoMoreData = true;
            }
            createListData(response);
            gLocked = false;
            Common.unmask("inboxListContainer-mask");
            if (gFromSearch) {
                $("#divSearchInbox").fadeOut();
            }
        }, function () { gLocked = false; Common.showScreenErrorMsg(); });
    } else {
        gLocked = false;
    }
}

function sendTransfer(data, model) {

    var transferToStructures = [], transferToStructureIds = [];
    var ccedTransfer = 0;
    var purposes = new CoreComponents.Lookup.Purposes().get(window.language);
    var hasPrivacyLevel = true;
    var htmlPrivacy = Resources.NoUserWithSelectedPrivacy;
    var structureExist = data.some(a => a.toStructureId !== null && a.toUserId === null);
    data.categoryId = model.categoryId;

    //data.allowMaintainTransfer = model.allowMaintainTransfer;
    for (var i = 0; i < data.length; i++) {
        data[i].FromStructureId = model.userStructure;
        if (model.fromSent) {
            data[i].ParentTransferId = model.parentTransferId;
        }
        else {

            data[i].ParentTransferId = model.transferId;
        }
        data[i].IsStructure = data[i].toUserId === null;
        data[i].DocumentId = model.documentId;
        data[i].DocumentPrivacyId = model.selectedPrivacy;
        var currentPurpose = $.grep(purposes, function (e) {
            return e.id.toString() === data[i].purposeId;
        });
        if (currentPurpose[0].cCed === true) {
            ccedTransfer++;
        }
        if (data[i].toUserId === null) {
            transferToStructures.push({ dataId: data[i].toStructureId, text: data[i].name });
            transferToStructureIds.push(data[i].toStructureId);
        } else {
            data[i].PrivacyId = 0;
            var userObj = new IdentityService().getFullUser(data[i].toUserId);
            if (userObj !== null) {
                var attributePrivacy = $.grep(userObj.attributes, function (e) {
                    return e.text === window.UserPrivacy ? e.value : 0;
                });
                if (attributePrivacy.length > 0) {
                    data[i].PrivacyId = attributePrivacy[0].value === "" ? 0 : attributePrivacy[0].value;
                }
            }
            var currentPrivacy = $.grep(model.privacies, function (e) {
                return e.id.toString() === data[i].PrivacyId.toString();
            });
            if (currentPrivacy !== null && currentPrivacy.length > 0) {
                if (data[i].DocumentPrivacyId > currentPrivacy[0].level) {
                    hasPrivacyLevel = false;
                    htmlPrivacy += ' \n ○ ' + data[i].name;
                }
            } else {
                hasPrivacyLevel = false;
                htmlPrivacy += ' \n ○ ' + data[i].name;
            }
        }
    }
    var userStructureIds = $("#hdStructureIds").val();
    var successCallback = function () {
        tryCloseModal(model.actionsComponentId);
        loadInboxList();
    };
    var message = SendTransferModal.checkAbilityToSend(transferToStructureIds.length === 0 ? userStructureIds.split(window.Seperator) : transferToStructureIds, transferToStructures, model.selectedPrivacy, model.privacies, null, false);
    if (message === "error") {
        return;
    }
    if (!hasPrivacyLevel) {
        message += (message !== "" ? " \n " : "") + htmlPrivacy;
    }
    if (message !== "") {
        if (!structureExist) {
            setTimeout(function () {
                Common.showConfirmMsg(message + " \n " + Resources.ContinueConfirmation, function () {
                    SendTransferModal.transfer(transferComponent, data, false, false, false, model.delegationId, true, null, null, model.signDocument, model.signatureTemplate, successCallback);
                }, function () {
                });
            }, 300);
        }
        else {
            if (window.SendWithoutStructureReceiverOrPrivacyLevel === "True") {
                setTimeout(function () {
                    Common.showConfirmMsg(message + " \n " + Resources.ContinueConfirmation, function () {
                        SendTransferModal.transfer(transferComponent, data, false, false, false, model.delegationId, true, null, null, model.signDocument, model.signatureTemplate, successCallback);
                    }, function () {
                    });
                }, 300);
            } else {
                setTimeout(function () {
                    Common.alertMsg(message);
                }, 300);
            }
        }
    } else {
        if (ccedTransfer === data.length) {
            setTimeout(function () {
                Common.showConfirmCcedMsg(Resources.AllSelectedTransfersAreCCconfirmation, function () {
                    SendTransferModal.transfer(transferComponent, data, true, false, false, model.delegationId, true, null, null, model.signDocument, model.signatureTemplate, successCallback);
                }, function () {
                    SendTransferModal.transfer(transferComponent, data, false, true, false, model.delegationId, true, null, null, model.signDocument, model.signatureTemplate, successCallback);
                });
            }, 300);
        } else {
            SendTransferModal.transfer(transferComponent, data, false, false, false, model.delegationId, true, null, null, model.signDocument, model.signatureTemplate, successCallback);
        }
    }
}
function unlock(id, delegationId, nodeId) {

    Common.showConfirmMsg(Resources.UnlockConfirmation, function () {
        var params = { "Id": id, "__RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val() };
        if (delegationId !== null) {
            params.delegationId = delegationId;
        }
        Common.ajaxPost('/Transfer/UnLock', params, function (response) {
            if (response === "False") {
                Common.showScreenErrorMsg();
            }
            else if (response === "FileInUse") {
                setTimeout(function () {
                    Common.alertMsg(Resources.FileInUse);
                }, 300);
            } else {
                if (!gFromVip) {
                    GridCommon.RefreshCurrentPage("grdInboxItems", true);
                } else {
                    var li = $($("input[data-id='" + id + "']").parents("li")[0]);
                    $("input[data-id='" + id + "']").attr('data-lockedbyme', 'false');
                    li.find('.unlockIcon').remove();
                    var actions = li.find('.mdl-action');
                    actions.append("<button class='btn btn-xs btn-primary mr-sm lockIcon' style='padding: 0px 3px;'><i class='fa fa-edit' title='" + Resources.Edit + "'></i></button>");
                    openDocumentDetails(id, delegationId, true, nodeId);
                }
            }
            Common.tryCloseDocumentModal(self.model.actionsComponentId);
        });
    });
}
function resend(data) {
    Common.showConfirmMsg(Resources.ResendConfirmation, function () {

        var broadcastModel = {};
        var exportedCategoryModel = new CategoryModel().findFullById(data.exportedDocumentCategoryId);
        if (typeof exportedCategoryModel !== 'undefined' && exportedCategoryModel !== "" && exportedCategoryModel !== null) {
            if (exportedCategoryModel.basicAttribute !== "" && exportedCategoryModel.basicAttribute !== null) {
                let basicAttributes = JSON.parse(exportedCategoryModel.basicAttribute);
                if (basicAttributes.length > 0) {
                    let receivingEntityObj = $.grep(basicAttributes, function (e) {
                        return e.Name === "ReceivingEntity";
                    });
                    if (receivingEntityObj[0].BroadcastReceivingEntity && receivingEntityObj[0].Type === "internal") {
                        broadcastModel.isInternalBroadcast = true;
                        broadcastModel.isBroadcast = true;
                    } else if (receivingEntityObj[0].BroadcastReceivingEntity) {
                        broadcastModel.isBroadcast = true;
                    }
                }
            }
        }
        var receivingEntities = data.receivingEntityId;
        var delegationId = null;
        var transferToType = broadcastModel.isInternalBroadcast ? TransferType.BroadcastSend : TransferType.Send;
        var transferToTxt = data.fromUserId;
        //    , , , self.model.fromUser, self, null, false, actionArray);
        //receivingEntities, delegationId, transferToType, transferToTxt, self, signatureTemplate, withSign, actionArray

        let modalWrapper = $(".modal-window");
        var modelIndex = new ExportOptions.ExportOptions();
        //let allPurposes = new Helper().get();
        var defaultPurposeForExport = window.ToViewPurpose /*allPurposes.filter(p => p.id === 13 || p.text.includes("To View") || p.text.includes("للعرض"))*/;
        modelIndex.purposes = new Helper().getPurpose();
        modelIndex.receivingEntities = receivingEntities;
        modelIndex.transferToType = transferToType;
        modelIndex.transferToUser = transferToTxt;
        modelIndex.transferToStructure = data.toStructureId;
        modelIndex.customAttributeDueDate = data.dueDate;
        modelIndex.documentId = data.exportedDocumentId;
        modelIndex.transferId = data.signedTransferId;
        modelIndex.fromVip = false;
        modelIndex.enableSendingRules = window.EnableSendingRules === "True";
        modelIndex.enableTransferToUsers = window.EnableTransferToUsers === "True";
        modelIndex.isStructureSender = window.IsStructureSender === "True";
        modelIndex.delegationId = delegationId;
        modelIndex.structureIds = $("#hdStructureIds").val().split(window.Seperator);
        modelIndex.fromStructureId = data.fromStructureId;
        modelIndex.signatureTemplate = null
        modelIndex.withSign = false;
        modelIndex.fromExport = false;
        modelIndex.fromResend = true;
        modelIndex.incomingDocumentId = data.documentId;
        modelIndex.isSpecific = false;
        if (transferToType == TransferType.BroadcastSend || transferToType == TransferType.BroadcastComplete) {
            modelIndex.isBroadcast = true;
            modelIndex.broadcastIds = data.SignedTransferId;
        }
        modelIndex.action = "Attribute.Export";
        var ExportOptionsView = new ExportOptions.ExportOptionsView(modalWrapper, modelIndex, function () {
            setTimeout(() => {
                GridCommon.Refresh('grdRejectedDocumentsItems');
                TreeNode.refreshTreeNodeCounts(TreeNode.Custom);
                tryCloseModal(self.model.actionsComponentId)

            }, 100);
        });
        modelIndex.defaultPurposeForExport = defaultPurposeForExport ? defaultPurposeForExport[0].id : null;
        ExportOptionsView.render();
        $('#modalExportOptions').modal('show');
        $("#modalExportOptions").off("hidden.bs.modal");
        $("#modalExportOptions").off("shown.bs.modal");

        $('#modalExportOptions').on('hidden.bs.modal', function () {
            $('#modalExportOptions').remove();
        });
    }, function () {

    });
}
function EditAfterExport(id) {
    Common.showConfirmMsg(Resources.EditAfterSignConfirmation, function () {
        Common.mask(document.body, "body-mask");
        Common.ajaxPost('/DocumentLock/EditAfterExport',
            {
                'documentId': id, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
            },
            function (result) {
                //swal.close();
                if (result.success) {
                    GridCommon.Refresh('grdRejectedDocumentsItems');
                    TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                    TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);
                    TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Closed);
                    TreeNode.refreshTreeNodeCounts(TreeNode.MyRequests);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Custom);
                    Common.showScreenSuccessMsg();
                    tryCloseModal(self.model.actionsComponentId)
                } else {
                    if (result.message == "")
                        Common.showScreenErrorMsg();
                    else {
                        setTimeout(() => {
                            Common.alertMsg(result.message);
                        }, 100);                    }
                }

                Common.unmask("body-mask");
            }, function () {
                swal.close();
                Common.unmask("body-mask");
                Common.showScreenErrorMsg();
            }, false);
    });
}

function showHideInstruction(data) {
    let modalWrapper = $(".modal-window");
    let modelIndex = new TransferInstructionIndex.TransferInstructionIndex();
    modelIndex.readOnly = true;
    modelIndex.data = data;
    let transferInstructionIndexView = new TransferInstructionIndex.TransferInstructionIndexView(modalWrapper, modelIndex);
    transferInstructionIndexView.render();
    $('#modalTransferInstruction').modal('show');
    $("#modalTransferInstruction").off("hidden.bs.modal");
    $("#modalTransferInstruction").off("shown.bs.modal");
    $('#modalTransferInstruction').on('shown.bs.modal', function () {
    });
    $('#modalTransferInstruction').on('hidden.bs.modal', function () {
        $('#modalTransferInstruction').remove();
    });
}

function ReturnWorkflow(id, transferId, sendDelegationId, structureReceiverIds, transferToType, withSign, SignatureTemplateId, documentId) {

    $('#' + self.model.ComponentId + '_btnProceed').attr("disabled", true);
    $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", true);
    $('#' + self.model.ComponentId + '_btnMyTransfer').attr("disabled", true);
    $('#' + self.model.ComponentId + '_btnReturn').button('loading');

    let userStructureId = $("#hdLoggedInStructureId").val();
    let params = {
        'id': id,
        'transferId': transferId === "" ? null : transferId,
        'purposeId': window.ReturnPurposeId,
        'structureId': userStructureId,
        'delegationId': sendDelegationId,
        'structureReceivers': structureReceiverIds,
        'transferToType': transferToType,
        'withSign': withSign,
        'SignatureTemplateId': SignatureTemplateId,
        'documentId': documentId === "" ? null : documentId,
        '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
    };


    Common.ajaxPost('/Transfer/Reply', params, function (data) {
        $('#' + self.model.ComponentId + '_btnProceed').attr("disabled", false);
        $('#' + self.model.ComponentId + '_btnProceed').button('reset');
        $('#' + self.model.ComponentId + '_btnReturn').button('disabled', false);
        $('#' + self.model.ComponentId + '_btnReturn').button('reset');
        if (data == "") {
            var nodeId = $('[data-inherit="' + TreeNode.Sent + '"]').first().data("id");
            var redirectTo = '#sent/' + nodeId;

            tryCloseModal(self.model.actionsComponentId);
            TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
            TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);
            TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
            TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
            TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
            $(".close-correspondence").trigger("click");
            $("[ref=btnCloseTransfer]").trigger("click");
            if ($('li .active').attr("data-id") == undefined) {
                window.location.href = "/";
            }
            else {
                window.location.reload();
            }

            //window.location.href = redirectTo;
            Common.showScreenSuccessMsg();
        }

        else if (data.message === 'FileInUse') {

            Common.alertMsg(Resources.FileInUse);

        }

        else if (data.message === 'OriginalFileInUse') {

            Common.alertMsg(Resources.OriginalFileInUse);

        }
        else {

            Common.showScreenErrorMsg(data.message);

        }


    }, function () { Common.showScreenErrorMsg(); }, false);
}
function sendToReceivingEntity(receivingEntities, delegationId, transferToType, transferToTxt, self, signatureTemplate, withSign, actionArray, isSigned = false) {

    var documentCarbonCopy = self.model.documentCarbonCopy;

    if (!actionArray?.includes("Attribute.Export") && !actionArray?.includes("Transfer.SignAndExport")) {
        receivingEntities = $("[ref='cmbCustomAttributeReceiver']").length > 0
            ? $("[ref='cmbCustomAttributeReceiver']").select2("data")
            : receivingEntities;

        documentCarbonCopy = $("[ref='cmbCustomAttributeCarbonCopy']").length > 0
            ? $("[ref='cmbCustomAttributeCarbonCopy']").select2("data")
            : self.model.documentCarbonCopy;
    }

    $.each(documentCarbonCopy, (index, x) => {
        x.isCC = true;
    });
    $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", false);
    $('#' + self.model.ComponentId + '_btnComplete').button('reset');
    $('#' + self.model.ComponentId + '_btnCompleteAndArchive').attr("disabled", false);
    $('#' + self.model.ComponentId + '_btnCompleteAndArchive').button('reset');
    $('#' + self.model.ComponentId + '_btnReplyToStructure').attr("disabled", false);
    $('#' + self.model.ComponentId + '_btnReplyToStructure').button('reset');
    $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", false);
    $('#' + self.model.ComponentId + '_btnReplyToUser').button('reset');
    $('#' + self.model.ComponentId + '_btnMyTransfer').attr("disabled", false);
    $('#' + self.model.ComponentId + '_btnMyTransfer').button('reset');
    $('#' + self.model.ComponentId + '_btnSend').attr("disabled", false);
    $('#' + self.model.ComponentId + '_btnSend').button('reset');
    $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", false);
    $('#' + self.model.ComponentId + '_btnSignTemplate').button('reset');
    $('#' + self.model.ComponentId + '_btnGroupDropup').attr("disabled", false);
    $('#' + self.model.ComponentId + '_btnGroupDropup').button('reset');
    $('#' + self.model.ComponentId + '_btnProceed').attr("disabled", false);
    $('#' + self.model.ComponentId + '_btnProceed').button('reset');
    $('#' + self.model.ComponentId + '_btnReturn').attr("disabled", false);
    $('#' + self.model.ComponentId + '_btnReturn').button('reset');
    $('#' + self.model.ComponentId + '_btnResume').attr("disabled", false);
    $('#' + self.model.ComponentId + '_btnResume').button('reset');
    $('#' + self.model.ComponentId + '_btnReinitiate').attr("disabled", false);
    $('#' + self.model.ComponentId + '_btnReinitiate').button('reset');

    let modalWrapper = $(".modal-window");
    if (actionArray != undefined) {
        if (actionArray.includes("Attribute.Export") || actionArray.includes("Transfer.SignAndExport"))
            var modelIndex = new ExportOptions.ExportOptions();

    }
    else {
        var modelIndex = new SendToReceivingEntityIndex.SendToReceivingEntityIndex();

    }
    //let allPurposes = new Helper().get();
    var defaultPurposeForExport = window.ToViewPurpose /*allPurposes.filter(p => p.id === 13 || p.text.includes("To View") || p.text.includes("للعرض"))*/;
    modelIndex.purposes = new Helper().getPurpose();
    modelIndex.receivingEntities = receivingEntities;
    modelIndex.transferToType = transferToType;
    modelIndex.transferToUser = transferToTxt;
    modelIndex.transferToStructure = transferToTxt;
    modelIndex.customAttributeDueDate = self.model.dueDate;
    modelIndex.documentId = self.model.documentId;
    modelIndex.transferId = self.model.transferId;
    modelIndex.fromVip = gFromVip;
    modelIndex.enableSendingRules = window.EnableSendingRules === "True";
    modelIndex.enableTransferToUsers = window.EnableTransferToUsers === "True";
    modelIndex.isStructureSender = window.IsStructureSender === "True";
    modelIndex.delegationId = delegationId;
    modelIndex.structureIds = $("#hdStructureIds").val().split(window.Seperator);
    modelIndex.fromStructureId = self.model.fromStructureId;
    modelIndex.signatureTemplate = signatureTemplate
    modelIndex.withSign = withSign;
    modelIndex.fromExport = true;
    modelIndex.isSpecific = false;
    modelIndex.documentCarbonCopy = documentCarbonCopy;
    modelIndex.referenceNumber = self.model.referenceNumber;
    modelIndex.fromStructureInbox = self.model.fromStructureInbox;

    modelIndex.isSigned = isSigned;
    if (transferToType == TransferType.BroadcastSend || transferToType == TransferType.BroadcastComplete) {
        modelIndex.isBroadcast = true;
        modelIndex.broadcastIds = self.model.transferId;
    }
    if (actionArray != undefined) {
        if (actionArray.includes("Attribute.Export") || actionArray.includes("Transfer.SignAndExport"))
            modelIndex.action = "Attribute.Export";
    }
    modelIndex.actionsComponentId = self.model.actionsComponentId;
    let sendToReceivingEntityIndexView = new SendToReceivingEntityIndex.SendToReceivingEntityIndexView(modalWrapper, modelIndex, function () {
        tryCloseModal(self.model.actionsComponentId);
        GridCommon.Refresh("grdInboxItems");
        if (self.model.fromVip) {
            $(".withBorders-o").addClass("waitingBackground");
            $("#inboxDocumentDetailsContainer").empty();
            if (!self.model.isSigned)
                $($("input[data-id='" + self.model.transferId + "']").parents("li")[0]).fadeOut().remove();
        }
        TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);
        TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
    } /*self.model.callback*/);

    if (purposeIdForSignature != 0) {
        Common.mask(document.body, "body-mask");
        modelIndex.purposeIdForSignature = purposeIdForSignature;
        sendToReceivingEntityIndexView.sendToReceivingEntitySubmit();
        $('#signatureTemplatesClose').click(function () {
            $('#modalSignatureTemplates').modal("hide");
        });
        Common.unmask(document.body, "body-mask");
    }
    else {
        if (actionArray != undefined && (actionArray.includes("Attribute.Export") || actionArray.includes("Transfer.SignAndExport")) && defaultPurposeForExport.length != 0) {
            modelIndex.defaultPurposeForExport = defaultPurposeForExport ? defaultPurposeForExport : null;
            var ExportOptionsView = new ExportOptions.ExportOptionsView(modalWrapper, modelIndex, function () {
                setTimeout(() => {
                    TreeNode.refreshTreeNodeCounts(TreeNode.Custom);
                    tryCloseModal(self.model.actionsComponentId)
                }, 100);

            });
            ExportOptionsView.render();
            Common.unmask(document.body, "body-mask");
            $('#modalExportOptions').modal('show');
            $("#modalExportOptions").off("hidden.bs.modal");
            $("#modalExportOptions").off("shown.bs.modal");

            $('#modalExportOptions').on('hidden.bs.modal', function () {
                $('#modalExportOptions').remove();
                if (fromSignAndExport) {
                    Common.tryCloseDocumentModal(self.model.actionsComponentId);
                    GridCommon.RefreshCurrentPage("grdInboxItems", false);
                    if (self.model.fromVip) {
                        TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);

                        $(".withBorders-o").addClass("waitingBackground");
                        $("#inboxDocumentDetailsContainer").empty();
                        $($("input[data-id='" + self.model.transferId + "']").parents("li")[0]).fadeOut().remove();
                    }
                    fromSignAndExport = false;
                }
            });
        }
        else {
            Common.unmask("body-mask");
            sendToReceivingEntityIndexView.render();

        }
    }
    $('#signatureTemplatesClose').click(function () {
        $('#modalSignatureTemplates').modal("hide");
    });
    $('#btnSignTemplateClose').click();

    $("#modalSendToReceivingEntity").off("shown.bs.modal");
    $('#modalSendToReceivingEntity').on('shown.bs.modal', function () {
        $("#hdSendDeligationId").val(delegationId);
        CKEDITOR.instances.txtAreaInstruction.focus();
    });
    $('#modalSendToReceivingEntity').on('hidden.bs.modal', function () {
        var categoryModel = new CategoryModel().findFullById(self.model.categoryId);
        $("#modalSendToReceivingEntity").remove();
        if (fromSignAndReply /*&& categoryModel.name == "Outgoing"*/) {
            Common.tryCloseDocumentModal(self.model.actionsComponentId);
            GridCommon.RefreshCurrentPage("grdInboxItems", false);
            if (self.model.fromVip || (window.InboxMode == "InboxVIPView" || window.InboxMode === 'LocalVIPView')) {
                TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);

                $(".withBorders-o").addClass("waitingBackground");
                $("#inboxDocumentDetailsContainer").empty();
                $($("input[data-id='" + self.model.transferId + "']").parents("li")[0]).fadeOut().remove();
            }

        }
        fromSignAndReply = false;
        swal.close();
    });
}
function openRecallReasonModal(callback) {
    // The modal has a header having >> "modal-header" div contains the closing button icon that closes the modal
    // The modal also has a textarea for notes, and two buttons: "Submit" and "Cancel".
    const modal = $(`
                        <div data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-hidden="true" class="modal fade modalRecallAction" id="RecallActionModal" tabindex="-1">
                        <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                        <div class="modal-header" style="border-bottom:0px; padding-bottom:3px;">
                        <button type="button" ref="recallActionClose" id="recallActionModalClose" class="close" data-dismiss="modal">&times;</button>
                        <h4 ref="modalRecallTitle" class="modal-title"></h4>
                        </div> 
                        <div class="modal-body" style="padding-top: 2px; ">
                        <form ref="formPost" method="post" data-parsley-validate="" novalidate="">
                        <div class="row">
                        <div class="col-md-12" ref="recallActionReasonContainer">
                        <label class="control-label field-required" style="font-size: medium; ">${Resources.Note}</label>
                        <textarea id="recallActionReason" rows="3" data-parsley-required="true"class="form-control" required></textarea>
                        <div class="invalid-feedback" style="display:none; color:red;">
                                         ${Resources.Thisfieldvalueisrequired}
                        </div>
                        </div>
                        </div>
                        </form>
                        </div>
                        <div class="modal-footer" style="border-top:0px;">
                        <button type="button" class="btn btn-primary" id="submitRecallActionReason">${Resources.Submit}</button>
                        <button type="button" class="btn btn-secondary" id="cancelRecallAction" data-bs-dismiss="modal">${Resources.Cancel}</button>
                        </div>
                        </div>
                        </div>
                        </div>
                        `);
    // UI Production: The modal is dynamically created using a jQuery template literal and appended to the body.

    $('body').append(modal); // This body is the default screen html body, so we basically append this modal template into the screen content

    modal.modal('show');   //  displays the modal
    modal.find('#submitRecallActionReason').on('click', function () {
        const textarea = modal.find('#recallActionReason');
        const reason = textarea.val().trim();// Removes any leading or trailing whitespace from the complete reason 
        const errorMsg = textarea.siblings('.invalid-feedback');

        if (!reason) {
            textarea.addClass('is-invalid');  // Adds red border
            errorMsg.show();  // Shows the error message
            modal.find('form').addClass('was-validated'); // Ensure Bootstrap applies styles
            return;
        }
        else {
            textarea.removeClass('is-invalid'); // Removes red border
            errorMsg.hide(); // Hides error message
            $("#recallActionModalClose").trigger("click");  //  triggering the close button to close the entire modal with its shadow
            callback(reason);
            //  modal.modal('hide');
        }
    });
    // Remove validation styles on input change
    modal.find('#recallActionReason').on('input', function () {
        $(this).removeClass('is-invalid');
        $(this).siblings('.invalid-feedback').hide();
    });
    modal.find('#cancelRecallAction').on('click', function () {
        modal.find('#recallActionReason').val('');
        $("#recallActionModalClose").trigger("click");
    });

    modal.on('hidden.bs.modal', function () {
        modal.remove();
    });
}

function recall(id, delegationId, self) {
    openRecallReasonModal(function (reason) {
        Common.ajaxPost('/Transfer/Recall',
            {
                'id': id, 'delegationId': delegationId, 'note': reason, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
            },
            function (result) {
                if (!result) {
                    Common.alertMsg(Resources.CannotBeRecalled, function () {
                        swal.close()
                        TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                        TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
                        TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                        TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);
                        TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                        TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                        GridCommon.RefreshCurrentPage(gTableName, false);
                    });
                } else {
                    swal.close()
                    $("#gridContainerDiv").show();
                    $("div [ref=" + self.model.parentComponentId + "]").remove();
                    TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Inbox); TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox); TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                    if (self.model.fromVip) {
                        $(".withBorders-o").addClass("waitingBackground");
                        $("#sentListContainer").empty();
                        $($("input[data-id='" + self.model.transferId + "']").parents("li")[0]).fadeOut().remove();
                    } else {
                        GridCommon.RefreshCurrentPage("grdSentItems", false);
                    }
                    $('.close').trigger('click');

                    if (!gFromVip) {
                        GridCommon.RefreshCurrentPage("grdInboxItems", true);
                    } else {
                        $(".withBorders-o").addClass("waitingBackground");
                        $("#inboxDocumentDetailsContainer").empty();
                        $($("input[data-id='" + self.model.transferId + "']").parents("li")[0]).fadeOut().remove();
                    }
                }
            }, function () { Common.showScreenErrorMsg(); }, false);

    });
}
function openDocumentDetails(id, delegationId, readOnly, nodeId, isCCed) {

    var params = { id: id };
    if (delegationId !== null) {
        params.delegationId = delegationId;
    }
    Common.ajaxGet('/Document/GetDocumentBasicInfoByTransferId', params, function (response) {
        $('input:checkbox').removeAttr('checked');
        $('#inboxListContainer li').removeClass("active");
        $("input[data-id='" + id + "']").parent().parent().parent().removeClass('unread').addClass("active");
        $("input[data-id='" + id + "']").attr('data-read', 'true');
        $("input[data-id='" + id + "']").prop('checked', true);

        let item = "liInbox" + nodeId;
        if (delegationId !== null) {
            item = "index-" + nodeId;
        }
        Common.setActiveSidebarMenu(item);
        $(".delegation").removeClass("active");
        var model = new VipDocumentDetails.VipDocumentDetails();
        model.categoryId = response.categoryId;
        model.readonly = false;
        model.delegationId = delegationId;
        model.id = id;
        model.documentId = response.id;
        model.referenceNumber = response.referenceNumber;
        model.categoryName = response.categoryName;
        model.statusId = response.status;
        model.createdByUser = response.createdByUser;
        model.readonly = readOnly === true;
        model.attachmentId = response.attachmentId;
        model.attachmentVersion = response.attachmentVersion;
        model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
        model.showMyTransfer = true;
        model.fromInbox = true;
        model.isCCed = isCCed;
        var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));

        var tabs = securityMatrix.SecurityNodes[nodeId].SecurityCategories[response.categoryId].Tabs;
        model.tabs = $.grep(tabs, function (element, index) {
            return !element.Name.includes("myTransfer") && !element.Name.includes("documentMetadata") &&
                !element.Name.includes("transferHistory") && !element.Name.includes("visualTracking") &&
                !element.Name.includes("activityLog") && !element.Name.includes("attachments") && !element.Name.includes("notes") &&
                !element.Name.includes("linkedDocument") && !element.Name.includes("nonArchivedAttachments");
        });
        model.tabsWithStatic = tabs;
        model.tabsActions = securityMatrix.SecurityNodes[nodeId].SecurityCategories[response.categoryId].SecurityTabs;
        model.showBackButton = false;
        model.showPreview = window.OpenCorrespondenceMode === "OpenCorrespondenceWithViewer" && response.attachmentId !== null;

        var wrapper = $("#inboxDocumentDetailsContainer");
        wrapper.empty();
        $(".modal-window").empty();
        var view = new VipDocumentDetails.VipDocumentDetailsView(wrapper, model);
        view.render();
        $(".documentHeader").hide();
        $(".waitingBackground").removeClass("waitingBackground");
    }, function () { Common.showScreenErrorMsg(); }, true);
}

function createMeetingResolutionDocument(id, templateId, prevDocId) {

    Common.mask(document.body, "body-mask");
    var createdByStructureId;
    var createdByStructureId;
    if (window.EnablePerStructure) {
        if ($("#hdLoggedInStructureId").val() != null && $("#hdLoggedInStructureId").val() != "")
            createdByStructureId = $("#hdLoggedInStructureId").val();
        else if (sessionStorage.getItem($("#hdUserId").val() + "loggedInStructure") !== null) {
            createdByStructureId = sessionStorage.getItem($("#hdUserId").val() + "loggedInStructure");
        }
    } else {
        if (sessionStorage.getItem($("#hdUserId").val() + "SelectedUserStrcuture") !== null) {
            createdByStructureId = sessionStorage.getItem($("#hdUserId").val() + "SelectedUserStrcuture");
        }
        else if ($("#hdStructureId").val() != null && $("#hdStructureId").val() != "")
            createdByStructureId = $("#hdStructureId").val();
    }

    var params = {
        'CategoryId': id,
        "TemplateId": templateId,
        "PreviousDocumentId": prevDocId,
        'CreatedByStructureId': createdByStructureId,
        '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
    };
    Common.ajaxPost('/Document/CreateMeetingResolutionDocument', params, function (data) {
        Common.unmask("body-mask");
        if (data.state == "1") {
            Common.alertMsg(Resources.NoPermssionCreateMeeting);
        } else {
            TreeNode.addToNodeCount(TreeNode.Draft, 1, 1);
            tryCloseModal(self.model.actionsComponentId);
            window.location.href = "/#document/" + data.id;
        }
    }, function () {
        Common.unmask("body-mask");
        Common.showScreenErrorMsg();
    });
}
function getDocumentIdInOriginalMail(documentId, callback) {
    Common.mask(document.body, "body-mask");
    Common.ajaxGet("/Attachment/List", { "documentId": documentId }, function (data) {
        const childIds = getChildIdsFromFolder("folder_originalMail", data);
        if (childIds && childIds.length > 0) {
            callback(childIds[0].split("_")[1]);
        } else {
            Common.unmask("body-mask");
            callback(null);

        }
        Common.unmask("body-mask");
    },
        function (error) {
            console.error("Error fetching data:", error);
            Common.unmask("body-mask");
            callback(null);

        }
    );
}
function getChildIdsFromFolder(folderId, data) {
    for (const item of data) {
        if (item.id === folderId && item.children) {
            return item.children.map(child => child.id);
        }
        if (item.children) {
            const result = getChildIdsFromFolder(folderId, item.children);
            if (result) {
                return result;
            }
        }
    }
    return null;
}
function validateAndPreviewAttachment(attachmentId, selectedSignature) {
    Common.unmask("body-mask");
    Common.mask(document.body, "body-mask");
    Common.ajaxGet(
        "/Attachment/PreviewAttachmentValidation",
        { "id": attachmentId, "delegationId": self.model.delegationId },
        function (response) {
            if (response === "OriginalFileInUse") {
                setTimeout(() => Common.alertMsg(Resources.OriginalFileInUse), 400);
                Common.unmask("body-mask");

            } else if (response === "AttachmentNotComplete") {
                setTimeout(() => Common.alertMsg(Resources.AttachmentNotComplete), 400);
                Common.unmask("body-mask");
            }
            else if (self.model.referenceNumber === null) {
                let url = `/Attachment/PreviewBeforeSign?attachmentId=${attachmentId}&documentId=${self.model.documentId}&delegationId=${self.model.delegationId}&signature=${selectedSignature}&GenerateReference=true`;
                openPdfInModal(url);
                Common.unmask("body-mask");

            }
            else {
                if (attachmentId == null && attachmentId == '0') return;
                let url = `/Attachment/PreviewBeforeSign?attachmentId=${attachmentId}&documentId=${self.model.documentId}&delegationId=${self.model.delegationId}&signature=${selectedSignature}&GenerateReference=false`;
                openPdfInModal(url);
                Common.unmask("body-mask");
            }

        }
    );
}
$(document).on('click', '#pdfModalClose', function () {
    $('#pdfModal').modal("hide");
});

function openPdfInModal(url) {
    // Set the iframe source to the PDF URL
    $('#pdfIframe').attr('src', url);
    // Show the modal
    $('#pdfModal').modal('show');

}
//function validateAndPreviewAttachment(attachmentId, selectedSignature) {
//    Common.ajaxGet(
//        "/Attachment/PreviewAttachmentValidation",
//        { "id": attachmentId, "delegationId": self.model.delegationId },
//        function (response) {
//            if (response === "OriginalFileInUse") {
//                setTimeout(() => Common.alertMsg(Resources.OriginalFileInUse), 400);
//            } else if (response === "DocumentNotRegistered"){
//                if (!attachmentId) return;
//                let url = `/Attachment/PreviewBeforeSign?attachmentId=${attachmentId}&documentId=${self.model.documentId}&delegationId=${self.model.delegationId}&signature=${selectedSignature}&GenerateReference=true`;
//                window.open(url, '_blank');
//            } else if (response === "AttachmentNotComplete") {
//                setTimeout(() => Common.alertMsg(Resources.AttachmentNotComplete), 400);
//            }
//            else {
//                if (!attachmentId) return;
//                let url = `/Attachment/PreviewBeforeSign?attachmentId=${attachmentId}&documentId=${self.model.documentId}&delegationId=${self.model.delegationId}&signature=${selectedSignature}&GenerateReference=false`;
//                window.open(url, '_blank');
//            }
//        }
//    );
//}

function TransferExport(actionArray) {
    try {
        $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", true);
        $('#' + self.model.ComponentId + '_btnCompleteAndArchive').attr("disabled", true);
        $('#' + self.model.ComponentId + '_btnSend').attr("disabled", true);
        //var actionArray = this.model.actionName.split("_");
        var receivingEntitiestosend = ($("[ref='cmbCustomAttributeReceiver']").length > 0 ? $("[ref='cmbCustomAttributeReceiver']").select2("data") : self.model.receivingEntities.map(x => x.id));
        sendToReceivingEntity(receivingEntitiestosend, self.model.delegationId, self.model.isInternalBroadcast ? TransferType.BroadcastSend : TransferType.Send, self.model.fromUser, self, null, false, actionArray);

    } catch (e) {
        $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", false);
        $('#' + self.model.ComponentId + '_btnComplete').button('reset');
        $('#' + self.model.ComponentId + '_btnCompleteAndArchive').attr("disabled", false);
        $('#' + self.model.ComponentId + '_btnCompleteAndArchive').button('reset');
        $('#' + self.model.ComponentId + '_btnSend').attr("disabled", false);
        $('#' + self.model.ComponentId + '_btnSend').button('reset');
    }
}


// clickMyTransfer

function clickMyTransfer() {
    Common.mask(document.body, "body-mask");
    Common.ajaxGet('/Document/CheckDocumentAttachmentLocked/', { "documentId": self.model.documentId }, function (retval) {
        if (retval) {
            Common.unmask("body-mask");
            Common.alertMsg(Resources.FileInUse);
            Common.unmask("body-mask");
        } else {

            var callback = function (data, isFollowUpTransfer) {
                if (window.EnableConfirmationMessage === "True" & !isFollowUpTransfer) {
                    Common.showConfirmMsg(Resources.ProceedConfirmation, function () {
                        sendTransfer(data, self.model);
                    });
                } else {
                    sendTransfer(data, self.model);
                }
            };
            var prioritiesList = [];
            Common.ajaxGet("Priority/List", null, function (data) {
                prioritiesList = data;
                for (var i = 0; i < data.length; i++) {
                    var text = data[i].name;
                    if (window.language === "ar" && data[i].nameAr !== "") {
                        text = data[i].nameAr;
                    } else if (window.language === "fr" && data[i].nameFr !== "") {
                        text = data[i].nameFr;
                    }
                    prioritiesList[i].text = text;
                }
            }, function () { Common.showScreenErrorMsg(); }, null, null, false);
            var categoryModel = new CategoryModel().findFullById(self.model.categoryId);

            if (categoryModel && categoryModel.basicAttribute.length > 0) {
                let basicAttributes = JSON.parse(categoryModel.basicAttribute);
                if (basicAttributes.length > 0) {
                    var dueDateRelatedToPriority = $.grep(basicAttributes, function (e) {
                        return e.Name === "DueDate";
                    });
                    gIsDueDateRelatedToPriority = dueDateRelatedToPriority[0].RelatedToPriority;
                }
            }
            let modalWrapper = $(".modal-window");
            transferComponent = new Transfer(self.model.userStructure, $("#hdUserId").val(), $("#hdStructureIds").val().split(window.Seperator),
                window.IsStructureSender === "True", window.EnableSendingRules === "True", window.EnableTransferToUsers === "True", self.model.dueDate, self.model.delegationId,
                callback, modalWrapper, false, null, gIsDueDateRelatedToPriority, prioritiesList, self.model.documentId, fromSignAndTransfer, false, (self.model.isSigned && categoryModel.name == 'Outgoing'));//check on Outgoing category to make sure this transfer not from ready for transfer 
            transferComponent.render();
            //if (fromSignAndTransfer == true) // to reset global variable after transfer
            //    fromSignAndTransfer = false;
            Common.unmask("body-mask");
            $('.modalTransfer').modal('show');
            $(".modalTransfer").off("hidden.bs.modal");
            $(".modalTransfer").off("shown.bs.modal");
            $('.modalTransfer').on('hidden.bs.modal', function () {
                $(".modalTransfer").parent().remove();
                if (fromSignAndTransfer /*&& categoryModel.name == "Outgoing"*/) {
                    Common.tryCloseDocumentModal(self.model.actionsComponentId);
                    GridCommon.RefreshCurrentPage("grdInboxItems", false);
                    if (self.model.fromVip || (window.InboxMode == "InboxVIPView" || window.InboxMode === 'LocalVIPView')) {
                        TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);

                        $(".withBorders-o").addClass("waitingBackground");
                        $("#inboxDocumentDetailsContainer").empty();
                        $($("input[data-id='" + self.model.transferId + "']").parents("li")[0]).fadeOut().remove();
                    }

                }
                if (fromSignAndTransfer == true) // to reset global variable after transfer
                    fromSignAndTransfer = false;
                swal.close();
            });
        }
    });
}
async function CheckUserCertificate() {
    try {
        const responseArray = await $.ajax({
            method: "GET",
            url: window.DSURL + "/api/userCertificate",
            headers: {
                "Authorization": "Bearer " + IdentityAccessToken
            },
            dataType: 'json',
        });

        if (!Array.isArray(responseArray) || responseArray.length === 0) {
            return Resources.NoCertificate;
        }
        const certificate = responseArray[0];

        if (!certificate.activated) {
            return Resources.CertificateNotActivated;
        }
        if (new Date(certificate.endDate) < Date.now()) {
            return Resources.CertificateExpired;
        }
        return null;

    } catch (error) {
        return Resources.FailedToGetCertificate;
    }

}
function ClickSignAndReplyToUser() {
    Common.ajaxGet('/Document/CheckDocumentAttachmentLocked/', { "documentId": self.model.documentId }, function (retval) {
        if (retval) {
            Common.alertMsg(Resources.FileInUse);
        } else {

            try {
                $('#' + self.model.ComponentId + '_Complete').attr("disabled", true);
                $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", true);
                $('#' + self.model.ComponentId + '_btnMyTransfer').attr("disabled", true);
                $('#' + self.model.ComponentId + '_btnReplyToStructure').button('loading');
                sendToReceivingEntity(self.model.replyToEntity, self.model.delegationId, TransferType.ReplyToUser, self.model.fromUser, self, null, false, null, self.model.isSigned);
            } catch (e) {
                $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnComplete').button('reset');
                $('#' + self.model.ComponentId + '_btnCompleteAndArchive').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnCompleteAndArchive').button('reset');
                $('#' + self.model.ComponentId + '_btnReplyToStructure').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnReplyToStructure').button('reset');
                $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnReplyToUser').button('reset');
                $('#' + self.model.ComponentId + '_btnMyTransfer').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnMyTransfer').button('reset');
            }
        }
    });
}
function signDocument(self, signType, templateId) {
    if (templateId == 0) {
        var templateId = $('input[name="rdbSignature"]:checked').val();

    }
    if (templateId == undefined) {
        Common.alertMsg(Resources.SignatureRequired);
        Common.unmask("body-mask");
        return;
    }
    if (signType == 0) {
        let parms = {
            'templateId': templateId,
            'documentId': self.model.documentId,
            'transferId': self.model.transferId,
            'delegationId': self.model.delegationId
        }
        Common.mask(document.body, "body-mask");
        $('#btnSubmitSign').attr("disabled", true);
        $('#btnSubmitSign').button('reset');
        var headers = {};
        headers.Authorization = 'Bearer ' + window.IdentityAccessToken;

        var BookMarkscallBack = () => {

            $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", false);
            $('#' + self.model.ComponentId + '_btnComplete').button('reset');
            $('#' + self.model.ComponentId + '_btnCompleteAndArchive').attr("disabled", false);
            $('#' + self.model.ComponentId + '_btnCompleteAndArchive').button('reset');
            $('#' + self.model.ComponentId + '_btnReplyToStructure').attr("disabled", false);
            $('#' + self.model.ComponentId + '_btnReplyToStructure').button('reset');
            $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", false);
            $('#' + self.model.ComponentId + '_btnReplyToUser').button('reset');
            $('#' + self.model.ComponentId + '_btnMyTransfer').attr("disabled", false);
            $('#' + self.model.ComponentId + '_btnMyTransfer').button('reset');
            $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", false);
            $('#' + self.model.ComponentId + '_btnSignTemplate').button('reset');
            $('#btnSubmitSign').attr("disabled", false);
            $('#btnSubmitSign').button('reset');
            $('#' + self.model.ComponentId + '_btnGroupDropup').attr("disabled", false);
            $('#' + self.model.ComponentId + '_btnGroupDropup').button('reset');
            $('#modalSignatureTemplates').modal("hide");

            tryCloseModal(self.model.actionsComponentId);
            Common.showScreenSuccessMsg(Resources.SigningDocument);
            Common.unmask("body-mask");
            let $removedItem = $($("input[data-id='" + self.model.transferId + "']").parents("li")[0]).clone();
            if (self.model.fromVip || (window.InboxMode == "InboxVIPView" || window.InboxMode === 'LocalVIPView')) {
                TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);

                $(".withBorders-o").addClass("waitingBackground");
                $("#inboxDocumentDetailsContainer").empty();
                $($("input[data-id='" + self.model.transferId + "']").parents("li")[0]).fadeOut().remove();
            } else {
                GridCommon.Refresh("grdInboxItems");
            }
            TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
            TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);
            $('.btn-SignOperations').remove();

            Common.ajaxPostWithHeaders('Transfer/SignDocument', parms,
                function (returnedData) {
                    var data = returnedData.message;

                    if (data == '') {
                        Common.showScreenSuccessMsg(Resources.SigningSuccess);
                        TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                        TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);
                    }
                    else {
                        if (self.model.fromVip) {

                            $('.VIPContainerItem').prepend($removedItem);
                            $(".withBorders-o").removeClass("waitingBackground");
                            $removedItem.trigger('click');
                        } else {
                            GridCommon.Refresh("grdInboxItems");
                        }
                        TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                        TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);
                        var errorMessage = Resources.SigningFailed;
                        if (Resources[data] !== undefined)
                            errorMessage += ": " + Resources[data];
                        else if (data = "Can't render barcode: not enough space")
                            errorMessage += ": " + data;
                        Common.showScreenErrorMsg(errorMessage);
                    }

                    //Common.showScreenSuccessMsg();
                    //sendToReceivingEntity(self.model.replyToEntity, self.model.delegationId, TransferType.SignAndSend, self.model.fromStructure, self);
                }, function (error, object) {
                    var msg = error ? error : "";
                    if (object && object.responseText) {
                        msg = object.responseText;
                    }
                    Common.showScreenErrorMsg(msg);

            }, false, null, headers, true)
        };
        CheckUserCanSign(self.model.documentId, self.model.transferId, self.model.delegationId, ["signature", "barcode"], BookMarkscallBack);
        //CheckBookMarksAreExist(self.model.documentId, self.model.transferId, ["signature"], BookMarkscallBack);//"barcode" // remove barcode bookmark check for now
    }
    else if (signType == 1) {// sign and send 
        signType = 0;
        sendToReceivingEntity(self.model.replyToEntity, self.model.delegationId, TransferType.Send, self.model.fromUser, self, templateId, true);
    }
    else if (signType == 2) { // sign and transfer
        signType = 0;
        Common.mask(document.body, "body-mask");
        self.model.signDocument = false;
        self.model.signatureTemplate = templateId;
        //$('#' + self.model.ComponentId + '_btnSignTemplate').click();


        let parms = {
            'templateId': templateId,
            'documentId': self.model.documentId,
            'transferId': self.model.transferId,
            'delegationId': self.model.delegationId
        }
        $('#btnSubmitSign').attr("disabled", true);
        $('#btnSubmitSign').button('reset');
        var BookMarkscallBack = () => {
            Common.ajaxPost('Transfer/SignDocument', parms, function (returnedData) {
                var data = returnedData.message;
                $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnComplete').button('reset');
                $('#' + self.model.ComponentId + '_btnReplyToStructure').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnReplyToStructure').button('reset');
                $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnReplyToUser').button('reset');
                $('#' + self.model.ComponentId + '_btnMyTransfer').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnMyTransfer').button('reset');
                $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnSignTemplate').button('reset');
                $('#btnSubmitSign').attr("disabled", false);
                $('#btnSubmitSign').button('reset');
                $('#' + self.model.ComponentId + '_btnGroupDropup').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnGroupDropup').button('reset');
                $("#modalSignatureTemplates").modal("hide");
                if (data == '') {
                    //Common.unmask("body-mask");
                    fromSignAndTransfer = true;
                    clickMyTransfer();
                    //$('#' + self.model.ComponentId + '_btnMyTransfer').click();
                    Common.showScreenSuccessMsg();

                    $('[id$=viewerFrame]').attr("src", $('[id$=viewerFrame]').attr("src"))
                    TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                    TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);
                    GridCommon.Refresh("grdInboxItems");
                    if (self.model.fromVip) {
                        TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);

                        $(".withBorders-o").addClass("waitingBackground");
                        $("#inboxDocumentDetailsContainer").empty();
                        $($("input[data-id='" + self.model.transferId + "']").parents("li")[0]).fadeOut().remove();
                    }


                }
                else if (data == 'NoSignatureConfigured') {
                    Common.alertMsg(Resources.NoSignatureConfigured);
                    Common.unmask("body-mask");
                }
                else if (data == 'NoAttachmentToSign') {
                    Common.alertMsg(Resources.NoAttachmentToSign);
                    Common.unmask("body-mask");
                }
                else if (data === "CheckoutFaild") {
                    Common.alertMsg(Resources.CheckoutFaild);
                    Common.unmask("body-mask");
                }
                else if (data === "SignFailed") {
                    Common.alertMsg(Resources.SignFailed);
                    Common.unmask("body-mask");
                }
                else if (data === "HasNoAccess") {
                    Common.alertMsg(Resources.HasNoAccess);
                    Common.unmask("body-mask");
                }
                else if (data === "CheckinFailed") {
                    Common.alertMsg(Resources.CheckinFailed);
                    Common.unmask("body-mask");
                }
                else if (data === "FileInUse") {
                    Common.alertMsg(Resources.FileInUse);
                    Common.unmask("body-mask");
                }
                else if (data === "Can't render barcode: not enough space") {
                    Common.alertMsg(data);
                    Common.unmask("body-mask");
                }
                else {
                    Common.showScreenErrorMsg();
                    Common.unmask("body-mask");
                }
                Common.unmask("body-mask");
                //Common.showScreenSuccessMsg();
                //sendToReceivingEntity(self.model.replyToEntity, self.model.delegationId, TransferType.SignAndSend, self.model.fromStructure, self);
            }, function (error, object) {
                var msg = error ? error : "";
                if (object && object.responseText) {
                    msg = object.responseText;
                }
                Common.showScreenErrorMsg(msg);

            });
        };
        CheckBookMarksAreExist(self.model.documentId, self.model.transferId, ["signature", "barcode"], BookMarkscallBack);//"barcode" // remove barcode bookmark check for now
        //Common.unmask("body-mask");
        //$('#' + self.model.ComponentId + '_btnMyTransfer').click();

    }
    else if (signType == 3) { // sign and reply to structure
        signType = 0;
        sendToReceivingEntity(self.model.replyToEntity, self.model.delegationId, TransferType.ReplyToStructure, self.model.fromStructure, self, templateId, true);
    }
    else if (signType == 4) { // sign and transfer
        signType = 0;
        Common.mask(document.body, "body-mask");
        self.model.signDocument = false;
        self.model.signatureTemplate = templateId;

        let parms = {
            'templateId': templateId,
            'documentId': self.model.documentId,
            'transferId': self.model.transferId,
            'delegationId': self.model.delegationId
        }
        $('#btnSubmitSign').attr("disabled", true);
        $('#btnSubmitSign').button('reset');
        var BookMarkscallBack = () => {
            Common.ajaxPost('Transfer/SignDocument', parms, function (returnedData) {
                var data = returnedData.message;
                $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnComplete').button('reset');
                $('#' + self.model.ComponentId + '_btnReplyToStructure').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnReplyToStructure').button('reset');
                $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnReplyToUser').button('reset');
                $('#' + self.model.ComponentId + '_btnMyTransfer').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnMyTransfer').button('reset');
                $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnSignTemplate').button('reset');
                $('#btnSubmitSign').attr("disabled", false);
                $('#btnSubmitSign').button('reset');
                $('#' + self.model.ComponentId + '_btnGroupDropup').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnGroupDropup').button('reset');
                $("#modalSignatureTemplates").modal("hide");
                if (data == '') {
                    //Common.unmask("body-mask");
                    fromSignAndReply = true;

                    Common.showScreenSuccessMsg();

                    $('[id$=viewerFrame]').attr("src", $('[id$=viewerFrame]').attr("src"))
                    TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                    TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);
                    GridCommon.Refresh("grdInboxItems");
                    if (self.model.fromVip) {
                        TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);

                        $(".withBorders-o").addClass("waitingBackground");
                        $("#inboxDocumentDetailsContainer").empty();
                        $($("input[data-id='" + self.model.transferId + "']").parents("li")[0]).fadeOut().remove();
                    }
                    self.model.isSigned = true;
                    //$('#' + self.model.ComponentId + '_btnReplyToUser').click();
                    ClickSignAndReplyToUser();
                }
                else if (data == 'NoSignatureConfigured') {
                    Common.alertMsg(Resources.NoSignatureConfigured);
                    Common.unmask("body-mask");
                }
                else if (data == 'NoAttachmentToSign') {
                    Common.alertMsg(Resources.NoAttachmentToSign);
                    Common.unmask("body-mask");
                }
                else if (data === "CheckoutFaild") {
                    Common.alertMsg(Resources.CheckoutFaild);
                    Common.unmask("body-mask");
                }
                else if (data === "SignFailed") {
                    Common.alertMsg(Resources.SignFailed);
                    Common.unmask("body-mask");
                }
                else if (data === "HasNoAccess") {
                    Common.alertMsg(Resources.HasNoAccess);
                    Common.unmask("body-mask");
                }
                else if (data === "CheckinFailed") {
                    Common.alertMsg(Resources.CheckinFailed);
                    Common.unmask("body-mask");
                }
                else if (data === "FileInUse") {
                    Common.alertMsg(Resources.FileInUse);
                    Common.unmask("body-mask");
                }
                else if (data === "Can't render barcode: not enough space") {
                    Common.alertMsg(data);
                    Common.unmask("body-mask");
                }
                else {
                    Common.showScreenErrorMsg();
                    Common.unmask("body-mask");
                }
                Common.unmask("body-mask");
            }, function (error, object) {
                var msg = error ? error : "";
                if (object && object.responseText) {
                    msg = object.responseText;
                }
                Common.showScreenErrorMsg(msg);

            });
        };
        CheckBookMarksAreExist(self.model.documentId, self.model.transferId, ["signature", "barcode"], BookMarkscallBack);//"barcode" // remove barcode bookmark check for now

    }
    else if (signType == 5) { // proceed 
        Common.mask(document.body, "body-mask");
        signType = 0;
        self.model.signDocument = true;
        self.model.signatureTemplate = templateId;
        proceed(self);
    }
    else if (signType == 6) { // preview before sign
        signType = 0;
        getDocumentIdInOriginalMail(self.model.documentId, function (attachmentId) {
            validateAndPreviewAttachment(attachmentId, templateId);
        });


        $("#modalSignatureTemplates").modal('hide'); // Close the modal after submission
    }
    else if (signType == 7) { // sign and Export
        signType = 0;
        Common.mask(document.body, "body-mask");
        self.model.signDocument = false;
        self.model.signatureTemplate = templateId;

        let parms = {
            'templateId': templateId,
            'documentId': self.model.documentId,
            'transferId': self.model.transferId,
            'delegationId': self.model.delegationId
        }
        $('#btnSubmitSign').attr("disabled", true);
        $('#btnSubmitSign').button('reset');
        var BookMarkscallBack = () => {
            Common.ajaxPost('Transfer/SignDocument', parms, function (returnedData) {
                var data = returnedData.message;
                $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnComplete').button('reset');
                $('#' + self.model.ComponentId + '_btnReplyToStructure').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnReplyToStructure').button('reset');
                $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnReplyToUser').button('reset');
                $('#' + self.model.ComponentId + '_btnMyTransfer').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnMyTransfer').button('reset');
                $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnSignTemplate').button('reset');
                $('#btnSubmitSign').attr("disabled", false);
                $('#btnSubmitSign').button('reset');
                $('#' + self.model.ComponentId + '_btnGroupDropup').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnGroupDropup').button('reset');
                $("#modalSignatureTemplates").modal("hide");
                if (data == '') {
                    //Common.unmask("body-mask");
                    //$('#' + self.model.ComponentId + '_btnTransferExport').click();
                    TransferExport(actionArray);
                    $('[id$=viewerFrame]').attr("src", $('[id$=viewerFrame]').attr("src"))
                    TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                    TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);
                    GridCommon.Refresh("grdInboxItems");
                    if (self.model.fromVip) {
                        TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);

                        $(".withBorders-o").addClass("waitingBackground");
                        $("#inboxDocumentDetailsContainer").empty();
                        $($("input[data-id='" + self.model.transferId + "']").parents("li")[0]).fadeOut().remove();
                    }
                }
                else if (data == 'NoSignatureConfigured') {
                    Common.alertMsg(Resources.NoSignatureConfigured);
                    Common.unmask("body-mask");
                }
                else if (data == 'NoAttachmentToSign') {
                    Common.alertMsg(Resources.NoAttachmentToSign);
                    Common.unmask("body-mask");
                }
                else if (data === "CheckoutFaild") {
                    Common.alertMsg(Resources.CheckoutFaild);
                    Common.unmask("body-mask");
                }
                else if (data === "SignFailed") {
                    Common.alertMsg(Resources.SignFailed);
                    Common.unmask("body-mask");
                }
                else if (data === "HasNoAccess") {
                    Common.alertMsg(Resources.HasNoAccess);
                    Common.unmask("body-mask");
                }
                else if (data === "CheckinFailed") {
                    Common.alertMsg(Resources.CheckinFailed);
                    Common.unmask("body-mask");
                }
                else if (data === "FileInUse") {
                    Common.alertMsg(Resources.FileInUse);
                    Common.unmask("body-mask");
                }
                else if (data === "Can't render barcode: not enough space") {
                    Common.alertMsg(data);
                    Common.unmask("body-mask");
                }
                else {
                    Common.showScreenErrorMsg();
                    Common.unmask("body-mask");
                }
                Common.unmask("body-mask");

            } ,function (error, object) {
                var msg = error ? error : "";
                if (object && object.responseText) {
                    msg = object.responseText;
                }
                Common.showScreenErrorMsg(msg);

            });
        };
        CheckBookMarksAreExist(self.model.documentId, self.model.transferId, ["signature", "barcode"], BookMarkscallBack);//"barcode" // remove barcode bookmark check for now
    }
}


const connection = new signalR.HubConnectionBuilder()
    .withUrl("/notificationhub")
    .withAutomaticReconnect()
    .build();

connection.on("RefreshInboxGrid", () => {
    if (self.model.fromVip) {
        TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
        $(".withBorders-o").addClass("waitingBackground");
        $("#inboxDocumentDetailsContainer").empty();
        $($("input[data-id='" + self.model.transferId + "']").parents("li")[0]).fadeOut().remove();
    }
    $('[ref="modalLinkedCorrespondenceDocument"]').modal('hide');
    $('[ref="modalLinkedCorrespondenceDocument"]').on('hidden.bs.modal', function () {
        $('.modal-documents').empty();
    });
    GridCommon.Refresh("grdInboxItems");
    TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
    TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);

});

connection.start()
    .catch(err => console.error("SignalR error:", err));
function CheckUserCanSign(documentId, transferId, delegationId, bookmarks, successCallBack) {
    Common.ajaxPost('Transfer/CheckUserCanSign', { documentId, transferId, delegationId, bookmarks }//"barcode" // remove barcode bookmark check for now
        , function (res) {
            if (res.result) {
                if (typeof (successCallBack) === "function") {
                    successCallBack();
                }
            }
            else {
                Common.unmask("body-mask");
                $('#modalSignatureTemplates').modal("hide");
                $('#btnSubmitSign').attr("disabled", false);
                $('#btnSubmitSign').button('reset');
                Common.alertMsg(Resources[res.message] + ": " + res.details);
            }
        }
        , (err) => {
            Common.unmask("body-mask");
            $('#modalSignatureTemplates').modal("hide");
            $('#btnSubmitSign').attr("disabled", false);
            $('#btnSubmitSign').button('reset');
            Common.showScreenErrorMsg();
        }
    );
}

function CheckBookMarksAreExist(documentId, transferId, bookmarks, BookMarkscallBack) {
    Common.ajaxPost('Transfer/CheckBookMarksAreExist', { documentId, transferId, BookMarks: ["signature"] }//"barcode" // remove barcode bookmark check for now
        , function (res) {
            if (res.result) {
                if (typeof (BookMarkscallBack) === "function") {
                    BookMarkscallBack();
                }
            }
            else {
                var bookmarksText = "\n";
                $.each(res.bookmarks, (i, value) => {
                    if (res.bookmarks.length == i + 1)
                        bookmarksText += value;
                    else
                        bookmarksText += value + "\n";
                });
                Common.unmask("body-mask");
                $('#modalSignatureTemplates').modal("hide");
                $('#btnSubmitSign').attr("disabled", false);
                $('#btnSubmitSign').button('reset');
                Common.alertMsg(Resources.BookmarksDosenotExist + ":" + bookmarksText);
            }
        }
        , (err) => {
            Common.unmask("body-mask");
            $('#modalSignatureTemplates').modal("hide");
            $('#btnSubmitSign').attr("disabled", false);
            $('#btnSubmitSign').button('reset');
            Common.showScreenErrorMsg();
        }
    );

}

function proceed(self) {
    var params = {
        "documentId": self.model.documentId,
        'TransferId': self.model.transferId,
        'workflowStepId': self.model.workflowStepId,
        'forSignature': self.model.forSignature,
        'signatureTemplateId': self.model.signatureTemplate,
        'delegationId': self.model.delegationId
    };
    Common.ajaxPost('/Workflow/Proceed', params, function (data) {
        if (!data.success) {
            Common.unmask("body-mask");
            if (data.message === 'FileInUse') {
                Common.alertMsg(Resources.FileInUse);
            }
            else if (data.message === 'OriginalFileInUse') {
                Common.alertMsg(Resources.OriginalFileInUse);
            }
            else if (data.message === 'NoWorkflowStepsAdded') {
                Common.alertMsg(Resources.NoWorkflowStepsAdded);
            }
            else if (data.message === 'NotAllUserHasSignatureRegion') {
                Common.alertMsg(Resources.NotAllUserHasSignatureRegion);
            }
            else if (data.message === 'DocumentIsCompleted') {
                Common.alertMsg(Resources.DocumentIsCompleted);
            }
            else if (data.message === 'TransferIsExist') {
                Common.alertMsg(Resources.TransferIsExist);
            }
            else {
                Common.showScreenErrorMsg(data.message);
            }
        }
        else if (data.success) {
            var nodeId = $('[data-inherit="' + TreeNode.Sent + '"]').first().data("id");
            var redirectTo = '#sent/' + nodeId;

            tryCloseModal(self.model.actionsComponentId);
            GridCommon.Refresh("grdInboxItems");
            if (self.model.fromVip) {
                $(".withBorders-o").addClass("waitingBackground");
                $("#inboxDocumentDetailsContainer").empty();
                $($("input[data-id='" + self.model.transferId + "']").parents("li")[0]).fadeOut().remove();
            }
            TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
            TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);
            TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
            TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
            TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
            $(".close-correspondence").trigger("click");
            $("[ref=btnCloseTransfer]").trigger("click");

            if ($('li .active').attr("data-id") == undefined) {
                //window.location.href = "/";
            }
            else {
                //window.location.reload();
            }

            //window.location.href = redirectTo;
            Common.unmask("body-mask");
            Common.showScreenSuccessMsg();
        }

    }, function () {
        Common.unmask("body-mask");
        Common.showScreenErrorMsg();
    });
}

async function getCategoryReferenceNumber(categoryId) {
    return new Promise((resolve, reject) => {
        let params = {
            'categoryId': categoryId
        };
        Common.ajaxGet('/CategoryReferenceNumber/GetByCategoryId/', params, function (retval) {
            resolve(retval.referenceNumberGeneratorType);
        }, function () {
            reject();
        }, false);
    });
}

function loadVipTaskPanel(model) {
    $("#" + model.parentComponentId + "_taskPanelHeader").html(model.referenceNumber);
    $("#" + model.parentComponentId + "_fromTaskPanel").html(model.fromStructure);
    $("#" + model.parentComponentId + "_toTaskPanel").html(model.toStructure);
    $("#" + model.parentComponentId + "_purposeTaskPanel").html(model.purpose);
    $("#" + model.parentComponentId + "_subjectTaskPanel").html(model.subject);
    $("#" + model.parentComponentId + "_transferDateTaskPanel").html(model.createdDate);
    //$("#" + model.parentComponentId + "_registerByTaskPanel").html(model.createdByUser);
    //$("#" + model.parentComponentId + "_taskPanelHeader").html(model.referenceNumber);
    //$("#" + model.parentComponentId + "_taskPanelHeader").html(model.referenceNumber);
    //$(self.refs['sendingEntity']).text(self.model.sendingEntity);
    //$(self.refs['receivingEntity']).text(self.model.receivingEntity);
    //$(self.refs['subject']).text(self.model.subject);
    //$(self.refs['fromStructure']).text(self.model.fromStructure);
    //$(self.refs['fromUser']).text(self.model.fromUser);
    //$(self.refs['toStructure']).text(self.model.toStructure);
    //$(self.refs['toUser']).text(self.model.toUser);
    //$(self.refs['purpose']).text(self.model.purpose);
    //$(self.refs['priority']).text(self.model.priority);
}


function markAsUnread(id, delegationId) {

    var params = {
        "Id": id,
        "__RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val()
    };

    if (delegationId !== null) {
        params.DelegationId = delegationId;
    }

    Common.ajaxPost('/Transfer/UnRead', params, function (response) {
        if (!response.success) {
            if (response.message === "FileInUse")
                setTimeout(function () {
                    Common.alertMsg(Resources.FileInUse);
                }, 300);
        }
        else
            TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
    });
}
var gFromVip = false;
//0=> only sign , 1 => sign and send , 2 => sign and Transfer ,3 => sign and reply to structure ,4 => sign and reply to user ,5 => proceed
var signType = 0;
var gIsDueDateRelatedToPriority = false;
var self;
var fromSignOnly = false;
var fromSignAndTransfer = false;
var fromSignAndReply = false;
var fromSignAndExport = false;
var actionArray;
function CompleteAndArchive(DmsFolderId) {
    Common.showConfirmMsg(Resources.CompleteOneTsfConfirmation, function () {
        var callback = function (rootFolderId) {
            if (!self.model.fromVip) {
                Common.mask(document.getElementById('grdInboxItems'), "inboxListContainer-mask");
            } else {
                Common.mask(document.getElementById('inboxListContainer'), "inboxListContainer-mask");
            }
            Common.ajaxPost('/Transfer/Complete',
                {
                    'ids': self.model.transferId, 'delegationId': self.model.delegationId, forArchive: true, rootFolderId: rootFolderId, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                },
                function (result) {
                    if (result[1]) {
                        if (result[1].message === "ArchivingFailed") {
                            setTimeout(function () {
                                Common.alertMsg(Resources.ArchivingFailed);
                            }, 300);
                            Common.unmask("inboxListContainer-mask");
                            return;
                        }
                    }
                    if (result[0]) {
                        if (result[0].updated && result[0].uncompletedDocumentReferenceNumber && !result[0].message) {
                            var msg = Resources.TransferWasCompletedNoOriginalMail;
                            if (result[0].documentAttachmentIdHasValue) {
                                msg = Resources.TransferWasCompletedNotLastOpenTransfer;
                            }
                            setTimeout(function () {
                                Common.unmask("inboxListContainer-mask");
                                Common.alertConfirmation(msg, function () {
                                    $("#gridContainerDiv").show();
                                    Common.unmask("inboxListContainer-mask");
                                    $("div [ref=" + self.model.parentComponentId + "]").remove();
                                    TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                                    TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);
                                    TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                                    TreeNode.refreshTreeNodeCounts(TreeNode.Closed);
                                    if (!gFromVip) {
                                        GridCommon.RefreshCurrentPage("grdInboxItems", true);
                                        $("#" + self.model.actionsComponentId + "_btnClose").click();
                                    } else {
                                        $(".withBorders-o").addClass("waitingBackground");
                                        $("#inboxDocumentDetailsContainer").empty();
                                        $($("input[data-id='" + self.model.transferId + "']").parents("li")[0]).fadeOut().remove();

                                    }
                                });
                            }, 300);

                        }
                        else if (result[0].updated) {
                            Common.unmask("inboxListContainer-mask");
                            tryCloseModal(self.model.actionsComponentId);
                            Common.showScreenSuccessMsg();
                            $("#gridContainerDiv").show();
                            $("div [ref=" + self.model.parentComponentId + "]").remove();
                            TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                            TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);
                            TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                            TreeNode.refreshTreeNodeCounts(TreeNode.Closed);
                            TreeNode.refreshTreeNodeCounts(TreeNode.MyRequests);
                            TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
                            if (!gFromVip) {
                                GridCommon.RefreshCurrentPage("grdInboxItems", true);
                                $("#" + self.model.actionsComponentId + "_btnClose").click();
                            } else {
                                $(".withBorders-o").addClass("waitingBackground");
                                $("#inboxDocumentDetailsContainer").empty();
                                $($("input[data-id='" + self.model.transferId + "']").parents("li")[0]).fadeOut().remove();
                            }
                        } else {
                            if (result[0].message === "HasLockedAttachmentsByUser") {
                                setTimeout(function () {
                                    Common.alertMsg(Resources.HasLockedAttachmentsByUser);
                                }, 300);
                                Common.unmask("inboxListContainer-mask");
                            } else if (result[0].message === "OriginalDocumentLockedByUser") {
                                setTimeout(function () {
                                    Common.alertMsg(Resources.OriginalDocumentLockedByUser);
                                }, 300);
                                Common.unmask("inboxListContainer-mask");
                            } else if (result[0].message === "TransferHasDifferentOwnerOrIsCarbonCopy") {
                                setTimeout(function () {
                                    Common.alertMsg(Resources.TransferHasDifferentOwnerOrIsCarbonCopy);
                                }, 300);
                                Common.unmask("inboxListContainer-mask");
                            } else if (result[0].message === "Cannotarchivebecausethereareotheropentransfers") {
                                setTimeout(function () {
                                    Common.alertMsg("Cannot archive because there are other open transfers");
                                }, 300);
                                Common.unmask("inboxListContainer-mask");
                            } else {
                                setTimeout(function () {
                                    Common.alertMsg(Resources.CorrespondenceNotCompleteNoOriginalMail);
                                }, 300);
                                Common.unmask("inboxListContainer-mask");
                            }
                        }
                    } else {
                        Common.showScreenErrorMsg();
                    }
                }, function () {
                    Common.showScreenErrorMsg();
                    Common.unmask("inboxListContainer-mask");
                }, false);
        }
        if (DmsFolderId) {
            callback(DmsFolderId);
        } else {
            let modalWrapper = $(".modal-window");
            modalWrapper.find("#modalCabinetTree").remove();
            var modalCabinetTree = new CabinetTree.CabinetTree();
            modalCabinetTree.callback = callback;
            var CabinetTreeView = new CabinetTree.CabinetTreeView(modalWrapper, modalCabinetTree);
            CabinetTreeView.render();

            $('#modalCabinetTree').modal('show');
            $("#modalCabinetTree").off("hidden.bs.modal");
            $("#modalCabinetTree").off("shown.bs.modal");
            $('#modalCabinetTree').on('shown.bs.modal', function () {
            });
            $('#modalCabinetTree').on('hidden.bs.modal', function () {
            });
        }
    });

}
var fromPeview = false;
var templateId = 0;
var purposeIdForSignature = 0;

class MyTransfer extends Intalio.Model {
    constructor() {
        super();
        this.transferId = null;
        this.delegationId = null;
        this.completeReasonNote = null;
        this.readonly = null;
        this.sendingEntity = null;
        this.receivingEntity = null;
        this.subject = null;
        this.fromStructure = null;
        this.fromUser = null;
        this.toStructure = null;
        this.toUser = null;
        this.purpose = null;
        this.priority = null;
        this.createdDate = null;
        this.dueDate = null;
        this.openedDate = null;
        this.privacyId = null;
        this.fromStructureId = null;
        this.documentId = null;
        this.receivingEntityId = null;
        this.ownerUserId = null;
        this.instruction = null;
        this.toStructureId = null;
        this.replyToEntity = null;
        this.sentToUser = null;
        this.privacies = null;
        this.withViewer = null;
        this.closedDate = null;
        this.fromSent = null;
        this.parentTransferId = null;
        this.parentComponentId = null;
        this.actionsComponentId = null;
        this.categoryId = null;
        this.fromVip = false;
        this.nodeId = null;
        this.isCCed = null;
        this.VoiceNote = false;
        this.isBroadcast = false;
        this.isInternalBroadcast = false;
        this.fromInbox = false;
        this.closedTransfer = false;
        this.byTemplate = false;
        this.forSignature = false;
        this.signDocument = false;
        this.signatureTemplate = null;
        this.workflowStepId = null;
        this.initiatorUser = null;
        this.isWorkflowReturned = false;
        this.hasReferenceNumber = false;
        this.hasAttachments = false;
        this.hasUserCofigureSignature = false;
        this.nextStepUserName = null;
        this.allowSign = false;
        this.referenceNumber = null;
        this.attachmentExtention = null;
        this.isSigned = false;
        this.isDocumentSigned = false;
        this.fromFollowUp = false;
        this.fromRejectedDocument = false;
        this.documentCarbonCopy = [];
        this.resendData = null;
        this.fromStructureInbox = false;
    }
}
var transferComponent;
class MyTransferView extends Intalio.View {
    constructor(element, model) {
        super(element, "mytransfer", model);
    }
    async render() {
        self = this;
        gFromVip = this.model.fromVip;

        if (self.model.forSignature == true) {
            Common.ajaxPost('/StructureSignature/GetTemplateIdByStructureIdAndUserId/', { "structureId": self.model.toStructureId }, function (retval) {
                if (retval) {
                    templateId = retval.data;
                }
            });
            Common.ajaxGet('CTS/Purpose/ListDefaultPurpose/', null, function (retval) {
                if (retval) {
                    purposeIdForSignature = retval;
                }
            });
        }
        var actionsPanel = "#" + self.model.parentComponentId + "_ActionsPanel";
        if (!gFromVip && self.model.actionsComponentId !== 'null' && self.model.actionsComponentId !== undefined)
            actionsPanel = "#" + self.model.actionsComponentId + "_ActionsPanel";

        if (gFromVip)
            loadVipTaskPanel(this.model);
        $(actionsPanel).empty();
        if (this.model.actionName != undefined) {
            actionArray = self.model.fromRejectedDocument == true ? [] : this.model.actionName.split("_");

            var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));
            var activenode = $('li .active[data-id]').attr("data-id") != undefined ? $('li .active[data-id]').attr("data-id") : $('.active').attr("data-id") != undefined ? $('.active').attr("data-id") : this.model.nodeId;
            if (this.model.fromFollowUp) {
                activenode = this.model.nodeId;
            }
            var Actions = securityMatrix.SecurityNodes[activenode].SecurityCategories[this.model.categoryId].SecurityTabs[this.model.tabId];
            var myTransferActions = self.model.fromRejectedDocument == true ? [] : Actions.filter(a => (a.JsFunction != null && a.JsFunction != "" && a.Type == TypeAction.Tab));//to get all custom actions of my transfer tab === TypeAction.Tab = 4

            for (var i = 0; i < myTransferActions.length; i++) {
                var actionName = myTransferActions[i].Name;
                var icone = myTransferActions[i].Icon;
                var color = myTransferActions[i].Color;
                var jsFunction = myTransferActions[i].JsFunction;
                var showInEditMode = myTransferActions[i].ShowInEditMode;
                var showInReadMode = myTransferActions[i].ShowInReadMode;
                if (actionName === Resources.GenerateBarcode || actionName === 'Transfer.GenerateBarcode' && this.model.referenceNumber) {
                    continue;
                }
                if ($(actionsPanel).find('#' + self.model.parentComponentId + '_' + actionName + '').length == 0) {
                     if (!this.model.readonly ) {
                    $(actionsPanel).append('<button id="' + self.model.parentComponentId + '_' + actionName + '" type="button" onclick="' + jsFunction + '" class="btn btn-vip-toolbar " data-loading-text="<i class="fa fa-spinner fa-spin"><i class="' + icone + '"></i> ' + actionName + '</button>');
                }
                else {
                    if (showInReadMode) {
                        $(actionsPanel).append('<button id="' + self.model.parentComponentId + '_' + actionName + '" type="button" onclick="' + jsFunction + '" class="btn btn-vip-toolbar " data-loading-text="<i class="fa fa-spinner fa-spin"><i class="' + icone + '"></i> ' + actionName + '</button>');
                    }
                }
}
            }
            if (self.model.fromRejectedDocument == true) {
                $(actionsPanel).append('<button id="' + self.model.ComponentId + '_btnResend" type="button" class="btn btn-vip-toolbar "><i class="fa fa-paper-plane mr-sm"></i>' + Resources.Resend + '</button>');
                $(actionsPanel).append('<button id="' + self.model.ComponentId + '_btnEditAfterExport" type="button" class="btn btn-vip-toolbar "><i class="fa fa-external-link-square mr-sm"></i>' + Resources.EditAfterExport + '</button>');
            }
            if (actionArray.includes("Attribute.Export") && self.model.isSigned && !self.model.DocumentIsCompleted && !self.model.readonly) {
                $(actionsPanel).append('<button ref="btnTransferExport" id="' + self.model.ComponentId + '_btnTransferExport" type="button" class="btn btn-vip-toolbar " data-loading-text="<i class="fa fa-spinner fa-spin"><i class="fa fa-check-square mr-sm"></i>' + Resources.Export + '</button>');
            }
            if (actionArray.includes("Transfer.Transfer") && !self.model.DocumentIsCompleted && !self.model.readonly) {
                //$(self.refs['btnMyTransfer']).show();
                $(actionsPanel).append('<button id="' + self.model.ComponentId + '_btnMyTransfer" type="button" class="btn btn-vip-toolbar " data-loading-text="<i class="fa fa-spinner fa-spin"><i class="fa fa-send mr-sm"></i>' + Resources.Transfer + '</button>');
            }
            if (actionArray.includes("Transfer.RejectRequest")) {
                $(actionsPanel).append('<button id="' + self.model.ComponentId + '_btnRejectRequest" type="button" class="btn btn-reject mr-sm " data-loading-text="<i class="fa fa-close"><i class="fa fa-close mr-sm"></i>' + Resources.Return + '</button>');
            }
            if (actionArray.includes("Transfer.AcceptRequest")) {
                $(actionsPanel).append('<button id="' + self.model.ComponentId + '_btnAcceptRequest" type="button" class="btn  btn-vip-toolbar " data-loading-text="<i class="fa fa-check fa-white"><i class="fa fa-check mr-sm"></i>' + Resources.Receive + '</button>');
            }
            if (actionArray.includes("Transfer.AcceptRequestAndTransfer")) {
                self.model.readonly = false;
                $(actionsPanel).append('<button id="' + self.model.ComponentId + '_btnMyTransfer" type="button" class="btn btn-vip-toolbar hidden" data-loading-text="<i class="fa fa-spinner fa-spin"><i class="fa fa-send mr-sm"></i>' + Resources.Transfer + '</button>');
                $(actionsPanel).append('<button id="' + self.model.ComponentId + '_btnAcceptRequestAndTransfer" type="button" class="btn  btn-vip-toolbar " data-loading-text="<i class="fa fa-check fa-white"><i class="fa fa-check mr-sm"></i>' + Resources.ReceiveAndTransfer + '</button>');
            }
            let userStructureId = $("#hdLoggedInStructureId").val();

            var structureSendingRulesIds = new CoreComponents.Lookup.SendingRules()
                .get(Number(userStructureId), self.model.delegationId);

            if (actionArray.includes("Transfer.ReplyToUser") && !self.model.DocumentIsCompleted && !self.model.readonly && (window.EnableSendingRules !== "True" || (window.EnableSendingRules === "True" && structureSendingRulesIds.length > 0 && structureSendingRulesIds.includes(self.model.replyToEntity[0].id)))) {
                //$(self.refs['btnReplyToUser']).show();
                $(actionsPanel).append('<button id="' + self.model.ComponentId + '_btnReplyToUser" type="button" class="btn btn-vip-toolbar "><i class="fa fa-reply mr-sm"></i>' + Resources.Reply + '</button>');
            }
            if (actionArray.includes("Transfer.Complete") && ((self.model.isInternalBroadcast && !self.model.DocumentIsCompleted && self.model.isCCed && self.model.fromInbox) || !self.model.readonly)) {
                //$(self.refs['btnComplete']).show();
                $(actionsPanel).append('<button id ="' + self.model.ComponentId + '_btnComplete" type = "button" transferAction = "Transfer.Complete" class="btn btn-vip-toolbar " ><i class="fa fa-check-square mr-sm"></i>' + Resources.Complete + '</button>');
            }
            if (actionArray.includes("Transfer.CompleteAndArchive") && ((self.model.isInternalBroadcast && !self.model.DocumentIsCompleted && self.model.isCCed && self.model.fromInbox) || !self.model.readonly)) {
                //$(self.refs['btnComplete']).show();
                Common.ajaxGet("Transfer/CheckIfLastOpenTransfer", { Id: self.model.transferId }, function (data) {
                    if (data == true)
                        $(actionsPanel).append('<button id ="' + self.model.ComponentId + '_btnCompleteAndArchive" type = "button" transferAction = "Transfer.CompleteAndArchive" class="btn btn-vip-toolbar " ><i class="fa fa-check-square mr-sm"></i>' + Resources.CompleteAndArchive + '</button>');
                }, function () { Common.showScreenErrorMsg(); }, null, null, false);
            }
            if (actionArray.includes("Transfer.CompleteAndArchive") && ((self.model.isInternalBroadcast && !self.model.DocumentIsCompleted && self.model.isCCed && self.model.fromInbox) || !self.model.readonly)) {
                //$(self.refs['btnComplete']).show();
                Common.ajaxGet("Transfer/CheckIfLastOpenTransfer", { Id: self.model.transferId }, function (data) {
                    if (data == true)
                        $(actionsPanel).append('<button id ="' + self.model.ComponentId + '_btnCompleteAndArchiveManual" type = "button" transferAction = "Transfer.CompleteAndArchiveManual" class="btn btn-vip-toolbar " ><i class="fa fa-check-square mr-sm"></i>' + Resources.CompleteAndArchiveManual + '</button>');
                }, function () { Common.showScreenErrorMsg(); }, null, null, false);
            }
            //if (actionArray.includes("Transfer.ReplyToStructure") && !self.model.DocumentIsCompleted && !self.model.readonly) {
            //    $(self.refs['btnReplyToStructure']).show();
            //    $(actionsPanel).append('<button id="' + self.model.ComponentId + '_btnReplyToStructure" type="button" class="btn btn-vip-toolbar ">' + Resources.ReplyToStructure + '</button>');
            //}
            if (actionArray.includes("Transfer.DismissCarbonCopy") && self.model.isCCed && self.model.fromInbox && !self.model.DocumentIsCompleted && !self.model.isBroadcast && !self.model.closedTransfer) {
                //$(self.refs['btnDismissCarbonCopy']).show();
                $(actionsPanel).append('<button id="' + self.model.ComponentId + '_btnDismissCarbonCopy" type="button" transferAction="Transfer.DismissCarbonCopy" class="btn btn-vip-toolbar ">' + Resources.DismissCopy + '</button>');
            }
            if (actionArray.includes("sent.AddCCAndSend") && self.model.fromSent && !self.model.isCCed) {
                $(actionsPanel).append('<button id="' + self.model.ComponentId + '_btnAddCCAndSend" type="button" class="btn btn-vip-toolbar "><i class="fa fa-cc mr-sm"></i>' + Resources.AddCCAndSend + '</button>');
            }
            if (actionArray.includes("sent.SendCopies") && self.model.fromSent && !self.model.isCCed) {
                $(actionsPanel).append('<button id="' + self.model.ComponentId + '_btnSendCopies" type="button" class="btn btn-vip-toolbar "><i class="fa fa-send mr-sm"></i>' + Resources.SendCopies + '</button>');
            }
            if ((actionArray.includes("Transfer.StartWorkflow")) && window.EnableTransferToUsers != 'False' && !self.model.DocumentIsCompleted && !self.model.readonly) {
                //$(self.refs['btnStartWorkflow']).show();
                $(actionsPanel).append('<button id="' + self.model.ComponentId + '_btnStartWorkflow" type="button" class="btn btn-vip-toolbar "><i class="fa fa-tasks mr-sm"></i>' + Resources.StartWorkflow + '</button>');

            }
            if ((actionArray.includes("Transfer.ReplyMeetingResolution")) && this.model.MeetingAgenda && !self.model.DocumentIsCompleted && !self.model.readonly) {

                //$(self.refs['btnReplyMeetingResolution']).show();
                $(actionsPanel).append('<button id="' + self.model.ComponentId + '_btnReplyMeetingResolution" type="button" class="btn btn-vip-toolbar ">' + Resources.ReplyMeetingResolution + '</button>');
            }
            if ((actionArray.includes("Transfer.EditAfterSign") || actionArray.includes(Resources.EditAfterSign)) && self.model.isSigned && !self.model.readonly) {
                $(actionsPanel).append('<button id="' + self.model.ComponentId + '_btnEditAfterSign" type="button" class="btn btn-vip-toolbar "><i class="fa fa-sign-out mr-sm"></i>' + Resources.EditAfterSign + '</button>');
            }
            if ((actionArray.includes("Transfer.Sign") || actionArray.some(action => action.startsWith("Transfer.Sign."))) && !document.getElementById(self.model.ComponentId + '_btnSignTemplate') && !self.model.DocumentIsCompleted && !self.model.readonly && self.model.forSignature && this.model.hasAttachments && (this.model.hasUserCofigureSignature || (/*this.model.byTemplate &&*/ this.model.templateHasSignature && this.model.attachmentExtention == "docx")) && self.model.allowSign && !self.model.isSigned) {
                //hasAnyPermission = true;
                //signmenu += '<li><a class="dropdown-item" id="' + self.model.ComponentId + '_btnSignTemplate" ref="btnSignTemplate" style="cursor: pointer">' + Resources.SignOnly + '</a></li>';
                $(actionsPanel).append('<button id="' + self.model.ComponentId + '_btnSignTemplate" type="button" class="btn btn-vip-toolbar "><i class="fa fa-fw fa-pencil-square-o mr-sm"></i>' + Resources.SignOnly + '</button>');

            }
            //var delegatedUserIds = $("#hdDelegatedUserIds").val().split(window.Splitter);
            //if (actionArray.includes("Unlock") && (self.model.ownerUserId !== null && self.model.ownerUserId === Number($("#hdUserId").val()) ||
            //    (self.model.ownerUserId !== null && delegatedUserIds.indexOf(self.model.ownerUserId.toString()) >= 0))) {

            //    $(actionsPanel).append('<button id="' + self.model.ComponentId + '_btnUnlock" type="button" class="btn btn-vip-toolbar ">' + Resources.Unlock + '</button>');

            //}
            //if (actionArray.includes("Transfer.SignTemplate") && !self.model.DocumentIsCompleted && !self.model.readonly && self.model.forSignature&& this.model.hasAttachments && (this.model.hasUserCofigureSignature || (this.model.byTemplate && this.model.templateHasSignature && this.model.attachmentExtention == "docx")) && self.model.allowSign) {

            //$(actionsPanel).append(
            //    '<div class="btn-group" role="group" aria-label="Button group with nested dropdown">
            //    <button type="button" id="' + self.model.ComponentId + '_btnSignTemplate" ref="btnSignTemplate" transferAction="Transfer.Sign" class="btn btn-sm btn-secondary btn-vip-toolbar">' + Resources.Sign + '</button>' +
            //    '<div class="btn-group btn-default" role="group">
            //     <button id="btnGroupDropup" ref="btnGroupDropup" type="button" class="btn btn-sm btn-secondary dropdown-toggle btn-vip-toolbar" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"> <i class="fa fa-caret-down ml-sm" aria-hidden="true"></i></button>' +
            //    '<ul class="dropdown-menu dropdown-margin" aria-labelledby="btnGroupDropup">' +
            //    '<li><a class="dropdown-item" id="' + self.model.ComponentId + '_btnSignAndSend" ref="btnSignAndSend" style="cursor: pointer">' + Resources.SignAndSend + '</a></li>' +
            //    '<li><a class="dropdown-item" id="' + self.model.ComponentId + '_btnSignAndTransfer" ref="btnSignAndTransfer" style="cursor:pointer">' + Resources.SignAndTransfer + '</a></li>' +
            //    //'<li><a class="dropdown-item" id="' + self.model.ComponentId + '_btnSignAndReplyToStructure" ref="btnSignAndReplyToStructure" style="cursor:pointer">' + Resources.SignAndReplyToStructure + '</a></li>' +
            //    '<li><a class="dropdown-item" id="' + self.model.ComponentId + '_btnSignAndReplyToUser" ref="btnSignAndReplyToUser" style="cursor:pointer">' + Resources.SignAndReplyToUser + '</a></li>' +
            //    '<li><a class="dropdown-item" id="' + self.model.ComponentId + '_btnPreviewBeforeSign" ref="btnPreviewBeforeSign" style="cursor:pointer">' + Resources.PreviewBeforeSign + '</a></li ></ul></div>
            //</div > ');

            /*}*/
            if (!self.model.DocumentIsCompleted && !self.model.readonly && self.model.forSignature && this.model.hasAttachments && (this.model.hasUserCofigureSignature || (/*this.model.byTemplate &&*/ this.model.templateHasSignature && this.model.attachmentExtention == "docx")) && self.model.allowSign && !self.model.isSigned) {
                var hasAnyPermission = false;
                var signmenu = '<div class="btn-group btn-SignOperations" role="group" aria-label="Button group with nested dropdown">'
                    + '<button id="btnGroupDropup" ref="btnGroupDropup" type="button" class="btn btn-sm btn-secondary dropdown-toggle btn-vip-toolbar" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" >' + Resources.SignOperations + '<i class="fa fa-caret-down ml-sm" aria-hidden="true"></i></button>'
                    + '<ul class="dropdown-menu dropdown-margin" aria-labelledby="btnGroupDropup">';


                if (actionArray.includes("Transfer.SignAndSend") || actionArray.some(action => action.startsWith("Transfer.SignAndSend.")) && !document.getElementById(self.model.ComponentId + '_btnSignAndSend')) {
                    hasAnyPermission = true;
                    signmenu += '<li><a class="dropdown-item" id="' + self.model.ComponentId + '_btnSignAndSend" ref="btnSignAndSend" style="cursor: pointer">' + Resources.SignAndSend + '</a></li>';
                }
                if (actionArray.includes("Transfer.SignAndTransfer") || actionArray.some(action => action.startsWith("Transfer.SignAndTransfer.")) && !document.getElementById(self.model.ComponentId + '_btnSignAndTransfer')) {
                    hasAnyPermission = true;
                    signmenu += '<li><a class="dropdown-item" id="' + self.model.ComponentId + '_btnSignAndTransfer" ref="btnSignAndTransfer" style="cursor:pointer">' + Resources.SignAndTransfer + '</a></li>';

                }
                if (actionArray.includes("Transfer.SignAndReplyToUser") || actionArray.some(action => action.startsWith("Transfer.SignAndReplyToUser.")) && !document.getElementById(self.model.ComponentId + '_btnSignAndReplyToUser')) {
                    hasAnyPermission = true;
                    signmenu += '<li><a class="dropdown-item" id="' + self.model.ComponentId + '_btnSignAndReplyToUser" ref="btnSignAndReplyToUser" style="cursor:pointer">' + Resources.SignAndReplyToUser + '</a></li>';

                }
                if (actionArray.includes("Transfer.SignAndExport") || actionArray.some(action => action.startsWith("Transfer.SignAndExport.")) && !document.getElementById(self.model.ComponentId + '_btnSignAndExport')) {
                    hasAnyPermission = true;
                    signmenu += '<li><a class="dropdown-item" id="' + self.model.ComponentId + '_btnSignAndExport" ref="btnSignAndExport" style="cursor:pointer">' + Resources.SignAndExport + '</a></li>';

                }
                if (actionArray.includes("Transfer.PreviewBeforeSign") || actionArray.some(action => action.startsWith("Transfer.PreviewBeforeSign.")) && !document.getElementById(self.model.ComponentId + '_btnPreviewBeforeSign')) {
                    hasAnyPermission = true;
                    signmenu += '<li><a class="dropdown-item" id="' + self.model.ComponentId + '_btnPreviewBeforeSign" ref="btnPreviewBeforeSign" style="cursor:pointer">' + Resources.PreviewBeforeSign + '</a></li ></ul></div>';
                }
                signmenu += '</ul></div></div>';
                if (hasAnyPermission)
                    $(actionsPanel).append(signmenu);

            }
            //   '<li><a class="dropdown-item" id="' + self.model.ComponentId + '_btnSignAndReplyToStructure" ref="btnSignAndReplyToStructure" style="cursor:pointer">' + Resources.SignAndReplyToStructure + '</a></li>' +            
            var currentCategoryReply = "";
            var currentCategoryModel = new CategoryModel().findFullById(self.model.categoryId);
            if (currentCategoryModel.configuration !== "" && currentCategoryModel.configuration !== null) {
                currentCategoryReply = JSON.parse(currentCategoryModel.configuration).Reply;
            }

            if (currentCategoryReply !== "" && actionArray.includes("Transfer.ReplyBy") && this.model.delegationId === null && !self.model.readonly) {
                //var dropReplyBy = $("#dropupReplyContainer .dropdown-menu");
                var dropReplyByItems = "";
                var categoriesSelected = Object.keys(currentCategoryReply);
                if (categoriesSelected.length > 0) {
                    for (var i = 0; i < categoriesSelected.length; i++) {
                        var userRole = (new IdentityService().getFullUser(parseInt($("#hdUserId").val()))).role.id;
                        var category = new CategoryModel().findById(parseInt(categoriesSelected[i]));
                        if (typeof category !== "undefined") {
                            if (category.roleIds.includes(userRole)) {
                                var categoryName = window.language == "en" ? category.name : window.language == "fr" ? category.nameFr : window.language == "ar" ? category.nameAr : category.name;
                                dropReplyByItems += "<li><a id='" + parseInt(categoriesSelected[i]) + "' class='dropdown-item " + self.model.ComponentId + "_reply-item' style='cursor:pointer'>" + categoryName + "</a></li>";
                            }
                        }
                    }
                    if (dropReplyByItems.length > 0) {
                        var dropReplyBy = `<div id="` + self.model.ComponentId + `_dropupReplyContainer" class="btn-group dropdown">
                                                <button type="button" transferAction="Transfer.ReplyBy" class="btn btn-vip-toolbar dropdown-toggle" data-toggle="dropdown">
                                                    ` + Resources.ReplyBy + `<i class="fa fa-caret-down ml-sm"></i>
                                                </button>
                                                <ul class="dropdown-menu">` + dropReplyByItems + `</ul>
                                            </div>`;
                        $(actionsPanel).append(dropReplyBy);
                    }
                }
            }

            //TODO
            if ((actionArray.includes("Transfer.DismissCarbonCopy"))) {
                $('#' + self.model.ComponentId + '_btnDismissCarbonCopy').show();
            }
        }
        if (!this.model.fromSent) {
            $(actionsPanel).append(

                '<button id="' + self.model.ComponentId + '_btnProceed" ref="btnProceed" type="button" class="btn btn-sm btn-secondary btn-vip-toolbar  hidden"> <i class="fa fa-arrow-right mr-sm"></i>' + ((this.model.forSignature && (this.model.byTemplate && !(this.model.isSigned || this.model.isDocumentSigned))) ? Resources.SignAndProceed : Resources.Proceed) + '</button>' +
                '<button id="' + self.model.ComponentId + '_btnResume" ref="btnResume" type = "button" class= "btn btn-sm btn-secondary btn-vip-toolbar  hidden">' + Resources.Resume + '</button>' +
                '<button id="' + self.model.ComponentId + '_btnReturn" ref="btnReturn" type="button" class="btn btn-sm btn-secondary btn-vip-toolbar  hidden"> <i class="fa fa-rotate-left mr-sm"></i>' + Resources.Return + '</button>' +
                '<button id="' + self.model.ComponentId + '_btnReinitiate" ref="btnReinitiate" type="button" class="btn btn-sm btn-secondary btn-vip-toolbar  hidden"> <i class="fa fa-rotate-right mr-sm"></i>' + Resources.Reinitiate + '</button>'
            );
        }
        $(actionsPanel).append(

            '<button id="' + self.model.ComponentId + '_btnViewFlow" ref="btnViewFlow" type="button" class="btn btn-sm btn-secondary btn-vip-toolbar  hidden"> <i class="fa fa-eye mr-sm"></i>' + Resources.ViewFlow + '</button>' +
            '<button id="' + self.model.ComponentId + '_btnPrint" ref="btnPrint" type="button" class="btn btn-sm btn-secondary btn-vip-toolbar  hidden">' + Resources.DeliveryNote + '</button>'
        );

        if (actionArray.includes("OtherAction.Export") || actionArray.includes("OtherAction.ExportAll") || actionArray.includes("OtherAction.EmailAttachment")) {
            var htmlOtherActions = '<div class="btn-group btn-default" role="group"><button id="' + self.model.ComponentId + '_btnGroupOtherAction" ref="btnGroupDropup" type="button" class="btn btn-sm btn-secondary dropdown-toggle btn-vip-toolbar" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">' + Resources.OtherActions + '<i class="fa fa-caret-down ml-sm" aria-hidden="true"></i></button>' +
                '<ul class="dropdown-menu dropdown-margin" aria-labelledby="btnGroupDropup">';

            if (actionArray.includes("OtherAction.Export")) {
                htmlOtherActions = htmlOtherActions + '<li><a class="dropdown-item" id="' + self.model.ComponentId + '_btnExport" ref="btnExport" style="cursor: pointer"><em class="fa fa-cloud-download mr-sm"></em>' + Resources.Export + '</a></li>'

            }
            if (actionArray.includes("OtherAction.ExportAll")) {
                htmlOtherActions = htmlOtherActions + '<li><a class="dropdown-item" id="' + self.model.ComponentId + '_btnExportAll" ref="btnExportAll" style="cursor:pointer"><em class="fa fa-cloud-download mr-sm"></em>' + Resources.ExportAll + '</a></li>'

            }
            if (actionArray.includes("OtherAction.EmailAttachment")) {
                htmlOtherActions = htmlOtherActions + '<li><a class="dropdown-item" id="' + self.model.ComponentId + '_btnEmailAttachment" ref="btnEmailAttachment" style="cursor:pointer"><em class="fa fa-envelope-o mr-sm"></em>' + Resources.Email + '</a></li></ul> </div> </div>'

            }
            $(actionsPanel).append(
                htmlOtherActions
            );

        }

        $(self.refs['sendingEntity']).text(self.model.sendingEntity);
        $(self.refs['receivingEntity']).text(self.model.receivingEntity);
        $(self.refs['subject']).text(self.model.subject);
        $(self.refs['fromStructure']).text(self.model.fromStructure);
        $(self.refs['fromUser']).text(self.model.fromUser);
        $(self.refs['toStructure']).text(self.model.toStructure);
        $(self.refs['toUser']).text(self.model.toUser);
        $(self.refs['purpose']).text(self.model.purpose);
        $(self.refs['priority']).text(self.model.priority);
        var referenceNumberGeneratorType = 0;
        if (!self.model.hasReferenceNumber) {
            try {
                referenceNumberGeneratorType = await getCategoryReferenceNumber(self.model.categoryId);
            } catch (error) {
                console.error('Error getting reference number generator type:', error);
            }
        }
        if (referenceNumberGeneratorType == 3) {
            //$(self.refs['btnSend']).show();
            $(actionsPanel).append('<button id="' + self.model.ComponentId + '_btnSend" type="button" class="btn btn-secondary m-xs" style="display:none">' + Resources.Send + '</button>');
        }

        //if ($("#" + self.model.parentComponentId + "_ActionsPanel").children().length > 0)
        //    $("#" + self.model.parentComponentId + "_ActionsPanel").show();

        if (self.model.categoryId == window.MeetingAgendaId || self.model.categoryId == window.MeetingResolutionId
            || self.model.categoryId == window.MeetingMinutesId || self.model.categoryId == window.SepResolutionId) {
            $('#' + self.model.ComponentId + '_btnReplyToStructure').hide();
            $('#' + self.model.ComponentId + '_btnReplyToUser').hide();
            $('#' + self.model.ComponentId + '_btnMyTransfer').hide();
        }

        if (self.model.instruction) {
            const instructiontext = self.model.instruction.replaceAll('\n', "").trim();

            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = instructiontext;
            const plainText = tempDiv.textContent || '';

            $(self.refs['instruction']).text(plainText);
            $(self.refs['instruction']).attr("title", plainText);
        } else {
            $(self.refs['btnShowInstructions']).remove();
        }


        $(self.refs['createdDate']).text(DateConverter.toHijriFormated(self.model.createdDate, null, window.CalendarType));
        $(self.refs['duedate']).text(DateConverter.toHijriFormated(self.model.dueDate, ConversionDateFormat.DateTimeFormat24_Slash, window.CalendarType));
        $(self.refs['openedDate']).text(DateConverter.toHijriFormated(self.model.openedDate, ConversionDateFormat.DateTimeFormat24_Slash, window.CalendarType));

        if (self.model.VoiceNote) {
            $(self.refs['VoiceNote']).attr("title", "VoiceNote_" + self.model.transferId);
            $(self.refs['VoiceNote']).attr('src', "/Transfer/GetTransferVoiceNoteById?TransferId=" + self.model.transferId);
        }

        if (self.model.closedDate) {
            $(self.refs['icnClosedDate']).attr("title", Resources.ClosedDate + ": " + DateConverter.toHijriFormated(self.model.closedDate, ConversionDateFormat.DateTimeFormat24_Slash, window.CalendarType));
            $(self.refs['icnClosedDate']).removeClass('hidden');

            if (self.model.readonly) {
                $(self.refs['closedDate']).text(DateConverter.toHijriFormated(self.model.closedDate, ConversionDateFormat.DateTimeFormat24_Slash, window.CalendarType));
            }
        } else {
            $(self.refs['closedDateContainer']).hide();
        }
        if (self.model.openedDate) {
            $(self.refs['icnOpenedDate']).attr("title", Resources.OpenedDate + ": " + DateConverter.toHijriFormated(self.model.openedDate, ConversionDateFormat.DateTimeFormat24_Slash, window.CalendarType));
            $(self.refs['icnOpenedDate']).removeClass('hidden');
        } else {
            $(self.refs['openedDateContainer']).hide();
        }
        self.model.selectedPrivacy = self.model.privacyId;
        self.model.receivingEntities = [];
        if (self.model.receivingEntityId) {
            for (var i = 0; i < self.model.receivingEntityId.length; i++) {
                self.model.receivingEntities.push({ 'id': self.model.receivingEntityId[i] });
            }
        }
        self.model.userStructure = self.model.toStructureId;
        var delegatedUserIds = $("#hdDelegatedUserIds").val().split(window.Splitter);
        //if ((self.model.ownerUserId !== null && self.model.ownerUserId === Number($("#hdUserId").val()) ||
        //    (self.model.ownerUserId !== null && delegatedUserIds.indexOf(self.model.ownerUserId.toString()) >= 0))) {

        //    $('.showUnlockButton').show();

        //}
        if (self.model.categoryId == 2) {
            $('#' + self.model.ComponentId + '_btnSignAndSend').hide();
        }
        if (self.model.fromStructure === '') {
            $(self.refs['fromStructureContainer']).hide();
        }
        if (self.model.toStructure === '') {
            $(self.refs['ToStructureContainer']).hide();
        }
        if (self.model.fromUser === '') {
            $(self.refs['fromUserContainer']).hide();
        }
        if (self.model.toUser === '') {
            $(self.refs['ToUserContainer']).hide();
        }

        if (self.model.readonly) {
            $(".fromContainer").hide();
            $(".toContainer").show();
            $(self.refs['closedDateContainer']).removeClass("hidden");
        } else {
            $(".fromContainer").show();
            $(".toContainer").hide();
        }
        if (self.model.fromSent && self.model.openedDate === "") {
            $(".showRecallButton").show();   // This displays the elements with class "showRecallButton"
            $(self.refs['btnRecall']).click(function () {
                if (window.EnableConfirmationMessage === "True") {
                    Common.showConfirmMsg(Resources.RecallConfirmation, function () {
                        recall(self.model.transferId, self.model.delegationId, self);
                    })
                }
                else {
                    recall(self.model.transferId, self.model.delegationId, self);
                }
            });
        }
        $(document).on('show.bs.modal', '.modal', function () {
            const baseZ = 1050;
            const currentZ = Math.max(...$('.modal:visible').map(function () {
                return parseInt($(this).css('z-index')) || 0;
            }).get(), 0);
            const newZ = (currentZ === 0 ? baseZ : currentZ) + 10;
            $(this).css('z-index', newZ);
            setTimeout(() => {
                $('.modal-backdrop:not(.modal-stack)').css('z-index', newZ - 1).addClass('modal-stack');
            }, 0);
        });
        $("#" + self.model.ComponentId + "_btnEditAfterSign").click(function () {
            Common.showConfirmMsg(Resources.EditAfterSignConfirmation, function () {
                Common.ajaxPost('/DocumentLock/UnlockDocument',
                    {
                        'documentId': self.model.documentId, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                    },
                    function (result) {
                        swal.close();
                        if (result === 3) {
                           swal.close();
                            setTimeout(function () {
                                Common.alertMsg(Resources.FileInUse);
                            }, 300);
                            
                        }
                        else {
                            if (self.model.fromVip || (window.InboxMode == "InboxVIPView" || window.InboxMode === 'LocalVIPView')) {
                                $(".withBorders-o").addClass("waitingBackground");
                                $("#inboxDocumentDetailsContainer").empty();
                                $($("input[data-id='" + self.model.transferId + "']").parents("li")[0]).fadeOut().remove();
                            }
                            
                            TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                            TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                            TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);
                            TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                            TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                            TreeNode.refreshTreeNodeCounts(TreeNode.Closed);
                            TreeNode.refreshTreeNodeCounts(TreeNode.MyRequests);
                            TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
                            $("#" + self.model.actionsComponentId + "_btnClose").click();
                        }

                    }, function () {
                        swal.close();
                    }, false);
            });
        });

        if (!self.model.readonly || self.model.fromSent) {

            let isBroadcastInternal = false;
            var receivingEntityObj = [];
            var sendingEntityObj = [];
            var currentCategoryModel = new CategoryModel().findFullById(self.model.categoryId);
            if (typeof currentCategoryModel !== 'undefined' && currentCategoryModel !== "" && currentCategoryModel !== null) {
                if (currentCategoryModel.basicAttribute !== "" && currentCategoryModel.basicAttribute !== null) {
                    let basicAttributes = JSON.parse(currentCategoryModel.basicAttribute);
                    if (basicAttributes.length > 0) {
                        receivingEntityObj = $.grep(basicAttributes, function (e) {
                            return e.Name === "ReceivingEntity";
                        });
                        sendingEntityObj = $.grep(basicAttributes, function (e) {
                            return e.Name === "SendingEntity";
                        });
                        if (receivingEntityObj[0].Type === "internal" && sendingEntityObj[0].Type === "internal") {
                            $(self.refs['sendingEntityContainer']).hide();
                            $(self.refs['receivingEntityContainer']).hide();
                        }
                        if (receivingEntityObj[0].BroadcastReceivingEntity && receivingEntityObj[0].Type === "internal") {
                            $('#' + self.model.ComponentId + '_btnReplyToStructure').remove();
                            $('#' + self.model.ComponentId + '_btnDismissCarbonCopy').remove();
                            $('#' + self.model.ComponentId + '_btnReplyToUser').remove();
                            $('#' + self.model.ComponentId + '_btnMyTransfer').remove();
                            $('#dropupReplyContainer').remove();
                            if (this.model.actionName != undefined) {
                                actionArray = this.model.actionName.split("_");

                                if ((actionArray.includes("Transfer.Send"))) {
                                    $('#' + self.model.ComponentId + '_btnSend').show();
                                }

                            }
                            // $(self.refs['btnSend']).show();
                        } else if (receivingEntityObj[0].BroadcastReceivingEntity) {
                            $('#' + self.model.ComponentId + '_btnDismissCarbonCopy').remove();
                            $('#dropupReplyContainer').remove();
                        }
                        if (receivingEntityObj[0].Type === "external" && self.model.categoryId == 2) {

                            let getTemplateParams = {
                                "id": self.model.categoryId
                            }
                            Common.ajaxPost('/DeliveryNote/GetTemplate', getTemplateParams, function (data) {

                                if (data != null) {
                                    $('#' + self.model.ComponentId + '_btnPrint').removeClass("hidden");
                                    $('#' + self.model.ComponentId + '_btnPrint').click(function () {
                                        printDeliveryNote(self, receivingEntityObj[0].Type);
                                    });
                                }
                            }, function () { }, false);

                        }

                    }
                }
            }
            if (receivingEntityObj[0].BroadcastReceivingEntity && receivingEntityObj[0].Type === "internal") {
                isBroadcastInternal = true;
            }
            $(document).on('click', '#signatureTemplatesClose', function () {
                $('#modalSignatureTemplates').modal("hide");
            });

            $('#' + self.model.ComponentId + '_btnReplyToUser').click(function () {
                ClickSignAndReplyToUser();
            });

            
            $('#' + self.model.ComponentId + '_btnResend').click(function () {
                resend(self.model.resendData);
            });
            $('#' + self.model.ComponentId + '_btnEditAfterExport').click(function () {
                EditAfterExport(self.model.documentId);
            });
            $('#' + self.model.ComponentId + '_btnReplyToStructure').click(function () {
                try {
                    $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", true);
                    $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", true);
                    $('#' + self.model.ComponentId + '_btnMyTransfer').attr("disabled", true);
                    $('#' + self.model.ComponentId + '_btnReplyToStructure').button('loading');
                    sendToReceivingEntity(self.model.replyToEntity, self.model.delegationId, TransferType.ReplyToStructure, self.model.fromStructure, self, null, false);
                } catch (e) {
                    $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnComplete').button('reset');
                    $('#' + self.model.ComponentId + '_btnCompleteAndArchive').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnCompleteAndArchive').button('reset');
                    $('#' + self.model.ComponentId + '_btnReplyToStructure').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnReplyToStructure').button('reset');
                    $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnReplyToUser').button('reset');
                    $('#' + self.model.ComponentId + '_btnMyTransfer').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnMyTransfer').button('reset');
                }
            });
            var gLocked = false;


            $('#' + self.model.ComponentId + '_btnMyTransfer').click(function () {
                clickMyTransfer();

            });

         
            $('#' + self.model.ComponentId + '_btnSend').click(function () {

                try {
                    Common.mask(document.body, "body-mask");
                    $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", true);
                    $('#' + self.model.ComponentId + '_btnCompleteAndArchive').attr("disabled", true);
                    $('#' + self.model.ComponentId + '_btnSend').attr("disabled", true);
                    if (referenceNumberGeneratorType == 3) {
                        sendToReceivingEntity(self.model.replyToEntity, self.model.delegationId, TransferType.Send, self.model.fromUser, self);
                    }
                    else {
                        sendToReceivingEntity(self.model.replyToEntity, self.model.delegationId, self.model.isInternalBroadcast ? TransferType.BroadcastSend : TransferType.Send, self.model.fromUser, self, null, false, actionArray);
                    }
                } catch (e) {
                    $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnComplete').button('reset');
                    $('#' + self.model.ComponentId + '_btnCompleteAndArchive').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnCompleteAndArchive').button('reset');
                    $('#' + self.model.ComponentId + '_btnSend').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnSend').button('reset');
                }
            });

            $('#' + self.model.ComponentId + '_btnUnlock').click(function () {
                Common.mask(document.body, "body-mask");
                unlock(self.model.transferId, self.model.delegationId, self.model.nodeId);
            });

            $('#' + self.model.ComponentId + '_btnReplyMeetingResolution').click(function () {

                var id = window.MeetingResolutionId;
                ;
                var params = { categoryId: id };
                var templateId = null;
                Common.ajaxGet('/Template/ListTemplatesByCategoryId', params, function (data) {

                    if (data.length > 0 && data[0].children.length > 0) {
                        templateId = data[0].children[0].id
                    }
                    createMeetingResolutionDocument(id, templateId, self.model.documentId);
                });
            });

            $("." + self.model.ComponentId +"_reply-item").on("click", function () {

                var categoryId = parseInt(this.id);
                var categoryName = $("#" + this.id + "").text();

                Common.ajaxGet('/Document/CheckDocumentAttachmentLocked/', { "documentId": self.model.documentId }, function (retval) {
                    if (retval) {
                        Common.alertMsg(Resources.FileInUse);
                    }
                    else {
                        let hasAttachments = self.model.hasAttachments;
                        Common.ajaxGet('/Document/CheckDocumentHasAttachment/', { "documentId": self.model.documentId }, function (value) {
                            hasAttachments = value;
                            if (hasAttachments) {

                                var model = new AttachmentsReplyByCategory.AttachmentsReplyByCategory();
                                var wrapper = $(".modal-window");
                                model.categoryId = categoryId;
                                model.categoryName = categoryName;
                                model.documentId = self.model.documentId;
                                model.transferId = self.model.transferId;
                                var view = new AttachmentsReplyByCategory.AttachmentsReplyByCategoryView(wrapper, model);
                                view.render();
                                $('.modalAttachmentsReplyByCategory').modal('show');
                                $(".modalAttachmentsReplyByCategory").off("hidden.bs.modal");
                                $(".modalAttachmentsReplyByCategory").off("shown.bs.modal");
                                $('.modalAttachmentsReplyByCategory').on('hidden.bs.modal', function () {
                                    $(".modalAttachmentsReplyByCategory").remove();
                                    swal.close();
                                });
                            }
                            else {
                                var params = { categoryId: categoryId };
                                var tempflag = false;
                                Common.ajaxGet('/Template/ListTemplatesByCategoryId', params, function (tempdata) {

                                    for (var i = 0; i < tempdata.length; i++) {
                                        if (tempdata[i].children.length > 0) {
                                            tempflag = true;
                                        }
                                    }

                                    if (tempflag) {
                                        var templateModel = new CreateByTemplate.CreateByTemplate();
                                        var wrapper = $(".modal-window");
                                        templateModel.categoryId = categoryId;
                                        templateModel.categoryName = categoryName;
                                        templateModel.isModal = true;
                                        templateModel.prevDocId = self.model.documentId;
                                        templateModel.attachmentIds = [];
                                        var view = new CreateByTemplate.CreateByTemplateView(wrapper, templateModel);
                                        view.render();
                                        $('.modalByTemplate').modal('show');
                                        $(".modalByTemplate").off("hidden.bs.modal");
                                        $(".modalByTemplate").off("shown.bs.modal");
                                        $('.modalByTemplate').on('hidden.bs.modal', function () {
                                            $(".modalByTemplate").remove();
                                            swal.close();
                                        });
                                    }
                                    else {
                                        CreateByTemplate.createDocumentforReplyByCategory(categoryId, null, self.model.documentId, [], self)

                                    }
                                });
                            }
                        })

                       

                    }
                })
            });
        }
        $('#' + self.model.ComponentId + '_btnTransferExport').click(function () {
            try {
                $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", true);
                $('#' + self.model.ComponentId + '_btnCompleteAndArchive').attr("disabled", true);
                $('#' + self.model.ComponentId + '_btnSend').attr("disabled", true);
                var receivingEntitiestosend = ($("[ref='cmbCustomAttributeReceiver']").length > 0 ? $("[ref='cmbCustomAttributeReceiver']").select2("data") : self.model.receivingEntities.map(x => x.id));
                sendToReceivingEntity(receivingEntitiestosend, self.model.delegationId, self.model.isInternalBroadcast ? TransferType.BroadcastSend : TransferType.Send, self.model.fromUser, self, null, false, actionArray);

            } catch (e) {
                $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnComplete').button('reset');
                $('#' + self.model.ComponentId + '_btnCompleteAndArchive').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnCompleteAndArchive').button('reset');
                $('#' + self.model.ComponentId + '_btnSend').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnSend').button('reset');
            }
        });
        if (self.model.isCCed) {
            $('#' + self.model.ComponentId + '_btnDismissCarbonCopy').click(function () {

                Common.showConfirmMsg(Resources.DismissCarbonCopyOneTsfConfirmation, function () {
                    Common.ajaxPost('/Transfer/DismissCarbonCopy',
                        {
                            'ids': self.model.transferId, 'delegationId': self.model.delegationId, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                        },
                        function (result) {
                            if (result[0]) {
                                if (result[0].updated) {
                                    tryCloseModal(self.model.actionsComponentId);
                                    Common.showScreenSuccessMsg();
                                    $("#gridContainerDiv").show();
                                    $("div [ref=" + self.model.parentComponentId + "]").remove();
                                    TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                                    TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);
                                    TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                                    TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                                    if (!gFromVip) {
                                        GridCommon.RefreshCurrentPage("grdInboxItems", true);
                                        if ($('li .active').attr("data-id") == undefined) {
                                            window.location.href = "/";
                                        }
                                        else {
                                            window.location.reload();
                                        }
                                    } else {
                                        $(".withBorders-o").addClass("waitingBackground");
                                        $("#inboxDocumentDetailsContainer").empty();
                                        $($("input[data-id='" + self.model.transferId + "']").parents("li")[0]).fadeOut().remove();
                                    }
                                } else {
                                    Common.showScreenErrorMsg();
                                }
                            } else {
                                Common.showScreenErrorMsg();
                            }
                        }, function () { Common.showScreenErrorMsg(); }, false);
                });
            });
        }
        function openCompleteReasonModal(callback) {
            // The modal has a header having >> "modal-header" div contains the closing button icon that closes the modal
            // The modal also has a textarea for notes, and two buttons: "Submit" and "Cancel".
            const modal = $(`
                        <div data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-hidden="true" class="modal fade modalCompleteAction" id="CompleteActionModal" tabindex="-1">
                        <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                        <div class="modal-header" style="border-bottom:0px; padding-bottom:3px;">
                        <button type="button" ref="completeClose" id="completeActionModalClose" class="close" data-dismiss="modal">&times;</button>
                        <h4 ref="modalCompleteTitle" class="modal-title"></h4>
                        </div> 
                        <div class="modal-body" style="padding-top: 2px; ">
                        <form ref="formPost" method="post" data-parsley-validate="" novalidate="">
                        <div class="row">
                        <div class="col-md-12" ref="completeReasonContainer">
                        <label class="control-label field-required" style="font-size: medium; ">${Resources.CompletionNotes}</label>
                        <textarea id="completeActionReason" rows="3" data-parsley-required="true"class="form-control" required></textarea>
                        <div class="invalid-feedback" style="display:none; color:red;">
                                                        ${Resources.RequiredField}
                        </div>
                        </div>
                        </div>
                        </form>
                        </div>
                        <div class="modal-footer" style="border-top:0px;">
                        <button type="button" class="btn btn-primary" id="submitCompleteActionReason">${Resources.Complete}</button>
                        <button type="button" class="btn btn-secondary" id="cancelCompleteAction" data-bs-dismiss="modal">${Resources.Cancel}</button>
                        </div>
                        </div>
                        </div>
                        </div>
                        `);
            // UI Production: The modal is dynamically created using a jQuery template literal and appended to the body.

            $('body').append(modal); // This body is the default screen html body, so we basically append this modal template into the screen content

            modal.modal('show');   //  displays the modal
            modal.find('#submitCompleteActionReason').on('click', function () {
                const textarea = modal.find('#completeActionReason');
                const reason = textarea.val().trim();// Removes any leading or trailing whitespace from the complete reason 
                const errorMsg = textarea.siblings('.invalid-feedback');

                if (!reason) {
                    textarea.addClass('is-invalid');  // Adds red border
                    errorMsg.show();  // Shows the error message
                    modal.find('form').addClass('was-validated'); // Ensure Bootstrap applies styles
                    return;
                }
                else {
                    textarea.removeClass('is-invalid'); // Removes red border
                    errorMsg.hide(); // Hides error message
                    $("#completeActionModalClose").trigger("click");  //  triggering the close button to close the entire modal with its shadow
                    callback(reason);
                    // Close the modal after the callback is executed
                }
            });
            // Remove validation styles on input change
            modal.find('#completeActionReason').on('input', function () {
                $(this).removeClass('is-invalid');
                $(this).siblings('.invalid-feedback').hide();
            });
            modal.find('#cancelCompleteAction').on('click', function () {
                modal.find('#completeActionReason').val('');
                modal.modal('hide');
            });

            modal.on('hidden.bs.modal', function () {
                modal.remove();
            });
        }

        function completeTransferAction(callback) {

            openCompleteReasonModal(function (reason) {

                if (!((self.model.fromVip) || (window.InboxMode == "InboxVIPView" || window.InboxMode === 'LocalVIPView'))) {
                    Common.mask(document.getElementById('grdInboxItems'), "inboxListContainer-mask");
                } else {
                    Common.mask(document.getElementById('inboxListContainer'), "inboxListContainer-mask");
                }
                Common.ajaxPost('/Transfer/Complete',
                    {
                        'ids': self.model.transferId, 'delegationId': self.model.delegationId,
                        'completeReasonNote': reason,
                        'fromStructure': self.model.fromStructureInbox,
                        '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                    },
                    function (result) {
                        Common.unmask("inboxListContainer-mask");
                        if (result[0]) {
                            if (result[0].updated && result[0].uncompletedDocumentReferenceNumber && !result[0].message) {
                                var msg = Resources.TransferWasCompletedNoOriginalMail;
                                if (result[0].documentAttachmentIdHasValue) {
                                    msg = Resources.TransferWasCompletedNotLastOpenTransfer;
                                }
                                setTimeout(function () {
                                    Common.alertConfirmation(msg, function () {

                                        $("#gridContainerDiv").show();

                                        $("div [ref=" + self.model.parentComponentId + "]").remove();
                                        TreeNode.refreshTreeNodeCounts(TreeNode.Inbox); TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox); TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                                        TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                                        TreeNode.refreshTreeNodeCounts(TreeNode.Closed);
                                        if (!gFromVip) {
                                            GridCommon.RefreshCurrentPage("grdInboxItems", true);
                                            $("#" + self.model.actionsComponentId + "_btnClose").click();
                                        } else {
                                            $(".withBorders-o").addClass("waitingBackground");
                                            $("#inboxDocumentDetailsContainer").empty();
                                            $($("input[data-id='" + self.model.transferId + "']").parents("li")[0]).fadeOut().remove();

                                        }
                                    });
                                }, 300);

                            } else if (result[0].updated) {
                                Common.showScreenSuccessMsg();
                                $("#gridContainerDiv").show();
                                $("div [ref=" + self.model.actionsComponentId + "]").remove();
                                $('.modal-backdrop').hide();
                                TreeNode.refreshTreeNodeCounts(TreeNode.Inbox); TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox); TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                                TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                                TreeNode.refreshTreeNodeCounts(TreeNode.Closed);
                                TreeNode.refreshTreeNodeCounts(TreeNode.MyRequests);
                                TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
                                if (!(gFromVip || (window.InboxMode == "InboxVIPView" || window.InboxMode === 'LocalVIPView'))) {
                                    GridCommon.RefreshCurrentPage("grdInboxItems", true);
                                    $("#" + self.model.actionsComponentId + "_btnClose").click();
                                } else {
                                    $(".withBorders-o").addClass("waitingBackground");
                                    $("#inboxDocumentDetailsContainer").empty();
                                    $($("input[data-id='" + self.model.transferId + "']").parents("li")[0]).fadeOut().remove();
                                }
                            } else {
                                if (result[0].message != null) {
                                    setTimeout(function () {
                                        Common.alertMsg(result[0].message);
                                    }, 300);
                                    //$('.close').trigger('click');

                                }
                                //} else if (result[0].message === "OriginalDocumentLockedByUser") {
                                //    setTimeout(function () {
                                //        Common.alertMsg(Resources.OriginalDocumentLockedByUser);
                                //    }, 300);
                                //    Common.unmask("inboxListContainer-mask");
                                //} else if (result[0].message === "TransferHasDifferentOwnerOrIsCarbonCopy") {
                                //    setTimeout(function () {
                                //        Common.alertMsg(Resources.TransferHasDifferentOwnerOrIsCarbonCopy);
                                //    }, 300);
                                //    Common.unmask("inboxListContainer-mask");
                                //} else if (result[0].message === "NoReferenceNumber") {
                                //    setTimeout(function () {
                                //        Common.alertMsg(Resources.NoReferenceNumber);
                                //    }, 300);
                                //    Common.unmask("inboxListContainer-mask");
                                //} else {
                                //    setTimeout(function () {
                                //        Common.alertMsg(Resources.CorrespondenceNotCompleteNoOriginalMail);
                                //    }, 300);
                                //    Common.unmask("inboxListContainer-mask");
                                //}
                            }
                        } else {
                            Common.showScreenErrorMsg();
                        }
                    }, function () {
                        Common.unmask("inboxListContainer-mask");
                        Common.showScreenErrorMsg();

                    }, false);
            })
        };



        $('#' + self.model.ComponentId + '_btnAddCCAndSend').click(function () {

            //Load dialog box with the structures that have not been yet selected
            //Let the user choose them, once he chooses, we have to send
            //Update the attributes for cc from the server side
            //Close dialogue

            const params = { id: self.model.transferId };
            if (self.model.delegationId != null) {
                params.delegationId = self.model.delegationId;
            }

            Common.ajaxGet('/Document/GetDocumentByTransferId', params, function (data) {
                let modalWrapper = $(".modal-window");
                let model = new AddCCAndSendModel();
                model.basicAttributes = data.basicAttributes !== null && data.basicAttributes !== "" ? JSON.parse(data.basicAttributes) : [];
                model.excludedStructures = data.carbonCopies.map(e => e.text);
                model.documentId = self.model.documentId;
                model.transferId = self.model.transferId;
                model.structureId = self.model.fromStructureId;
                model.title = Resources.AddCCAndSend;

                const viewComponent = new AddCCAndSendView(modalWrapper, model);
                viewComponent.render();

                $(viewComponent.refs["btnSend"]).click(function () {
                    $(viewComponent.refs["btnSend"]).button("loading");

                    const documentsViewModel = $(viewComponent.refs[`${model.ComponentId}_receipientSelect`]).select2('data').map(e => ({
                        DocumentId: model.documentId,
                        DocumentPrivacyId: 1,
                        FromStructureId: model.structureId,
                        IsStructure: true,
                        ParentTransferId: model.transferId,
                        PrivateInstruction: false,
                        cced: true,
                        dueDate: "",
                        followUp: false,
                        instruction: "",
                        name: e.text,
                        priorityId: 1,
                        purposeId: 10,
                        toStructureId: e.id,
                        toUserId: null
                    }));
                    if (documentsViewModel.length === 0) {
                        Common.alertMsg(Resources.AtLeastOneRecipient);
                        $(viewComponent.refs["btnSend"]).button("reset");
                        return;
                    }
                    Common.ajaxPostJSON('Transfer/AddCCAndSend', JSON.stringify(documentsViewModel), function (response) {
                        location.reload();
                    }, function () {
                        Common.showScreenErrorMsg();
                        $(viewComponent.refs["btnSend"]).button("reset");
                    });

                })


                let modalClass = viewComponent.refs['modalSendCC'];
                $(modalClass).modal('show');
                $(modalClass).off("hidden.bs.modal");
                $(modalClass).off("shown.bs.modal");
                $(modalClass).on('hidden.bs.modal', function () {
                    $(modalClass).parent().remove();
                    swal.close();
                });
            }, null, true);
        });

        $('#' + self.model.ComponentId + '_btnSendCopies').click(function () {
            const params = { id: self.model.transferId };
            if (self.model.delegationId != null) {
                params.delegationId = self.model.delegationId;
            }

            Common.ajaxGet('/Document/GetDocumentByTransferId', params, function (data) {
                let modalWrapper = $(".modal-window");
                let model = new AddCCAndSendModel();
                model.basicAttributes = data.basicAttributes !== null && data.basicAttributes !== "" ? JSON.parse(data.basicAttributes) : [];
                model.excludedStructures = [];
                model.selectedStructures = data.carbonCopies;
                model.documentId = self.model.documentId;
                model.transferId = self.model.transferId;
                model.structureId = self.model.fromStructureId;
                model.title = Resources.SendCopies;

                const viewComponent = new AddCCAndSendView(modalWrapper, model);
                viewComponent.render();

                $(viewComponent.refs["btnSend"]).click(function () {
                    $(viewComponent.refs["btnSend"]).button("loading");

                    const documentsViewModel = $(viewComponent.refs[`${model.ComponentId}_receipientSelect`]).select2('data').map(e => ({
                        DocumentId: model.documentId,
                        DocumentPrivacyId: 1,
                        FromStructureId: model.structureId,
                        IsStructure: true,
                        ParentTransferId: model.transferId,
                        PrivateInstruction: false,
                        cced: true,
                        dueDate: "",
                        followUp: false,
                        instruction: "",
                        name: e.text,
                        priorityId: 1,
                        purposeId: 10,
                        toStructureId: e.id,
                        toUserId: null
                    }));
                    if (documentsViewModel.length === 0) {
                        Common.alertMsg(Resources.AtLeastOneRecipient);
                        $(viewComponent.refs["btnSend"]).button("reset");
                        return;
                    }
                    Common.ajaxPostJSON('Transfer/SendCopies', JSON.stringify(documentsViewModel), function (response) {
                        location.reload();
                    }, function () {
                        Common.showScreenErrorMsg();
                        $(viewComponent.refs["btnSend"]).button("reset");
                    });

                });


                let modalClass = viewComponent.refs['modalSendCC'];
                $(modalClass).modal('show');
                $(modalClass).off("hidden.bs.modal");
                $(modalClass).off("shown.bs.modal");
                $(modalClass).on('hidden.bs.modal', function () {
                    $(modalClass).parent().remove();
                    swal.close();
                });
            }, null, true);
        });


        if (!self.model.readonly || self.model.isInternalBroadcast) {
            $('#' + self.model.ComponentId + '_btnComplete').click(function () {

                if (window.EnableConfirmationMessage === "True") {
                    Common.showConfirmMsg(Resources.CompleteOneTsfConfirmation, function () {
                        completeTransferAction();
                    })
                }
                else {
                    completeTransferAction();
                }
            });
        }
        if (self.model.workflowStepId != null && self.model.workflowStepId != undefined) {
            $('#' + self.model.ComponentId + '_btnSend').addClass('hidden');
            $('#' + self.model.ComponentId + '_btnPrint').addClass('hidden');
            $('#' + self.model.ComponentId + '_btnComplete').addClass('hidden');
            $('#' + self.model.ComponentId + '_btnCompleteAndArchive').addClass('hidden');
            $('#' + self.model.ComponentId + '_btnReplyToStructure').addClass('hidden');
            $('#' + self.model.ComponentId + '_btnReplyToUser').addClass('hidden');
            $('#' + self.model.ComponentId + '_btnGroupDropup').addClass('hidden');
            $('#' + self.model.ComponentId + '_btnMyTransfer').addClass('hidden');
            $(self.refs['dropupReplyContainer']).addClass('hidden');
            $('#' + self.model.ComponentId + '_btnSignTemplate').addClass('hidden');
            $('#' + self.model.ComponentId + '_btnSignAndSend').addClass('hidden');
            $('#' + self.model.ComponentId + '_btnSignAndTransfer').addClass('hidden');
            $('#' + self.model.ComponentId + '_btnSignAndReplyToStructure').addClass('hidden');
            $('#' + self.model.ComponentId + '_btnSignAndReplyToUser').addClass('hidden');
            $('#' + self.model.ComponentId + '_btnRejectTemplate').addClass('hidden');
            $('#' + self.model.ComponentId + '_btnStartWorkflow').addClass('hidden');

            $('#' + self.model.ComponentId + '_btnViewFlow').removeClass('hidden');
            if (self.model.isWorkflowReturned) {
                $('#' + self.model.ComponentId + '_btnReinitiate').removeClass('hidden');
                $('#' + self.model.ComponentId + '_btnReturn').addClass('hidden');
                $('#' + self.model.ComponentId + '_btnResume').removeClass('hidden');
                $('#' + self.model.ComponentId + '_btnProceed').addClass('hidden');
            }
            else {
                $('#' + self.model.ComponentId + '_btnReturn').removeClass('hidden');
                $('#' + self.model.ComponentId + '_btnReinitiate').addClass('hidden');
                $('#' + self.model.ComponentId + '_btnResume').addClass('hidden');
                $('#' + self.model.ComponentId + '_btnProceed').removeClass('hidden');

            }
        }
        $(self.refs['btnShowInstructions']).click(function () {
            showHideInstruction(self.model.instruction);
        });

        let model = new SignatureTemplates();
        let wrapper = $(".modal-window");
        wrapper.empty();
        let view = new SignatureTemplatesView(wrapper, model);
        view.render();


        $('#' + self.model.ComponentId + '_btnSignTemplate').click( async function () {
            //fromSignOnly = true;
            const certMessage = await CheckUserCertificate();
            if (certMessage) {
                Common.unmask("body-mask");
                Common.alertMsg(certMessage);
                $(self.refs['btnSignTemplate']).attr("disabled", false);
                $(self.refs['btnGroupDropup']).attr("disabled", false);
                $(self.refs['btnSignTemplate']).button('reset');
                $(self.refs['btnGroupDropup']).button('reset');
                $('#btnGroupDropup').prop('disabled', false);
                return;
            }
            if (fromSignAndTransfer) {
                signType = 2;
                fromSignAndTransfer = false;
            }
            else if (fromSignAndExport) {
                signType = 7;
                //fromSignAndExport = false;
            }
            else if (fromSignAndReply) {
                signType = 4;
                fromSignAndReply = false;
            }
            else
                signType = 0;
            $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", true);
            $('#' + self.model.ComponentId + '_btnCompleteAndArchive').attr("disabled", true);
            $('#' + self.model.ComponentId + '_btnReplyToStructure').attr("disabled", true);
            $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", true);
            $('#' + self.model.ComponentId + '_btnMyTransfer').attr("disabled", true);
            $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", true);
            $('#' + self.model.ComponentId + '_btnGroupDropup').attr("disabled", true);
            $('#' + self.model.ComponentId + '_btnStartWorkflow').attr("disabled", true);
            Common.mask(document.body, "body-mask");
            if (templateId != 0) {
                //$('#btnSubmitSign').click();
                signDocument(self, signType, templateId);
            }
            else {

                $.ajax({
                    method: "GET",
                    url: window.DSURL + "/api/signature/template",
                    headers: {
                        "Authorization": "Bearer " + IdentityAccessToken
                    },
                    success: function (res) {
                        $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", false);
                        $('#' + self.model.ComponentId + '_btnComplete').button('reset');
                        $('#' + self.model.ComponentId + '_btnCompleteAndArchive').attr("disabled", false);
                        $('#' + self.model.ComponentId + '_btnCompleteAndArchive').button('reset');
                        $('#' + self.model.ComponentId + '_btnReplyToStructure').attr("disabled", false);
                        $('#' + self.model.ComponentId + '_btnReplyToStructure').button('reset');
                        $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", false);
                        $('#' + self.model.ComponentId + '_btnReplyToUser').button('reset');
                        $('#' + self.model.ComponentId + '_btnMyTransfer').attr("disabled", false);
                        $('#' + self.model.ComponentId + '_btnMyTransfer').button('reset');
                        $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", false);
                        $('#' + self.model.ComponentId + '_btnSignTemplate').button('reset');
                        $('#' + self.model.ComponentId + '_btnGroupDropup').attr("disabled", false);
                        $('#' + self.model.ComponentId + '_btnGroupDropup').button('reset');
                        $('#' + self.model.ComponentId + '_btnStartWorkflow').attr("disabled", false);
                        $('#' + self.model.ComponentId + '_btnStartWorkflow').button('reset');
                        Common.unmask("body-mask");
                        if (res.length == 0) {
                            Common.alertMsg(Resources.NoTemplatesFound);

                        }
                        else if (res.length == 1) {
                            signDocument(self, signType, res[0].id);
                        }
                        else {
                            $('#modalSignatureTemplates').modal('show');
                            $("#modalSignatureTemplates").off("hidden.bs.modal");
                            $("#modalSignatureTemplates").off("shown.bs.modal");
                            $("#tblSignature tbody").html("");

                            $(self.refs['modalCTSPurpose']).on('hidden.bs.modal', function () {
                                swal.close();
                            });

                            for (let j = 0; j < res.length / 2; j++) {
                                $("#tblSignature tbody").append('<tr id="tr' + j + '"></tr>');
                            }
                            var count = 0, k = 0;

                            $.each(res, function (i, val) {
                                $("#tr" + k).append('<td style="width: 250px;"><div class="form-check"><input class="form-check-input" style="margin:6px;" type="radio" name="rdbSignature" id="rdb' + val.id + '" value="' + val.id + '"><label for="rdb' + val.id + '"><img for="rdb' + val.id + '" id="img' + val.id + '"src=""  class="rounded mx-auto d-block" alt="' + val.name + '" width="75%"></label></div></td>');

                                $("#img" + val.id).attr("src", window.DSURL + "/api/signature/template/" + val.id + "/image?token=" + IdentityAccessToken);

                                if ((count + 1) % 2 == 0)
                                    k++;

                                count++;
                            });
                        }
                        //Common.unmask("body-mask");
                    },
                    error: function (res) {
                        $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", false);
                        $('#' + self.model.ComponentId + '_btnComplete').button('reset');
                        $('#' + self.model.ComponentId + '_btnReplyToStructure').attr("disabled", false);
                        $('#' + self.model.ComponentId + '_btnReplyToStructure').button('reset');
                        $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", false);
                        $('#' + self.model.ComponentId + '_btnReplyToUser').button('reset');
                        $('#' + self.model.ComponentId + '_btnMyTransfer').attr("disabled", false);
                        $('#' + self.model.ComponentId + '_btnMyTransfer').button('reset');
                        $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", false);
                        $('#' + self.model.ComponentId + '_btnSignTemplate').button('reset');
                        $('#' + self.model.ComponentId + '_btnGroupDropup').attr("disabled", false);
                        $('#' + self.model.ComponentId + '_btnGroupDropup').button('reset');
                        Common.showScreenErrorMsg();
                        Common.unmask("body-mask");
                    }
                });

            }
        });

        $('#' + self.model.ComponentId + '_btnSignAndSend').click( async function () {
            Common.mask(document.body, "body-mask");
            const certMessage = await CheckUserCertificate();
            if (certMessage) {
                Common.unmask("body-mask");
                Common.alertMsg(certMessage);
                $(self.refs['btnSignTemplate']).attr("disabled", false);
                $(self.refs['btnGroupDropup']).attr("disabled", false);
                $(self.refs['btnSignTemplate']).button('reset');
                $(self.refs['btnGroupDropup']).button('reset');
                $('#btnGroupDropup').prop('disabled', false);
                return;
            }
            if (templateId != 0) {

                Common.ajaxGet('/Document/CheckDocumentAttachmentLocked/', { "documentId": self.model.documentId }, function (retval) {
                    if (retval) {
                        Common.unmask("body-mask");
                        Common.alertMsg(Resources.FileInUse);

                    } else {
                        signType = 1;
                        $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnReplyToStructure').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnMyTransfer').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnGroupDropup').attr("disabled", true);
                        signDocument(self, signType, templateId);

                    }
                })
            } else {
                Common.ajaxGet('/Document/CheckDocumentAttachmentLocked/', { "documentId": self.model.documentId }, function (retval) {
                    if (retval) {
                        Common.unmask("body-mask");
                        Common.alertMsg(Resources.FileInUse);
                    } else {
                        signType = 1;
                        $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnReplyToStructure').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnMyTransfer').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnGroupDropup').attr("disabled", true);


                        $.ajax({
                            method: "GET",
                            url: window.DSURL + "/api/signature/template",
                            headers: {
                                "Authorization": "Bearer " + IdentityAccessToken
                            },
                            success: function (res) {
                                $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnComplete').button('reset');
                                $('#' + self.model.ComponentId + '_btnReplyToStructure').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnReplyToStructure').button('reset');
                                $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnReplyToUser').button('reset');
                                $('#' + self.model.ComponentId + '_btnMyTransfer').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnMyTransfer').button('reset');
                                $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnSignTemplate').button('reset');
                                $('#' + self.model.ComponentId + '_btnGroupDropup').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnGroupDropup').button('reset');
                                if (res.length == 0) {
                                    Common.alertMsg(Resources.NoTemplatesFound);
                                    Common.unmask("body-mask");
                                }
                                else if (res.length == 1) {

                                    sendToReceivingEntity(self.model.replyToEntity, self.model.delegationId, TransferType.Send, self.model.fromUser, self, res[0].id, true);

                                }
                                else {
                                    $('#modalSignatureTemplates').modal('show');
                                    $("#modalSignatureTemplates").off("hidden.bs.modal");
                                    $("#modalSignatureTemplates").off("shown.bs.modal");
                                    $("#tblSignature tbody").html("");

                                    $(self.refs['modalCTSPurpose']).on('hidden.bs.modal', function () {
                                        swal.close();
                                    });

                                    for (let j = 0; j < res.length / 2; j++) {
                                        $("#tblSignature tbody").append('<tr id="tr' + j + '"></tr>');
                                    }
                                    var count = 0, k = 0;

                                    $.each(res, function (i, val) {
                                        $("#tr" + k).append('<td style="width: 250px;"><div class="form-check"><input class="form-check-input" type="radio" style="margin:6px;" name="rdbSignature" id="rdb' + val.id + '" value="' + val.id + '"><label for="rdb' + val.id + '"><img for="rdb' + val.id + '" id="img' + val.id + '"src=""  class="rounded mx-auto d-block" alt="' + val.name + '" width="75%"></label></div></td>');

                                        $("#img" + val.id).attr("src", window.DSURL + "/api/signature/template/" + val.id + "/image?token=" + IdentityAccessToken);

                                        if ((count + 1) % 2 == 0)
                                            k++;

                                        count++;
                                    });
                                    Common.unmask("body-mask");
                                }

                            },
                            error: function (res) {
                                $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnComplete').button('reset');
                                $('#' + self.model.ComponentId + '_ReplyToStructure').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_ReplyToStructure').button('reset');
                                $('#' + self.model.ComponentId + '_ReplyToUser').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_ReplyToUser').button('reset');
                                $('#' + self.model.ComponentId + '_MyTransfer').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnMyTransfer').button('reset');
                                $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnSignTemplate').button('reset');
                                $('#' + self.model.ComponentId + '_btnGroupDropup').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnGroupDropup').button('reset');
                                Common.showScreenErrorMsg();
                                Common.unmask("body-mask");
                            }

                        });

                    }
                });
            }


        });

        $('#' + self.model.ComponentId + '_btnSignAndTransfer').click( async function () {
            Common.mask(document.body, "body-mask");
            const certMessage = await CheckUserCertificate();
            if (certMessage) {
                Common.unmask("body-mask");
                Common.alertMsg(certMessage);
                $(self.refs['btnSignTemplate']).attr("disabled", false);
                $(self.refs['btnGroupDropup']).attr("disabled", false);
                $(self.refs['btnSignTemplate']).button('reset');
                $(self.refs['btnGroupDropup']).button('reset');
                $('#btnGroupDropup').prop('disabled', false);
                return;
            }
            fromSignAndTransfer = true;
            if (templateId != 0) {
                Common.ajaxGet('/Document/CheckDocumentAttachmentLocked/', { "documentId": self.model.documentId }, function (retval) {
                    if (retval) {
                        Common.unmask("body-mask");
                        Common.alertMsg(Resources.FileInUse);

                    } else {
                        signType = 2;
                        $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnReplyToStructure').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnMyTransfer').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnGroupDropup').attr("disabled", true);
                        signDocument(self, signType, templateId);
                    }
                })
            }
            else {
                Common.mask(document.body, "body-mask");
                Common.ajaxGet('/Document/CheckDocumentAttachmentLocked/', { "documentId": self.model.documentId }, function (retval) {
                    if (retval) {
                        Common.unmask("body-mask");
                        Common.alertMsg(Resources.FileInUse);

                    } else {
                        signType = 2;
                        $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnReplyToStructure').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnMyTransfer').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnGroupDropup').attr("disabled", true);

                        $('#' + self.model.ComponentId + '_btnSignTemplate').click();

                    }
                })
            }

        });

        $('#' + self.model.ComponentId + '_btnSignAndExport').click( async function () {
            Common.mask(document.body, "body-mask");
            const certMessage = await CheckUserCertificate();
            if (certMessage) {
                Common.unmask("body-mask");
                Common.alertMsg(certMessage);
                $(self.refs['btnSignTemplate']).attr("disabled", false);
                $(self.refs['btnGroupDropup']).attr("disabled", false);
                $(self.refs['btnSignTemplate']).button('reset');
                $(self.refs['btnGroupDropup']).button('reset');
                $('#btnGroupDropup').prop('disabled', false);
                return;
            }
            fromSignAndExport = true;
            if (templateId != 0) {
                Common.ajaxGet('/Document/CheckDocumentAttachmentLocked/', { "documentId": self.model.documentId }, function (retval) {
                    if (retval) {
                        Common.unmask("body-mask");
                        Common.alertMsg(Resources.FileInUse);

                    } else {
                        signType = 7;
                        $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnReplyToStructure').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnMyTransfer').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnGroupDropup').attr("disabled", true);
                        signDocument(self, signType, templateId);
                    }
                })
            }
            else {
                Common.mask(document.body, "body-mask");
                Common.ajaxGet('/Document/CheckDocumentAttachmentLocked/', { "documentId": self.model.documentId }, function (retval) {
                    if (retval) {
                        Common.unmask("body-mask");
                        Common.alertMsg(Resources.FileInUse);

                    } else {
                        signType = 7;
                        $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnReplyToStructure').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnMyTransfer').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnGroupDropup').attr("disabled", true);

                        $('#' + self.model.ComponentId + '_btnSignTemplate').click();

                    }
                })
            }

        });
        $('#' + self.model.ComponentId + '_btnSignAndReplyToStructure').click( async function () {
            Common.mask(document.body, "body-mask");
            const certMessage = await CheckUserCertificate();
            if (certMessage) {
                Common.unmask("body-mask");
                Common.alertMsg(certMessage);
                $(self.refs['btnSignTemplate']).attr("disabled", false);
                $(self.refs['btnGroupDropup']).attr("disabled", false);
                $(self.refs['btnSignTemplate']).button('reset');
                $(self.refs['btnGroupDropup']).button('reset');
                $('#btnGroupDropup').prop('disabled', false);
                return;
            }
            if (templateId != 0) {

                Common.ajaxGet('/Document/CheckDocumentAttachmentLocked/', { "documentId": self.model.documentId }, function (retval) {
                    if (retval) {
                        Common.unmask("body-mask");
                        Common.alertMsg(Resources.FileInUse);
                    } else {
                        signType = 3;
                        $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnReplyToStructure').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnMyTransfer').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnGroupDropup').attr("disabled", true);
                        signDocument(self, signType, templateId);

                    }
                })
            }
            else {
                Common.ajaxGet('/Document/CheckDocumentAttachmentLocked/', { "documentId": self.model.documentId }, function (retval) {
                    if (retval) {
                        Common.unmask("body-mask");
                        Common.alertMsg(Resources.FileInUse);
                    } else {
                        signType = 3;
                        $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnReplyToStructure').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnMyTransfer').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnGroupDropup').attr("disabled", true);


                        $.ajax({
                            method: "GET",
                            url: window.DSURL + "/api/signature/template",
                            headers: {
                                "Authorization": "Bearer " + IdentityAccessToken
                            },
                            success: function (res) {
                                $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnComplete').button('reset');
                                $('#' + self.model.ComponentId + '_btnReplyToStructure').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnReplyToStructure').button('reset');
                                $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnReplyToUser').button('reset');
                                $('#' + self.model.ComponentId + '_btnMyTransfer').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnMyTransfer').button('reset');
                                $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnSignTemplate').button('reset');
                                $('#' + self.model.ComponentId + '_btnGroupDropup').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnGroupDropup').button('reset');
                                if (res.length == 0) {
                                    Common.alertMsg(Resources.NoTemplatesFound);
                                    Common.unmask("body-mask");
                                }
                                else if (res.length == 1) {

                                    sendToReceivingEntity(self.model.replyToEntity, self.model.delegationId, TransferType.ReplyToStructure, self.model.fromStructure, self, res[0].id, true);
                                    Common.unmask("body-mask");
                                }
                                else {

                                    $('#modalSignatureTemplates').modal('show');
                                    $("#modalSignatureTemplates").off("hidden.bs.modal");
                                    $("#modalSignatureTemplates").off("shown.bs.modal");
                                    $("#tblSignature tbody").html("");

                                    $(self.refs['modalCTSPurpose']).on('hidden.bs.modal', function () {
                                        swal.close();
                                    });

                                    for (let j = 0; j < res.length / 2; j++) {
                                        $("#tblSignature tbody").append('<tr id="tr' + j + '"></tr>');
                                    }
                                    var count = 0, k = 0;

                                    $.each(res, function (i, val) {
                                        $("#tr" + k).append('<td style="width: 250px;"><div class="form-check"><input class="form-check-input" type="radio" style="margin:6px;" name="rdbSignature" id="rdb' + val.id + '" value="' + val.id + '"><label for="rdb' + val.id + '"><img for="rdb' + val.id + '" id="img' + val.id + '"src=""  class="rounded mx-auto d-block" alt="' + val.name + '" width="75%"></label></div></td>');

                                        $("#img" + val.id).attr("src", window.DSURL + "/api/signature/template/" + val.id + "/image?token=" + IdentityAccessToken);

                                        if ((count + 1) % 2 == 0)
                                            k++;

                                        count++;
                                    });
                                }
                                Common.unmask("body-mask");
                            },
                            error: function (res) {
                                $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnComplete').button('reset');
                                $('#' + self.model.ComponentId + '_btnReplyToStructure').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnReplyToStructure').button('reset');
                                $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnReplyToUser').button('reset');
                                $('#' + self.model.ComponentId + '_btnMyTransfer').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnMyTransfer').button('reset');
                                $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnSignTemplate').button('reset');
                                $('#' + self.model.ComponentId + '_btnGroupDropup').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnGroupDropup').button('reset');
                                Common.showScreenErrorMsg();
                                Common.unmask("body-mask");
                            }
                        });
                    }

                });
            }
        });

        $('#' + self.model.ComponentId + '_btnSignAndReplyToUser').click( async function () {
            Common.mask(document.body, "body-mask");
            const certMessage = await CheckUserCertificate();
            if (certMessage) {
                Common.unmask("body-mask");
                Common.alertMsg(certMessage);
                $(self.refs['btnSignTemplate']).attr("disabled", false);
                $(self.refs['btnGroupDropup']).attr("disabled", false);
                $(self.refs['btnSignTemplate']).button('reset');
                $(self.refs['btnGroupDropup']).button('reset');
                $('#btnGroupDropup').prop('disabled', false);
                return;
            }
            fromSignAndReply = true;
            if (templateId != 0) {
                Common.ajaxGet('/Document/CheckDocumentAttachmentLocked/', { "documentId": self.model.documentId }, function (retval) {
                    //Common.unmask("body-mask");
                    if (retval) {
                        Common.unmask("body-mask");
                        Common.alertMsg(Resources.FileInUse);

                    } else {
                        signType = 4;
                        $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnReplyToStructure').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnMyTransfer').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnGroupDropup').attr("disabled", true);
                        signDocument(self, signType, templateId);

                    }
                })



            } else {
                Common.ajaxGet('/Document/CheckDocumentAttachmentLocked/', { "documentId": self.model.documentId }, function (retval) {
                    //Common.unmask("body-mask");
                    if (retval) {
                        Common.unmask("body-mask");
                        Common.alertMsg(Resources.FileInUse);

                    } else {
                        signType = 4;
                        $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnReplyToStructure').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnMyTransfer').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnGroupDropup').attr("disabled", true);

                        $('#' + self.model.ComponentId + '_btnSignTemplate').click();
                    }
                })
            }



        })

        $('#' + self.model.ComponentId + '_btnRejectTemplate').click(function () {

            $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", true);
            $('#' + self.model.ComponentId + '_btnReplyToStructure').attr("disabled", true);
            $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", true);
            $('#' + self.model.ComponentId + '_btnMyTransfer').attr("disabled", true);
            $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", true);
            $('#' + self.model.ComponentId + '_btnGroupDropup').attr("disabled", true);

            let parms = {
                'documentId': self.model.documentId,
                'transferId': self.model.transferId
            }


            Common.ajaxPost('Transfer/RejectDocument', parms, function (data) {
                $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnComplete').button('reset');
                $('#' + self.model.ComponentId + '_btnReplyToStructure').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnReplyToStructure').button('reset');
                $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnReplyToUser').button('reset');
                $('#' + self.model.ComponentId + '_btnMyTransfer').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnMyTransfer').button('reset');
                $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnSignTemplate').button('reset');
                $('#' + self.model.ComponentId + '_btnGroupDropup').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnGroupDropup').button('reset');
                if (data) {
                    Common.showScreenSuccessMsg();
                    sendToReceivingEntity(self.model.replyToEntity, self.model.delegationId, TransferType.SignAndSend, self.model.fromUser, self);
                }
                else {
                    Common.showScreenErrorMsg();
                }

            })

        })

        $(document).ready(function () {
            Common.unmask("body-mask");
        });
        $('#btnSubmitSign').click(function () {
            Common.unmask("body-mask");
            templateId = 0;
            if (fromSignOnly || fromSignAndTransfer) {
                fromSignOnly = false;
                fromSignAndTransfer = false;
                Common.mask(document.body, "body-mask");
            }
            const selectedSignature = $('input[name="rdbSignature"]:checked');
            templateId = selectedSignature.val();

            if (fromPeview) {
                getDocumentIdInOriginalMail(self.model.documentId, function (attachmentId) {
                    if (templateId == undefined || templateId == 0) {
                        Common.alertMsg(Resources.SignatureRequired);
                        Common.unmask("body-mask");
                        return;
                    }
                    validateAndPreviewAttachment(attachmentId, templateId);
                    fromPeview = false;
                    templateId = 0;// this is for resting so if I choose a different sign action to open the template and not take the value chosen from the preview
                    $("#modalSignatureTemplates").modal("hide");
                });
            }
            else {
                signDocument(self, signType, templateId);
                templateId = 0;// if not in the else it will run before the callback of   getDocumentIdInOriginalMail(self.model.documentId, function (attachmentId) {
            }                    //if (templateId == undefined || templateId == 0) { and it will be zero so it will not preview attachment

        })

        $('#' + self.model.ComponentId + '_btnPreviewBeforeSign').click(function () {
            Common.mask(document.body, "body-mask");
            if (templateId != 0) {

                Common.ajaxGet('/Document/CheckDocumentAttachmentLocked/', { "documentId": self.model.documentId }, function (retval) {
                    Common.unmask("body-mask");
                    if (retval) {
                        Common.alertMsg(Resources.FileInUse);

                    } else {
                        signType = 6;
                        $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnReplyToStructure').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnMyTransfer').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnGroupDropup').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnStartWorkflow').attr("disabled", true);
                        getDocumentIdInOriginalMail(self.model.documentId, function (attachmentId) {
                            validateAndPreviewAttachment(attachmentId, templateId);
                        });

                    }
                })



            } else {
                Common.ajaxGet('/Document/CheckDocumentAttachmentLocked/', { "documentId": self.model.documentId }, function (retval) {
                    Common.unmask("body-mask");
                    if (retval) {
                        Common.alertMsg(Resources.FileInUse);

                    }
                    else {
                        signType = 6;
                        $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnReplyToStructure').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnMyTransfer').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnGroupDropup').attr("disabled", true);
                        $('#' + self.model.ComponentId + '_btnStartWorkflow').attr("disabled", true);


                        $.ajax({
                            method: "GET",
                            url: window.DSURL + "/api/signature/template",
                            headers: {
                                "Authorization": "Bearer " + IdentityAccessToken
                            },
                            success: function (res) {
                                $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnComplete').button('reset');
                                $('#' + self.model.ComponentId + '_btnReplyToStructure').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnReplyToStructure').button('reset');
                                $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnReplyToUser').button('reset');
                                $('#' + self.model.ComponentId + '_btnMyTransfer').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnMyTransfer').button('reset');
                                $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnSignTemplate').button('reset');
                                $('#' + self.model.ComponentId + '_btnGroupDropup').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnGroupDropup').button('reset');
                                if (res.length == 0) {
                                    Common.alertMsg(Resources.NoTemplatesFound);
                                    Common.unmask("body-mask");
                                }
                                else if (res.length == 1) {
                                    let selectedTemplateId = res[0].id;
                                    getDocumentIdInOriginalMail(self.model.documentId, function (attachmentId) {
                                        validateAndPreviewAttachment(attachmentId, selectedTemplateId);
                                    });
                                }
                                else {
                                    fromPeview = true;
                                    $("#modalSignatureTemplates").modal('show');
                                    $("#modalSignatureTemplates").off("hidden.bs.modal");
                                    $("#modalSignatureTemplates").off("shown.bs.modal");
                                    $("#tblSignature tbody").html("");

                                    $(self.refs['modalCTSPurpose']).on('hidden.bs.modal', function () {
                                        swal.close();
                                    });

                                    for (let j = 0; j < res.length / 2; j++) {
                                        $("#tblSignature tbody").append('<tr id="tr' + j + '"></tr>');
                                    }
                                    var count = 0, k = 0;

                                    $.each(res, function (i, val) {
                                        $("#tr" + k).append('<td style="width: 250px;"><div class="form-check"><input class="form-check-input" type="radio" style="margin:6px;" name="rdbSignature" id="rdb' + val.id + '" value="' + val.id + '"><label for="rdb' + val.id + '"><img for="rdb' + val.id + '" id="img' + val.id + '"src=""  class="rounded mx-auto d-block" alt="' + val.name + '" width="75%"></label></div></td>');

                                        $("#img" + val.id).attr("src", window.DSURL + "/api/signature/template/" + val.id + "/image?token=" + IdentityAccessToken);

                                        if ((count + 1) % 2 == 0)
                                            k++;

                                        count++;
                                    });
                                }
                            },
                            error: function (res) {
                                $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnComplete').button('reset');
                                $('#' + self.model.ComponentId + '_ReplyToStructure').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_ReplyToStructure').button('reset');
                                $('#' + self.model.ComponentId + '_ReplyToUser').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_ReplyToUser').button('reset');
                                $('#' + self.model.ComponentId + '_MyTransfer').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnMyTransfer').button('reset');
                                $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnSignTemplate').button('reset');
                                $('#' + self.model.ComponentId + '_btnGroupDropup').attr("disabled", false);
                                $('#' + self.model.ComponentId + '_btnGroupDropup').button('reset');
                                Common.showScreenErrorMsg();
                                //Common.unmask("body-mask");
                            }

                        });
                    }

                })

            }
        })
        //StartWorkflow
        $('#' + self.model.ComponentId + '_btnStartWorkflowOld').click(function () {

            Common.ajaxGet('/Document/CheckDocumentAttachmentLocked/', { "documentId": self.model.documentId }, function (retval) {
                if (retval) {
                    Common.alertMsg(Resources.FileInUse);
                } else {
                    let getParams = {
                        "documentId": self.model.documentId,
                        'delegationId': self.model.delegationId,
                        'TransferId': self.model.transferId,
                    }
                    Common.ajaxGet('/Workflow/GetWorkflow/', getParams, function (retval) {

                        let modalWrapper = $(".modal-window");
                        modalWrapper.find("#modalWorkflow").remove();
                        var modalWorkflow = new Workflow.Workflow();
                        modalWorkflow.documentId = self.model.documentId;
                        modalWorkflow.transferId = self.model.transferId;
                        modalWorkflow.enableTransferToUsers = window.EnableTransferToUsers;
                        modalWorkflow.workflowSteps = retval.workflowUsers;
                        modalWorkflow.workflowId = retval.workflowId;
                        modalWorkflow.isDraft = true;
                        var WorkflowView = new Workflow.WorkflowView(modalWrapper, modalWorkflow);
                        WorkflowView.render();

                        $('#modalWorkflow').modal('show');
                        $("#modalWorkflow").off("hidden.bs.modal");
                        $("#modalWorkflow").off("shown.bs.modal");
                        $('#modalWorkflow').on('shown.bs.modal', function () {
                            document.getElementById('cmbUsers').focus();
                        });
                        $('#modalWorkflow').on('hidden.bs.modal', function () {
                        });



                    })
                }
            });

        })

        $('#' + self.model.ComponentId + '_btnStartWorkflow').click(function () {

            Common.ajaxGet('/Document/CheckDocumentAttachmentLocked/', { "documentId": self.model.documentId }, function (retval) {

                if (retval) {
                    Common.alertMsg(Resources.FileInUse);
                } else {
                    let getParams = {
                        "documentId": self.model.documentId,
                        'delegationId': self.model.delegationId,
                        'TransferId': self.model.transferId,
                    }
                    Common.ajaxGet('/Workflow/GetWorkflow/', getParams, function (retval) {

                        if (retval.workflowId != null && retval.workflowId != 0) {
                            let modalWrapper = $(".modal-window");
                            modalWrapper.find("#modalWorkflow").remove();
                            var modalWorkflow = new Workflow.Workflow();
                            modalWorkflow.documentId = self.model.documentId;
                            modalWorkflow.transferId = self.model.transferId;
                            modalWorkflow.enableTransferToUsers = window.EnableTransferToUsers;
                            modalWorkflow.workflowSteps = retval.workflowUsers;
                            modalWorkflow.workflowId = retval.workflowId;
                            modalWorkflow.callBack = function () {
                                tryCloseModal(self.model.actionsComponentId);
                            }
                            modalWorkflow.isDraft = true;
                            var WorkflowView = new Workflow.WorkflowView(modalWrapper, modalWorkflow);
                            WorkflowView.render();

                            $('#modalWorkflow').modal('show');
                            $("#modalWorkflow").off("hidden.bs.modal");
                            $("#modalWorkflow").off("shown.bs.modal");
                            $('#modalWorkflow').on('shown.bs.modal', function () {
                                document.getElementById('cmbUsers').focus();
                            });
                            $('#modalWorkflow').on('hidden.bs.modal', function () {
                            });
                        }

                        else {
                            let postParams = {
                                "documentId": self.model.documentId,
                                'delegationId': self.model.delegationId,
                                'TransferId': self.model.transferId,
                            }
                            Common.ajaxPost('/Workflow/CreateWorkflow', postParams, function (retVal) {
                                let modalWrapper = $(".modal-window");
                                modalWrapper.find("#modalWorkflow").remove();
                                var modalWorkflow = new Workflow.Workflow();
                                modalWorkflow.documentId = self.model.documentId;
                                modalWorkflow.transferId = self.model.transferId;
                                modalWorkflow.enableTransferToUsers = window.EnableTransferToUsers;
                                modalWorkflow.workflowId = retVal.data;
                                modalWorkflow.isDraft = true;
                                modalWorkflow.callBack = function () {
                                    tryCloseModal(self.model.actionsComponentId);
                                }
                                var WorkflowView = new Workflow.WorkflowView(modalWrapper, modalWorkflow);
                                WorkflowView.render();

                                $('#modalWorkflow').modal('show');
                                $("#modalWorkflow").off("hidden.bs.modal");
                                $("#modalWorkflow").off("shown.bs.modal");
                                $('#modalWorkflow').on('shown.bs.modal', function () {
                                    document.getElementById('cmbUsers').focus();
                                });
                                $('#modalWorkflow').on('hidden.bs.modal', function () {
                                });
                            })
                        }


                    })
                }

            });



        });

        //Proceed workflow=
        $('#' + self.model.ComponentId + '_btnProceed').click(function () {
            signType = 5;
            var msg = (self.model.forSignature && (self.model.byTemplate && !(self.model.isSigned || self.model.isDocumentSigned))) ? Resources.SureToSignAndProceed + " " + self.model.nextStepUserName + " ?" : Resources.SureToProceed + " " + self.model.nextStepUserName + " ?";
            if (self.model.forSignature && (self.model.byTemplate && !(self.model.isSigned || self.model.isDocumentSigned))) {
                Common.showConfirmMsg(msg, () => {
                    $.ajax({
                        method: "GET",
                        url: window.DSURL + "/api/signature/template",
                        headers: {
                            "Authorization": "Bearer " + IdentityAccessToken
                        },
                        success: function (res) {
                            $('#' + self.model.ComponentId + '_btnReturn').attr("disabled", false);
                            $('#' + self.model.ComponentId + '_btnReturn').button('reset');
                            $('#' + self.model.ComponentId + '_btnProceed').attr("disabled", false);
                            $('#' + self.model.ComponentId + '_btnProceed').button('reset');

                            if (res.length == 0) {
                                Common.alertMsg(Resources.NoTemplatesFound);
                            }
                            else if (res.length == 1) {
                                self.model.signDocument = true;
                                self.model.signatureTemplate = res[0].id;

                                proceed(self);
                            }
                            else {
                                $("#modalSignatureTemplates").modal('show');
                                $("#modalSignatureTemplates").off("hidden.bs.modal");
                                $("#modalSignatureTemplates").off("shown.bs.modal");
                                $("#tblSignature tbody").html("");

                                $(self.refs['modalCTSPurpose']).on('hidden.bs.modal', function () {
                                    swal.close();
                                });

                                for (let j = 0; j < res.length / 2; j++) {
                                    $("#tblSignature tbody").append('<tr  id="tr' + j + '"></tr>');
                                }
                                var count = 0, k = 0;

                                $.each(res, function (i, val) {
                                    $("#tr" + k).append('<td style="width: 250px;"><div class="form-check"><input class="form-check-input" style="margin:6px;" type="radio" name="rdbSignature" id="rdb' + val.id + '" value="' + val.id + '"><label for="rdb' + val.id + '"><img for="rdb' + val.id + '" id="img' + val.id + '"src=""  class="rounded mx-auto d-block" alt="' + val.name + '" width="75%"></label></div></td>');

                                    $("#img" + val.id).attr("src", window.DSURL + "/api/signature/template/" + val.id + "/image?token=" + IdentityAccessToken);

                                    if ((count + 1) % 2 == 0)
                                        k++;

                                    count++;
                                });
                            }
                        }

                    });
                })
            }
            else {
                Common.showConfirmMsg(msg, () => {
                    proceed(self);
                })
            }


        })

        $('#' + self.model.ComponentId + '_btnReturn').click(function () {
            try {

                var msg = Resources.SureToReturn + " " + self.model.initiatorUser + " ?";
                // sendToReceivingEntity(self.model.replyToEntity, self.model.delegationId, TransferType.ReplyToInitiator, self.model.initiatorUser, self, null, false);
                Common.showConfirmMsg(msg, () => {
                    let structureReceiverIds = $('.cmbCustomAttributeReceiver').val();
                    if (self.model.transferToType == TransferType.ReplyToUser || self.model.transferToType == TransferType.ReplyToStructure || !structureReceiverIds) {
                        structureReceiverIds = [];
                        for (var i = 0; i < self.model.receivingEntities.length; i++) {
                            structureReceiverIds.push(self.model.receivingEntities[i].id);
                        }
                    }
                    ReturnWorkflow(self.model.documentId, self.model.transferId, self.model.delegationId, structureReceiverIds, TransferType.ReplyToInitiator, false, null, self.model.documentId);
                });



            } catch (e) {
                $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnComplete').button('reset');
                $('#' + self.model.ComponentId + '_btnCompleteAndArchive').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnCompleteAndArchive').button('reset');
                $('#' + self.model.ComponentId + '_btnReplyToStructure').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnReplyToStructure').button('reset');
                $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnReplyToUser').button('reset');
                $('#' + self.model.ComponentId + '_btnMyTransfer').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnMyTransfer').button('reset');
            }
        });

        $('#' + self.model.ComponentId + '_btnReinitiate').click(function () {
            try {
                (self.model.workflowStepId != null && self.model.workflowStepId != undefined)
                {

                    $('#' + self.model.ComponentId + '_btnResume').attr("disabled", true);
                    $('#' + self.model.ComponentId + '_btnReinitiate').attr("disabled", true);
                    $('#' + self.model.ComponentId + '_btnReinitiate').button('loading');

                    let param = {
                        'documentId': self.model.documentId !== null ? self.model.documentId : 0,
                        'transferId': self.model.transferId,
                        'workflowStepId': self.model.workflowStepId,
                        'delegationId': self.model.delegationId
                    }

                    Common.ajaxPost('Workflow/Cancel', param,
                        function (response) {
                            $('#' + self.model.ComponentId + '_btnResume').attr("disabled", false);
                            $('#' + self.model.ComponentId + '_btnResume').button('reset');
                            $('#' + self.model.ComponentId + '_btnReinitiate').attr("disabled", false);
                            $('#' + self.model.ComponentId + '_btnReinitiate').button('reset');
                            if (response.success) {
                                $('#' + self.model.ComponentId + '_btnSend').removeClass('hidden');
                                $('#' + self.model.ComponentId + '_btnComplete').removeClass('hidden');
                                $('#' + self.model.ComponentId + '_btnCompleteAndArchive').removeClass('hidden');
                                $('#' + self.model.ComponentId + '_btnReplyToStructure').removeClass('hidden');
                                $('#' + self.model.ComponentId + '_btnReplyToUser').removeClass('hidden');
                                $('#' + self.model.ComponentId + '_btnGroupDropup').removeClass('hidden');
                                $('#' + self.model.ComponentId + '_btnMyTransfer').removeClass('hidden');
                                $(self.refs['dropupReplyContainer']).removeClass('hidden');
                                $('#' + self.model.ComponentId + '_btnSignTemplate').removeClass('hidden');
                                $('#' + self.model.ComponentId + '_btnSignAndSend').removeClass('hidden');
                                $('#' + self.model.ComponentId + '_btnSignAndTransfer').removeClass('hidden');
                                $('#' + self.model.ComponentId + '_btnSignAndReplyToStructure').removeClass('hidden');
                                $('#' + self.model.ComponentId + '_btnSignAndReplyToUser').removeClass('hidden');
                                $('#btnSubmitSign').removeClass('hidden');
                                $('#' + self.model.ComponentId + '_btnStartWorkflow').removeClass('hidden');

                                $('#' + self.model.ComponentId + '_btnReturn').addClass('hidden');
                                $('#' + self.model.ComponentId + '_btnProceed').addClass('hidden');
                                $('#' + self.model.ComponentId + '_btnReinitiate').addClass('hidden');
                                $('#' + self.model.ComponentId + '_btnResume').addClass('hidden');
                                $('#' + self.model.ComponentId + '_btnViewFlow').addClass('hidden');
                                self.model.workflowStepId = null;
                                self.model.isWorkflowReturned = false;
                                self.model.initiatorUser = null;
                                tryCloseModal(self.model.actionsComponentId);
                            }
                            else {
                                if (response.message === "FileInUse") {
                                    setTimeout(function () {
                                        Common.alertMsg(Resources.FileInUse);
                                    }, 300);
                                } else if (response.message === 'OriginalFileInUse') {

                                    Common.alertMsg(Resources.OriginalFileInUse);

                                }
                            }
                        },
                        function () {
                            $('#' + self.model.ComponentId + '_btnResume').attr("disabled", false);
                            $('#' + self.model.ComponentId + '_btnResume').button('reset');
                            $('#' + self.model.ComponentId + '_btnReinitiate').attr("disabled", false);
                            $('#' + self.model.ComponentId + '_btnReinitiate').button('reset');
                            Common.showScreenErrorMsg();
                        });


                }
            } catch (e) {
                $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnComplete').button('reset');
                $('#' + self.model.ComponentId + '_btnCompleteAndArchive').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnCompleteAndArchive').button('reset');
                $('#' + self.model.ComponentId + '_btnReplyToStructure').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnReplyToStructure').button('reset');
                $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnReplyToUser').button('reset');
                $('#' + self.model.ComponentId + '_btnMyTransfer').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnMyTransfer').button('reset');
                $('#' + self.model.ComponentId + '_btnProceed').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnProceed').button('reset');
                $('#' + self.model.ComponentId + '_btnReturn').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnReturn').button('reset');
                $('#' + self.model.ComponentId + '_btnResume').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnResume').button('reset');
                $('#' + self.model.ComponentId + '_btnReinitiate').attr("disabled", false);
                $('#' + self.model.ComponentId + '_btnReinitiate').button('reset');

            }
        });

        $('#' + self.model.ComponentId + '_btnResume').click(function () {
            $('#' + self.model.ComponentId + '_btnResume').attr("disabled", true);
            $('#' + self.model.ComponentId + '_btnResume').button('loading');
            $('#' + self.model.ComponentId + '_btnReinitiate').attr("disabled", true);

            let param = {
                'documentId': self.model.documentId !== null ? self.model.documentId : 0,
                'transferId': self.model.transferId,
                'workflowStepId': self.model.workflowStepId,
                'delegationId': self.model.delegationId
            }

            Common.ajaxPost('Workflow/Resume', param,
                function (response) {
                    $('#' + self.model.ComponentId + '_btnResume').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnResume').button('reset');
                    $('#' + self.model.ComponentId + '_btnReinitiate').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnReinitiate').button('reset');
                    if (response.success) {
                        var nodeId = $('[data-inherit="' + TreeNode.Sent + '"]').first().data("id");
                        var redirectTo = '#sent/' + nodeId;

                        tryCloseModal(self.model.actionsComponentId);
                        TreeNode.refreshTreeNodeCounts(TreeNode.Inbox); TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox); TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                        TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
                        TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                        $(".close-correspondence").trigger("click");
                        $("[ref=btnCloseTransfer]").trigger("click");
                        if ($('li .active').attr("data-id") == undefined) {
                            window.location.href = "/";
                        }
                        else {
                            window.location.reload();
                        }

                        //window.location.href = redirectTo;
                        Common.showScreenSuccessMsg();
                    }

                    else {
                        if (response.message === "FileInUse") {
                            setTimeout(function () {
                                Common.alertMsg(Resources.FileInUse);
                            }, 300);
                        }
                        else if (response.message === 'OriginalFileInUse') {

                            Common.alertMsg(Resources.OriginalFileInUse);

                        }
                    }
                },
                function () {
                    $('#' + self.model.ComponentId + '_btnResume').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnResume').button('reset');
                    $('#' + self.model.ComponentId + '_btnReinitiate').attr("disabled", false);
                    $('#' + self.model.ComponentId + '_btnReinitiate').button('reset');
                    Common.showScreenErrorMsg();
                });
        })

        $('#' + self.model.ComponentId + '_btnViewFlow').click(function () {

            let getParams = {
                "DocumentId": self.model.documentId,
                'TransferId': self.model.transferId,
                'workflowStepId': self.model.workflowStepId,
                'delegationId': self.model.delegationId
            }
            Common.ajaxGet('/Workflow/GetWorkflow/', getParams, function (retval) {
                let modalWrapper = $(".modal-window");
                modalWrapper.find("#modalWorkflow").remove();
                var model = new Workflow.Workflow();
                model.documentId = self.model.documentId;
                model.transferId = self.model.transferId;
                model.enableTransferToUsers = window.EnableTransferToUsers;
                model.workflowSteps = retval.workflowUsers;
                model.workflowId = retval.workflowId;
                model.edit = true;
                if (self.model.isWorkflowReturned === false || self.model.readonly) {
                    model.readOnly = true;
                }
                var WorkflowView = new Workflow.WorkflowView(modalWrapper, model);
                WorkflowView.render();



                $('#modalWorkflow').modal('show');
                $("#modalWorkflow").off("hidden.bs.modal");
                $("#modalWorkflow").off("shown.bs.modal");
                $('#modalWorkflow').on('shown.bs.modal', function () {
                    document.getElementById('cmbUsers').focus();
                });
                $('#modalWorkflow').on('hidden.bs.modal', function () {
                });

            });

        });

        $('#' + self.model.ComponentId + '_btnExport').click(function () {
            var wrapper = $(".modal-window");
            var model = new ReportCorrespondenceDetailExport.ReportCorrespondenceDetailExport();
            model.documentId = self.model.documentId;
            model.delegationId = self.model.delegationId;
            var reportCorrespondenceDetailExportView = new ReportCorrespondenceDetailExport.ReportCorrespondenceDetailExportView(wrapper, model);
            reportCorrespondenceDetailExportView.render();

            $("#modalReportCorrespondenceDetailExport").off("hidden.bs.modal");
            $("#modalReportCorrespondenceDetailExport").off("shown.bs.modal");
            $("#modalReportCorrespondenceDetailExport").on('shown.bs.modal', function () { });
            $("#modalReportCorrespondenceDetailExport").on('hidden.bs.modal', function () { });
            $("#modalReportCorrespondenceDetailExport").modal("show");

        });

        $('#' + self.model.ComponentId + '_btnExportAll').click(function () {
            var wrapper = $(".modal-window");
            var model = new ReportAttachmentDetailExport.ReportAttachmentDetailExport();
            model.documentId = self.model.documentId;
            var reportAttachmentDetailExportView = new ReportAttachmentDetailExport.ReportAttachmentDetailExportView(wrapper, model);
            reportAttachmentDetailExportView.render();

            $("#modalReportAttachmentDetailExport").off("hidden.bs.modal");
            $("#modalReportAttachmentDetailExport").off("shown.bs.modal");
            $("#modalReportAttachmentDetailExport").on('shown.bs.modal', function () { });
            $("#modalReportAttachmentDetailExport").on('hidden.bs.modal', function () { });


            var customModalCss = `
                    .custom-modal-size {
                        max-width: 1000px;
                    }
                `;


            var styleElement = document.createElement('style');
            styleElement.type = 'text/css';
            styleElement.appendChild(document.createTextNode(customModalCss));
            document.head.appendChild(styleElement);


            $("#modalReportAttachmentDetailExport .modal-dialog").addClass("custom-modal-size");

            $("#modalReportAttachmentDetailExport").modal("show");
        });




        $('#' + self.model.ComponentId + '_btnEmailAttachment').click(function () {
            var wrapper = $(".modal-window");
            var model = new EmailAttachment.EmailAttachmentModal();
            model.documentId = self.model.documentId;
            model.transferId = self.model.transferId;
            var viewModal = new EmailAttachment.EmailAttachmentModalView(wrapper, model, function () { tryCloseModal(self.model.actionsComponentId); });
            viewModal.render();
        });

        $(self.refs['instruction']).click(function () {

            $(self.refs['instruction']).focus();
            if ($(this)[0].innerHTML != "") {
                //navigator.clipboard.writeText($(this)[0].text());
                navigator.clipboard.writeText($(this)[0].text().trim());
                showToast(Resources.CopiedToClipboard, 500, $(self.refs['instruction']).parent()[0]);
            }
        });

        $('#' + self.model.ComponentId + '_btnRejectRequest').click(function () {
            if (window.EnableConfirmationMessage === "True") {
                Common.showConfirmMsg(Resources.RejectRequestConfirmation, function () {
                    let modalWrapper = $(".modal-window");
                    let modelIndex = new NoteComponentIndex.NoteComponentIndex();
                    modelIndex.actionsComponentId = self.model.actionsComponentId;
                    modelIndex.callback = function (response) {

                        if (response && response.success) {
                            tryCloseModal(self.model.actionsComponentId);
                            if (self.model.fromVip || (window.InboxMode == "InboxVIPView" || window.InboxMode === 'LocalVIPView')) {
                            markAsUnread(self.model.transferId, self.model.delegationId);
                            TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                                TreeNode.refreshTreeNodeCounts(TreeNode.Custom);

                            $(".withBorders-o").addClass("waitingBackground");
                            $("#inboxDocumentDetailsContainer").empty();
                            $($("input[data-id='" + self.model.transferId + "']").parents("li")[0]).fadeOut().remove();
                        }
                        else {
                            markAsUnread(self.model.transferId, self.model.delegationId);
                            GridCommon.RefreshCurrentPage("grdInboxItems", true);
                            TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                                TreeNode.refreshTreeNodeCounts(TreeNode.Custom);
                        }
                            Common.showScreenSuccessMsg();

                        } else if (response && !response.success && response.message != null) {
                            setTimeout(function () {
                                Common.alertMsg(response.message);
                            }, 500);
                        } else {
                            Common.showScreenErrorMsg();
                        }
                    };
                    let NoteComponentIndexView = new NoteComponentIndex.NoteComponentIndexView(modalWrapper, modelIndex);

                    NoteComponentIndexView.render({
                        url: '/Transfer/RejectRequest',
                        params: {
                            "id": self.model.transferId
                        }
                    });

                });
            }
            else {
                let modalWrapper = $(".modal-window");
                let modelIndex = new NoteComponentIndex.NoteComponentIndex();
                modelIndex.actionsComponentId = self.model.actionsComponentId;
                modelIndex.callback = function (response) {
                    if (response && response.success) {
                        tryCloseModal(self.model.actionsComponentId);
                        if (self.model.fromVip || (window.InboxMode == "InboxVIPView" || window.InboxMode === 'LocalVIPView')) {
                        markAsUnread(self.model.transferId, self.model.delegationId);
                        TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                            TreeNode.refreshTreeNodeCounts(TreeNode.Custom);

                        $(".withBorders-o").addClass("waitingBackground");
                        $("#inboxDocumentDetailsContainer").empty();
                        $($("input[data-id='" + self.model.transferId + "']").parents("li")[0]).fadeOut().remove();
                    }
                    else {
                        markAsUnread(self.model.transferId, self.model.delegationId);
                        GridCommon.RefreshCurrentPage("grdInboxItems", true);
                        TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                            TreeNode.refreshTreeNodeCounts(TreeNode.Custom);
                    }
                        Common.showScreenSuccessMsg();

                    } else if (response && !response.success && response.message != null) {
                        setTimeout(function () {
                            Common.alertMsg(response.message);
                        }, 500);
                    } else {
                        Common.showScreenErrorMsg();
                    }
                };
                let NoteComponentIndexView = new NoteComponentIndex.NoteComponentIndexView(modalWrapper, modelIndex);

                NoteComponentIndexView.render({
                    url: '/Transfer/RejectRequest',
                    params: {
                        "id": self.model.transferId
                    }
                });


            }
        });
        $('#' + self.model.ComponentId + '_btnAcceptRequest').click(function () {
            Common.showConfirmMsg(Resources.AcceptRequestConfirmation, function () {
                var params = { "id": self.model.transferId };
                Common.ajaxPost('/Transfer/AcceptRequest', params, function (response) {
                    if (response && response.success) {
                    tryCloseModal(self.model.actionsComponentId);
                        if (self.model.fromVip || (window.InboxMode == "InboxVIPView" || window.InboxMode === 'LocalVIPView')) {
                        markAsUnread(self.model.transferId, self.model.delegationId);
                        TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                            TreeNode.refreshTreeNodeCounts(TreeNode.Custom);

                        $(".withBorders-o").addClass("waitingBackground");
                        $("#inboxDocumentDetailsContainer").empty();
                        $($("input[data-id='" + self.model.transferId + "']").parents("li")[0]).fadeOut().remove();
                    }
                    else {
                        markAsUnread(self.model.transferId, self.model.delegationId);
                        GridCommon.RefreshCurrentPage("grdInboxItems", true);
                        TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                            TreeNode.refreshTreeNodeCounts(TreeNode.Custom);
                    }
                        Common.showScreenSuccessMsg();
                    } else if (response && !response.success && response.message != null) {
                        setTimeout(function () {
                            Common.alertMsg(response.message);
                        }, 500);
                    } else {
                        Common.showScreenErrorMsg();
                    }
                });
            });
        });

        $('#' + self.model.ComponentId + '_btnAcceptRequestAndTransfer').click(function () {
            Common.showConfirmMsg(Resources.AcceptRequestConfirmation, function () {
                var params = { "id": self.model.transferId };
                Common.mask(document.body, "body-mask");
                Common.ajaxPost('/Transfer/AcceptRequest', params, function (response) {
                    if (response == '' || response.success == true) {
                        //tryCloseModal(self.model.actionsComponentId);
                        $('#' + self.model.ComponentId + '_btnMyTransfer').click();
                        Common.unmask("body-mask");
                        Common.showScreenSuccessMsg();
                    }
                    if (self.model.fromVip || (window.InboxMode == "InboxVIPView" || window.InboxMode === 'LocalVIPView')) {
                        markAsUnread(self.model.transferId, self.model.delegationId);
                        TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);

                        $(".withBorders-o").addClass("waitingBackground");
                        $("#inboxDocumentDetailsContainer").empty();
                        $($("input[data-id='" + self.model.transferId + "']").parents("li")[0]).fadeOut().remove();

                    }
                    else {
                        markAsUnread(self.model.transferId, self.model.delegationId);
                        GridCommon.RefreshCurrentPage("grdInboxItems", true);
                        TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                    }



                });
            });
        });
        EventReceiver.MyTransferAfterRender(self);

        $("#" + self.model.parentComponentId + "_taskPanelHeaderDiv").on("click", function () {
            if (this.ariaExpanded != 'true') {
                $("#" + self.model.parentComponentId + "_taskPanelHeaderDiv").addClass('panel-header-primary')
                $("#" + self.model.parentComponentId + "_taskPanelHeaderDiv i").addClass('icon-default')
                $("#" + self.model.parentComponentId + "_taskPanelHeaderDiv").removeClass('panel-header-default')
                $("#" + self.model.parentComponentId + "_taskPanelHeaderDiv i").removeClass('icon-primary')
                // hide previous item before you open the current one
            }
            else {
                $("#" + self.model.parentComponentId + "_taskPanelHeaderDiv").addClass('panel-header-default')
                $("#" + self.model.parentComponentId + "_taskPanelHeaderDiv i").addClass('icon-primary')

                $("#" + self.model.parentComponentId + "_taskPanelHeaderDiv").removeClass('panel-header-primary')
                $("#" + self.model.parentComponentId + "_taskPanelHeaderDiv i").removeClass('icon-default')


            }

        })

        $('#pdfModal').on('hidden.bs.modal', function () {
            $('#' + self.model.ComponentId + '_btnComplete').attr("disabled", false);
            $('#' + self.model.ComponentId + '_btnComplete').button('reset');
            $('#' + self.model.ComponentId + '_btnReplyToStructure').attr("disabled", false);
            $('#' + self.model.ComponentId + '_btnReplyToStructure').button('reset');
            $('#' + self.model.ComponentId + '_btnReplyToUser').attr("disabled", false);
            $('#' + self.model.ComponentId + '_btnReplyToUser').button('reset');
            $('#' + self.model.ComponentId + '_btnMyTransfer').attr("disabled", false);
            $('#' + self.model.ComponentId + '_btnMyTransfer').button('reset');
            $('#' + self.model.ComponentId + '_btnSignTemplate').attr("disabled", false);
            $('#' + self.model.ComponentId + '_btnSignTemplate').button('reset');
            $('#' + self.model.ComponentId + '_btnGroupDropup').attr("disabled", false);
            $('#' + self.model.ComponentId + '_btnGroupDropup').button('reset');
            $('#' + self.model.ComponentId + '_btnStartWorkflow').attr("disabled", false);
            $('#' + self.model.ComponentId + '_btnStartWorkflow').button('reset');
        });
    }
}

function refreshCurrentTab() {
    const documentId = self.model.documentId;
    const transferId = self.model.transferId;

    const attributeTab = $("[ref=tabDocumentDetails] li.active a");
    const tabId = attributeTab.data("id");
    const target = attributeTab.attr("href");
    const typeId = attributeTab.data("typeid");
    let jsfunction = attributeTab.data("function");
    const url = attributeTab.data("url");
    const openiframe = attributeTab.data("openiframe");
    const securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));

    const params = { id: transferId };
    if (self.model.delegationId !== null) {
        params.delegationId = delegationId;
    }
    Common.ajaxGet('/Document/GetDocumentBasicInfoByTransferId', params, function (response) {
        const tabsActions = securityMatrix.SecurityNodes[self.model.nodeId ?? window.location.hash.split("/").at(-1)].SecurityCategories[self.model.categoryId].SecurityTabs;

        if (typeId.toString() == TabType.Url) {
            url = url.replace("$documentId", documentId).replace("$transferId", transferId);
            const div = target.replace("#", "");
            if (!openInIframe) {
                Common.ajaxGet(url, null, function (response) {
                    let wrapper = null;
                    wrapper = document.getElementById(div);
                    wrapper.innerHTML = "";
                    wrapper.innerHTML = response;
                });
            }
            else {
                let wrapper = null;
                wrapper = document.getElementById(div + "Iframe");
                wrapper.src = url;
            }
            return;
        }


        const customActions = $.grep(tabsActions[tabId], function (element) {
            return !self.model.readonly ? element.ShowInEditMode !== false : element.ShowInReadMode !== false;
        });

        let actions = undefined;
        for (let i = 0; i < tabsActions[tabId].length; i++) {
            if (i == 0) {
                actions = tabsActions[tabId][i].Name;
            }
            else {
                actions += "_" + tabsActions[tabId][i].Name;
            }
        }
        jsfunction = jsfunction
            .replace("$documentId", documentId)
            // .replace("$basketId", basketId)
            .replace("$transferId", transferId)
            .replace("$customactions", JSON.stringify(customActions))
            .replace("$readOnly", self.model.readonly)
            .replace("$delegationId", self.model.delegationId)
            .replace("$tabId", tabId)
            .replace("$ComponentId", "'" + target.split("_tab")[0].slice(1) + "'")
            .replace("$categoryId", self.model.categoryId)
            .replace("$categories", new Categories().get(window.language))
            .replace("$fromSent", self.model.fromSent)
            .replace("$fromInbox", self.model.fromInbox)
            .replace("$isCced", self.model.isCced)
            .replace("$fromDraft", false)
            .replace("$DocumentIsCompleted", response.DocumentIsCompleted)
            .replace("$sentToUser", self.model.sentToUser)
            .replace("$parentLinkedDocumentId", response.parentLinkedDocumentId)
            .replace("$actionName", "'" + actions + "'")
            .replace("$modalComponentId", "'" + self.model.parentComponentId + "'")
            // .replace("$followupId", "'" + model.followupId + "'")
            .replace("$nodeId", "'" + self.model.nodeId + "'")
            .replace("$teamId", response.teamId)
            .replace("$fromManageCorrespondance", response.fromManageCorrespondance)

        eval(jsfunction);
    });
}



export default { MyTransfer, MyTransferView };
