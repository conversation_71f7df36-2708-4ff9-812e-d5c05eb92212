﻿
using Aspose.Pdf.Operators;
using Intalio.Core;
using Intalio.Core.DAL;
using Intalio.Core.Model;
using Intalio.CTS.Core;
using Intalio.CTS.Core.API;
using Intalio.CTS.Core.DAL;
using Intalio.CTS.Core.Model;
using Intalio.CTS.Core.Utility;
using Intalio.CTS.Filters;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Intalio.CTS.Controllers
{
    [Route("[controller]/[action]")]
    public class TransferController : BaseController
    {
        #region Fields

        private readonly IHubContext<CommunicationHub> _hub;

        #endregion

        #region Ctor

        public TransferController(IHubContext<CommunicationHub> hub)
        {
            _hub = hub;
        }

        #endregion

        #region Ajax



        [HttpPost]
        public IActionResult PrintDeliveryNote(long documentId, short categoryId, string receivingEntity)
        {
            if (categoryId == 2 && receivingEntity == "external")
            {
                try
                {
                    return Ok(ManageTransfer.PrintDeliveryNote(documentId, categoryId));
                }
                catch (Exception ex)
                {

                    return HttpAjaxError(ex);
                }
            }
            else
            {
                return BadRequest();
            }

        }





        /// <summary>
        /// List transfer inbox
        /// </summary>
        /// <param name="draw"></param>
        /// <param name="start">Starting record - The number of records to skip before starting to return records from the query. Default value is 0.</param>
        /// <param name="length">Number of records to be returned</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(GridListViewModel<TransferListViewModel>), 200)]
        //[ValidateAntiForgeryToken]
        public JsonResult ListInbox([FromForm] int draw, [FromForm] int start, [FromForm] int length)
        {
            try
            {
                short nodeId = Request.Form["NodeId"].Count > 0 ? Convert.ToInt16(Request.Form["NodeId"][0]) : default;
                long? delegationId = null;
                if (Request.Form["DelegationId"].Count > 0 && !string.IsNullOrEmpty(Request.Form["DelegationId"][0]))
                {
                    delegationId = Convert.ToInt64(Request.Form["DelegationId"][0]);
                }
                int categoryId = Request.Form["CategoryId"].Count > 0 ? Convert.ToInt32(Request.Form["CategoryId"][0]) : default;
                string fromDate = Request.Form["FromDate"].Count > 0 ? Convert.ToString(Request.Form["FromDate"][0]) : string.Empty;
                string toDate = Request.Form["ToDate"].Count > 0 ? Convert.ToString(Request.Form["ToDate"][0]) : string.Empty;
                string referenceNumber = Request.Form["ReferenceNumber"].Count > 0 ? Convert.ToString(Request.Form["ReferenceNumber"][0]) : string.Empty;
                bool read = Request.Form["Read"].Count > 0 ? Convert.ToBoolean(Request.Form["Read"][0]) : default;
                bool locked = Request.Form["Locked"].Count > 0 ? Convert.ToBoolean(Request.Form["Locked"][0]) : default;
                bool overdue = Request.Form["Overdue"].Count > 0 ? Convert.ToBoolean(Request.Form["Overdue"][0]) : default;
                string subject = Request.Form["Subject"].Count > 0 ? Convert.ToString(Request.Form["Subject"][0]) : string.Empty;
                short priorityId = Request.Form["PriorityId"].Count > 0 ? Convert.ToInt16(Request.Form["PriorityId"][0]) : default;
                short privacyId = Request.Form["PrivacyId"].Count > 0 ? Convert.ToInt16(Request.Form["PrivacyId"][0]) : default;
                short purposeId = Request.Form["PurposeId"].Count > 0 ? Convert.ToInt16(Request.Form["PurposeId"][0]) : default;
                long structureId = Request.Form["StructureId"].Count > 0 ? Convert.ToInt16(Request.Form["StructureId"][0]) : default;
                List<long> structureIds = Request.Form["StructureIds[]"].SelectMany(value => value.Split(Constants.SEPARATOR)).Select(long.Parse).ToList();
                   
                
                bool fromStructureInbox = Request.Form["FromStructureInbox"].Count > 0 ? Convert.ToBoolean(Request.Form["FromStructureInbox"][0]) : default;
                List<long> userIds = Request.Form["UserIds[]"]
               .SelectMany(value => value.Split(Constants.SEPARATOR)) // Split each value using the separator
               .Select(long.Parse) 
                .ToList();
                string period = Request.Form["Period"].Count > 0 ? Convert.ToString(Request.Form["Period"][0]) : string.Empty;

                //structureIds = structureIds.Count != 0 ? structureIds : StructureIds;
              
                if ((structureId == default || !StructureIds.Any(S => S == structureId)) && Intalio.CTS.Core.Configuration.EnablePerStructure)
                {
                    return Json(new
                    {
                        draw,
                        recordsTotal = 0,
                        recordsFiltered = 0,
                        data = new List<TransferListViewModel>()
                    });
                }

                var result = ManageNode.GetNodeConditionsInExpressionBuilder(nodeId);
                ExpressionBuilderFilters filter = result.Filters;
                if(delegationId !=null)
                {
                    var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(UserId, delegationId.Value) : null;
                    var toUserIdFilter = filter.FirstOrDefault(f => f.PropertyName == "ToUserId" && f.Value != null);
                    if (toUserIdFilter != null)
                    {
                        toUserIdFilter.Value = delegation.FromUserId;
                    }
                }
                if (priorityId != default)
                {
                    filter.Add("Document.PriorityId", priorityId, Intalio.Core.Operator.Equals);
                }
                if (privacyId != default)
                {
                    filter.Add("Document.PrivacyId", privacyId, Intalio.Core.Operator.Equals);
                }
                if (purposeId != default)
                {
                    filter.Add("PurposeId", purposeId, Intalio.Core.Operator.Equals);
                }
                if (categoryId != default)
                {
                    filter.Add("Document.CategoryId", categoryId, Operator.Equals);
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate);
                    filter.Add("CreatedDate.Date", from.Date, Intalio.Core.Operator.GreaterThanOrEqual);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate);
                    filter.Add("CreatedDate.Date", to.Date, Intalio.Core.Operator.LessThanOrEqualTo);
                }
                if (!string.IsNullOrEmpty(referenceNumber))
                {
                    filter.Add("Document.ReferenceNumber", referenceNumber, Intalio.Core.Operator.Contains);
                }
                if (read)
                {
                    filter.Add("OpenedDate", null, Operator.NotEqual);
                }
                if (locked)
                {
                    filter.Add("OwnerUserId", null, Operator.NotEqual);
                    filter.Add("ToUserId", null, Operator.Equals);
                }
                if (result.OverDue.HasValue)
                {
                    if (result.OverDue.Value)
                    {
                        filter.Add("DueDate", DateTime.Now.Date, Operator.LessThan);
                    }
                    else
                    {
                        filter.Add("DueDate", DateTime.Now.Date, Operator.GreaterThanOrEqual);
                    }
                }
                else if (overdue)
                {
                    filter.Add("DueDate", DateTime.Now.Date, Operator.LessThan);
                }
                if (!string.IsNullOrEmpty(subject))
                {
                    filter.Add("Document.SubjectSearch", Core.Helper.GetSearchString(subject), Operator.Contains);
                }
                if (!structureIds.IsNullOrEmpty())
                {
                    filter.Add("FromStructureId", structureIds, Operator.Any);
                }
                if (!userIds.IsNullOrEmpty())
                {
                    filter.Add("FromUserId", userIds, Operator.Any);
                }
                if (/*!fromStructure &&*/ structureId != default && Intalio.CTS.Core.Configuration.EnablePerStructure)
                {
                    filter.Add("ToStructureId", structureId, Operator.Equals);
                }
                if (period != default)
                {
                    DateTime now = DateTime.Now;
                    switch (period)
                    {
                        case "day": // Last day
                            filter.Add("CreatedDate", now.AddDays(-1).Date, Operator.GreaterThanOrEqual);
                            break;
                        case "week": // Last week
                            filter.Add("CreatedDate", now.AddDays(-7).Date, Operator.GreaterThanOrEqual);
                            break;
                        case "month": // Last month
                            filter.Add("CreatedDate", now.AddMonths(-1).Date, Operator.GreaterThanOrEqual);
                            break;
                        default:
                            break;
                    }
                }



                List<SortExpression> sorting = new List<SortExpression>();
                string column = Request.Form["order[0][column]"];
                string sort = Request.Form["order[0][dir]"];
                if (!string.IsNullOrEmpty(column))
                {
                    SortDirection sortDirection = sort == "asc" ? SortDirection.Ascending : SortDirection.Descending;
                    column = Request.Form[$"columns[{Convert.ToInt32(column)}][data]"];
                    if (Core.Configuration.InboxMode == "InboxDefaultWithGrouping")
                    {
                        switch (column)
                        {
                            case "categoryId":
                                sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "Category.Name" });
                                break;
                            case "subject":
                                sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "Subject" });
                                break;
                        }
                    }
                    else
                    {
                        switch (column)
                        {
                            case "categoryId":
                                sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "Document.Category.Name" });
                                break;
                            case "subject":
                                sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "Document.Subject" });
                                break;
                            case "transferDate":
                                sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "CreatedDate" });
                                break;
                        }
                    }
                }
                else
                {
                    //check if Inbox Mode in parameters is Inbox Default With Grouping so not need to set any sorting 
                    if (Core.Configuration.InboxMode == "InboxDefaultWithGrouping")
                        sorting = null;
                    else
                        sorting.Add(new SortExpression { Order = SortDirection.Descending, PropertyName = "CreatedDate" });
                }
                var retValue = (0, new List<TransferListViewModel>());
                retValue = ManageTransfer.ListInbox(start, length, UserId, StructureIds, structureId, IsStructureReceiver, PrivacyLevel, delegationId, filter, sorting, Language, fromStructureInbox);
                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = retValue.Item2
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex, draw);
            }
        }

        /// <summary>
        /// List completed transfers
        /// </summary>
        /// <param name="draw"></param>
        /// <param name="start">Starting record - The number of records to skip before starting to return records from the query. Default value is 0.</param>
        /// <param name="length">Number of records to be returned</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(GridListViewModel<TransferListViewModel>), 200)]
        //[ValidateAntiForgeryToken]
        public JsonResult ListCompleted([FromForm] int draw, [FromForm] int start, [FromForm] int length)
        {
            try
            {
                short nodeId = Request.Form["NodeId"].Count > 0 ? Convert.ToInt16(Request.Form["NodeId"][0]) : default;
                long? delegationId = null;
                if (Request.Form["DelegationId"].Count > 0 && !string.IsNullOrEmpty(Request.Form["DelegationId"][0]))
                {
                    delegationId = Convert.ToInt64(Request.Form["DelegationId"][0]);
                }
                int categoryId = Request.Form["CategoryId"].Count > 0 ? Convert.ToInt32(Request.Form["CategoryId"][0]) : default;
                string fromDate = Request.Form["FromDate"].Count > 0 ? Convert.ToString(Request.Form["FromDate"][0]) : string.Empty;
                string toDate = Request.Form["ToDate"].Count > 0 ? Convert.ToString(Request.Form["ToDate"][0]) : string.Empty;
                string referenceNumber = Request.Form["ReferenceNumber"].Count > 0 ? Convert.ToString(Request.Form["ReferenceNumber"][0]) : string.Empty;
                string subject = Request.Form["Subject"].Count > 0 ? Convert.ToString(Request.Form["Subject"][0]) : string.Empty;
                short priorityId = Request.Form["PriorityId"].Count > 0 ? Convert.ToInt16(Request.Form["PriorityId"][0]) : default;
                short privacyId = Request.Form["PrivacyId"].Count > 0 ? Convert.ToInt16(Request.Form["PrivacyId"][0]) : default;
                short purposeId = Request.Form["PurposeId"].Count > 0 ? Convert.ToInt16(Request.Form["PurposeId"][0]) : default;
                long structureId = Request.Form["StructureId"].Count > 0 ? Convert.ToInt16(Request.Form["StructureId"][0]) : default;
                List<long> structureIds = Request.Form["StructureIds[]"].Count > 0 ? Request.Form["StructureIds[]"][0].Split(Constants.SEPARATOR).Select(long.Parse).ToList() : new List<long>();
                List<long> userIds = Request.Form["UserIds[]"].Count > 0 ? Request.Form["UserIds[]"][0].Split(Constants.SEPARATOR).Select(long.Parse).ToList() : new List<long>();
                //structureIds = structureIds.Count != 0 ? structureIds : StructureIds;

                if ((structureId == default || !StructureIds.Any(S => S == structureId)) && Intalio.CTS.Core.Configuration.EnablePerStructure)
                {
                    return Json(new
                    {
                        draw,
                        recordsTotal = 0,
                        recordsFiltered = 0,
                        data = new List<TransferListViewModel>()
                    });
                }

                var result = ManageNode.GetNodeConditionsInExpressionBuilder(nodeId);
                ExpressionBuilderFilters filter = result.Filters;

                if (priorityId != default)
                {
                    filter.Add("Document.PriorityId", priorityId, Operator.Equals);
                }
                if (privacyId != default)
                {
                    filter.Add("Document.PrivacyId", privacyId, Operator.Equals);
                }
                if (purposeId != default)
                {
                    filter.Add("PurposeId", purposeId, Operator.Equals);
                }

                if (categoryId != default)
                {
                    filter.Add("Document.CategoryId", categoryId, Operator.Equals);
                }
                if (structureId != default && Intalio.CTS.Core.Configuration.EnablePerStructure)
                {
                    filter.Add("ToStructureId", structureId, Operator.Equals);
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate);
                    filter.Add("CreatedDate.Date", from.Date, Operator.GreaterThanOrEqual);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate);
                    filter.Add("CreatedDate.Date", to.Date, Operator.LessThanOrEqualTo);
                }
                if (!string.IsNullOrEmpty(referenceNumber))
                {
                    filter.Add("Document.ReferenceNumber", referenceNumber, Operator.Contains);
                }
                if (!string.IsNullOrEmpty(subject))
                {
                    filter.Add("Document.SubjectSearch", Core.Helper.GetSearchString(subject), Operator.Contains);
                }
                if (!structureIds.IsNullOrEmpty())
                {
                    filter.Add("FromStructureId", structureIds, Operator.Any);
                }
                if (!userIds.IsNullOrEmpty())
                {
                    filter.Add("FromUserId", userIds, Operator.Any);
                }

                List<SortExpression> sorting = new List<SortExpression>();
                string column = Request.Form["order[0][column]"];
                string sort = Request.Form["order[0][dir]"];
                if (!string.IsNullOrEmpty(column))
                {
                    SortDirection sortDirection = sort == "asc" ? SortDirection.Ascending : SortDirection.Descending;
                    column = Request.Form[$"columns[{Convert.ToInt32(column)}][data]"];
                    switch (column)
                    {
                        case "categoryId":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "Document.Category.Name" });
                            break;
                        case "subject":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "Document.Subject" });
                            break;
                        case "transferDate":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "CreatedDate" });
                            break;
                    }
                }
                else
                {
                    sorting.Add(new SortExpression { Order = SortDirection.Descending, PropertyName = "ClosedDate" });
                }
                var retValue = (0, new List<TransferListViewModel>());
                retValue = ManageTransfer.ListCompleted(start, length, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, delegationId, filter, sorting, Language);
                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = retValue.Item2
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex, draw);
            }
        }

        /// <summary>
        /// Get inbox transfer count
        /// </summary>
        /// <param name="nodeId">Node id</param>
        /// <param name="delegationId">Delegation id - optional</param>
        /// <param name="loggedInStructureId">loggedIn Structure Id</param>
        /// <param name="fromStructure">come from Structure node</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetInboxCounts(short nodeId, long? delegationId, long? loggedInStructureId, bool fromStructure = false)
        {
            try
            {
                if ((loggedInStructureId == null || !StructureIds.Any(S => S == loggedInStructureId.Value)) && Intalio.CTS.Core.Configuration.EnablePerStructure)
                {
                    (int Total, int Today) errValue = (0, 0);
                    return Ok(new { errValue.Total, errValue.Today });
                }
                var result = ManageNode.GetNodeConditionsInExpressionBuilder(nodeId);
                if (delegationId != null)
                {
                    var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(UserId, delegationId.Value) : null;
                    var toUserIdFilter = result.Filters.FirstOrDefault(f => f.PropertyName == "ToUserId" && f.Value != null);
                    if (toUserIdFilter != null)
                    {
                        toUserIdFilter.Value = delegation.FromUserId;
                    }
                }
                (int Total, int Today, int Unread) retValue = ManageTransfer.GetInboxCounts(UserId, StructureIds, loggedInStructureId, IsStructureReceiver, PrivacyLevel, delegationId, result.Filters, result.OverDue,fromStructure);
                return Ok(new { retValue.Total, retValue.Today, retValue.Unread });
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// Get completed transfer count
        /// </summary>
        /// <param name="nodeId">Node id</param>
        /// <param name="delegationId">Delegation id - optional</param>
        /// <param name="loggedInStructureId">loggedIn Structure Id</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetCompletedCounts(short nodeId, long? delegationId, long? loggedInStructureId)
        {
            try
            {

                if ((loggedInStructureId == null || !StructureIds.Any(S => S == loggedInStructureId.Value)) && Intalio.CTS.Core.Configuration.EnablePerStructure)
                {
                    (int Total, int Today) errValue = (0, 0);
                    return Ok(new { errValue.Total, errValue.Today });
                }

                var result = ManageNode.GetNodeConditionsInExpressionBuilder(nodeId);
                (int Total, int Today) retValue = ManageTransfer.GetCompletedCounts(UserId, StructureIds, loggedInStructureId, IsStructureReceiver, PrivacyLevel, delegationId, result.Filters);
                return Ok(new { retValue.Total, retValue.Today });
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// Get transfer information
        /// </summary>
        /// <param name="id">Transfer id</param>
        /// <param name="delegationId">Delegation id - optional</param>
        /// <param name="categoryId">Category id - optional</param>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(TransferGridFormatModel), 200)]
        [Produces("application/json")]
        public async Task<IActionResult> GetTransferInfoById(long id, long? delegationId,bool fromRejectedDocument=false, long categoryId = 0, bool fromStructure = false)
        {
            try
            {
                var retValue = await ManageTransfer.GetTransferInfoById(UserId, StructureIds, fromStructure ? true : IsStructureReceiver, PrivacyLevel, id, delegationId, Language, categoryId, fromRejectedDocument);
                return Ok(retValue);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }
        /// <summary>
        /// Get transfer information
        /// </summary>
        /// <param name="TransferId">Transfer id</param>
        /// <param name="delegationId">Delegation id - optional</param>
        /// <returns></returns>
        [CustomTabsAuthorizationFilter(new string[] { nameof(CustomTabs.TransfersHistory) })]

        [HttpGet]
        [ProducesResponseType(typeof(TransferGridFormatModel), 200)]
        [Produces("application/json")]
        public async Task<IActionResult> GetTransferVoiceNoteById(long TransferId, long? delegationId)
        {
            try
            {
                var retValue = await ManageTransfer.GetTransferVoiceNoteById(UserId, StructureIds, IsStructureReceiver, PrivacyLevel, TransferId, delegationId, Language);
                if (retValue == null)
                {
                    return Unauthorized();
                }
                return File(retValue.Data, retValue.ContentType);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }
        /// <summary>
        /// Update Open date and Owner
        /// </summary>
        /// <param name="id">Transfer id</param>
        /// <param name="delegationId">Delegation id - optional</param>
        /// <returns></returns>
        [HttpPost]
        //[ValidateAntiForgeryToken]
        public IActionResult View(long id, long? delegationId)
        {
            try
            {
                ManageTransfer.View(UserId, StructureIds, IsStructureReceiver, PrivacyLevel, id, delegationId);
                return Ok();
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        //////////////////////////
        /// <summary>
        /// UnReadMessage
        /// </summary>
        /// <param name="id">Transfer id</param>
        /// <param name="delegationId">Delegation id - optional</param>
        /// <returns></returns>
        [HttpPost]
        //[ValidateAntiForgeryToken]
        public IActionResult UnRead(long id, long? delegationId)
        {
            try
            {
                var result = ManageTransfer.UnRead(UserId, StructureIds, IsStructureReceiver, PrivacyLevel, id, delegationId, Language);
                return Ok(new { success = result.Item1, message = result.Item2 });
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }
        /// <summary>
        /// Lock transfer
        /// </summary>
        /// <param name="id">Transfer id</param>
        /// <param name="delegationId">Delegation id - optional</param>
        /// <returns></returns>
        [HttpPost]
        //[ValidateAntiForgeryToken]
        public async Task<IActionResult> Lock(long id, long? delegationId, bool fromStructureInbox = false)
        {
            try
            {
                var result = await ManageTransfer.Lock(UserId, StructureIds, IsStructureReceiver, PrivacyLevel, id, delegationId);

                if (delegationId.HasValue)
                {
                    if (result.OwnerDelegatedUserId.HasValue && result.OwnerDelegatedUserId.Value != UserId)
                    {
                        var user = Intalio.Core.API.ManageUser.Find(result.OwnerUserId.Value);
                        return Ok(Language == Intalio.Core.Language.EN ?
                            $"{user.Firstname} {user.Lastname}" : $"{IdentityHelperExtension.GetFullName(user.Id, Language)}");
                    }
                }
                else
                {
                    if (result.OwnerUserId.HasValue && result.OwnerUserId.Value != UserId)
                    {
                        var user = Intalio.Core.API.ManageUser.Find(result.OwnerUserId.Value);
                        return Ok(Language == Intalio.Core.Language.EN ?
                            $"{user.Firstname} {user.Lastname}" : $"{IdentityHelperExtension.GetFullName(user.Id, Language)}");
                    }
                }
                return Ok(result.Locked);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// Unlock transfer
        /// </summary>
        /// <param name="id">Transfer id</param>
        /// <param name="delegationId">Delegation id - optional</param>
        /// <returns></returns>
        [HttpPost]
        //[ValidateAntiForgeryToken]
        public IActionResult UnLock(long id, long? delegationId)
        {
            try
            {
                var retValue = ManageTransfer.UnLock(UserId, id, RoleId, StructureIds[0], delegationId);
                if (!retValue.Success)
                {
                    return Ok(retValue.Message);
                }
                return Ok();
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }



        /// <summary>
        /// Accept transfer Request
        /// </summary>
        /// <param name="id">Transfer id</param>
        /// <returns></returns>
        [HttpPost]
        //[ValidateAntiForgeryToken]
        public IActionResult AcceptRequest(long id)
        {
            try
            {
                return Ok(ManageTransfer.AcceptRequest(UserId, id, RoleId, StructureIds[0], Language));
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// Reject transfer Request
        /// </summary>
        /// <param name="id">Transfer id</param>
        /// <param name="rejectionReason">rejection Reason</param>
        /// <returns></returns>
        [HttpPost]
        //[ValidateAntiForgeryToken]
        public IActionResult RejectRequest(long id,string rejectionReason)
        {
            try
            {
                return Ok(ManageTransfer.RejectRequest(UserId, id, rejectionReason,Language));
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// List transfer history
        /// </summary>
        /// <param name="draw"></param>
        /// <param name="start">Starting record - The number of records to skip before starting to return records from the query. Default value is 0.</param>
        /// <param name="length">Number of records to be returned</param>
        /// <param name="documentId">Document id</param>
        /// <param name="transferId">Transfer id - optional</param>
        /// <returns></returns>
        [CustomTabsAuthorizationFilter(new string[] { nameof(CustomTabs.TransfersHistory) })]

        [HttpPost]
        [ProducesResponseType(typeof(GridListViewModel<TransferListViewModel>), 200)]
        public async Task<JsonResult> ListTransferHistory([FromForm] int draw, [FromForm] int start, [FromForm] int length, long documentId, long? transferId)
        {
            try
            {
                long? delegationId = null;
                if (Request.Form["DelegationId"].Count > 0 && !string.IsNullOrEmpty(Request.Form["DelegationId"][0]))
                {
                    delegationId = Convert.ToInt64(Request.Form["DelegationId"][0]);
                }

                List<SortExpression> sorting = new List<SortExpression>();
                string column = Request.Form["order[0][column]"];
                string sort = Request.Form["order[0][dir]"];
                if (!string.IsNullOrEmpty(column))
                {
                    SortDirection sortDirection = sort == "asc" ? SortDirection.Ascending : SortDirection.Descending;
                    column = Request.Form[$"columns[{Convert.ToInt32(column)}][data]"];
                    switch (column)
                    {
                        case "dueDate":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "DueDate" });
                            break;
                        case "closedDate":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "ClosedDate" });
                            break;
                        case "transferDate":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "CreatedDate" });
                            break;
                        case "transferTime":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "CreatedDate" });
                            break;
                     // SortExpression is a class of 2-attributes Order and PropertyName that takes input of a column from Transfer table in the DB
                    }
                }
                else
                {
                    sorting.Add(new SortExpression { Order = SortDirection.Descending, PropertyName = "CreatedDate" });
                }
                var retValue = (0, new List<TransferListViewModel>());
                retValue = await ManageTransfer.ListTransferHistory(start, length, documentId, transferId, UserId, RoleId, StructureIds[0], StructureIds, IsStructureReceiver, PrivacyLevel, delegationId, sorting, Language);
                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = retValue.Item2
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex, draw);
            }
        }

        /// <summary>
        /// Complete transfers
        /// </summary>
        /// <param name="ids">Ids of transfers to be completed</param>
        /// <param name="delegationId">Delegation id - optional</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(CompletedDocumentTransferModel), 200)]
        [Produces("application/json")]
        public async Task<IActionResult> Complete(List<long> ids, long? delegationId, string completeReasonNote = null, bool? completeFollowUp = false, List<long?> documentIds = null, bool? forArchive = false,bool fromRejectedDocument = false, long? rootFolderId = null, bool fromStructure = false)
        {
            try
            {
                if (forArchive ?? false)
                {
                    if (!ManageTransfer.CheckIfLastOpenTransfer(ids))
                    {
                        return Ok(new List<CompletedDocumentTransferModel> { new CompletedDocumentTransferModel(){
                            Message= "Cannotarchivebecausethereareotheropentransfers",
                            Updated = false,
                        }
                        });
                    }
                }
                List<CompletedDocumentTransferModel> result = ManageTransfer.Complete(UserId, ids, StructureIds, fromStructure ? true : IsStructureReceiver, PrivacyLevel, completeReasonNote, delegationId, false, completeFollowUp, fromRejectedDocument, Language, documentIds);
                if (result[0].Updated && (forArchive ?? false))
                {
                    var response = await ManageTransfer.Archive(UserId, ids[0], RoleId, StructureId, StructureIds, IsStructureReceiver, PrivacyLevel, rootFolderId, delegationId);
                    if (!response.Success)
                    {
                        if (!ManageTransfer.UnArchive(ids[0], UserId, StructureIds, IsStructureReceiver, PrivacyLevel, Language))
                            result.Add(new CompletedDocumentTransferModel()
                            {
                                Message = "UnArchive Error",
                                Updated = false,
                            });
                        if (response.Message.Split('_')[1] != "0")
                        {
                            ManageTransfer.DeleteDMSFolder(Convert.ToInt64(response.Message.Split('_')[1]));
                        }
                        result.Add(new CompletedDocumentTransferModel()
                        {
                            Message = response.Message.Split('_')[0],
                            Updated = false,
                        });
                    }
                }
                if (Core.Configuration.EnableEmailNotification && EnableNotifications)
                {
                    if (completeFollowUp.Value)
                    {
                        // add on followup task completed
                        foreach (var res in result)
                        {
                            if (res.TransferId <= 0)
                            {
                                continue;
                            }
                            try
                            {
                                ManageNotification.CompleteFollowUp(res.TransferId);
                            }
                            catch (System.Exception ex)
                            {
                                ExceptionLogger.WriteEntry(ex.ToString());
                            }

                        }
                    }
                    else
                    {
                        for (int i = 0; i < result.Count; i++)
                        {
                            if (result[i].TransferId > 0)
                            {
                                ManageNotification.Complete(result[i].TransferId);
                            }
                        }
                    }
                }
                return Ok(result);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// List sent transfers
        /// </summary>
        /// <param name="draw"></param>
        /// <param name="start">Starting record - The number of records to skip before starting to return records from the query. Default value is 0.</param>
        /// <param name="length">Number of records to be returned</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(GridListViewModel<TransferListViewModel>), 200)]
        public JsonResult ListSent([FromForm] int draw, [FromForm] int start, [FromForm] int length)
        {
            try
            {
                short nodeId = Request.Form["NodeId"].Count > 0 ? Convert.ToInt16(Request.Form["NodeId"][0]) : default;
                long? delegationId = null;
                if (Request.Form["DelegationId"].Count > 0 && !string.IsNullOrEmpty(Request.Form["DelegationId"][0]))
                {
                    delegationId = Convert.ToInt64(Request.Form["DelegationId"][0]);
                }
                int categoryId = Request.Form["CategoryId"].Count > 0 ? Convert.ToInt32(Request.Form["CategoryId"][0]) : default;
                string fromDate = Request.Form["FromDate"].Count > 0 ? Convert.ToString(Request.Form["FromDate"][0]) : string.Empty;
                string toDate = Request.Form["ToDate"].Count > 0 ? Convert.ToString(Request.Form["ToDate"][0]) : string.Empty;
                string referenceNumber = Request.Form["ReferenceNumber"].Count > 0 ? Convert.ToString(Request.Form["ReferenceNumber"][0]) : string.Empty;
                string subject = Request.Form["Subject"].Count > 0 ? Convert.ToString(Request.Form["Subject"][0]) : string.Empty;
                short priorityId = Request.Form["PriorityId"].Count > 0 ? Convert.ToInt16(Request.Form["PriorityId"][0]) : default;
                short privacyId = Request.Form["PrivacyId"].Count > 0 ? Convert.ToInt16(Request.Form["PrivacyId"][0]) : default;
                short purposeId = Request.Form["PurposeId"].Count > 0 ? Convert.ToInt16(Request.Form["PurposeId"][0]) : default;
                long structureId = Request.Form["StructureId"].Count > 0 ? Convert.ToInt16(Request.Form["StructureId"][0]) : default;
                List<long> structureIds = Request.Form["StructureIds[]"].Count > 0 ? Request.Form["StructureIds[]"][0].Split(Constants.SEPARATOR).Select(long.Parse).ToList() : new List<long>();
                List<long> userIds = Request.Form["UserIds[]"].Count > 0 ? Request.Form["UserIds[]"][0].Split(Constants.SEPARATOR).Select(long.Parse).ToList() : new List<long>();
                bool fromStructure = Request.Form["FromStructure"].Count > 0 ? Convert.ToBoolean(Request.Form["FromStructure"][0]) : default;
                //structureIds = structureIds.Count != 0 ? structureIds : StructureIds;

                if ((structureId == default || !StructureIds.Any(S => S == structureId)) && Intalio.CTS.Core.Configuration.EnablePerStructure)
                {
                    return Json(new
                    {
                        draw,
                        recordsTotal = 0,
                        recordsFiltered = 0,
                        data = new List<TransferListViewModel>()
                    });
                }
                var result = ManageNode.GetNodeConditionsInExpressionBuilder(nodeId);
                ExpressionBuilderFilters filter = result.Filters;

                if (priorityId != default)
                {
                    filter.Add("Document.PriorityId", priorityId, Operator.Equals);
                }
                if (privacyId != default)
                {
                    filter.Add("Document.PrivacyId", privacyId, Operator.Equals);
                }
                if (purposeId != default)
                {
                    filter.Add("PurposeId", purposeId, Operator.Equals);
                }

                if (categoryId != default)
                {
                    filter.Add("Document.CategoryId", categoryId, Operator.Equals);
                }
                if (!fromStructure && structureId != default && Intalio.CTS.Core.Configuration.EnablePerStructure)
                {
                    filter.Add("FromStructureId", structureId, Operator.Equals);
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate);
                    filter.Add("CreatedDate.Date", from.Date, Operator.GreaterThanOrEqual);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate);
                    filter.Add("CreatedDate.Date", to.Date, Operator.LessThanOrEqualTo);
                }
                if (!string.IsNullOrEmpty(referenceNumber))
                {
                    filter.Add("Document.ReferenceNumber", referenceNumber, Operator.Contains);
                }
                if (!string.IsNullOrEmpty(subject))
                {
                    filter.Add("Document.SubjectSearch", Core.Helper.GetSearchString(subject), Operator.Contains);
                }
                if ( !structureIds.IsNullOrEmpty())
                {
                    filter.Add("ToStructureId", structureIds, Operator.Any);
                }
                if (!userIds.IsNullOrEmpty())
                {
                    filter.Add("ToUserId", userIds, Operator.Any);
                }

                List<SortExpression> sorting = new List<SortExpression>();
                string column = Request.Form["order[0][column]"];
                string sort = Request.Form["order[0][dir]"];
                if (!string.IsNullOrEmpty(column))
                {
                    SortDirection sortDirection = sort == "asc" ? SortDirection.Ascending : SortDirection.Descending;
                    column = Request.Form[$"columns[{Convert.ToInt32(column)}][data]"];

                    if (Core.Configuration.InboxMode == "InboxDefaultWithGrouping")
                    {
                        switch (column)
                        {
                            case "categoryId":
                                sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "Category.Name" });
                                break;
                            case "subject":
                                sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "Subject" });
                                break;
                        }
                    }
                    else
                    {
                        switch (column)
                        {
                            case "categoryId":
                                sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "Document.Category.Name" });
                                break;
                            case "subject":
                                sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "Document.Subject" });
                                break;
                            case "transferDate":
                                sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "CreatedDate" });
                                break;
                        }
                    }


                }
                else
                {
                    //check if Inbox Mode in parameters is Inbox Default With Grouping so not need to set any sorting 
                    if (Core.Configuration.InboxMode == "InboxDefaultWithGrouping")
                        sorting = null;
                    else
                        sorting.Add(new SortExpression { Order = SortDirection.Descending, PropertyName = "CreatedDate" });

                }
                var retValue = (0, new List<TransferListViewModel>());
                retValue = ManageTransfer.ListSent(start, length, UserId, structureId, delegationId, filter, sorting, Language, fromStructure);
                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = retValue.Item2
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex, draw);
            }
        }

        /// <summary>
        /// Get sent transfers count
        /// </summary>
        /// <param name="nodeId">Node id</param>
        /// <param name="delegationId">Delegation id - optional</param>
        /// <param name="loggedInStructureId">loggedIn Structure Id</param>
        /// <param name="fromStructure">come from Structure node</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetSentCounts(  short nodeId, long? delegationId, long? loggedInStructureId, bool fromStructure = false)
        {
            try
            {

                if ((loggedInStructureId == null || !StructureIds.Any(S => S == loggedInStructureId.Value)) && Intalio.CTS.Core.Configuration.EnablePerStructure)
                {
                    (int Total, int Today) errValue = (0, 0);
                    return Ok(new { errValue.Total, errValue.Today });
                }

                var result = ManageNode.GetNodeConditionsInExpressionBuilder(nodeId);
                (int Total, int Today) retValue = ManageTransfer.GetSentCounts(UserId, StructureIds, loggedInStructureId, delegationId, result.Filters, fromStructure);
                return Ok(new { retValue.Total, retValue.Today });
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// Recall transfer
        /// </summary>
        /// <param name="id">Transfer id</param>
        /// <param name="delegationId">Delegation id - optional</param>
        /// <returns></returns>
        /// 

        [HttpPost]

        public IActionResult OldRecall(long id, long? delegationId, string note,bool fromExported)
        {
            try
            {
                var resullt = ManageTransfer.Recall(UserId, id, delegationId, note, Language, fromExported);
                if (fromExported && resullt)
                {
                    _hub.Recall(id, Language);
                }
                return Ok(resullt);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// Recall With New transfer
        /// </summary>
        /// <param name="id">Transfer id</param>
        /// <param name="delegationId">Delegation id - optional</param>
        /// <returns></returns>
        /// 

        [HttpPost]

        public async Task<IActionResult> Recall(long id, long? delegationId, string note, bool fromExported)
        {
            try
            {
                var resullt = await ManageTransfer.RecallWithNewTransfer(UserId,StructureId , StructureIds,IsStructureSender , IsStructureReceiver,PrivacyLevel ,id, delegationId, note, _hub,Language, fromExported);
                if (fromExported && resullt)
                {
                    _hub.Recall(id, Language);
                }
                return Ok(resullt);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        // <summary>
        /// StructureUserRecall transfer
        /// </summary>
        /// <param name="id">Transfer id</param>
        /// <param name="delegationId">Delegation id - optional</param>
        /// <returns></returns>
        /// 

        [HttpPost]

        public IActionResult StructureUserRecall(long userId, long id, long? delegationId, string? note)
        {
            try
            {
                return Ok(ManageTransfer.StructureUserRecall(userId, id, delegationId, note, Language));
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }
        /// <summary>
        /// Transfer list of transfers
        /// </summary>
        /// <param name="transfers">Transfers to be transferred</param>
        /// <param name="delegationId">Delegation id - optional</param>
        /// <param name="maintainTransfer">maintainTransfer for make it stil open</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> Transfer([FromBody] List<TransferModel> transfers, long? delegationId, bool maintainTransfer, bool withSign, long? signatureTemplateId)
        {
            try
            {
                var authHeader = HttpContext.Request.Headers["Authorization"].ToString();
                var usertoken = authHeader.StartsWith("Bearer ") ? authHeader.Substring("Bearer ".Length) : authHeader;

                Log.Information("isftesting save transfers: " + JsonConvert.SerializeObject(transfers));
                List<TransferedDocumentModel> result = new List<TransferedDocumentModel>();
                if (withSign && signatureTemplateId != null)
                {
                    var signed = await ManageTransfer.SignDocument(transfers[0].DocumentId, transfers[0].ParentTransferId, UserId, StructureIds, IsStructureReceiver, IsStructureSender, PrivacyLevel, signatureTemplateId.Value, delegationId, Language, usertoken);
                    if (!signed.updated)
                    {
                        var restored = await ManageAttachment.RestoreWordDocument(transfers[0].DocumentId, usertoken);

                        if (!restored)
                        {
                            throw new InvalidOperationException("Signing failed and restoration of the original Word document also failed.");
                        }
                        result.Add(new TransferedDocumentModel()
                        {
                            DocumentId = transfers[0].DocumentId,
                            Message = signed.message,
                            Updated = signed.updated,
                            ParentTransferId = transfers[0].ParentTransferId,
                        });
                        Log.Information("isftesting save result: " + JsonConvert.SerializeObject(result));
                        return Ok(result);
                    }
                }
                //List<TransferedDocumentModel> result = ManageTransfer.Transfer(UserId, StructureId, StructureIds, IsStructureSender, IsStructureReceiver, PrivacyLevel, transfers, maintainTransfer, delegationId);
                result = await ManageTransfer.Transfer(UserId, StructureId, StructureIds, IsStructureSender, IsStructureReceiver, PrivacyLevel, transfers, maintainTransfer, delegationId, language: Language);
                for (int i = 0; i < result.Count; i++)
                {
                    if (result[i].Updated)
                    {
                        if (result[i].DocumentId > 0)
                        {
                            if (result[i].ParentTransferId.HasValue)
                            {
                                _hub.Transfer(result[i].ParentTransferId.Value, Language);
                            }
                            else
                            {
                                _hub.Send(result[i].DocumentId, Language);
                            }
                        }
                    }
                }
                Log.Information("isftesting save result: " + JsonConvert.SerializeObject(result));
                return Ok(result);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }

        }

        /// <summary>
        /// Reply to transfer
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]      
        public async Task<IActionResult> Reply(SendReplyModel model, bool maintainTransfer = false)
        {
            try
            {
                var authHeader = HttpContext.Request.Headers["Authorization"].ToString();
                var usertoken = authHeader.StartsWith("Bearer ") ? authHeader.Substring("Bearer ".Length) : authHeader;

                if (!StructureIds.Contains(model.StructureId))
                {
                    model.StructureId = StructureId;
                }
                if (model.WithSign && model.SignatureTemplateId != null)
                {
                    var signed = await ManageTransfer.SignDocument(model.DocumentId.Value, model.TransferId, UserId, StructureIds, IsStructureReceiver, IsStructureSender, PrivacyLevel, model.SignatureTemplateId.Value, model.DelegationId, Language, usertoken);
                    if (signed.updated) { return Ok(signed.message); }
                    else
                    {
                        var res = await ManageAttachment.RestoreWordDocument(model.DocumentId.Value, usertoken);
                        if (!res)
                        {
                            throw new InvalidOperationException("Failed to restore Word document.");
                        }
                        return Ok(signed.message);
                    }
                }
                //var result = ManageTransfer.Reply(UserId, StructureIds, IsStructureSender, IsStructureReceiver, PrivacyLevel, model.Id, model.TransferId ?? 0, model.PurposeId, model.DueDate, model.Instruction, model.StructureId, model.DelegationId, model.TransferToType);
                var result = await ManageTransfer.ReplyAsync(UserId, StructureIds, IsStructureSender, model.FromStructure ? true : IsStructureReceiver, PrivacyLevel, model.Id, model.TransferId ?? 0, model.PurposeId, model.DueDate, model.Instruction, model.VoiceNote, model.VoiceNotePrivacy, model.StructureId, model.DelegationId, model.TransferToType, maintainTransfer,language:Language);
                if (result.Replied)
                {
                    _hub.Reply(model.Id, Language);
                    return Ok();
                }
                else if (!string.IsNullOrEmpty(result.Message))
                {
                    return Ok(result.Message);
                }
                return StatusCode(Microsoft.AspNetCore.Http.StatusCodes.Status401Unauthorized);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// Dismiss carbon copy(ies) by transfer(s) id(s)
        /// </summary>
        /// <param name="ids">Transfer(s) id(s)</param>
        /// <param name="delegationId">Delegation id - optional</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(List<UpdateTransferCheckModel>), 200)]
        [Produces("application/json")]
        public IActionResult DismissCarbonCopy(List<long> ids, long? delegationId)
        {
            try
            {
                return Ok(ManageTransfer.DismissCarbonCopy(UserId, ids, StructureIds, IsStructureReceiver, PrivacyLevel, delegationId));
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// List transfer inbox for vip mode
        /// </summary>
        /// <param name="start">Starting record - The number of records to skip before starting to return records from the query. Default value is 0.</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(List<TransferInboxListViewVipModel>), 200)]
        //[ValidateAntiForgeryToken]
        public IActionResult ListInboxVip([FromForm] int start)
        {
            try
            {
                short nodeId = Request.Form["NodeId"].Count > 0 ? Convert.ToInt16(Request.Form["NodeId"][0]) : default;
                long? delegationId = null;
                if (Request.Form["DelegationId"].Count > 0 && !string.IsNullOrEmpty(Request.Form["DelegationId"][0]))
                {
                    delegationId = Convert.ToInt64(Request.Form["DelegationId"][0]);
                }
                int categoryId = Request.Form["CategoryId"].Count > 0 ? Convert.ToInt32(Request.Form["CategoryId"][0]) : default;
                string fromDate = Request.Form["FromDate"].Count > 0 ? Convert.ToString(Request.Form["FromDate"][0]) : string.Empty;
                string toDate = Request.Form["ToDate"].Count > 0 ? Convert.ToString(Request.Form["ToDate"][0]) : string.Empty;
                string referenceNumber = Request.Form["ReferenceNumber"].Count > 0 ? Convert.ToString(Request.Form["ReferenceNumber"][0]) : string.Empty;
                bool read = Request.Form["Read"].Count > 0 ? Convert.ToBoolean(Request.Form["Read"][0]) : default;
                bool locked = Request.Form["Locked"].Count > 0 ? Convert.ToBoolean(Request.Form["Locked"][0]) : default;
                bool overdue = Request.Form["Overdue"].Count > 0 ? Convert.ToBoolean(Request.Form["Overdue"][0]) : default;
                string subject = Request.Form["Subject"].Count > 0 ? Convert.ToString(Request.Form["Subject"][0]) : string.Empty;
                short priorityId = Request.Form["PriorityId"].Count > 0 ? Convert.ToInt16(Request.Form["PriorityId"][0]) : default;
                short privacyId = Request.Form["PrivacyId"].Count > 0 ? Convert.ToInt16(Request.Form["PrivacyId"][0]) : default;
                short purposeId = Request.Form["PurposeId"].Count > 0 ? Convert.ToInt16(Request.Form["PurposeId"][0]) : default;
                long structureId = Request.Form["StructureId"].Count > 0 ? Convert.ToInt16(Request.Form["StructureId"][0]) : default;
                List<long> structureIds = Request.Form["StructureIds[]"].Count > 0 ? Request.Form["StructureIds[]"][0].Split(Constants.SEPARATOR).Select(long.Parse).ToList() : new List<long>();
                List<long> userIds = Request.Form["UserIds[]"].Count > 0 ? Request.Form["UserIds[]"][0].Split(Constants.SEPARATOR).Select(long.Parse).ToList() : new List<long>();
                bool fromStructure = Request.Form["FromStructure"].Count > 0 ? Convert.ToBoolean(Request.Form["FromStructure"][0]) : default;

                if ((structureId == default || !StructureIds.Any(S => S == structureId)) && Intalio.CTS.Core.Configuration.EnablePerStructure)
                {
                    return Ok(new List<TransferInboxListViewVipModel>());
                }

                var result = ManageNode.GetNodeConditionsInExpressionBuilder(nodeId);
                ExpressionBuilderFilters filter = result.Filters;

                if (priorityId != default)
                {
                    filter.Add("Document.PriorityId", priorityId, Operator.Equals);
                }
                if (privacyId != default)
                {
                    filter.Add("Document.PrivacyId", privacyId, Operator.Equals);
                }
                if (purposeId != default)
                {
                    filter.Add("PurposeId", purposeId, Operator.Equals);
                }
                if (categoryId != default)
                {
                    filter.Add("Document.CategoryId", categoryId, Operator.Equals);
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate);
                    filter.Add("CreatedDate.Date", from.Date, Operator.GreaterThanOrEqual);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate);
                    filter.Add("CreatedDate.Date", to.Date, Operator.LessThanOrEqualTo);
                }
                if (!string.IsNullOrEmpty(referenceNumber))
                {
                    filter.Add("Document.ReferenceNumber", referenceNumber, Operator.Contains);
                }
                if (read)
                {
                    filter.Add("OpenedDate", null, Operator.NotEqual);
                }
                if (locked)
                {
                    filter.Add("OwnerUserId", null, Operator.NotEqual);
                    filter.Add("ToUserId", null, Operator.Equals);
                }
                if (overdue || result.OverDue.HasValue)
                {
                    filter.Add("DueDate", DateTime.Now, Operator.LessThan);
                }
                if (!string.IsNullOrEmpty(subject))
                {
                    filter.Add("Document.SubjectSearch", Core.Helper.GetSearchString(subject), Operator.Contains);
                }
                if (!structureIds.IsNullOrEmpty())
                {
                    filter.Add("FromStructureId", structureIds, Operator.Any);
                }
                if (!userIds.IsNullOrEmpty())
                {
                    filter.Add("FromUserId", userIds, Operator.Any);
                }
                if (/*!fromStructure&&*/structureId != default && Intalio.CTS.Core.Configuration.EnablePerStructure)
                {
                    filter.Add("ToStructureId", structureId, Operator.Equals);
                }

                return Ok(ManageTransfer.ListInboxVip(start, UserId,structureId ,StructureIds, IsStructureReceiver, PrivacyLevel, delegationId, filter, Language,fromStructure));
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// List completed transfers for vip mode
        /// </summary>
        /// <param name="start">Starting record - The number of records to skip before starting to return records from the query. Default value is 0.</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(List<TransferCompletedListViewVipModel>), 200)]
        //[ValidateAntiForgeryToken]
        public IActionResult ListCompletedVip([FromForm] int start)
        {
            try
            {
                short nodeId = Request.Form["NodeId"].Count > 0 ? Convert.ToInt16(Request.Form["NodeId"][0]) : default;
                long? delegationId = null;
                if (Request.Form["DelegationId"].Count > 0 && !string.IsNullOrEmpty(Request.Form["DelegationId"][0]))
                {
                    delegationId = Convert.ToInt64(Request.Form["DelegationId"][0]);
                }
                int categoryId = Request.Form["CategoryId"].Count > 0 ? Convert.ToInt32(Request.Form["CategoryId"][0]) : default;
                string fromDate = Request.Form["FromDate"].Count > 0 ? Convert.ToString(Request.Form["FromDate"][0]) : string.Empty;
                string toDate = Request.Form["ToDate"].Count > 0 ? Convert.ToString(Request.Form["ToDate"][0]) : string.Empty;
                string referenceNumber = Request.Form["ReferenceNumber"].Count > 0 ? Convert.ToString(Request.Form["ReferenceNumber"][0]) : string.Empty;
                string subject = Request.Form["Subject"].Count > 0 ? Convert.ToString(Request.Form["Subject"][0]) : string.Empty;
                short priorityId = Request.Form["PriorityId"].Count > 0 ? Convert.ToInt16(Request.Form["PriorityId"][0]) : default;
                short privacyId = Request.Form["PrivacyId"].Count > 0 ? Convert.ToInt16(Request.Form["PrivacyId"][0]) : default;
                short purposeId = Request.Form["PurposeId"].Count > 0 ? Convert.ToInt16(Request.Form["PurposeId"][0]) : default;
                long structureId = Request.Form["StructureId"].Count > 0 ? Convert.ToInt16(Request.Form["StructureId"][0]) : default;
                List<long> structureIds = Request.Form["StructureIds[]"].Count > 0 ? Request.Form["StructureIds[]"][0].Split(Constants.SEPARATOR).Select(long.Parse).ToList() : new List<long>();
                List<long> userIds = Request.Form["UserIds[]"].Count > 0 ? Request.Form["UserIds[]"][0].Split(Constants.SEPARATOR).Select(long.Parse).ToList() : new List<long>();
                if ((structureId == default || !StructureIds.Any(S => S == structureId)) && Intalio.CTS.Core.Configuration.EnablePerStructure)
                {
                    return Ok(new List<TransferCompletedListViewVipModel>());
                }
                var result = ManageNode.GetNodeConditionsInExpressionBuilder(nodeId);
                ExpressionBuilderFilters filter = result.Filters;

                if (priorityId != default)
                {
                    filter.Add("Document.PriorityId", priorityId, Operator.Equals);
                }
                if (privacyId != default)
                {
                    filter.Add("Document.PrivacyId", privacyId, Operator.Equals);
                }
                if (purposeId != default)
                {
                    filter.Add("PurposeId", purposeId, Operator.Equals);
                }

                if (categoryId != default)
                {
                    filter.Add("Document.CategoryId", categoryId, Operator.Equals);
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate);
                    filter.Add("CreatedDate.Date", from.Date, Operator.GreaterThanOrEqual);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate);
                    filter.Add("CreatedDate.Date", to.Date, Operator.LessThanOrEqualTo);
                }
                if (!string.IsNullOrEmpty(referenceNumber))
                {
                    filter.Add("Document.ReferenceNumber", referenceNumber, Operator.Contains);
                }
                if (!string.IsNullOrEmpty(subject))
                {
                    filter.Add("Document.SubjectSearch", Core.Helper.GetSearchString(subject), Operator.Contains);
                }
                if (!structureIds.IsNullOrEmpty())
                {
                    filter.Add("FromStructureId", structureIds, Operator.Any);
                }
                if (!userIds.IsNullOrEmpty())
                {
                    filter.Add("FromUserId", userIds, Operator.Any);
                }
                if (structureId != default && Intalio.CTS.Core.Configuration.EnablePerStructure)
                {
                    filter.Add("ToStructureId", structureId, Operator.Equals);
                }

                return Ok(ManageTransfer.ListCompletedVip(start, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, delegationId, filter, Language));
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// List sent transfers for vip mode
        /// </summary>
        /// <param name="start">Starting record - The number of records to skip before starting to return records from the query. Default value is 0.</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(List<TransferSentListViewVipModel>), 200)]
        public IActionResult ListSentVip([FromForm] int start)
        {
            try
            {
                short nodeId = Request.Form["NodeId"].Count > 0 ? Convert.ToInt16(Request.Form["NodeId"][0]) : default;
                long? delegationId = null;
                if (Request.Form["DelegationId"].Count > 0 && !string.IsNullOrEmpty(Request.Form["DelegationId"][0]))
                {
                    delegationId = Convert.ToInt64(Request.Form["DelegationId"][0]);
                }
                int categoryId = Request.Form["CategoryId"].Count > 0 ? Convert.ToInt32(Request.Form["CategoryId"][0]) : default;
                string fromDate = Request.Form["FromDate"].Count > 0 ? Convert.ToString(Request.Form["FromDate"][0]) : string.Empty;
                string toDate = Request.Form["ToDate"].Count > 0 ? Convert.ToString(Request.Form["ToDate"][0]) : string.Empty;
                string referenceNumber = Request.Form["ReferenceNumber"].Count > 0 ? Convert.ToString(Request.Form["ReferenceNumber"][0]) : string.Empty;
                string subject = Request.Form["Subject"].Count > 0 ? Convert.ToString(Request.Form["Subject"][0]) : string.Empty;
                short priorityId = Request.Form["PriorityId"].Count > 0 ? Convert.ToInt16(Request.Form["PriorityId"][0]) : default;
                short privacyId = Request.Form["PrivacyId"].Count > 0 ? Convert.ToInt16(Request.Form["PrivacyId"][0]) : default;
                short purposeId = Request.Form["PurposeId"].Count > 0 ? Convert.ToInt16(Request.Form["PurposeId"][0]) : default;
                long structureId = Request.Form["StructureId"].Count > 0 ? Convert.ToInt16(Request.Form["StructureId"][0]) : default;
                List<long> structureIds = Request.Form["StructureIds[]"].Count > 0 ? Request.Form["StructureIds[]"][0].Split(Constants.SEPARATOR).Select(long.Parse).ToList() : new List<long>();
                List<long> userIds = Request.Form["UserIds[]"].Count > 0 ? Request.Form["UserIds[]"][0].Split(Constants.SEPARATOR).Select(long.Parse).ToList() : new List<long>();
                bool fromStructure = Request.Form["FromStructure"].Count > 0 ? Convert.ToBoolean(Request.Form["FromStructure"][0]) : default;

                if ((structureId == default || !StructureIds.Any(S => S == structureId)) && Intalio.CTS.Core.Configuration.EnablePerStructure)
                {
                    return Ok(new List<TransferSentListViewVipModel>());
                }
                var result = ManageNode.GetNodeConditionsInExpressionBuilder(nodeId);
                ExpressionBuilderFilters filter = result.Filters;

                if (priorityId != default)
                {
                    filter.Add("Document.PriorityId", priorityId, Operator.Equals);
                }
                if (privacyId != default)
                {
                    filter.Add("Document.PrivacyId", privacyId, Operator.Equals);
                }
                if (purposeId != default)
                {
                    filter.Add("PurposeId", purposeId, Operator.Equals);
                }

                if (categoryId != default)
                {
                    filter.Add("Document.CategoryId", categoryId, Operator.Equals);
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate);
                    filter.Add("CreatedDate.Date", from.Date, Operator.GreaterThanOrEqual);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate);
                    filter.Add("CreatedDate.Date", to.Date, Operator.LessThanOrEqualTo);
                }
                if (!string.IsNullOrEmpty(referenceNumber))
                {
                    filter.Add("Document.ReferenceNumber", referenceNumber, Operator.Contains);
                }
                if (!string.IsNullOrEmpty(subject))
                {
                    filter.Add("Document.SubjectSearch", Core.Helper.GetSearchString(subject), Operator.Contains);
                }
                if (!structureIds.IsNullOrEmpty())
                {
                    filter.Add("ToStructureId", structureIds, Operator.Any);
                }
                if (!userIds.IsNullOrEmpty())
                {
                    filter.Add("ToUserId", userIds, Operator.Any);
                }
                if (!fromStructure && structureId != default && Intalio.CTS.Core.Configuration.EnablePerStructure)
                {
                    filter.Add("FromStructureId", structureId, Operator.Equals);
                }

                return Ok(ManageTransfer.ListSentVip(start, UserId,structureId, delegationId, filter, Language,fromStructure));
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// Send broadcast transfers
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        //[ValidateAntiForgeryToken]
        public async Task<IActionResult> BroadcastSend(SendReplyModel model)
        {
            try
            {
                if (!IsStructureSender && model.StructureReceivers.Any(x => !StructureIds.Any(t => t == x)))
                {
                    return Ok("NotStructureSender");
                }
                if (!StructureIds.Contains(model.StructureId))
                {
                    model.StructureId = StructureId;
                }
                var result = await ManageTransfer.BroadcastSend(UserId, StructureIds, IsStructureSender, IsStructureReceiver, PrivacyLevel, model.Id, model.TransferId, model.DueDate, model.Instruction, model.StructureId, model.DelegationId);
                if (result.Sent)
                {
                    _hub.Send(model.Id, Language);
                    return Ok();
                }
                else if (result.Message == "FileInUse" || result.Message == "CheckAtleastOnePurposeCCed")
                {
                    return Ok(result.Message);
                }
                return StatusCode(Microsoft.AspNetCore.Http.StatusCodes.Status401Unauthorized);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// Complete broadcast transfers
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        //[ValidateAntiForgeryToken]
        public IActionResult BroadcastComplete(BroadcastCompleteModel model)
        {
            try
            {
                if (!IsStructureSender && model.StructureReceivers.Any(x => !StructureIds.Any(t => t == x)))
                {
                    return Ok("NotStructureSender");
                }
                if (!StructureIds.Contains(model.StructureId))
                {
                    model.StructureId = StructureId;
                }
                List<CompletedDocumentTransferModel> result = ManageTransfer.BroadcastComplete(UserId, StructureIds, IsStructureSender, IsStructureReceiver, PrivacyLevel, model.Id, model.TransferIds, model.DueDate, model.Instruction, model.StructureId, model.DelegationId);

                for (int i = 0; i < result.Count; i++)
                {
                    if (result[i].DocumentId > 0)
                    {
                        _hub.Send(result[i].DocumentId, Language);
                    }
                }
                return Ok(result);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// Get transfer and document information
        /// </summary>
        /// <param name="id">Transfer id</param>
        /// <param name="delegationId">Delegation id - optional</param>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(TransferGridFormatModel), 200)]
        [Produces("application/json")]
        public async Task<IActionResult> GetTransferDetailsById(long id, long? delegationId)
        {
            try
            {
                var retValue = await ManageTransfer.GetTransferDetailsById(UserId, StructureIds, IsStructureReceiver, PrivacyLevel, id, delegationId, Language);
                return Ok(retValue);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// Get transfer owner id
        /// </summary>
        /// <param name="id">Transfer id</param>
        /// <param name="delegationId">Delegation id - optional</param>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(TransferGridFormatModel), 200)]
        [Produces("application/json")]
        public async Task<IActionResult> GetTransferOwnerId(long id, long? delegationId)
        {
            try
            {
                var retValue = await ManageTransfer.GetTransferOwnerId(UserId, StructureIds, IsStructureReceiver, PrivacyLevel, id, delegationId);
                return Ok(retValue);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// Cancel transfer
        /// </summary>
        /// <param name="id">Transfer id</param>
        /// <param name="delegationId">Delegation id - optional</param>
        /// <returns></returns>
        [HttpPost]
        //[Authorize(Roles = "Administrator")]
        //[CustomAuthorizationFilter(new int[] { (int)Role.Administrator })]

        public IActionResult Cancel(long id, long? delegationId)
        {
            try
            {
                return Ok(ManageTransfer.Cancel(UserId, id, RoleId, delegationId, Language));
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }
        /// <summary>
        /// Sign documment using viewer
        /// </summary>
        /// <param name="documentId"></param>
        /// <param name="templateId"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> SignDocument(long documentId, long templateId, long? transferId, long? delegationId)
        {
            try
            {
                ManageTransfer.StartSigning(documentId, transferId);

                var authHeader = HttpContext.Request.Headers["Authorization"].ToString();
                var usertoken = authHeader.StartsWith("Bearer ") ? authHeader.Substring("Bearer ".Length) : authHeader;
                var systemtoken = Core.Configuration.IdentityAccessToken;

                var retVal = await ManageTransfer.SignDocument(documentId, transferId, UserId, StructureIds, IsStructureReceiver, IsStructureSender, PrivacyLevel, templateId, delegationId, Language, usertoken);
               
                ManageTransfer.EndSigning(documentId, transferId);
                if (!retVal.updated)
                {
                    var restored = await ManageAttachment.RestoreWordDocument(documentId, usertoken);

                    if (!restored)
                    {
                        return Ok(retVal.message);
                        //throw new InvalidOperationException("Signing failed and restoration of the original Word document also failed.");
                    }
                }
                return Ok(new { message = retVal.message, signedTransferId = retVal.signedTransferId });
            }
            catch (Exception ex)
            {
                ManageTransfer.EndSigning(documentId, transferId.Value);
                return HttpAjaxError(ex);
            }
        }

        [HttpPost]
        public async Task<IActionResult> RejectDocument(long documentId, long transferId)
        {
            try
            {
                return Ok(await ManageTransfer.RejectDocument(documentId, transferId, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, Language));
            }
            catch (Exception ex)
            {

                return HttpAjaxError(ex);
            }
        }


        [HttpPost]
        //[Authorize(Roles = "Administrator")]
        //[CustomAuthorizationFilter(new int[] { (int)Role.Administrator })]
        [CustomMenusAuthorizationFilter(new string[] { nameof(CustomMenus.ManageCorrespondence) })]

        public IActionResult ReAssign(long transferId, long newUserId)
        {
            try
            {
                return Ok(ManageTransfer.ReAssign(transferId, newUserId, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, Language));
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }
        [HttpPost]
        //[Authorize(Roles = "Administrator")]
        //[CustomAuthorizationFilter(new int[] { (int)Role.Administrator })]
        [CustomMenusAuthorizationFilter(new string[] { nameof(CustomMenus.ManageCorrespondence),nameof(CustomMenus.ManageStructureUsersCorrespondences) })]

        public IActionResult ReAssignWithoutTransfer(long transferId, long newUserId, string note)
        {
            try
            {
                return Ok(ManageTransfer.ReAssignWithoutTransfer(transferId, newUserId, UserId, StructureIds, IsStructureReceiver,  PrivacyLevel, note, Language));
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        [HttpPost]
        //[Authorize(Roles = "Administrator")]
        [CustomMenusAuthorizationFilter(new string[] { nameof(CustomMenus.ManageCorrespondence) })]

        public IActionResult UnArchive(long transferId)
        {
            try
            {
                return Ok(ManageTransfer.UnArchive(transferId, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, Language));
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }


        /// <summary>
        /// Preview Transfers For Moving
        /// </summary>
        /// <param name="draw"></param>
        /// <param name="start">Starting record - The number of records to skip before starting to return records from the query. Default value is 0.</param>
        /// <param name="length">Number of records to be returned</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(GridListViewModel<MoveTransferListViewModel>), 200)]
        [CustomMenusAuthorizationFilter(new string[] { nameof(CustomMenus.MoveTransfers) })]

        public async Task<JsonResult> PreviewTransfersForMoving([FromForm] int draw, [FromForm] int start, [FromForm] int length)
        {
            try
            {
                MoveTransferModel model = Request.Form["Model"].Count > 0 ? Intalio.Core.Helper.DeserializeJson<MoveTransferModel>(Request.Form["Model"][0]) : new MoveTransferModel();
                List<SortExpression> sorting = new List<SortExpression>();
                string column = Request.Form["order[0][column]"];
                string sort = Request.Form["order[0][dir]"];
                if (!string.IsNullOrEmpty(column))
                {
                    SortDirection sortDirection = sort == "asc" ? SortDirection.Ascending : SortDirection.Descending;
                    column = Request.Form[$"columns[{Convert.ToInt32(column)}][data]"];
                    switch (column)
                    {
                        case "categoryId":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "CategoryId" });
                            break;
                        case "referenceNumber":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "ReferenceNumber" });
                            break;
                        case "subject":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "Subject" });
                            break;
                        case "createdDate":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "CreatedDate" });
                            break;
                    }
                }
                else
                {
                    sorting.Add(new SortExpression { Order = SortDirection.Descending, PropertyName = "CreatedDate" });
                }
                var retValue = (0, new List<MoveTransferListViewModel>());
                retValue = await ManageTransfer.PreviewTransfersForMoving(model, start, length, sorting);
                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = retValue.Item2
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }

        /// <summary>
        /// Move transfers
        /// </summary>
        /// <param name="moveTransferModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> MoveTransfers(MoveTransferModel moveTransferModel)
        {
            try
            {
                var result = await ManageTransfer.MoveTransfers(moveTransferModel, Language);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// Move transfers
        /// </summary>
        /// <param name="moveTransferModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> CheckTransfersForMoving(MoveTransferModel moveTransferModel)
        {
            try
            {
                var result = await ManageTransfer.CheckTransfersForMoving(moveTransferModel, ViwerRoleId, Language);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }
        /// <summary>
        /// Check If Last Open Transfer
        /// </summary>
        /// <param name="Id">Node id</param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult CheckIfLastOpenTransfer(long Id)
        {
            try
            {
                return Ok(ManageTransfer.CheckIfLastOpenTransfer(new List<long> { Id }));
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// Sign documment using viewer
        /// </summary>
        /// <param name="documentId"></param>
        /// <param name="transferId"></param>
        /// <param name="BookMarks"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> CheckBookMarksAreExist(long documentId, long? transferId, List<string> BookMarks)
        {
            try
            {
                var retValue = await ManageTransfer.CheckBookMarksAreExist(documentId, transferId, BookMarks);
                return Ok(new { result = retValue.result, bookmarks = retValue.bookmarks });
            }
            catch (Exception ex)
            {

                return HttpAjaxError(ex);
            }

        }

        /// <summary>
        /// Sign documment using viewer
        /// </summary>
        /// <param name="documentId"></param>
        /// <param name="transferId"></param>
        /// <param name="BookMarks"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> CheckUserCanSign(long documentId, long? transferId, long? delegationId, List<string> BookMarks)
        {
            try
            {
                var retValue = await ManageTransfer.CheckUserCanSign(documentId, UserId, StructureIds, IsStructureReceiver, IsStructureSender, PrivacyLevel, transferId, delegationId, BookMarks);
                return Ok(new { result = retValue.result, retValue.message, retValue.details });
            }
            catch (Exception ex)
            {

                return HttpAjaxError(ex);
            }

        }
        #endregion

        #region Follow up

        /// <summary>
        /// TransferFollowUpToAssignee
        /// </summary>
        /// <param name="model">Transfer to be transferred</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> TransferFollowUpToAssigneeAsync(AssigneeTransferModel model)
        {
            try
            {
                List<TransferedDocumentModel> result = await ManageTransfer.TransferFollowUpToAssigneeAsync(UserId, StructureId, StructureIds, IsStructureSender, IsStructureReceiver, PrivacyLevel, model, Language);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }

        }

        /// <summary>
        /// Complete transfers
        /// </summary>
        /// <param name="ids">Ids of transfers to be completed</param>
        /// <param name="delegationId">Delegation id - optional</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(CompletedDocumentTransferModel), 200)]
        [Produces("application/json")]
        public IActionResult RequestToComplete(List<long> ids, long? delegationId)
        {
            try
            {
                List<CompletedDocumentTransferModel> result = ManageTransfer.RequestToComplete(UserId, ids, StructureIds, IsStructureReceiver, PrivacyLevel, delegationId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// Postpone task
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> PostponeTask([FromBody] PostponeTaskModel request)
        {
            try
            {
                bool result = await ManageTransfer.PostponeTask(UserId, StructureId, StructureIds, IsStructureSender, IsStructureReceiver, PrivacyLevel, request);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }
        /// <summary>
        /// Postpone Transfer
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> PostponeTransfer([FromBody] PostponeTransferModel request)
        {
            try
            {
                bool result = await ManageTransfer.PostponeTransfer( request,UserId , Language);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// List followups
        /// </summary>
        /// <param name="draw"></param>
        /// <param name="start">Starting record - The number of records to skip before starting to return records from the query. Default value is 0.</param>
        /// <param name="length">Number of records to be returned</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(GridListViewModel<TransferListViewModel>), 200)]
        public JsonResult ListFollowUps([FromForm] int draw, [FromForm] int start, [FromForm] int length)
        {
            try
            {
                short nodeId = Request.Form["NodeId"].Count > 0 ? (!string.IsNullOrEmpty(Request.Form["NodeId"][0]) ? Convert.ToInt16(Request.Form["NodeId"][0]) : default) : default;
                long? delegationId = null;
                if (Request.Form["DelegationId"].Count > 0 && !string.IsNullOrEmpty(Request.Form["DelegationId"][0]))
                {
                    delegationId = Convert.ToInt64(Request.Form["DelegationId"][0]);
                }
                string fromDate = Request.Form["FromDate"].Count > 0 ? Convert.ToString(Request.Form["FromDate"][0]) : string.Empty;
                string toDate = Request.Form["ToDate"].Count > 0 ? Convert.ToString(Request.Form["ToDate"][0]) : string.Empty;
                string referenceNumber = Request.Form["ReferenceNumber"].Count > 0 ? Convert.ToString(Request.Form["ReferenceNumber"][0]) : string.Empty;
                string subject = Request.Form["Subject"].Count > 0 ? Convert.ToString(Request.Form["Subject"][0]) : string.Empty;
                short priorityId = Request.Form["PriorityId"].Count > 0 ? Convert.ToInt16(Request.Form["PriorityId"][0]) : default;
                short statusId = Request.Form["StatusId"].Count > 0 ? Convert.ToInt16(Request.Form["StatusId"][0]) : default;
                string instructions = Request.Form["Instructions"].Count > 0 ? Convert.ToString(Request.Form["Instructions"][0]) : string.Empty;

                List<long> userIds = Request.Form["UserIds[]"].Count > 0 ? Request.Form["UserIds[]"][0].Split(Constants.SEPARATOR).Select(long.Parse).ToList() : new List<long>();
                //Dictionary<string, string> followUpFilters = new Dictionary<string, string>();
                var result = ManageNode.GetNodeConditionsInExpressionBuilder(nodeId);
                ExpressionBuilderFilters filter = result.Filters;

                if (priorityId != default)
                {
                    filter.Add("Document.PriorityId", priorityId, Operator.Equals);
                }
                if (statusId != default)
                {
                    filter.Add("Document.StatusId", statusId, Operator.Equals);
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    //followUpFilters.Add("followUpFromDate", ("\"followUpFromDate\"" + ":" + "\"" + DateOnly.Parse(fromDate).ToString("yyyy-MM-dd") + "T00:00:00+03:00" + "\"").ToLower());
                    DateTime from = DateTime.Parse(fromDate);
                    //filter.Add("Document.CreatedDate.Date", from.Date, Operator.GreaterThanOrEqual);
                    filter.Add("Document.DocumentDate", from.Date, Operator.GreaterThanOrEqual);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    //followUpFilters.Add("followUpToDate", ("\"followUpToDate\"" + ":" + "\"" + DateOnly.Parse(toDate).ToString("yyyy-MM-dd") + "T00:00:00+03:00" + "\"").ToLower());
                    DateTime to = DateTime.Parse(toDate);
                    filter.Add("Document.DueDate", to.Date, Operator.LessThanOrEqualTo);
                }
                if (!string.IsNullOrEmpty(referenceNumber))
                {
                    filter.Add("Document.ReferenceNumber", referenceNumber, Operator.Contains);
                }
                if (!string.IsNullOrEmpty(subject))
                {
                    filter.Add("Document.SubjectSearch", Core.Helper.GetSearchString(subject), Operator.Contains);
                }
                if (!string.IsNullOrEmpty(instructions))
                {
                    //followUpFilters.Add("instructions", "\"instructions\":" + "\"" + instructions.ToLower());
                    filter.Add("Document.DocumentForm.Body", instructions, Operator.Contains);
                }
                if (!userIds.IsNullOrEmpty())
                {
                    filter.Add("FromUserId", userIds, Operator.Any);
                }
                List<SortExpression> sorting = new List<SortExpression>();
                string column = Request.Form["order[0][column]"];
                string sort = Request.Form["order[0][dir]"];
                if (!string.IsNullOrEmpty(column))
                {
                    SortDirection sortDirection = sort == "asc" ? SortDirection.Ascending : SortDirection.Descending;
                    column = Request.Form[$"columns[{Convert.ToInt32(column)}][data]"];
                    switch (column)
                    {
                        case "categoryId":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "Document.Category.Name" });
                            break;
                        case "subject":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "Document.Subject" });
                            break;
                        case "transferDate":
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "CreatedDate" });
                            break;
                    }
                }
                else
                {
                    sorting.Add(new SortExpression { Order = SortDirection.Descending, PropertyName = "CreatedDate" });
                }
                var retValue = (0, new List<TransferListViewModel>());
                retValue = ManageTransfer.ListFollowUps(start, length, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, delegationId, filter, sorting, Language);
                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = retValue.Item2
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex, draw);
            }
        }

        /// <summary>
        /// Send email reminder for task
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> SendEmailReminder([FromBody] EmailReminderModel request)
        {
            try
            {
                bool result = await ManageTransfer.SendEmailReminder(request, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, request.DelegationId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        [CustomActionsAuthorizationFilter(new string[] { nameof(CustomActions.sent_AddCCAndSend) })]
        [HttpPost]
        public async Task<IActionResult> AddCCAndSend ([FromBody] List<TransferModel> transfers)
        {
            return await CreateTransfers(transfers, true, true);
        }

        [CustomActionsAuthorizationFilter(new string[] { nameof(CustomActions.sent_SendCopies) })]
        [HttpPost]
        public async Task<IActionResult> SendCopies ([FromBody] List<TransferModel> transfers)
        {
            return await CreateTransfers(transfers, false, false);
        }

        private async Task<IActionResult> CreateTransfers(List<TransferModel> transfers, bool addCC, bool withFiltering)
        {
            try
            {
                List<TransferedDocumentModel> result = new List<TransferedDocumentModel>();
                var filtered = withFiltering ? ManageTransfer.FilterWithoutCarbonCopy(transfers) : transfers;

                Action<List<TransferModel>>? action = null;

                if(addCC)
                {
                    action = (res) => ManageTransfer.AddCarbonCopy(res);
                }
                result = await ManageTransfer.Transfer(UserId, StructureId, StructureIds, IsStructureSender, IsStructureReceiver, PrivacyLevel, filtered, onAfterAddition: action, language: Language, fromSendAddCC: true);

                for (int i = 0; i < result.Count; i++)
                {
                    if (!result[i].Updated || result[i].DocumentId <= 0)
                    {
                        continue;
                    }
                    if (result[i].ParentTransferId.HasValue)
                    {
                        _hub.Transfer(result[i].ParentTransferId.Value, Language);
                    }
                    else
                    {
                        _hub.Send(result[i].DocumentId, Language);
                    }
                }
                return Ok(result);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// Send a document as email.
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        //[ValidateAntiForgeryToken]
        [Produces("application/json")]
        public async Task<IActionResult> SendCorrespondenceAsEmail([FromBody] TransferEmailViewModel model)
        {
            try
            {
                var result = await ManageTransfer.SendCorrespondenceAsEmail(model);
                return Ok(new { Success = result });
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }


        [CustomTabsAuthorizationFilter(new string[] { nameof(CustomTabs.ExportedRequests) })]

        [HttpPost]
        public JsonResult ListExportedRequests(long documentId)
        {
            try
            {
                var retValue =  new List<TransferListViewModel>();
                retValue =  ManageTransfer.ListExportedRequests( documentId, Language);
                return Json(new
                {
                    
                    data = retValue
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }

        [HttpPost]
        [ProducesResponseType(typeof(GridListViewModel<TransferListViewModel>), 200)]
        public async Task<JsonResult> ListExportedDocumentsGrid([FromForm] int draw, [FromForm] int start, [FromForm] int length)
        {
            try
            {
                string referenceNumber = Request.Form["ReferenceNumber"].Count > 0 ? Convert.ToString(Request.Form["ReferenceNumber"][0]) : string.Empty;
                long structureId = Request.Form["StructureId"].Count > 0 ? Convert.ToInt16(Request.Form["StructureId"][0]) : default;
                ExpressionBuilderFilters filter = new ExpressionBuilderFilters();
                if (!string.IsNullOrEmpty(referenceNumber))
                {
                    filter.Add("ExportedDocument.ReferenceNumber", Core.Helper.GetSearchString(referenceNumber), Operator.Contains);
                }
                if (/*!fromStructure &&*/ structureId != default && Intalio.CTS.Core.Configuration.EnablePerStructure)
                {
                    filter.Add("FromStructureId", structureId, Operator.Equals);
                }
                List<SortExpression> sorting = new List<SortExpression>();
                string column = Request.Form["order[0][column]"];
                string sort = Request.Form["order[0][dir]"];

                //sorting.Add(new SortExpression { Order = SortDirection.Descending, PropertyName = "ModifiedDate" });

                var retValue = await ManageTransfer.ListExportedDocuments(start, length, UserId, StructureId, IsStructureReceiver, PrivacyLevel, filter, sorting, Language);
                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = retValue.Item2
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }
           [HttpPost]
        [ProducesResponseType(typeof(GridListViewModel<TransferListViewModel>), 200)]
        public async Task<JsonResult> ListRejectedDocumentsGrid([FromForm] int draw, [FromForm] int start, [FromForm] int length)
        {
            try
            {
                string referenceNumber = Request.Form["ReferenceNumber"].Count > 0 ? Convert.ToString(Request.Form["ReferenceNumber"][0]) : string.Empty;
                long structureId = Request.Form["StructureId"].Count > 0 ? Convert.ToInt16(Request.Form["StructureId"][0]) : default;
                ExpressionBuilderFilters filter = new ExpressionBuilderFilters();
                if (!string.IsNullOrEmpty(referenceNumber))
                {
                    filter.Add("ExportedDocument.ReferenceNumber", Core.Helper.GetSearchString(referenceNumber), Operator.Contains);
                }
                if (/*!fromStructure &&*/ structureId != default && Intalio.CTS.Core.Configuration.EnablePerStructure)
                {
                    filter.Add("FromStructureId", structureId, Operator.Equals);
                }
                List<SortExpression> sorting = new List<SortExpression>();
                string column = Request.Form["order[0][column]"];
                string sort = Request.Form["order[0][dir]"];

                sorting.Add(new SortExpression { Order = SortDirection.Descending, PropertyName = "ModifiedDate" });

                var retValue = await ManageTransfer.ListRejectedDocuments(start, length, UserId, StructureId, IsStructureReceiver, PrivacyLevel, filter, sorting, Language);
                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = retValue.Item2
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }
        [HttpPost]
        public IActionResult Dismiss(long id, long? delegationId, string note, bool fromExported)
        {
            try
            {
                var result = ManageTransfer.Dismiss(UserId, id, delegationId, note, StructureId, Language, fromExported);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }
        [HttpGet]
        public async Task<IActionResult> GetExportedDocumentsTotalCount()
        {
            try
            {
                int retValue = await ManageTransfer.GetExportedDocumentsTotalCount(UserId,StructureId, IsStructureReceiver, PrivacyLevel);
                return Ok(retValue);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }
        [HttpGet]
        public async Task<IActionResult> GetExportedDocumentsTodayCount()
        {
            try
            {
                int retValue = await ManageTransfer.GetExportedDocumentsTodayCount(UserId, StructureId, IsStructureReceiver, PrivacyLevel);
                return Ok(retValue);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetExportedDocumentsUnreadCount()
        {
            try
            {
                int retValue = await ManageTransfer.GetExportedDocumentsUnreadCount(UserId, StructureId, IsStructureReceiver, PrivacyLevel);
                return Ok(retValue);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetRejectedDocumentsTotalCount()
        {
            try
            {
                int retValue = await ManageTransfer.GetRejectedDocumentsTotalCount(UserId, StructureId, IsStructureReceiver, PrivacyLevel);
                return Ok(retValue);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }
        [HttpGet]
        public async Task<IActionResult> GetRejectedDocumentsTodayCount()
        {
            try
            {
                int retValue = await ManageTransfer.GetRejectedDocumentsTodayCount(UserId, StructureId, IsStructureReceiver, PrivacyLevel);
                return Ok(retValue);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetRejectedDocumentsUnreadCount()
        {
            try
            {
                int retValue = await ManageTransfer.GetRejectedDocumentsUnreadCount(UserId, StructureId, IsStructureReceiver, PrivacyLevel);
                return Ok(retValue);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }



        #endregion
    }
}