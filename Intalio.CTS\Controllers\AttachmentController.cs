using Aspose.Pdf.Operators;
using Aspose.Slides.Charts;
using Intalio.Core;
using Intalio.Core.DAL;
using Intalio.CTS.Core;
using Intalio.CTS.Core.API;
using Intalio.CTS.Core.DAL;
using Intalio.CTS.Core.Model;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Intalio.CTS.Controllers
{
    [Route("[controller]/[action]")]
    public class AttachmentController : BaseController
    {
        #region Ajax


        [HttpGet]
        [Produces("application/json")]
        public async Task<IActionResult> GetAttachmentForDocument(long documentId)
        {
            var attachments = await ManageAttachment.getAttachmentForDocument(documentId);
            return Ok(attachments);
        }

        [HttpGet]
        //[ProducesResponseType(typeof(FileContentResult), 200)]
        [Produces("application/json")]
        public async Task<byte[]> AttachmentContentWithBurnedAnnotations(long id, long? transferId, long? delegationId)
        {
            try
            {

                var authHeader = HttpContext.Request.Headers["Authorization"].ToString();
                var usertoken = authHeader.StartsWith("Bearer ") ? authHeader.Substring("Bearer ".Length) : authHeader;

                var item = new Core.DAL.Attachment().FindIncludeAttachmentSecurity(id);
                if (ManageAttachmentSecurity.ChackAttachmentSecurity(item, UserId, StructureIds))
                {
                    var file = await ManageAttachment.DownloadFromViewerWithAllAnnotations(id, transferId, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, delegationId, true, usertoken);
                    if (file != null)
                    {
                        string contentDisposition = file.GetType().GetProperty("ContentDisposition").GetValue(file).ToString();
                        var contentType = file.GetType().GetProperty("ContentType").GetValue(file).ToString();
                        byte[] data = (byte[])file.GetType().GetProperty("Data").GetValue(file);
                        Response.Headers.Add("Content-Disposition", contentDisposition);
                        Response.Headers.Add("ContentType", contentType);
                        return data;
                    }
                }
                else
                {
                    return null;
                }
            }
            catch (Exception ex)
            {
                return null;
            }
            return null;
        }



        /// <summary>
        ///  List attachments of a certain document
        /// </summary>
        /// <param name="documentId">Document Id</param>
        /// <param name="transferId">Transfer id - optional</param>
        /// <param name="delegationId">Delegation id - optional</param>
        /// <param name="parentDocumentId">Delegation id - optional</param>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(List<TreeNode>), 200)]
        [Produces("application/json")]

        public async Task<IActionResult> List(long documentId, long? transferId, long? delegationId, long? parentDocumentId)
        {
            try
            {
                var attachmentNodes = await ManageAttachment.List(documentId, transferId, UserId, RoleId, StructureIds[0], StructureIds, IsStructureReceiver, PrivacyLevel, delegationId, parentDocumentId, Language);
                List<TreeNode> retValue = new List<TreeNode>();
                retValue.Add(new TreeNode
                {
                    Id = "0",
                    Text = TranslationUtility.Translate("Documents", Language),
                    Type = ((int)NodeType.Folder).ToString(),
                    ParentId = null,
                    Icon = Core.Helper.GetIcon("folder"),
                    Children = attachmentNodes,
                    State = new TreeNodeState()
                    {
                        Disabled = false,
                        Opened = true,
                        Selected = false
                    },
                    Count = attachmentNodes.Count
                });
                return Ok(retValue);
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }

        /// <summary>
        /// Create/edit an attachment folder or rename an attachment file
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> Index(AttachmentNodeModel model)
        {
            try
            {
                Intalio.Core.Model.Result retValue = new Intalio.Core.Model.Result();
                bool success = false;
                int code = Microsoft.AspNetCore.Http.StatusCodes.Status400BadRequest;
                if (ModelState.IsValid)
                {
                    if (Core.Helper.ContainsSpecialCharacter(model.Name))
                    {
                        retValue.Message = TranslationUtility.Translate("NotAllowedCharacters", Language);
                    }
                    else
                    {
                        if (model.Type == NodeType.Folder)
                        {
                            if (model.Id.HasValue)
                            {
                                string message = ManageFolder.Edit(model, Language, UserId, StructureIds, IsStructureReceiver, PrivacyLevel);
                                if (string.IsNullOrEmpty(message))
                                {
                                    success = true;
                                }
                                else
                                {
                                    retValue.Message = message;
                                }
                            }
                            else
                            {
                                string message = ManageFolder.Create(model, Language, UserId, StructureIds, IsStructureReceiver, PrivacyLevel);
                                if (string.IsNullOrEmpty(message))
                                {
                                    success = true;
                                }
                                else
                                {
                                    retValue.Message = message;
                                }
                            }
                        }
                        else
                        {
                            if (model.Id.HasValue)
                            {
                                model.Name = Path.GetFileNameWithoutExtension(model.Name);
                                string message = await ManageAttachment.Rename(model, Language, UserId, StructureIds, IsStructureReceiver, PrivacyLevel);
                                if (string.IsNullOrEmpty(message))
                                {
                                    success = true;
                                }
                                else
                                {
                                    retValue.Message = message;
                                }
                            }
                        }
                    }
                }
                if (success)
                {
                    retValue.Id = model.Id.Value;
                }
                code = Microsoft.AspNetCore.Http.StatusCodes.Status200OK;
                return StatusCode(code, retValue);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// Uploads an attachment file
        /// </summary>
        /// <param name="documentId">Document id</param>
        /// <param name="transferId">Transfer id - optional</param>
        /// <param name="parentId">Parent folder id</param>
        /// <param name="categoryId">Category id</param>
        /// <param name="delegationId">Delegation id - optional</param>
        /// <param name="scannedFile"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(TreeNode), 200)]
        [Produces("application/json")]
        [RequestSizeLimit(2147483648)] //2 GB
        [RequestFormLimits(MultipartBodyLengthLimit = 1073741824)]//1 GB
        public async Task<IActionResult> Upload([FromForm] long documentId, [FromForm] long? transferId, [FromForm] long? parentId, [FromForm] short categoryId, [FromForm] long? delegationId, [FromForm] FileViewModel scannedFile = null, [FromForm] bool fromRejectedDocument = false)
        {
            try
            {
                int code = Microsoft.AspNetCore.Http.StatusCodes.Status400BadRequest;
                FileViewModel fileAttachment = null;

                if (Request.Form.Files.Count > 0 && Request.Form.Files[0] != null)
                {
                    fileAttachment = new FileViewModel();
                    var file = Request.Form.Files[0];

                    fileAttachment.Name = Intalio.Core.Helper.GetFileName(file.FileName);
                    fileAttachment.FileSize = file.Length;
                    fileAttachment.ContentType = file.ContentType;
                    fileAttachment.Data = file.OpenReadStream().StreamToByte();
                    fileAttachment.Extension = Intalio.Core.Helper.GetFileExtension(file.FileName).ToLower();

                    if (fileAttachment.Name?.Length > 200)
                    {
                        code = Microsoft.AspNetCore.Http.StatusCodes.Status400BadRequest;
                        Intalio.Core.Model.Result retValue = new Intalio.Core.Model.Result();
                        retValue.Message = TranslationUtility.Translate("FileNameTooBig", Language);
                        return StatusCode(code, retValue);

                    }

                }
                else if (scannedFile != null && scannedFile.Data != null)
                {
                    fileAttachment = new FileViewModel();
                    fileAttachment = scannedFile;
                }
                if (fileAttachment != null)
                {
                    var IsUnique = ManageAttachment.CheckUnique(null, parentId, documentId, fileAttachment.Name, fileAttachment.Extension);
                    if (!IsUnique.Item1)
                    {
                        var attachmentId = await ManageAttachment.UploadAttachment(documentId, transferId, parentId, categoryId, fileAttachment, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, delegationId, StructureId, RoleId, fromRejectedDocument);
                        if (attachmentId != null)
                        {
                            code = Microsoft.AspNetCore.Http.StatusCodes.Status200OK;
                            
                            
                            return StatusCode(code, new TreeNode
                            {
                                Id = $"file_{attachmentId}",
                                Text = $"{fileAttachment.Name}.{fileAttachment.Extension}",
                                Title = $"{fileAttachment.Name}.{fileAttachment.Extension}",
                                Type = ((int)NodeType.File).ToString(),
                                Icon = Core.Helper.GetIcon(fileAttachment.Extension),
                                ParentId = parentId.HasValue ? $"folder_{parentId}" : "0",
                                Data = new TreeNodeData()
                                {
                                    HasEditAccess = true,
                                    Version = "1.0",
                                    IsWord = Intalio.Core.Helper.IsWord($"{fileAttachment.Name}.{fileAttachment.Extension}"),
                                    isUserCreated = true
                                },
                                State = new TreeNodeState()
                                {
                                    Disabled = false,
                                    Opened = true,
                                    Selected = false
                                }
                            });
                        }
                        else
                        {
                            code = Microsoft.AspNetCore.Http.StatusCodes.Status400BadRequest;
                            Intalio.Core.Model.Result retValue = new Intalio.Core.Model.Result();
                            retValue.Message = TranslationUtility.Translate("CouldNotUploadFile", Language);
                            return StatusCode(code, retValue);
                        }
                    }
                    else
                    {
                        code = Microsoft.AspNetCore.Http.StatusCodes.Status409Conflict;
                        Intalio.Core.Model.Result retValue = new Intalio.Core.Model.Result();
                        retValue.Id = IsUnique.Item2;
                        retValue.Message = TranslationUtility.Translate("SameFileNameExists", Language);
                        return StatusCode(code, retValue);
                    }
                }
                else
                {
                    return StatusCode(code, null);
                }
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// Uploads an original mail attachment
        /// </summary>
        /// <param name="documentId">Document id</param>
        /// <param name="transferId">Transfer id - optional</param>
        /// <param name="categoryId">Category id</param>
        /// <param name="delegationId">Delegation id - optional</param>
        /// <param name="scannedFile"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(TreeNode), 200)]
        [Produces("application/json")]
        [RequestSizeLimit(2147483648)] //2 GB
        [RequestFormLimits(MultipartBodyLengthLimit = 1073741824)]//1 GB
        public async Task<IActionResult> UploadOriginalMail([FromForm] long documentId, [FromForm] long? transferId, [FromForm] short categoryId, [FromForm] long? delegationId, [FromForm] FileViewModel scannedFile = null)
        {
            try
            {
                int code = Microsoft.AspNetCore.Http.StatusCodes.Status400BadRequest;
                FileViewModel fileAttachment = null;

                if (Request.Form.Files.Count > 0 && Request.Form.Files[0] != null)
                {
                    fileAttachment = new FileViewModel();
                    var file = Request.Form.Files[0];

                    fileAttachment.Name = Intalio.Core.Helper.GetFileName(file.FileName);
                    fileAttachment.FileSize = file.Length;
                    fileAttachment.ContentType = file.ContentType;
                    fileAttachment.Data = file.OpenReadStream().StreamToByte();
                    fileAttachment.Extension = Intalio.Core.Helper.GetFileExtension(file.FileName).ToLower();

                }
                else if (scannedFile != null && scannedFile.Data != null)
                {
                    fileAttachment = new FileViewModel();
                    fileAttachment = scannedFile;
                }
                if (fileAttachment != null)
                {
                    var isUnique = ManageAttachment.CheckUnique(null, null, documentId, fileAttachment.Name, fileAttachment.Extension);
                    if (!isUnique.Item1)
                    {
                        var attachmentId = await ManageAttachment.UploadOriginalMail(documentId, transferId, categoryId, fileAttachment, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, delegationId, StructureId, RoleId);
                        if (attachmentId != null)
                        {
                           // bool flag = new Attachment().CheckIsAttachmentOriginal(attachmentId.Value);
                            //if (flag)
                            //{
                                Document item = !transferId.HasValue ? await new Document().FindIncludeDocumentFormAsync(documentId) : await new Document().FindIncludeDocumentMetadataAsync(documentId);
                                if (item.AttachmentId.HasValue)
                                {
                                    var attachment = new Attachment().Find(attachmentId.Value);
                                    var storageAttachmentModel = await ManageAttachment.GetStorageAttachmentModel(attachment.StorageAttachmentId);
                                    bool hasSignature;
                                if (storageAttachmentModel.Data != null && (storageAttachmentModel.Extension == "doc" || storageAttachmentModel.Extension == "docx"))
                                    {
                                        hasSignature = ManageTemplate.CheckFileHasSignatureImage(storageAttachmentModel.Data, "signature");
                                        item.TemplateHasSignature = hasSignature;
                                        item.Update();
                                    }
                                }
                            //}
                            code = Microsoft.AspNetCore.Http.StatusCodes.Status200OK;
                            return StatusCode(code, new TreeNode
                            {
                                Id = $"file_{attachmentId}",
                                Text = $"{fileAttachment.Name}.{fileAttachment.Extension}",
                                Title = $"{fileAttachment.Name}.{fileAttachment.Extension}",
                                Type = ((int)NodeType.File).ToString(),
                                Icon = Core.Helper.GetIcon(fileAttachment.Extension),
                                ParentId = "folder_originalMail",
                                Data = new TreeNodeData()
                                {
                                    HasEditAccess = true,
                                    Version = "1.0",
                                    IsWord = Intalio.Core.Helper.IsWord($"{fileAttachment.Name}.{fileAttachment.Extension}")
                                },
                                State = new TreeNodeState()
                                {
                                    Disabled = false,
                                    Opened = true,
                                    Selected = false
                                }
                            });
                        }
                        else
                        {
                            code = Microsoft.AspNetCore.Http.StatusCodes.Status400BadRequest;
                            Intalio.Core.Model.Result retValue = new Intalio.Core.Model.Result();
                            retValue.Message = TranslationUtility.Translate("CouldNotUploadFile", Language);
                            return StatusCode(code, retValue);
                        }
                    }
                    else
                    {
                        code = Microsoft.AspNetCore.Http.StatusCodes.Status409Conflict;
                        Intalio.Core.Model.Result retValue = new Intalio.Core.Model.Result();
                        retValue.Id = isUnique.Item2;
                        retValue.Message = TranslationUtility.Translate("SameFileNameExists", Language);
                        return StatusCode(code, retValue);
                    }
                }
                else
                {
                    return StatusCode(code, null);
                }
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// Convert To Pdf
        /// </summary>
        /// <param name="id">id</param>
        /// <param name="delegationId">Delegation id - optional</param>
        /// <returns></returns>
        [HttpPost]
        [Produces("application/json")]
        public async Task<IActionResult> ConvertToPdf(long id, long? delegationId)
        {
            try
            {
                var result = await ManageAttachment.ConvertToPdf(id, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, delegationId);
                if (result.Converted)
                {
                    return Ok();
                }
                else if (!string.IsNullOrEmpty(result.Message))
                {
                    return Ok(result.Message);
                }
                return StatusCode(Microsoft.AspNetCore.Http.StatusCodes.Status401Unauthorized);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// Delete an attachment
        /// </summary>
        /// <param name="id">Attachment id</param>
        /// <param name="documentId">Document id</param>
        /// <param name="transferId">Transfer id - optional</param>
        /// <param name="delegationId">Delegation id - optional</param>
        /// <returns></returns>
        [HttpDelete]
        public async Task<IActionResult> DeleteFile(long id, long documentId, long? transferId, long? delegationId)
        {
            try
            {
                Intalio.Core.Model.Result retValue = new Intalio.Core.Model.Result();
                int code = Microsoft.AspNetCore.Http.StatusCodes.Status400BadRequest;
                bool deleted = await ManageAttachment.Delete(id, documentId, transferId, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, delegationId);
                if (!deleted)
                {
                    retValue.Message = TranslationUtility.Translate("CouldNotDeleteFile", Language);
                }
                code = Microsoft.AspNetCore.Http.StatusCodes.Status200OK;
                return StatusCode(code, retValue);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// Delete attachment folder
        /// </summary>
        /// <param name="id">Folder id</param>
        /// <param name="documentId">Document id</param>
        /// <param name="transferId">Transfer id - optional</param>
        /// <param name="delegationId">Delegation id - optional</param>
        /// <returns></returns>
        [HttpDelete]
        public IActionResult DeleteFolder(long id, long documentId, long? transferId, long? delegationId)
        {
            try
            {
                Intalio.Core.Model.Result retValue = new Intalio.Core.Model.Result();
                int code = Microsoft.AspNetCore.Http.StatusCodes.Status400BadRequest;
                string deleted = ManageFolder.Delete(id, documentId, transferId, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, delegationId);
                if (deleted == "NoPermission")
                {
                    retValue.Message = TranslationUtility.Translate("CouldNotDeleteFolder", Language);
                }
                else if (deleted == "FolderHasChildren")
                {
                    retValue.Message = TranslationUtility.Translate("FolderHasChildren", Language);
                }
                code = Microsoft.AspNetCore.Http.StatusCodes.Status200OK;
                return StatusCode(code, retValue);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// Replace an attachment file
        /// </summary>
        /// <param name="id">Attachment id</param>
        /// <param name="documentId">Document id</param>
        /// <param name="transferId">Transfer id - optional</param>
        /// <param name="parentId">Parent folder id</param>
        /// <param name="delegationId">Delegation id - optional</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(TreeNode), 200)]
        [Produces("application/json")]
        [RequestSizeLimit(2147483648)] //2 GB
        [RequestFormLimits(MultipartBodyLengthLimit = 1073741824)]//1 GB
        public async Task<IActionResult> Replace([FromForm] long id, [FromForm] long documentId, [FromForm] long? transferId, [FromForm] long? parentId, [FromForm] long? delegationId)
        {
            try
            {
                int code = Microsoft.AspNetCore.Http.StatusCodes.Status400BadRequest;
                FileViewModel fileAttachment = null;

                if (Request.Form.Files.Count > 0 && Request.Form.Files[0] != null)
                {
                    fileAttachment = new FileViewModel();
                    var file = Request.Form.Files[0];

                    fileAttachment.Name = Intalio.Core.Helper.GetFileName(file.FileName);
                    fileAttachment.FileSize = file.Length;
                    fileAttachment.ContentType = file.ContentType;
                    fileAttachment.Data = file.OpenReadStream().StreamToByte();
                    fileAttachment.Extension = Intalio.Core.Helper.GetFileExtension(file.FileName).ToLower();

                }
                if (fileAttachment != null)
                {
                    var isUnique = ManageAttachment.CheckUnique(id, parentId, documentId, fileAttachment.Name, fileAttachment.Extension);
                    if (!isUnique.Item1)
                    {
                        var attachment = await ManageAttachment.Replace(id, fileAttachment, documentId, transferId, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, delegationId, parentId != null && parentId == 0, StructureId, RoleId);
                        if (attachment.Id != default(long))
                        {
                            code = Microsoft.AspNetCore.Http.StatusCodes.Status200OK;
                            Document item = !transferId.HasValue ? await new Document().FindIncludeDocumentFormAsync(documentId) : await new Document().FindIncludeDocumentMetadataAsync(documentId);
                            if (item.AttachmentId.HasValue)
                            {
                                var attachmentData = new Attachment().Find(attachment.Id.Value);
                                var storageAttachmentModel = await ManageAttachment.GetStorageAttachmentModel(attachmentData.StorageAttachmentId);
                                bool hasSignature;
                                if (storageAttachmentModel.Data != null && (storageAttachmentModel.Extension == "doc" || storageAttachmentModel.Extension == "docx"))
                                {
                                    hasSignature = ManageTemplate.CheckFileHasSignatureImage(storageAttachmentModel.Data, "signature");
                                    item.TemplateHasSignature = hasSignature;
                                    item.Update();
                                }
                            }
                            return StatusCode(code, new TreeNode
                            {
                                Id = $"file_{attachment.Id}",
                                Text = $"{fileAttachment.Name}.{fileAttachment.Extension}",
                                Title = $"{fileAttachment.Name}.{fileAttachment.Extension}",
                                Type = ((int)NodeType.File).ToString(),
                                Icon = Core.Helper.GetIcon(fileAttachment.Extension),
                                ParentId = parentId.HasValue ? $"folder_{parentId}" : "0",
                                Data = new TreeNodeData()
                                {
                                    HasEditAccess = true,
                                    IsWord = Intalio.Core.Helper.IsWord($"{fileAttachment.Name}.{fileAttachment.Extension}")
                                },
                                State = new TreeNodeState()
                                {
                                    Disabled = false,
                                    Opened = true,
                                    Selected = false
                                }
                            });
                        }
                        else
                        {
                            code = Microsoft.AspNetCore.Http.StatusCodes.Status400BadRequest;
                            Intalio.Core.Model.Result retValue = new Intalio.Core.Model.Result();
                            if (string.IsNullOrEmpty(attachment.Message))
                            {
                                retValue.Message = TranslationUtility.Translate("CouldNotUploadFile", Language);
                            }
                            else
                            {
                                retValue.Message = TranslationUtility.Translate(attachment.Message, Language);
                            }
                            return StatusCode(code, retValue);
                        }
                    }
                    else
                    {
                        code = Microsoft.AspNetCore.Http.StatusCodes.Status409Conflict;
                        Intalio.Core.Model.Result retValue = new Intalio.Core.Model.Result();
                        retValue.Id = isUnique.Item2;
                        retValue.Message = TranslationUtility.Translate("SameFileNameExists", Language);
                        return StatusCode(code, retValue);
                    }
                }
                else
                {
                    return StatusCode(code, null);
                }
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// Download an attachment file
        /// </summary>
        /// <param name="id">Attachment id</param>
        /// <param name="documentId">Document id</param>
        /// <param name="transferId">Transfer id - optional</param>
        /// <param name="delegationId">Delegation id - optional</param>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(FileContentResult), 200)]
        [Produces("application/json")]
        public async Task<IActionResult> Download(long id, long? transferId, long? delegationId)
        {
            try
            {
                var authHeader = HttpContext.Request.Headers["Authorization"].ToString();
                var usertoken = authHeader.StartsWith("Bearer ") ? authHeader.Substring("Bearer ".Length) : authHeader;

                var item = new Core.DAL.Attachment().FindIncludeAttachmentSecurity(id);
                if (ManageAttachmentSecurity.ChackAttachmentSecurity(item, UserId, StructureIds))
                {

                    if (Configuration.DownloadWithAnnotation)
                    {
                        var file = await ManageAttachment.DownloadFromViewer(id, transferId, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, delegationId, true, usertoken);
                        if (file != null)
                        {
                            string contentDisposition = file.GetType().GetProperty("ContentDisposition").GetValue(file).ToString();
                            var contentType = file.GetType().GetProperty("ContentType").GetValue(file).ToString();
                            byte[] data = (byte[])file.GetType().GetProperty("Data").GetValue(file);
                            Response.Headers.Add("Content-Disposition", contentDisposition);
                            return File(data, contentType);
                        }
                    }
                    else
                    {
                        var attachment = await ManageAttachment.Download(id, transferId, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, delegationId, true, RoleId);
                        if (attachment != null)
                        {
                            string name = attachment.Name;
                            name = Regex.Replace(name, @"\s+", " ").Replace(" ", "_");
                            Response.Headers.Add("Content-Disposition", "attachment; filename=\"" + WebUtility.UrlEncode(name) + "." + attachment.Extension + "\"");
                            return File(attachment.Data, attachment.ContentType);
                        }
                    }
                }
                else
                {
                    return StatusCode(Microsoft.AspNetCore.Http.StatusCodes.Status401Unauthorized);
                }
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
            return NoContent();
        }

        /// <summary>
        /// Validation for generate letter
        /// </summary>
        /// <param name="id">Attachment id</param>
        /// <param name="generateReceivingEntity">Generate Letter For Receiving Entities</param>
        /// <param name="generateCarbonCopy">Generate Letter For CCed</param>
        /// <param name="delegationId">Delegation id - optional</param>
        /// <returns></returns>
        [HttpGet]
        [Produces("application/json")]
        public async Task<IActionResult> GenerateLetterValidation(long id, bool generateReceivingEntity, bool generateCarbonCopy, long? delegationId)
        {
            try
            {
                var result = await ManageAttachment.GenerateLetterValidation(id, generateReceivingEntity, generateCarbonCopy, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, delegationId);
                if (result.IsValid)
                {
                    return Ok();
                }
                else if (!string.IsNullOrEmpty(result.Message))
                {
                    return Ok(result.Message);
                }
                return StatusCode(Microsoft.AspNetCore.Http.StatusCodes.Status400BadRequest);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }


        /// <summary>
        /// Preview the generated letter
        /// </summary>
        /// <remarks>
        /// Generate word document for each receiving entity and carbon copy.<br/><br/>
        /// Document status: <br/>
        /// <li><b>Draft</b> : replace all bookmarks</li>
        /// <li><b>In Progress</b> : replace receiving entity and carbon copy bookmarks</li>
        /// Merge all documents generated into one word document
        /// Convert the document to pdf in case <b>convertToPdf</b> is true and return the file to preview it
        /// </remarks>
        /// <param name="id">Attachment id</param>
        /// <param name="generateReceivingEntity">Generate Letter For Receiving Entities</param>
        /// <param name="generateCarbonCopy">Generate Letter For CCed</param>
        /// <param name="delegationId">Delegation id - optional</param>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(FileContentResult), 200)]
        [Produces("application/json")]
        public async Task<IActionResult> PreviewGenerateLetter(long id, bool generateReceivingEntity, bool generateCarbonCopy, long? delegationId)
        {
            try
            {
                var result = await ManageAttachment.GenerateLetter(id, generateReceivingEntity, generateCarbonCopy, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, delegationId, isPreview: true);
                if (result.FileAttachment != null)
                {
                    string name = result.FileAttachment.Name;
                    name = Regex.Replace(name, @"\s+", " ").Replace(" ", "_");
                    return File(result.FileAttachment.Data, result.FileAttachment.ContentType);
                }
                else
                {
                    return NoContent();
                }
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// Generate letter for each receiving entity and carbon copy
        /// </summary>
        /// <remarks>
        /// Generate word document for each receiving entity and carbon copy.<br/><br/>
        /// Document status: <br/>
        /// <li><b>Draft</b> : replace all bookmarks</li>
        /// <li><b>In Progress</b> : replace receiving entity and carbon copy bookmarks</li>
        /// Merge all documents generated into one word document
        /// Convert the document to pdf in case <b>convertToPdf</b> is true and replace it in storage
        /// </remarks>
        /// <param name="id">Attachment id</param>
        /// <param name="generateReceivingEntity">Generate Letter For Receiving Entities</param>
        /// <param name="generateCarbonCopy">Generate Letter For CCed</param>
        /// <param name="delegationId">Delegation id - optional</param>
        /// <returns></returns>
        [HttpGet]
        [Produces("application/json")]
        public async Task<IActionResult> GenerateLetter(long id, bool generateReceivingEntity, bool generateCarbonCopy, long? delegationId)
        {
            try
            {
                var result = await ManageAttachment.GenerateLetter(id, generateReceivingEntity, generateCarbonCopy, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, delegationId);
                if (result.Generated)
                {
                    return Ok();
                }
                else if (!string.IsNullOrEmpty(result.Message))
                {
                    return Ok(result.Message);
                }
                return StatusCode(Microsoft.AspNetCore.Http.StatusCodes.Status400BadRequest);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }


        /// <summary>
        ///  List attachments of a certain followUp
        /// </summary>
        /// <param name="draw"></param>
        /// <param name="start">Starting record - The number of records to skip before starting to return records from the query. Default value is 0.</param>
        /// <param name="length">Number of records to be returned</param>
        /// <param name="documentId">Document Id</param>
        /// <param name="transferId">Transfer id - optional</param>
        /// <param name="delegationId">Delegation id - optional</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<JsonResult> ListAttachments(int draw, int start, int length, long documentId, long? transferId, long? delegationId, long? parentDocumentId)
        {
            try
            {
                var retValue = await ManageAttachment.ListAttachments(draw, start, length, documentId, transferId, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, delegationId, parentDocumentId, Language);

                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = retValue.Item2
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }


        [HttpGet]
        [Produces("application/json")]
        public async Task<IActionResult> PreviewAttachmentValidation(long id, long? delegationId)
        {
            try
            {
                var result = await ManageAttachment.PreviewAttachmentValidation(id, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, delegationId);
                if (result.IsValid)
                {
                    return Ok();
                }
                else if (!string.IsNullOrEmpty(result.Message))
                {
                    return Ok(result.Message);
                }
                return StatusCode(Microsoft.AspNetCore.Http.StatusCodes.Status400BadRequest);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }
        [HttpGet]
        [ProducesResponseType(typeof(FileContentResult), 200)]
        [Produces("application/json")]
        public async Task<IActionResult> PreviewBeforeSign(long attachmentId, long documentId, long? delegationId, long signature, bool GenerateReference)
        {
            try
            {
                var result = await ManageAttachment.PreviewAttachment(attachmentId, documentId, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, delegationId, signature, GenerateReference);
                if (result.FileAttachment != null)
                {
                    string name = result.FileAttachment.Name;
                    name = Regex.Replace(name, @"\s+", " ").Replace(" ", "_");
                    return File(result.FileAttachment.Data, result.FileAttachment.ContentType);
                }
                else
                {
                    return NoContent();
                }
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }
        #region Versions

        /// <summary>
        ///  List an attachment version history
        /// </summary>
        /// <param name="draw"></param>
        /// <param name="start">Starting record - The number of records to skip before starting to return records from the query. Default value is 0.</param>
        /// <param name="length">Number of records to be returned</param>
        /// <param name="documentId">Document id</param>
        /// <param name="fileId">File id</param>
        /// <param name="transferId">Transfer id - optional</param>
        /// <param name="delegationId">Delegation id - optional</param>
        /// <param name="forCopy">Delegation id - optional</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(Intalio.Core.Model.GridListViewModel<AttachmentVersionModel>), 200)]
        public async Task<JsonResult> ListVersionHistory(int draw, int start, int length, long documentId, long fileId, long? transferId, long? delegationId,bool forCopy = false)
        {
            try
            {
                var item = new Core.DAL.Attachment().FindIncludeAttachmentSecurity(fileId);
                if (item != null && ManageAttachmentSecurity.ChackAttachmentSecurity(item, UserId, StructureIds))
                {
                    var retValue = await ManageAttachment.ListVersionHistory(fileId, start, length, documentId, transferId, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, delegationId, forCopy);
                    return Json(new
                    {
                        draw,
                        recordsTotal = retValue.Item1,
                        recordsFiltered = retValue.Item1,
                        data = retValue.Item2
                    });
                }
                else
                {
                    return Json(new
                    {
                        draw,
                        recordsTotal = 0,
                        recordsFiltered = 0,
                        data = "",
                        Message = 401
                    });
                }
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }

        /// <summary>
        /// Delete an attachment version
        /// </summary>
        /// <param name="id">Version id</param>
        /// <param name="fileId">Attachment id</param>
        /// <param name="documentId">Document id</param>
        /// <param name="transferId">Transfer id - optional</param>
        /// <param name="delegationId">Delegation id - optional</param>
        /// <returns></returns>
        [HttpDelete]
        public async Task<IActionResult> DeleteVersion(long id, long fileId, long documentId, long? transferId, long? delegationId)
        {
            try
            {
                Intalio.Core.Model.Result retValue = new Intalio.Core.Model.Result();
                int code = Microsoft.AspNetCore.Http.StatusCodes.Status400BadRequest;
                bool deleted = await ManageAttachment.DeleteVersion(id, fileId, documentId, transferId, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, delegationId);
                if (!deleted)
                {
                    retValue.Message = TranslationUtility.Translate("CouldNotDeleteVersion", Language);
                }
                code = Microsoft.AspNetCore.Http.StatusCodes.Status200OK;
                return StatusCode(code, retValue);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// Restore an attachment version
        /// </summary>
        /// <param name="id">Version id</param>
        /// <param name="fileId">Attachment id</param>
        /// <param name="documentId">Document id</param>
        /// <param name="transferId">Transfer id - optional</param>
        /// <param name="delegationId">Delegation id - optional</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> RestoreVersion(long id, long fileId, long documentId, long? transferId, long? delegationId)
        {
            try
            {
                int code = Microsoft.AspNetCore.Http.StatusCodes.Status400BadRequest;
                string retValue = string.Empty;
                string restoreResult = await ManageAttachment.RestoreVersion(id, fileId, documentId, transferId, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, delegationId,Language);
                switch (restoreResult)
                {
                    case "Success":
                        retValue = "Success";
                        break;
                    case "FileNotFound":
                        retValue = TranslationUtility.Translate("FileNotFound", Language);
                        break;
                    case "VersionNotFound":
                        retValue = TranslationUtility.Translate("VersionNotFound", Language);
                        break;
                    case "StorageError":
                        break;
                    case "FileSameNameExists":
                        retValue = TranslationUtility.Translate("VersionFileNameExist", Language);
                        break;
                    case "NoPermission":
                        retValue = TranslationUtility.Translate("CouldNotRestoreVersion", Language);
                        break;
                    case "CantRestoreVersionOfSignedDocument":
                        retValue = TranslationUtility.Translate("CantRestoreVersionOfSignedDocument", Language);
                        break;
                    case "OriginalFileInUse":
                        retValue = TranslationUtility.Translate("OriginalFileInUse", Language);
                        break;
                    case "FileInUse":
                        retValue = TranslationUtility.Translate("FileInUse", Language);
                        break;


                }
                code = Microsoft.AspNetCore.Http.StatusCodes.Status200OK;
                return StatusCode(code, retValue);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        #endregion

        #region AttachmentSecurity
        /// <summary>
        /// Adding Attachment Security
        /// </summary>
        /// <param name="Model">Attachment Security Model</param>
        /// <param name="AttachmentId">Attachment Id</param>
        /// <param name="DiscardCahnges">Attachment Id</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> Security(
    [FromBody] List<AttachmentSecurityModel> Model,
    [FromQuery] long? AttachmentId,
    [FromQuery] bool DiscardCahnges = false,
    [FromQuery] long? FolderId = null, long? DocumentId = null, bool IsAppend = false)
        {
            try
            {
                
                if (FolderId.HasValue)
                {
                    string message = string.Empty;
                    var filesInFolder = new Attachment().ListTreeFolderAttachments(documentId: (long)DocumentId, UserId: UserId, StructureIds: new List<long>(), FolderId);
                    List<string> attachmentsNames = new List<string>();
                    foreach (var file in filesInFolder)
                    {
                        var attachment = file;

                        var document = new Document().Find(attachment.DocumentId.Value);
                        if (document.AttachmentId == attachment.Id)
                        {
                            return StatusCode(StatusCodes.Status405MethodNotAllowed, "can't apply security to original document");
                        }

                        if (attachment.CreatedByUserId != UserId)
                        {
                            attachmentsNames.Add(attachment.Name + "." + attachment.Extension);
                            //return StatusCode(StatusCodes.Status401Unauthorized);
                            continue;

                        }

                        if (attachment.IsLocked ?? false)
                        {
                            var currentVersion = await ManageAttachment.GetCurrentVersionNumber(attachment.StorageAttachmentId);
                            var res = await ManageAttachmentSecurity.discardChangesAndCheckOutFile(attachment.Id, currentVersion);
                            if (!res)
                            {
                                return StatusCode(StatusCodes.Status500InternalServerError, "error in discard changes");
                            }
                            else
                            {
                                attachment.IsLocked = null;
                                attachment.LockedByUserId = null;
                                attachment.Update();
                            }
                        }

                        bool result;
                        if (IsAppend)
                        {
                           
                            result = await ManageAttachmentSecurity.AppendAsync(Model, attachment.Id);
                            if (!result)
                            {
                             
                                continue; 
                            }
                        }
                        else
                        {
                            
                            result = await ManageAttachmentSecurity.AddAsync(Model, attachment.Id);
                            if (!result)
                            {
                                
                                continue; 
                            }
                        }
                    }
                    if (attachmentsNames.Count > 0)
                    {
                        message = TranslationUtility.Translate("SecurityNotAppliedYouAreNotTheCreatorOn", Language) + " : \n" + string.Join("\n", attachmentsNames);
                    }
                    return Ok(new { message });
                }
                else
                {
                    
                    var attachment = new Attachment().Find((long)AttachmentId);
                    var document = new Document().Find(attachment.DocumentId.Value);

                    if (document.AttachmentId == AttachmentId)
                    {
                        return StatusCode(StatusCodes.Status405MethodNotAllowed, "can't apply security to original document");
                    }

                    if (attachment.CreatedByUserId != UserId)
                    {
                        return StatusCode(StatusCodes.Status401Unauthorized);
                    }

                    if (attachment.IsLocked ?? false)
                    {
                        var currentVersion = await ManageAttachment.GetCurrentVersionNumber(attachment.StorageAttachmentId);
                        var res = await ManageAttachmentSecurity.discardChangesAndCheckOutFile((long)AttachmentId, currentVersion);
                        if (!res)
                        {
                            return StatusCode(StatusCodes.Status500InternalServerError, "error in discard changes");
                        }
                        else
                        {
                            attachment.IsLocked = null;
                            attachment.LockedByUserId = null;
                            attachment.Update();
                        }
                    }

                    bool result = await ManageAttachmentSecurity.AddAsync(Model, AttachmentId);
                    if (result)
                        return Ok(true);
                    else
                        return BadRequest("Failed to save security for the attachment.");
                }
            }
            catch (Exception ex)
            {
                return StatusCode(StatusCodes.Status500InternalServerError, "Internal Server Error: " + ex.Message);
            }
        }





        /// <summary>
        /// Delete an attachment
        /// </summary>
        /// <param name="AttachmentId">Attachment id</param>
        /// <returns></returns>
        [HttpDelete]
        public async Task<IActionResult> RemoveSecurity([FromQuery] long AttachmentId,long? FolderId = null, long? DocumentId = null)
        {
            try
            {
                if (FolderId.HasValue)
                {
                    string message = string.Empty;
                    var filesInFolder = new Attachment().ListTreeFolderAttachments(documentId: (long)DocumentId, UserId: UserId, StructureIds: new List<long>(), FolderId);
                    List<string> attachmentsNames = new List<string>();
                    foreach (var file in filesInFolder)
                    {
                        var attachment = file;

                        var document = new Document().Find(attachment.DocumentId.Value);
                        if (document.AttachmentId == attachment.Id)
                        {
                            return StatusCode(StatusCodes.Status405MethodNotAllowed, "can't apply security to original document");
                        }

                        if (attachment.CreatedByUserId != UserId)
                        {
                            attachmentsNames.Add(attachment.Name + "." + attachment.Extension);
                            //return StatusCode(StatusCodes.Status401Unauthorized);
                            continue;
                        }

                        bool result = await ManageAttachmentSecurity.RemoveSecurityByAttachmentId(attachment.Id);
                        if (!result)
                        {
                            return BadRequest("Failed to remove security for one or more files in the folder.");
                        }
                    }
                    if (attachmentsNames.Count>0)
                    {
                        message = TranslationUtility.Translate("SecurityNotAppliedYouAreNotTheCreatorOn", Language) + " : \n" + string.Join("\n", attachmentsNames);
                    }
                    return Ok(message);
                }
                else
                {
                    var attachment = new Attachment().Find(AttachmentId);
                    var document = new Document().Find(attachment.DocumentId.Value);
                    if (document.AttachmentId == AttachmentId)
                    {
                        return StatusCode(StatusCodes.Status405MethodNotAllowed, "can't apply security to original document");
                    }

                    if (attachment.CreatedByUserId != UserId)
                    {
                        return StatusCode(StatusCodes.Status401Unauthorized);
                    }
                    bool Result = await ManageAttachmentSecurity.RemoveSecurityByAttachmentId(AttachmentId);
                    if (Result)
                        return Ok(true);
                    else
                        return BadRequest("Failed to save security");
                }
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }


        /// <summary>
        /// Return The security For An Attachment
        /// </summary>
        /// <param name="attachmentId">Attachment id</param>
        /// <returns></returns>
        [HttpGet]
        [Produces("application/json")]
        public async Task<IActionResult> GetAttachmentSecurity(long attachmentId, long? FolderId)
        {
            try
            {
                if (FolderId != null)
                {
                    return Ok(new List<AttachmentSecurity>());
                }
                var attachment = new Attachment().Find(attachmentId);
                var document = new Document().Find(attachment.DocumentId.Value);
                if (document.AttachmentId == attachmentId)
                {
                    return StatusCode(StatusCodes.Status405MethodNotAllowed, "can't apply security to original document");
                }

                if (attachment.CreatedByUserId != UserId )
                {
                    return StatusCode(StatusCodes.Status401Unauthorized);
                }
                var list = await new AttachmentSecurity().FindByAttachmentIdIncludeUserAndStructure(attachmentId);
                var returnedList = list.Select(x => new
                {
                    structureId = x.StructureId,
                    userId = x.UserId,
                    name = x.UserId == null ? x.Structure.Name : x.User.Firstname + " " + x.User.Lastname,
                    disable = x.UserId != null && x.UserId == UserId && x.StructureId != 0&&x.StructureId==StructureId
                }).ToList();
                return Ok(returnedList);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// Return The security For An Attachment
        /// </summary>
        /// <param name="AttachmentId">Attachment id</param>
        /// <param name="Model">Data</param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult CheckLockUserIdIsIncluded([FromBody] List<AttachmentSecurityModel> Model, [FromQuery] long AttachmentId, long? FolderId)
        {
            try
            {
                if (FolderId != null)
                {
                    return Ok(new { status = true, username = "" });
                }
                var attachment = new Attachment().FindIncludeAttachmentSecurity(AttachmentId);
                var document = new Document().Find(attachment.DocumentId.Value);
                if (document.AttachmentId == AttachmentId)
                {
                    return StatusCode(StatusCodes.Status405MethodNotAllowed, "can't apply security to original document");
                }

                if (attachment.CreatedByUserId != UserId)
                {
                    return StatusCode(StatusCodes.Status401Unauthorized);
                }
                if (attachment.IsLocked ?? false)
                {
                    var username = ManageUser.GetFullName(attachment.LockedByUserId.Value, Language);
                    return Ok(new { status = Model.Select(x => x.toUserId).Contains(attachment.LockedByUserId), username = username });
                }
                return Ok(new { status = true, username = "" });
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// Return The uploader security For An Attachment
        /// </summary>
        /// <param name="attachmentId">Attachment id</param>
        /// <returns></returns>
        [HttpGet]
        [Produces("application/json")]
        public IActionResult GetUploaderAttachmentSecurity(long attachmentId, long FolderId)
        {
            try
            {
                if (FolderId != null)
                {
                    return Ok(new List<object>(){
                        new{
                        structureId = (long?)StructureId,
                        userId = (long?)UserId,
                        name = ManageUser.GetFullName(UserId, Language),
                        disable = true
                        }
                    });
                }
                var attachment = new Attachment().Find(attachmentId);
                var document = new Document().Find(attachment.DocumentId.Value);
                if (document.AttachmentId == attachmentId)
                {
                    return StatusCode(StatusCodes.Status405MethodNotAllowed, "can't apply security to original document");
                }

                if (attachment.CreatedByUserId != UserId)
                {
                    return StatusCode(StatusCodes.Status401Unauthorized);
                }
                return Ok(new List<object>(){
                        new{
                        structureId = (long?)StructureId,
                        userId = (long?)UserId,
                        name = ManageUser.GetFullName(UserId, Language),
                        disable = true
                        }
                    });
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }
        #endregion



        [HttpGet]
        [Produces("application/json")]
        public IActionResult CheckAttachmentisLocked(long attachmentId)
        {
            var attachment = new Attachment().Find(attachmentId);
            return Ok(attachment.IsLocked == true || attachment.DocumentLockId.HasValue);
        }

        [HttpGet]
        public async Task<JsonResult> ListFollowUpAttachments(int draw, int start, int length, long documentId, long? transferId, long? delegationId, long? parentDocumentId)
        {
            try
            {
                var retValue = await ManageAttachment.ListFollowUpAttachments(draw, start, length, documentId, transferId, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, delegationId, parentDocumentId, Language);

                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = retValue.Item2
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }
        [HttpGet]
        public async Task<IActionResult> GetAttachmentProperties(long attachmentId)
        {
            try
            {
                var retValue = await ManageAttachment.GetAttachmentProperties(attachmentId, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, null, Language);

                return Ok(retValue);
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }

        [HttpPost]
        public async Task<IActionResult> SaveAttachmentProperties(long attachmentId, string attachmentForm)
        {
            try
            {
                var retValue = await ManageAttachment.SaveAttachmentProperties(attachmentId, attachmentForm, UserId, StructureIds, IsStructureReceiver, PrivacyLevel, null, Language);

                return Ok(retValue);
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetCurrentVersionNumber(long attachmentId)
        {
            var attachment = new Attachment().Find(attachmentId);
            return Ok(attachment!=null?await ManageAttachment.GetCurrentVersionNumber(attachment.StorageAttachmentId):"");
        }
        #endregion
    }
}
