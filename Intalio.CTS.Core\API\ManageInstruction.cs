﻿using Intalio.Core;
using Intalio.CTS.Core.DAL;
using Intalio.CTS.Core.Model;
using Intalio.CTS.Core.Utility;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Intalio.CTS.Core.API
{
  
        public static class ManageInstruction
        {
           
            public static bool Create(InstructionModel model,long CreatedByUserId, long structureId)
            {
                bool retValue=false;
                var instruction = new Instruction
                {
                    UserId = model.UserId,
                    Instructions = model.Instructions,
                    DocumentId = model.DocumentId,
                    CreatedByUserId = CreatedByUserId,
                    CreatedDate = DateTime.Now,
                    StructureId = structureId,
                    ManagerStructureId=model.ManagerStructureId
                };
                instruction.Insert(); 
                model.Id = instruction.Id;
                retValue = true;
                return retValue;
            }


        public static bool Edit(InstructionModel model, long userId,long structureId)
        {
            bool retValue = false;
            Instruction item = new Instruction().Find((long)model.Id);
            if (item != null)
            {
                item.Instructions = model.Instructions;
                item.StructureId = structureId;
                item.UserId=model.UserId;
                item.ManagerStructureId = model.ManagerStructureId;
                item.Update();
                retValue = true;
            }
            return retValue;
            
        }


        public static bool Delete(long id , long userId)
            {
                bool retValue = false;
                var item= new Instruction().Find(id);
                if (item != null && item.CreatedByUserId == userId)
                {
                    item.Delete(id);
                    retValue = true;
                }
               return retValue ;
            }


        public static async Task<(int, List<InstructionListViewModel>)> List(int startIndex, int pageSize,
        long documentId, long userId, int roleId, long StructureId, Language language = Language.EN)
        {

            var retValue = (0, new List<InstructionListViewModel>());


            Instruction item = new Instruction();
            var countResult = item.GetCount(documentId, userId);
            var itemList = await item.ListAsync(startIndex, pageSize, documentId, userId);

            retValue = (await countResult, itemList.Select(t => new InstructionListViewModel
            {
                Id = t.Id,
                Instructions = t.Instructions,
                DocumentId = t.DocumentId,
                UserId = t.UserId,
                StructureId = StructureId,
                CreatedDate = t.CreatedDate,
                ManagerStructureId = t.ManagerStructureId,
                StructureName=ManageStructure.GetStructureNameById(t.StructureId),
                ManagerStructureName= ManageStructure.GetStructureNameById(t.ManagerStructureId),
                Name = t.User != null
                ? (language == Language.EN ? $"{t.User.Firstname} {t.User.Lastname}" : IdentityHelperExtension.GetFullName(t.User.Id, language)) : string.Empty,

                CreatedBy = t.CreatedByUser != null
                ? (language == Language.EN ? $"{t.CreatedByUser.Firstname} {t.CreatedByUser.Lastname}" : IdentityHelperExtension.GetFullName(t.CreatedByUser.Id, language)) : string.Empty,
                CreatedByUserId = t.CreatedByUserId

            }).ToList());


            return retValue;
        }
    }
    }

